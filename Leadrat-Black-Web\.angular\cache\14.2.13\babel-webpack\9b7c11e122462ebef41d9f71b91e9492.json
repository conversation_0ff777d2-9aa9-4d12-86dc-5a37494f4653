{"ast": null, "code": "// Copy-pasted from:\n// https://github.com/substack/semver-compare/blob/master/index.js\n//\n// Inlining this function because some users reported issues with\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\n//\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\nexport default function (a, b) {\n  a = a.split('-');\n  b = b.split('-');\n  var pa = a[0].split('.');\n  var pb = b[0].split('.');\n\n  for (var i = 0; i < 3; i++) {\n    var na = Number(pa[i]);\n    var nb = Number(pb[i]);\n    if (na > nb) return 1;\n    if (nb > na) return -1;\n    if (!isNaN(na) && isNaN(nb)) return 1;\n    if (isNaN(na) && !isNaN(nb)) return -1;\n  }\n\n  if (a[1] && b[1]) {\n    return a[1] > b[1] ? 1 : a[1] < b[1] ? -1 : 0;\n  }\n\n  return !a[1] && b[1] ? 1 : a[1] && !b[1] ? -1 : 0;\n} //# sourceMappingURL=semver-compare.js.map", "map": null, "metadata": {}, "sourceType": "module"}