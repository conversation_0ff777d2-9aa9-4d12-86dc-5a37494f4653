{"ast": null, "code": "/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function (object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n\n    return object;\n  };\n}\n\nexport default createBaseFor;", "map": null, "metadata": {}, "sourceType": "module"}