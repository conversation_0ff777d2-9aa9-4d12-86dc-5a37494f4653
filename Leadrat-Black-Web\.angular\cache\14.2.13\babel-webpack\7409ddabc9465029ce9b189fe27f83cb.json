{"ast": null, "code": "// so it doesn't throw if no window or matchMedia\nvar w = typeof window !== 'undefined' ? window : {\n  screen: {},\n  navigator: {}\n};\n\nvar matchMedia = (w.matchMedia || function () {\n  return {\n    matches: false\n  };\n}).bind(w); // passive events test\n// adapted from https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n\n\nvar passiveOptionAccessed = false;\nvar options = {\n  get passive() {\n    return passiveOptionAccessed = true;\n  }\n\n}; // have to set and remove a no-op listener instead of null\n// (which was used previously), because Edge v15 throws an error\n// when providing a null callback.\n// https://github.com/rafgraph/detect-passive-events/pull/3\n// eslint-disable-next-line @typescript-eslint/no-empty-function\n\nvar noop = function () {};\n\nw.addEventListener && w.addEventListener('p', noop, options);\nw.removeEventListener && w.removeEventListener('p', noop, false);\nvar supportsPassiveEvents = passiveOptionAccessed;\nvar supportsPointerEvents = ('PointerEvent' in w);\nvar onTouchStartInWindow = ('ontouchstart' in w);\nvar touchEventInWindow = ('TouchEvent' in w); // onTouchStartInWindow is the old-old-legacy way to determine a touch device\n// and many websites interpreted it to mean that the device is a touch only phone,\n// so today browsers on a desktop/laptop computer with a touch screen (primary input mouse)\n// have onTouchStartInWindow as false (to prevent from being confused with a\n// touchOnly phone) even though they support the TouchEvents API, so need to check\n// both onTouchStartInWindow and touchEventInWindow for TouchEvent support,\n// however, some browsers (chromium) support the TouchEvents API even when running on\n// a mouse only device (touchEventInWindow true, but onTouchStartInWindow false)\n// so the touchEventInWindow check needs to include an coarse pointer media query\n\nvar supportsTouchEvents = onTouchStartInWindow || touchEventInWindow && matchMedia('(any-pointer: coarse)').matches;\nvar hasTouch = (w.navigator.maxTouchPoints || 0) > 0 || supportsTouchEvents; // userAgent is used as a backup to correct for known device/browser bugs\n// and when the browser doesn't support interaction media queries (pointer and hover)\n// see https://caniuse.com/css-media-interaction\n\nvar userAgent = w.navigator.userAgent || ''; // iPads now support a mouse that can hover, however the media query interaction\n// feature results always say iPads only have a coarse pointer that can't hover\n// even when a mouse is connected (anyFine and anyHover are always false),\n// this unfortunately indicates a touch only device but iPads should\n// be classified as a hybrid device, so determine if it is an iPad\n// to indicate it should be treated as a hybrid device with anyHover true\n\nvar isIPad = matchMedia('(pointer: coarse)').matches && // both iPad and iPhone can \"request desktop site\", which sets the userAgent to Macintosh\n// so need to check both userAgents to determine if it is an iOS device\n// and screen size to separate iPad from iPhone\n/iPad|Macintosh/.test(userAgent) && Math.min(w.screen.width || 0, w.screen.height || 0) >= 768;\nvar hasCoarsePrimaryPointer = (matchMedia('(pointer: coarse)').matches || // if the pointer is not coarse and not fine then the browser doesn't support\n// interaction media queries (see https://caniuse.com/css-media-interaction)\n// so if it has onTouchStartInWindow assume it has a coarse primary pointer\n!matchMedia('(pointer: fine)').matches && onTouchStartInWindow) && // bug in firefox (as of v81) on hybrid windows devices where the interaction media queries\n// always indicate a touch only device (only has a coarse pointer that can't hover)\n// so assume that the primary pointer is not coarse for firefox windows\n!/Windows.*Firefox/.test(userAgent);\nvar hasAnyHoverOrAnyFinePointer = matchMedia('(any-pointer: fine)').matches || matchMedia('(any-hover: hover)').matches || // iPads might have an input device that can hover, so assume it has anyHover\nisIPad || // if no onTouchStartInWindow then the browser is indicating that it is not a touch only device\n// see above note for supportsTouchEvents\n!onTouchStartInWindow; // a hybrid device is one that both hasTouch and\n// any input can hover or has a fine pointer, or the primary pointer is not coarse\n// if it's not a hybrid, then if it hasTouch it's touchOnly, otherwise it's mouseOnly\n\nvar deviceType = hasTouch && (hasAnyHoverOrAnyFinePointer || !hasCoarsePrimaryPointer) ? 'hybrid' : hasTouch ? 'touchOnly' : 'mouseOnly';\nvar primaryInput = deviceType === 'mouseOnly' ? 'mouse' : deviceType === 'touchOnly' ? 'touch' : // if the device is a hybrid, then if the primary pointer is coarse\n// assume the primaryInput is touch, otherwise assume it's mouse\nhasCoarsePrimaryPointer ? 'touch' : 'mouse';\nexport { deviceType, primaryInput, supportsPassiveEvents, supportsPointerEvents, supportsTouchEvents }; //# sourceMappingURL=detect-it.esm.js.map", "map": null, "metadata": {}, "sourceType": "module"}