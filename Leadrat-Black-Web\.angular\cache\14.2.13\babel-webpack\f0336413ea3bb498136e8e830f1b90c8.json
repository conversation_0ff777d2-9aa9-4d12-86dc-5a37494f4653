{"ast": null, "code": "import { Subject } from '../Subject';\nimport { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function windowTime(windowTimeSpan) {\n  let scheduler = async;\n  let windowCreationInterval = null;\n  let maxWindowSize = Number.POSITIVE_INFINITY;\n\n  if (isScheduler(arguments[3])) {\n    scheduler = arguments[3];\n  }\n\n  if (isScheduler(arguments[2])) {\n    scheduler = arguments[2];\n  } else if (isNumeric(arguments[2])) {\n    maxWindowSize = Number(arguments[2]);\n  }\n\n  if (isScheduler(arguments[1])) {\n    scheduler = arguments[1];\n  } else if (isNumeric(arguments[1])) {\n    windowCreationInterval = Number(arguments[1]);\n  }\n\n  return function windowTimeOperatorFunction(source) {\n    return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));\n  };\n}\n\nclass WindowTimeOperator {\n  constructor(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));\n  }\n\n}\n\nclass CountedSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._numberOfNextedValues = 0;\n  }\n\n  next(value) {\n    this._numberOfNextedValues++;\n    super.next(value);\n  }\n\n  get numberOfNextedValues() {\n    return this._numberOfNextedValues;\n  }\n\n}\n\nclass WindowTimeSubscriber extends Subscriber {\n  constructor(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    super(destination);\n    this.destination = destination;\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n    this.windows = [];\n    const window = this.openWindow();\n\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      const closeState = {\n        subscriber: this,\n        window,\n        context: null\n      };\n      const creationState = {\n        windowTimeSpan,\n        windowCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));\n    } else {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        window,\n        windowTimeSpan\n      };\n      this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));\n    }\n  }\n\n  _next(value) {\n    const windows = this.windows;\n    const len = windows.length;\n\n    for (let i = 0; i < len; i++) {\n      const window = windows[i];\n\n      if (!window.closed) {\n        window.next(value);\n\n        if (window.numberOfNextedValues >= this.maxWindowSize) {\n          this.closeWindow(window);\n        }\n      }\n    }\n  }\n\n  _error(err) {\n    const windows = this.windows;\n\n    while (windows.length > 0) {\n      windows.shift().error(err);\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    const windows = this.windows;\n\n    while (windows.length > 0) {\n      const window = windows.shift();\n\n      if (!window.closed) {\n        window.complete();\n      }\n    }\n\n    this.destination.complete();\n  }\n\n  openWindow() {\n    const window = new CountedSubject();\n    this.windows.push(window);\n    const destination = this.destination;\n    destination.next(window);\n    return window;\n  }\n\n  closeWindow(window) {\n    window.complete();\n    const windows = this.windows;\n    windows.splice(windows.indexOf(window), 1);\n  }\n\n}\n\nfunction dispatchWindowTimeSpanOnly(state) {\n  const {\n    subscriber,\n    windowTimeSpan,\n    window\n  } = state;\n\n  if (window) {\n    subscriber.closeWindow(window);\n  }\n\n  state.window = subscriber.openWindow();\n  this.schedule(state, windowTimeSpan);\n}\n\nfunction dispatchWindowCreation(state) {\n  const {\n    windowTimeSpan,\n    subscriber,\n    scheduler,\n    windowCreationInterval\n  } = state;\n  const window = subscriber.openWindow();\n  const action = this;\n  let context = {\n    action,\n    subscription: null\n  };\n  const timeSpanState = {\n    subscriber,\n    window,\n    context\n  };\n  context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);\n  action.add(context.subscription);\n  action.schedule(state, windowCreationInterval);\n}\n\nfunction dispatchWindowClose(state) {\n  const {\n    subscriber,\n    window,\n    context\n  } = state;\n\n  if (context && context.action && context.subscription) {\n    context.action.remove(context.subscription);\n  }\n\n  subscriber.closeWindow(window);\n} //# sourceMappingURL=windowTime.js.map", "map": null, "metadata": {}, "sourceType": "module"}