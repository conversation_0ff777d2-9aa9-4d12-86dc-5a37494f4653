{"ast": null, "code": "/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n    HOT_SPAN = 16;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeNow = Date.now;\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\n\nfunction shortOut(func) {\n  var count = 0,\n      lastCalled = 0;\n  return function () {\n    var stamp = nativeNow(),\n        remaining = HOT_SPAN - (stamp - lastCalled);\n    lastCalled = stamp;\n\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n\n    return func.apply(undefined, arguments);\n  };\n}\n\nexport default shortOut;", "map": null, "metadata": {}, "sourceType": "module"}