{"ast": null, "code": "import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\n\nfunction assignMergeValue(object, key, value) {\n  if (value !== undefined && !eq(object[key], value) || value === undefined && !(key in object)) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nexport default assignMergeValue;", "map": null, "metadata": {}, "sourceType": "module"}