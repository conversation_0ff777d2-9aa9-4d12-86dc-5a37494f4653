{"ast": null, "code": "import { Subject } from '../Subject';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowWhen(closingSelector) {\n  return function windowWhenOperatorFunction(source) {\n    return source.lift(new WindowOperator(closingSelector));\n  };\n}\n\nclass WindowOperator {\n  constructor(closingSelector) {\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowSubscriber(subscriber, this.closingSelector));\n  }\n\n}\n\nclass WindowSubscriber extends OuterSubscriber {\n  constructor(destination, closingSelector) {\n    super(destination);\n    this.destination = destination;\n    this.closingSelector = closingSelector;\n    this.openWindow();\n  }\n\n  notifyNext(_outerValue, _innerValue, _outerIndex, _innerIndex, innerSub) {\n    this.openWindow(innerSub);\n  }\n\n  notifyError(error) {\n    this._error(error);\n  }\n\n  notifyComplete(innerSub) {\n    this.openWindow(innerSub);\n  }\n\n  _next(value) {\n    this.window.next(value);\n  }\n\n  _error(err) {\n    this.window.error(err);\n    this.destination.error(err);\n    this.unsubscribeClosingNotification();\n  }\n\n  _complete() {\n    this.window.complete();\n    this.destination.complete();\n    this.unsubscribeClosingNotification();\n  }\n\n  unsubscribeClosingNotification() {\n    if (this.closingNotification) {\n      this.closingNotification.unsubscribe();\n    }\n  }\n\n  openWindow(innerSub = null) {\n    if (innerSub) {\n      this.remove(innerSub);\n      innerSub.unsubscribe();\n    }\n\n    const prevWindow = this.window;\n\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n\n    const window = this.window = new Subject();\n    this.destination.next(window);\n    let closingNotifier;\n\n    try {\n      const {\n        closingSelector\n      } = this;\n      closingNotifier = closingSelector();\n    } catch (e) {\n      this.destination.error(e);\n      this.window.error(e);\n      return;\n    }\n\n    this.add(this.closingNotification = subscribeToResult(this, closingNotifier));\n  }\n\n} //# sourceMappingURL=windowWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}