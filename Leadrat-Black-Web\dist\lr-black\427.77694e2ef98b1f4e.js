"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[427],{11427:(_e,L,r)=>{r.d(L,{v:()=>me});var e=r(5e3),c=r(93075),h=r(82722),m=r(2976),d=r(51420),v=r(61021),O=r(26733),x=r(82667),y=r(97047),A=r(85768),g=r(40553),b=r(66844),M=r(77225),N=r(30166),f=r(32049),w=r(38827),D=r(65620),P=r(71511),F=r(63253),K=r(22313),R=r(1880),C=r(69808),S=r(24376),V=r(46302),G=r(5441),W=r(17447),j=r(18995);const $=["fileInput"],z=["validModal"];function H(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",31)(2,"browse-drop-upload",32),e.\u0275\u0275listener("uploadedFile",function(n){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onFileSelection(n))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}2&a&&(e.\u0275\u0275advance(2),e.\u0275\u0275property("allowedFileType","excel")("isExcelFile",!0))}function Z(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",33)(2,"div",34)(3,"div",35)(4,"div",36),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",37),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"a",38)(10,"div",39),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.currentStep=1)}),e.\u0275\u0275text(11),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",40),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.replaceFile())}),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(16,"input",41,42),e.\u0275\u0275listener("change",function(n){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.onFileSelection(n.target.files[0]))}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(6,4,"BULK_LEAD.successfully-upload")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==t.selectedFile?null:t.selectedFile.name),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(12,6,"BUTTONS.delete")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(15,8,"BUTTONS.replace")," ")}}const Q=function(a){return{"pe-none opacity-6":a}};function J(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275element(1,"div",43),e.\u0275\u0275elementStart(2,"div",44)(3,"a",45),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.navigateToHome())}),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",46),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.uploadFile())}),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(5,3,"BUTTONS.cancel")," Import"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(7,Q,1==t.currentStep)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(8,5,"BUTTONS.proceed"),"")}}function X(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",16)(2,"div",17),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",18)(6,"div",19),e.\u0275\u0275element(7,"span",20),e.\u0275\u0275elementStart(8,"span",21),e.\u0275\u0275text(9),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"a",22),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.trackDownload())}),e.\u0275\u0275elementStart(12,"div",23),e.\u0275\u0275element(13,"span",24),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"span",25),e.\u0275\u0275text(15,"Download template"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(16,"div",26),e.\u0275\u0275text(17),e.\u0275\u0275pipe(18,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(19,"div",27),e.\u0275\u0275elementStart(20,"div",28),e.\u0275\u0275element(21,"span",20),e.\u0275\u0275elementStart(22,"span",21),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"div")(26,"div",29),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"div",30),e.\u0275\u0275text(30),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(31,"div",27),e.\u0275\u0275elementStart(32,"div",28),e.\u0275\u0275element(33,"span",20),e.\u0275\u0275elementStart(34,"span",21),e.\u0275\u0275text(35),e.\u0275\u0275pipe(36,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(37,"div")(38,"div",29),e.\u0275\u0275text(39),e.\u0275\u0275pipe(40,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(41,"div",30),e.\u0275\u0275text(42),e.\u0275\u0275pipe(43,"translate"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(44,H,3,2,"ng-container",13),e.\u0275\u0275template(45,Z,18,10,"ng-container",13),e.\u0275\u0275template(46,J,9,9,"ng-container",13),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,13,"BULK_LEAD.importing-description")),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(10,15,"GLOBAL.step-1-")),e.\u0275\u0275advance(2),e.\u0275\u0275property("href",t.excelTemplatePath,e.\u0275\u0275sanitizeUrl),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(18,17,"BULK_LEAD.download-description"),""),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(24,19,"GLOBAL.step-2-")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(28,21,"BULK_LEAD.prepare-import-file")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("change the dummy data in the sample file to your ",t.moduleName," details"),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(36,23,"GLOBAL.step-3-")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(40,25,"BULK_LEAD.upload-your-file")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(43,27,"BULK_LEAD.upload-description")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",1==t.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",2==t.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",1==t.currentStep||2==t.currentStep)}}function Y(a,s){1&a&&(e.\u0275\u0275elementStart(0,"div",77),e.\u0275\u0275text(1," Sheet is a required field."),e.\u0275\u0275elementEnd())}function q(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275element(1,"div",43),e.\u0275\u0275elementStart(2,"div",60)(3,"div",61),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",62)(6,"div",84),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(8,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",65),e.\u0275\u0275element(10,"ng-select",85),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(),l=t.index,n=t.$implicit,i=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(l+2),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.displayName),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(11,6,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",i.formKeys)("formControlName",n.mappingControlName)}}function ee(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,q,12,8,"ng-container",13),e.\u0275\u0275elementContainerEnd()),2&a){const t=s.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!("Name"===t.displayName&&"Name"===t.mappingControlName||"Primary Number"===t.displayName&&"ContactNo"===t.mappingControlName))}}function te(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form",78),e.\u0275\u0275element(2,"div",43),e.\u0275\u0275elementStart(3,"div",60)(4,"div",61),e.\u0275\u0275text(5,"2"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",62)(7,"div",63),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(10,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",65)(12,"form-errors-wrapper",79),e.\u0275\u0275element(13,"ng-select",80),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(15,"div",43),e.\u0275\u0275elementStart(16,"div",60)(17,"div",61),e.\u0275\u0275text(18,"3"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"div",62)(20,"div",63),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(23,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"div",65)(25,"form-errors-wrapper",81),e.\u0275\u0275element(26,"ng-select",82),e.\u0275\u0275pipe(27,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(28,ee,2,1,"ng-container",83),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("formGroup",t.leadMappingForm),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,12,"GLOBAL.name")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.leadMappingForm.controls.Name),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(14,14,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",t.formKeys),e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(22,16,"GLOBAL.primary-number")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.leadMappingForm.controls.ContactNo),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(27,18,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",t.formKeys),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.getBulkFields(t.leadBulkFields))}}function ne(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275element(1,"div",43),e.\u0275\u0275elementStart(2,"div",60)(3,"div",61),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",62)(6,"div",84),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(8,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",65),e.\u0275\u0275element(10,"ng-select",85),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(),l=t.index,n=t.$implicit,i=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(l+2),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.displayName),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(11,6,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",i.formKeys)("formControlName",n.mappingControlName)}}function le(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,ne,12,8,"ng-container",13),e.\u0275\u0275elementContainerEnd()),2&a){const t=s.$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!("Name"===t.displayName&&"Name"===t.mappingControlName||"Primary Number"===t.displayName&&"ContactNo"===t.mappingControlName))}}function ae(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"form",78),e.\u0275\u0275element(1,"div",43),e.\u0275\u0275elementStart(2,"div",60)(3,"div",61),e.\u0275\u0275text(4,"2"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",62)(6,"div",63),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",65)(11,"form-errors-wrapper",79),e.\u0275\u0275element(12,"ng-select",80),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(14,"div",43),e.\u0275\u0275elementStart(15,"div",60)(16,"div",61),e.\u0275\u0275text(17,"3"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",62)(19,"div",63),e.\u0275\u0275text(20),e.\u0275\u0275pipe(21,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(22,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"div",65)(24,"form-errors-wrapper",81),e.\u0275\u0275element(25,"ng-select",82),e.\u0275\u0275pipe(26,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(27,le,2,1,"ng-container",83),e.\u0275\u0275elementEnd()),2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("formGroup",t.dataMappingForm),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,12,"GLOBAL.name")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.dataMappingForm.controls.Name),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(13,14,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",t.formKeys),e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(21,16,"GLOBAL.primary-number")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.dataMappingForm.controls.ContactNo),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(26,18,"GLOBAL.select-a-field")),e.\u0275\u0275property("virtualScroll",!0)("items",t.formKeys),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.getBulkFields(t.dataBulkFields))}}function ie(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",47)(2,"div",48),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",49)(6,"span",50),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"span",51),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"span"),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"span",51),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"span",50),e.\u0275\u0275text(19,"fields that match our"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"span",52),e.\u0275\u0275text(21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"span"),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(25,"div",53)(26,"div",54),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"div",55)(30,"span",56),e.\u0275\u0275text(31),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"span"),e.\u0275\u0275text(33),e.\u0275\u0275pipe(34,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(35,"div",57)(36,"span",58),e.\u0275\u0275text(37),e.\u0275\u0275pipe(38,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(39,"span"),e.\u0275\u0275text(40),e.\u0275\u0275pipe(41,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(42,"span",51),e.\u0275\u0275text(43),e.\u0275\u0275pipe(44,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(45,"span"),e.\u0275\u0275text(46),e.\u0275\u0275pipe(47,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(48,"div",59)(49,"div",60)(50,"div",61),e.\u0275\u0275text(51,"1"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(52,"div",62)(53,"div",63),e.\u0275\u0275text(54),e.\u0275\u0275pipe(55,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(56,"div",64),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(57,"div",65)(58,"div",66)(59,"ng-select",67),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.selectedSheet=n)})("change",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.onSheetSelection())}),e.\u0275\u0275pipe(60,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(61,Y,2,0,"div",68),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(62,te,29,20,"ng-container",69),e.\u0275\u0275template(63,ae,28,20,"ng-template",null,70,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(65,"div",71)(66,"span",72),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.navigateToHome())}),e.\u0275\u0275text(67),e.\u0275\u0275pipe(68,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(69,"span",73),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.currentStep=2)}),e.\u0275\u0275element(70,"span",74),e.\u0275\u0275elementStart(71,"span",75),e.\u0275\u0275text(72),e.\u0275\u0275pipe(73,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(74,"span",76),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.isValidForm())}),e.\u0275\u0275text(75),e.\u0275\u0275pipe(76,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=e.\u0275\u0275reference(64),l=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,25,"GLOBAL.field-mapping")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,27,"GLOBAL.select-the")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,29,"GLOBAL.csv")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(14,31,"GLOBAL.or")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(17,33,"GLOBAL.excel")),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(l.getAppName()),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(24,35,"GLOBAL.fields")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(28,37,"GLOBAL.s-no")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(l.getAppName()),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(34,39,"GLOBAL.fields")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(38,41,"GLOBAL.csv")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(41,43,"GLOBAL.or")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(44,45,"GLOBAL.excel")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(47,47,"GLOBAL.fields")),e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(55,49,"BULK_LEAD.select-sheet")),e.\u0275\u0275advance(5),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(60,51,"GLOBAL.select-sheet")),e.\u0275\u0275property("virtualScroll",!0)("items",l.sheetNames)("ngModel",l.selectedSheet),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!l.selectedSheet),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","leads"==l.moduleName)("ngIfElse",t),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(68,53,"BUTTONS.cancel"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(73,55,"BUTTONS.back")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(76,57,"GLOBAL.confirm-mapping"))}}function re(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",87)(1,"h3",88),e.\u0275\u0275text(2,"Please choose the sheet and verify the mapped fields"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",89)(4,"button",90),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.modalRef.hide())}),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"button",91),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.confirmSheet())}),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd()()()}2&a&&(e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(6,2,"BUTTONS.cancel")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,4,"GLOBAL.verified")))}function oe(a,s){if(1&a&&e.\u0275\u0275template(0,re,10,6,"div",86),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngIf",t.isValidModal)}}function se(a,s){if(1&a&&e.\u0275\u0275text(0),2&a){const t=s.item;e.\u0275\u0275textInterpolate2(" ",t.firstName," ",t.lastName," ")}}function pe(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",110),e.\u0275\u0275element(1,"input",111)(2,"span",112),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,l=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",l.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName,"")}}function de(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",92)(2,"div",17),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",93),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"div",94),e.\u0275\u0275element(8,"span",95),e.\u0275\u0275elementStart(9,"span",96),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"span",97),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(14,"div",98),e.\u0275\u0275elementStart(15,"div",99)(16,"div",100),e.\u0275\u0275element(17,"span",95),e.\u0275\u0275elementStart(18,"span",101),e.\u0275\u0275text(19," field mapping done"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(20,"div",102),e.\u0275\u0275elementStart(21,"div",103)(22,"div",100),e.\u0275\u0275element(23,"span",95),e.\u0275\u0275elementStart(24,"span",101),e.\u0275\u0275text(25),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"span",104),e.\u0275\u0275text(27),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",105)(29,"ng-select",106),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.selectedUserId=n)}),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275template(31,se,1,2,"ng-template",107),e.\u0275\u0275template(32,pe,4,5,"ng-template",108),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275element(33,"div",43),e.\u0275\u0275elementStart(34,"div",44)(35,"span",72),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.navigateToHome())}),e.\u0275\u0275text(36),e.\u0275\u0275pipe(37,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"span",73),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.currentStep=3)}),e.\u0275\u0275element(39,"span",74),e.\u0275\u0275elementStart(40,"span",75),e.\u0275\u0275text(41),e.\u0275\u0275pipe(42,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(43,"span",109),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(),i=e.\u0275\u0275reference(40);return e.\u0275\u0275resetView("leads"==n.moduleName?n.sendLeadMappingDetails(i):n.sendDataMappingDetails(i))}),e.\u0275\u0275text(44,"Finish Importing"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,14,"GLOBAL.review-finalize-your-import")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("please review the complete process of ",t.moduleName," importing and finish it."),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,16,"GLOBAL.file-uploaded")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==t.selectedFile?null:t.selectedFile.name),e.\u0275\u0275advance(12),e.\u0275\u0275textInterpolate1("Assign these ",t.moduleName," to"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("The ",t.moduleName," in your file record can be assigned to any user(s)."),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(30,18,"GLOBAL.select-users")),e.\u0275\u0275property("virtualScroll",!0)("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("closeOnSelect",!1)("ngModel",t.selectedUserId),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(37,20,"BUTTONS.cancel")," Import"),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(42,22,"BUTTONS.back"))}}function ce(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"h5",113),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"div",114)(3,"h4",115),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"h5",116),e.\u0275\u0275text(6,"You can check "),e.\u0275\u0275elementStart(7,"span",117),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.openBulkUploadedStatusModal())}),e.\u0275\u0275text(8,"\u201cExcel Upload Tracker\u201d"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(9," to view upload status "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"button",118),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return n.navigateToHome(),e.\u0275\u0275resetView(n.modalService.hide())}),e.\u0275\u0275text(11),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd()()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1("",t.moduleName," Upload Scheduled"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",t.moduleName," upload has been scheduled. "),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(12,3,"BULK_LEAD.got-it"),"")}}const k=function(a){return{"gray-scale":a}};let me=(()=>{class a{constructor(t,l,n,i,p,u,_,o){this.modalRef=t,this.modalService=l,this._store=n,this.router=i,this.fb=p,this.headerTitle=u,this.metaTitle=_,this.trackingService=o,this.tick={path:"assets/animations/tick.json"},this.isTemplateDownloaded=!1,this.isFileDataUpdated=!1,this.isFileUploadTriggered=!1,this.isFileTypeSupported=!0,this.currentStep=1,this.isValidModal=!1,this.allowedFileTypes=m.Aa7,this.stopper=new e.EventEmitter,this.duplicateLeads={},this.formKeys=[],this.res=!1,this.duplicateUsers=[],this.allActiveUsers=[],this.selectedUserId=[],this.excelSheets={},this.sheetNames=[],this.canAssignToAny=!1,this.selectedOption=d.qd.None,this.selectedSubOption=d.t4.All,this.leadBulkFields=m.uCS,this.dataBulkFields=m.CdV,this.moduleName="/leads/bulk-upload"==window.location.pathname?"leads":"data",this.trackingModuleName="/leads/bulk-upload"==window.location.pathname?"Leads":"Data",this.options=[{label:`New ${this.moduleName}`,value:d.qd.None},{label:`Create Duplicate ${this.moduleName}`,value:d.qd.CreateDuplicateLead},{label:`Override Existing ${this.moduleName}`,value:d.qd.OverideExisitingLeadInformation},{label:"Update Missing Information",value:d.qd.UpdateMissingInformation}],this.subOptions=[{label:"All",value:d.t4.All},{label:"Parent only",value:d.t4.ParentOnly},{label:"Latest Child Lead",value:d.t4.LatestChildLead}],this.getAppName=v.PO,"leads"==this.moduleName?(this.metaTitle.setTitle("CRM | Import Leads"),this.headerTitle.setLangTitle("SIDEBAR.import-leads")):(this.metaTitle.setTitle("CRM | Import Data"),this.headerTitle.setLangTitle("SIDEBAR.import-data")),this.initializeForms()}get filteredOptions(){return"data"===this.moduleName?this.options.filter(t=>(null==t?void 0:t.value)!==d.qd.CreateDuplicateLead):this.options}ngOnInit(){this.trackingService.trackFeature(`Web.${this.trackingModuleName}.Page.BulkUpload.View`),this.initializeForms(),this._store.select(M.Zu).pipe((0,h.R)(this.stopper)).subscribe(t=>{null==t||!t.length||(null!=t&&t.includes("Permissions.Users.AssignToAny")?(this.canAssignToAny=!0,this._store.dispatch(new N.MI)):this._store.dispatch(new N.VJ))}),this._store.select(A.fN).pipe((0,h.R)(this.stopper)).subscribe(t=>{var l,n;this.globalSettingsDetails=t,(0,v.Qr)(t)||("leads"===this.moduleName?this.excelTemplatePath=null!==(l=this.globalSettingsDetails)&&void 0!==l&&l.isCustomLeadFormEnabled?m.Tkk:m.$Lq:"data"===this.moduleName&&(this.excelTemplatePath=null!==(n=this.globalSettingsDetails)&&void 0!==n&&n.isCustomLeadFormEnabled?m.IW:m.E2e))}),this._store.select(f.Bh).pipe((0,h.R)(this.stopper)).subscribe(t=>{var l;this.activeUsers=null==t?void 0:t.filter(n=>n.isActive),this.activeUsers=null===(l=this.activeUsers)||void 0===l?void 0:l.map(n=>Object.assign(Object.assign({},n),{Name:n.firstName+" "+n.lastName})),this.activeUsers=(0,v.vF)(this.activeUsers,"")}),this._store.select(f.Sh).pipe((0,h.R)(this.stopper)).subscribe(t=>{var l;this.allActiveUsers=null==t?void 0:t.filter(n=>n.isActive),this.allActiveUsers=null===(l=this.allActiveUsers)||void 0===l?void 0:l.map(n=>Object.assign(Object.assign({},n),{Name:n.firstName+" "+n.lastName})),this.allActiveUsers=(0,v.vF)(this.allActiveUsers,"")}),this._store.select(f.Xf).pipe((0,h.R)(this.stopper)).subscribe(t=>{this.userData=t})}initializeForms(){this.leadMappingForm=this.fb.group({Name:[null,c.kI.required],ContactNo:[null,c.kI.required],AlternateContactNo:[null],CountryCode:[null],AlternativeNoCountryCode:[null],Email:[null],Notes:[null],UpperBudget:[null],LowerBudget:[null],Currency:[null],Location:[null],SubCommunity:[null],Community:[null],TowerName:[null],City:[null],State:[null],Country:[null],PostalCode:[null],EnquiredFor:[null],Property:[null],Project:[null],SubPropertyType:[null],BasePropertyType:[null],Source:[null],NoOfBHK:[null],BR:[null],BHKType:[null],Beds:[null],Baths:[null],FurnishStatus:[null],PreferredFloor:[null],OfferingType:[null],SubSource:[null],AgencyName:[null],ReferralContactNo:[null],ReferralName:[null],ReferralEmail:[null],ChannelPartnerName:[null],ChannelPartnerExecutiveName:[null],ChannelPartnerContactNo:[null],BaseStatus:[null],SubStatus:[null],ScheduledDate:[null],CompanyName:[null],Designation:[null],CampaignName:[null],CarpetArea:[null],CarpetAreaUnit:[null],BuiltUpArea:[null],BuiltUpAreaUnit:[null],SaleableArea:[null],SaleableAreaUnit:[null],PropertyArea:[null],PropertyAreaUnit:[null],NetArea:[null],NetAreaUnit:[null],UnitName:[null],Nationality:[null],ClusterName:[null],Purpose:[null],CustomerCity:[null],CustomerState:[null],CustomerLocation:[null],CustomerCommunity:[null],CustomerSubCommunity:[null],CustomerTowerName:[null],CustomerCountry:[null],CustomerPostalCode:[null],Profession:[null],ClosingManager:[null],SourcingManager:[null],PossessionDate:[null],AssignToUser:[null]}),this.dataMappingForm=this.fb.group({Name:[null,c.kI.required],ContactNo:[null,c.kI.required],AltContactNo:[null],CountryCode:[null],AlternativeNoCountryCode:[null],Email:[null],Notes:[null],UpperBudget:[null],LowerBudget:[null],Currency:[null],Location:[null],SubCommunity:[null],Community:[null],TowerName:[null],City:[null],State:[null],Country:[null],PostalCode:[null],EnquiryFor:[null],Property:[null],Project:[null],SubPropertyType:[null],BasePropertyType:[null],Source:[null],NoOfBHKs:[null],BR:[null],BHKType:[null],Beds:[null],Baths:[null],FurnishStatus:[null],PreferredFloor:[null],OfferingType:[null],SubSource:[null],AgencyName:[null],ReferralContactNo:[null],ReferralName:[null],ReferralEmail:[null],ChannelPartnerName:[null],ChannelPartnerExecutiveName:[null],ChannelPartnerContactNo:[null],CompanyName:[null],Designation:[null],CampaignName:[null],CarpetArea:[null],CarpetAreaUnit:[null],BuiltUpArea:[null],BuiltUpAreaUnit:[null],SaleableArea:[null],SaleableAreaUnit:[null],PropertyArea:[null],PropertyAreaUnit:[null],NetArea:[null],NetAreaUnit:[null],UnitName:[null],Nationality:[null],ClusterName:[null],Purpose:[null],CustomerCity:[null],CustomerState:[null],CustomerLocation:[null],CustomerCommunity:[null],CustomerSubCommunity:[null],CustomerTowerName:[null],CustomerCountry:[null],CustomerPostalCode:[null],Profession:[null],ClosingManager:[null],SourcingManager:[null],PossessionDate:[null],AssignedToUser:[null]})}checkFileFormat(t){const i=/(?:\.([^.]+))?$/.exec(t);return this.isFileTypeSupported=["xls","xlx","csv","xlsx"].includes(i[1]),this.isFileTypeSupported}onFileSelection(t){this.selectedFile=t,this.currentStep=this.currentStep<2?this.currentStep+1:this.currentStep}onAutoMapChange(){var t,l;const n="leads"===this.moduleName,i=n?m.uCS:m.CdV,p=n?this.leadMappingForm:this.dataMappingForm,o="Name",I="Primary Number",T=null!==(t=this.formKeys)&&void 0!==t&&t.includes(o)?o:null,ue=null!==(l=this.formKeys)&&void 0!==l&&l.includes(I)?I:null;i.forEach(({displayName:E,mappingControlName:U})=>{var B;E&&p.controls[U]&&(null===(B=this.formKeys)||void 0===B?void 0:B.includes(E))&&p.patchValue({[U]:E})}),T&&p.patchValue({Name:T,ContactNo:ue})}uploadFile(){!this.isFileTypeSupported||("leads"===this.moduleName?(this._store.dispatch(new g.grv(this.selectedFile)),this._store.select(b.T3).subscribe(t=>{var l,n,i;this.excelSheets=null==t?void 0:t.multiSheetColumnNames,this.excelSheets&&(this.sheetNames=Object.keys(this.excelSheets)),this.selectedSheet=this.sheetNames[0],this.formKeys=null==t?void 0:t.columnNames,this.selectedName=null===(l=this.formKeys)||void 0===l?void 0:l.find(p=>"Name"===p),this.selectedNumber=null===(n=this.formKeys)||void 0===n?void 0:n.find(p=>"Primary Number"===p),this.s3BucketKey=null==t?void 0:t.s3BucketKey,!(null===(i=this.formKeys)||void 0===i)&&i.length&&(this.currentStep=3),this.resetForms(),this.onAutoMapChange()}),this.trackingService.trackFeature(`Web.${this.trackingModuleName}.Button.Proceed.Click`)):(this._store.dispatch(new x.b8(this.selectedFile)),this._store.select(y.E5).subscribe(t=>{var l,n,i;this.excelSheets=null==t?void 0:t.multiSheetColumnNames,this.excelSheets&&(this.sheetNames=Object.keys(this.excelSheets)),this.selectedSheet=this.sheetNames[0],this.formKeys=null==t?void 0:t.columnNames,this.selectedName=null===(l=this.formKeys)||void 0===l?void 0:l.find(p=>"Name"===p),this.selectedNumber=null===(n=this.formKeys)||void 0===n?void 0:n.find(p=>"Primary Number"===p),this.s3BucketKey=null==t?void 0:t.s3BucketKey,!(null===(i=this.formKeys)||void 0===i)&&i.length&&(this.currentStep=3),this.resetForms(),this.onAutoMapChange()})))}replaceFile(){this.fileInput.nativeElement.click()}getBulkFields(t){var l;return null!==(l=this.globalSettingsDetails)&&void 0!==l&&l.isCustomLeadFormEnabled?t.filter(n=>"BHK"!==n.displayName&&"BHKType"!==n.displayName):t.filter(n=>!["Enquired Sub-Community","Enquired Community","Enquired Tower Name","BR","Beds","Baths","Furnish Status","Preferred Floor","Offering Type","Property Area","Property Area Unit","Net Area","Net Area Unit","Unit Number/Name","Cluster Name","Nationality","Customer Community","Customer SubCommunity","Customer TowerName"].includes(n.displayName))}isValidForm(){const l="leads"===this.moduleName?this.leadMappingForm:this.dataMappingForm;this.selectedSheet&&l.valid?(this.isValidModal=!0,this.modalRef=this.modalService.show(this.validModal,{class:"modal-350 modal-dialog-centered ip-modal-unset",keyboard:!1})):(0,v._5)(l)}selectOption(t){this.selectedOption=t}confirmSheet(){this.currentStep=4,this.modalRef.hide()}sendLeadMappingDetails(t){var l,n,i,p,u;if(!this.selectedSheet||!this.leadMappingForm.controls.Name.value||!this.leadMappingForm.controls.ContactNo.value)return;const _={s3BucketKey:this.s3BucketKey,mappedColumnsData:{},fileName:null===(l=this.selectedFile)||void 0===l?void 0:l.name,userIds:this.selectedUserId,createType:this.selectedOption?this.selectedOption:d.qd.None,createSubType:this.selectedSubOption?this.selectedSubOption:d.t4.All,timeZoneId:(null===(i=null===(n=this.userData)||void 0===n?void 0:n.timeZoneInfo)||void 0===i?void 0:i.timeZoneId)||(0,v.p5)(),baseUTcOffset:(null===(u=null===(p=this.userData)||void 0===p?void 0:p.timeZoneInfo)||void 0===u?void 0:u.baseUTcOffset)||(0,v.Bj)()};m.uCS.filter(o=>"BR"!==o.displayName).map(o=>{this.leadMappingForm.value[o.mappingControlName]&&(_.mappedColumnsData[o.mappingControlName]=this.leadMappingForm.value[o.mappingControlName])}),_.sheetName=this.selectedSheet||this.sheetNames[0],this._store.dispatch(new g.l2K(_)),this._store.dispatch(new g.v5r(1,10)),this._store.select(b.Gz).pipe((0,h.R)(this.stopper)).subscribe(o=>{null==o?this.navigateToHome():(this.count=o,this.notUploadedLeadsExcelPath=null==o?void 0:o.excelUrl,0===this.modalService.getModalsCount()&&(this.modalRef=this.modalService.show(t,Object.assign({},{class:"modal-400 top-modal ph-modal-unset",ignoreBackdropClick:!0,keyboard:!1}))))})}sendDataMappingDetails(t){var l,n,i,p,u;if(!this.selectedSheet||!this.dataMappingForm.controls.Name.value||!this.dataMappingForm.controls.ContactNo.value)return;const _={s3BucketKey:this.s3BucketKey,mappedColumnData:{},fileName:null===(l=this.selectedFile)||void 0===l?void 0:l.name,userIds:this.selectedUserId,createType:this.selectedOption?this.selectedOption:d.qd.None,timeZoneId:(null===(i=null===(n=this.userData)||void 0===n?void 0:n.timeZoneInfo)||void 0===i?void 0:i.timeZoneId)||(0,v.p5)(),baseUTcOffset:(null===(u=null===(p=this.userData)||void 0===p?void 0:p.timeZoneInfo)||void 0===u?void 0:u.baseUTcOffset)||(0,v.Bj)()};m.CdV.filter(o=>"BR"!==o.displayName).map(o=>{this.dataMappingForm.value[o.mappingControlName]&&(_.mappedColumnData[o.mappingControlName]=this.dataMappingForm.value[o.mappingControlName])}),_.sheetName=this.selectedSheet||this.sheetNames[0],this._store.dispatch(new x.WR(_)),this._store.dispatch(new x.aD(1,10)),this._store.select(y.$).pipe((0,h.R)(this.stopper)).subscribe(o=>{null==o?this.navigateToHome():(this.count=o,this.notUploadedLeadsExcelPath=null==o?void 0:o.excelUrl,0===this.modalService.getModalsCount()&&(this.modalRef=this.modalService.show(t,Object.assign({},{class:"modal-400 top-modal ph-modal-unset",ignoreBackdropClick:!0,keyboard:!1}))))})}openBulkUploadedStatusModal(){this.navigateToHome(),this.modalRef&&this.modalRef.hide(),this.modalRef=this.modalService.show(O.W,{class:"modal-1100 modal-dialog-centered h-100 tb-modal-unset",initialState:{fieldType:this.moduleName}})}onSheetSelection(){var t;this.formKeys=null===(t=this.excelSheets)||void 0===t?void 0:t[this.selectedSheet]}navigateToHome(){"leads"==this.moduleName?(this.router.navigate(["leads"]),this.trackingService.trackFeature(`Web.${this.trackingModuleName}.Button.CancelImport.Click`)):this.router.navigate(["data"])}resetForms(){this.leadMappingForm&&this.leadMappingForm.reset(),this.dataMappingForm&&this.dataMappingForm.reset()}trackDownload(){this.trackingService.trackFeature(`Web.${this.trackingModuleName}.Button.DownloadTemplate.Click`)}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(w.UZ),e.\u0275\u0275directiveInject(w.tT),e.\u0275\u0275directiveInject(D.yh),e.\u0275\u0275directiveInject(P.F0),e.\u0275\u0275directiveInject(c.qu),e.\u0275\u0275directiveInject(F.g),e.\u0275\u0275directiveInject(K.Dx),e.\u0275\u0275directiveInject(R.e))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["bulk-upload"]],viewQuery:function(t,l){if(1&t&&(e.\u0275\u0275viewQuery($,5),e.\u0275\u0275viewQuery(z,5)),2&t){let n;e.\u0275\u0275queryRefresh(n=e.\u0275\u0275loadQuery())&&(l.fileInput=n.first),e.\u0275\u0275queryRefresh(n=e.\u0275\u0275loadQuery())&&(l.validModal=n.first)}},decls:41,vars:30,consts:[[1,"py-30","px-24","position-relative"],[1,"flex-center-col"],[1,"header-3","text-coal"],[1,"flex-center","mt-20"],["src","../../../../assets/images/file-upload.svg"],[1,"w-170","ip-w-100px","ph-w-70",3,"ngClass"],["src","../../../../assets/images/map-fields.svg",3,"ngClass"],["src","../../../../assets/images/review-import.svg",3,"ngClass"],[1,"flex-center"],[1,"flex-center-col","pl-16"],[1,"fw-600","text-sm","text-gray-90","mt-6"],[1,"fw-semi-bold","text-large","text-black-100","mt-2","text-center"],[1,"w-115","ip-w-45","ph-w-15"],[4,"ngIf"],["validModal",""],["trackerInfoModal",""],[1,"mt-20","p-20","bg-white","br-4"],[1,"fw-700","header-3","text-black-100"],[1,"flex-between","mt-20"],[1,"d-flex"],[1,"dot","dot-xxs","bg-slate-250","mr-8","mt-6"],[1,"text-nowrap","fw-700","text-dark-gray","text-large","mr-10"],[1,"d-flex",3,"href","click"],[1,"border-accent-green","br-50","mr-4"],[1,"icon","ic-down-to-line","ic-accent-green","ic-x-xs","m-4"],[1,"fw-700","text-accent-green","text-large","text-decoration-underline"],[1,"fw-semi-bold","text-sm","text-dark-gray","mt-6","ml-60"],[1,"border-bottom-slate-20","mt-12","ml-60"],[1,"d-flex","mt-12"],[1,"fw-700","text-black-200","text-large"],[1,"fw-semi-bold","text-sm","text-dark-gray","mt-6"],[1,"version-two"],[3,"allowedFileType","isExcelFile","uploadedFile"],[1,"bg-white","px-20","py-40"],[1,"border-green-dashed-2","flex-center-col","bg-green-150"],[1,"align-center-col","py-40","text-black-200"],[1,"fw-semi-bold","header-4","text-center"],[1,"mt-4","fw-700","header-3","text-truncate-1","break-all"],[1,"align-center","fw-600","text-large","mt-10","text-lowercase"],[1,"text-red-450","mr-10","text-decoration-underline",3,"click"],[1,"text-aqua-750","text-decoration-underline",3,"click"],["type","file",3,"change"],["fileInput",""],[1,"border-bottom-slate-20"],[1,"d-flex","flex-end","p-10","bg-white"],[1,"fw-600","text-large","text-black-200","text-decoration-underline","mr-20",3,"click"],[1,"btn-coal",3,"ngClass","click"],[1,"bg-white","p-20","br-4","text-black-100","mt-20"],[1,"fw-700","header-4"],[1,"d-flex","flex-wrap","fw-semi-bold","text-large","mt-4"],[1,"text-nowrap"],[1,"fw-700","mx-2"],[1,"text-accent-green","mx-2","fw-700"],[1,"d-flex","tb-w-100-40","bg-coal","scrollbar","text-large","fw-semi-bold","text-white","p-6"],[1,"min-w-150"],[1,"min-w-250"],[1,"text-accent-green","mr-2"],[1,"flex-grow-1","ml-40","ip-ml-0","mr-10"],[1,"fw-700","mr-2"],[1,"scrollbar","tb-w-100-40","hmq-h-100-405","h-100-250","bg-white","ng-select-xs"],[1,"align-center","py-10","px-16","w-100","text-large"],[1,"min-w-150","text-dark-gray","fw-700"],[1,"min-w-300","fw-semi-bold","text-black-100","flex-between","mr-16"],[1,"field-label-clear-m-req"],[1,"ic-dashed-arrow"],[1,"align-center","flex-grow-1"],[1,"position-relative"],["ResizableDropdown","",1,"w-200","mr-10",3,"virtualScroll","items","ngModel","placeholder","ngModelChange","change"],["class","text-xxs error-message position-absolute fw-semi-bold nbottom-15 right-20",4,"ngIf"],[4,"ngIf","ngIfElse"],["dataForm",""],[1,"box-shadow-10","flex-end","p-10","bg-white"],[1,"fw-600","text-large","text-black-200","text-decoration-underline","mr-20","cursor-pointer",3,"click"],[1,"br-4","p-8","border","mr-10","cursor-pointer",3,"click"],[1,"ic-chevron-left","ic-light-gray","ic-x-xs","mr-8"],[1,"fw-600","text-large","text-dark-gray"],[1,"w-140","btn-coal",3,"click"],[1,"text-xxs","error-message","position-absolute","fw-semi-bold","nbottom-15","right-20"],[3,"formGroup"],["label","Name",3,"control"],["formControlName","Name","ResizableDropdown","",1,"w-200","mr-10",3,"virtualScroll","items","placeholder"],["label","Primary Number",3,"control"],["formControlName","ContactNo","ResizableDropdown","",1,"w-200","mr-10",3,"virtualScroll","items","placeholder"],[4,"ngFor","ngForOf"],[1,"field-label-clear-m"],["ResizableDropdown","",1,"w-200","mr-10",3,"virtualScroll","items","formControlName","placeholder"],["class","p-20",4,"ngIf"],[1,"p-20"],[1,"text-black-100","text-center","fw-semi-bold","mb-10"],[1,"flex-end","mt-30","flex-center"],["id","cancel","data-automate-id","cancel",1,"btn-gray",3,"click"],["id","ok","data-automate-id","ok",1,"btn-coal","ml-20",3,"click"],[1,"bg-white","p-20","mt-20","br-4","scrollbar","h-100-290"],[1,"fw-semi-bold","header-4","text-black-100"],[1,"align-center","mt-20"],[1,"dot","dot-xxs","bg-slate-250","mr-8"],[1,"fw-700","text-black-200","text-large","mr-10","text-nowrap"],[1,"fw-600","text-large","text-accent-green","text-decoration-underline"],[1,"border-bottom-slate-20","mt-8","ml-24"],[1,"mt-16"],[1,"align-center"],[1,"fw-700","text-large","text-black-200"],[1,"border-bottom-slate-20","mt-12","ml-24"],[1,"mt-12"],[1,"text-sm","text-dark-gray","fw-semi-bold","mt-2","ml-12"],[1,"ng-select-sm","mt-10","ml-12","dashboard-dropdown"],["ResizableDropdown","","bindLabel","Name","bindValue","id","name","user",1,"w-25","tb-w-33","ip-w-50","ph-w-100",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange"],["ng-label-tmp",""],["ng-option-tmp",""],[1,"btn-coal","w-140",3,"click"],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],[1,"checkmark"],[1,"px-20","py-16","fw-semi-bold","bg-coal","text-white","text-capitalize"],[1,"p-20","flex-center-col"],[1,"text-black-100","fw-600","mb-10","text-center","word-break","line-break","text-capitalize"],[1,"text-black-100","fw-semi-bold","text-center","word-break","line-break"],[1,"cursor-pointer","text-accent-green","header-3","fw-600",3,"click"],[1,"btn-coal","mt-30",3,"click"]],template:function(t,l){1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",3),e.\u0275\u0275element(5,"img",4)(6,"span",5)(7,"img",6)(8,"span",5)(9,"img",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"div",8)(11,"div",9)(12,"div",10),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",11),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(18,"span",12),e.\u0275\u0275elementStart(19,"div",9)(20,"div",10),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"div",11),e.\u0275\u0275text(24),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(26,"span",12),e.\u0275\u0275elementStart(27,"div",1)(28,"div",10),e.\u0275\u0275text(29),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"div",11),e.\u0275\u0275text(32),e.\u0275\u0275pipe(33,"translate"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(34,X,47,29,"ng-container",13),e.\u0275\u0275template(35,ie,77,59,"ng-container",13),e.\u0275\u0275template(36,oe,1,1,"ng-template",null,14,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(38,de,45,24,"ng-container",13),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(39,ce,13,5,"ng-template",null,15,e.\u0275\u0275templateRefExtractor)),2&t&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("Importing ",l.moduleName," becomes more easier"),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",1==l.currentStep||2==l.currentStep?"border-dashed-bottom-2":"border-accent-green"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(26,k,1==l.currentStep||2==l.currentStep)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",4!==l.currentStep?"border-dashed-bottom-2":"border-accent-green"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(28,k,4!==l.currentStep)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(14,14,"GLOBAL.step-1")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(17,16,"PROJECTS_BLOCK_INFO.upload-file"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(22,18,"GLOBAL.step-2")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(25,20,"PROJECTS_BLOCK_INFO.map-the-fields")," "),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(30,22,"GLOBAL.step-3")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(33,24,"LEADS.review-import")," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",1==l.currentStep||2==l.currentStep),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",3==l.currentStep),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",4==l.currentStep))},dependencies:[c._Y,c.JJ,c.JL,c.On,C.mk,C.sg,C.O5,S.w9,S.ir,S.mR,V.z,c.sg,c.u,G.b,W.s,j.X$],encapsulation:2}),a})()}}]);