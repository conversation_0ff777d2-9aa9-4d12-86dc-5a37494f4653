{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nvar PatternParser = /*#__PURE__*/function () {\n  function PatternParser() {\n    _classCallCheck(this, <PERSON><PERSON>Parser);\n  }\n\n  _createClass(<PERSON><PERSON><PERSON>ars<PERSON>, [{\n    key: \"parse\",\n    value: function parse(pattern) {\n      this.context = [{\n        or: true,\n        instructions: []\n      }];\n      this.parsePattern(pattern);\n\n      if (this.context.length !== 1) {\n        throw new Error('Non-finalized contexts left when pattern parse ended');\n      }\n\n      var _this$context$ = this.context[0],\n          branches = _this$context$.branches,\n          instructions = _this$context$.instructions;\n\n      if (branches) {\n        return {\n          op: '|',\n          args: branches.concat([expandSingleElementArray(instructions)])\n        };\n      }\n      /* istanbul ignore if */\n\n\n      if (instructions.length === 0) {\n        throw new Error('Pattern is required');\n      }\n\n      if (instructions.length === 1) {\n        return instructions[0];\n      }\n\n      return instructions;\n    }\n  }, {\n    key: \"startContext\",\n    value: function startContext(context) {\n      this.context.push(context);\n    }\n  }, {\n    key: \"endContext\",\n    value: function endContext() {\n      this.context.pop();\n    }\n  }, {\n    key: \"getContext\",\n    value: function getContext() {\n      return this.context[this.context.length - 1];\n    }\n  }, {\n    key: \"parsePattern\",\n    value: function parsePattern(pattern) {\n      if (!pattern) {\n        throw new Error('Pattern is required');\n      }\n\n      var match = pattern.match(OPERATOR);\n\n      if (!match) {\n        if (ILLEGAL_CHARACTER_REGEXP.test(pattern)) {\n          throw new Error(\"Illegal characters found in a pattern: \".concat(pattern));\n        }\n\n        this.getContext().instructions = this.getContext().instructions.concat(pattern.split(''));\n        return;\n      }\n\n      var operator = match[1];\n      var before = pattern.slice(0, match.index);\n      var rightPart = pattern.slice(match.index + operator.length);\n\n      switch (operator) {\n        case '(?:':\n          if (before) {\n            this.parsePattern(before);\n          }\n\n          this.startContext({\n            or: true,\n            instructions: [],\n            branches: []\n          });\n          break;\n\n        case ')':\n          if (!this.getContext().or) {\n            throw new Error('\")\" operator must be preceded by \"(?:\" operator');\n          }\n\n          if (before) {\n            this.parsePattern(before);\n          }\n\n          if (this.getContext().instructions.length === 0) {\n            throw new Error('No instructions found after \"|\" operator in an \"or\" group');\n          }\n\n          var _this$getContext = this.getContext(),\n              branches = _this$getContext.branches;\n\n          branches.push(expandSingleElementArray(this.getContext().instructions));\n          this.endContext();\n          this.getContext().instructions.push({\n            op: '|',\n            args: branches\n          });\n          break;\n\n        case '|':\n          if (!this.getContext().or) {\n            throw new Error('\"|\" operator can only be used inside \"or\" groups');\n          }\n\n          if (before) {\n            this.parsePattern(before);\n          } // The top-level is an implicit \"or\" group, if required.\n\n\n          if (!this.getContext().branches) {\n            // `branches` are not defined only for the root implicit \"or\" operator.\n\n            /* istanbul ignore else */\n            if (this.context.length === 1) {\n              this.getContext().branches = [];\n            } else {\n              throw new Error('\"branches\" not found in an \"or\" group context');\n            }\n          }\n\n          this.getContext().branches.push(expandSingleElementArray(this.getContext().instructions));\n          this.getContext().instructions = [];\n          break;\n\n        case '[':\n          if (before) {\n            this.parsePattern(before);\n          }\n\n          this.startContext({\n            oneOfSet: true\n          });\n          break;\n\n        case ']':\n          if (!this.getContext().oneOfSet) {\n            throw new Error('\"]\" operator must be preceded by \"[\" operator');\n          }\n\n          this.endContext();\n          this.getContext().instructions.push({\n            op: '[]',\n            args: parseOneOfSet(before)\n          });\n          break;\n\n        /* istanbul ignore next */\n\n        default:\n          throw new Error(\"Unknown operator: \".concat(operator));\n      }\n\n      if (rightPart) {\n        this.parsePattern(rightPart);\n      }\n    }\n  }]);\n\n  return PatternParser;\n}();\n\nexport { PatternParser as default };\n\nfunction parseOneOfSet(pattern) {\n  var values = [];\n  var i = 0;\n\n  while (i < pattern.length) {\n    if (pattern[i] === '-') {\n      if (i === 0 || i === pattern.length - 1) {\n        throw new Error(\"Couldn't parse a one-of set pattern: \".concat(pattern));\n      }\n\n      var prevValue = pattern[i - 1].charCodeAt(0) + 1;\n      var nextValue = pattern[i + 1].charCodeAt(0) - 1;\n      var value = prevValue;\n\n      while (value <= nextValue) {\n        values.push(String.fromCharCode(value));\n        value++;\n      }\n    } else {\n      values.push(pattern[i]);\n    }\n\n    i++;\n  }\n\n  return values;\n}\n\nvar ILLEGAL_CHARACTER_REGEXP = /[\\(\\)\\[\\]\\?\\:\\|]/;\nvar OPERATOR = new RegExp( // any of:\n'(' + // or operator\n'\\\\|' + // or\n'|' + // or group start\n'\\\\(\\\\?\\\\:' + // or\n'|' + // or group end\n'\\\\)' + // or\n'|' + // one-of set start\n'\\\\[' + // or\n'|' + // one-of set end\n'\\\\]' + ')');\n\nfunction expandSingleElementArray(array) {\n  if (array.length === 1) {\n    return array[0];\n  }\n\n  return array;\n} //# sourceMappingURL=AsYouTypeFormatter.PatternParser.js.map", "map": null, "metadata": {}, "sourceType": "module"}