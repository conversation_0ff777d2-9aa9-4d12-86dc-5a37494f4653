{"ast": null, "code": "import extractNationalNumberFromPossiblyIncompleteNumber from './extractNationalNumberFromPossiblyIncompleteNumber.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport checkNumberLength from './checkNumberLength.js';\n/**\r\n * Strips national prefix and carrier code from a complete phone number.\r\n * The difference from the non-\"FromCompleteNumber\" function is that\r\n * it won't extract national prefix if the resultant number is too short\r\n * to be a complete number for the selected phone numbering plan.\r\n * @param  {string} number — Complete phone number digits.\r\n * @param  {Metadata} metadata — Metadata with a phone numbering plan selected.\r\n * @return {object} `{ nationalNumber: string, carrierCode: string? }`.\r\n */\n\nexport default function extractNationalNumber(number, metadata) {\n  // Parsing national prefixes and carrier codes\n  // is only required for local phone numbers\n  // but some people don't understand that\n  // and sometimes write international phone numbers\n  // with national prefixes (or maybe even carrier codes).\n  // http://ucken.blogspot.ru/2016/03/trunk-prefixes-in-skype4b.html\n  // Google's original library forgives such mistakes\n  // and so does this library, because it has been requested:\n  // https://github.com/catamphetamine/libphonenumber-js/issues/127\n  var _extractNationalNumbe = extractNationalNumberFromPossiblyIncompleteNumber(number, metadata),\n      carrierCode = _extractNationalNumbe.carrierCode,\n      nationalNumber = _extractNationalNumbe.nationalNumber;\n\n  if (nationalNumber !== number) {\n    if (!shouldHaveExtractedNationalPrefix(number, nationalNumber, metadata)) {\n      // Don't strip the national prefix.\n      return {\n        nationalNumber: number\n      };\n    } // Check the national (significant) number length after extracting national prefix and carrier code.\n    // Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature.\n\n\n    if (metadata.possibleLengths()) {\n      // The number remaining after stripping the national prefix and carrier code\n      // should be long enough to have a possible length for the country.\n      // Otherwise, don't strip the national prefix and carrier code,\n      // since the original number could be a valid number.\n      // This check has been copy-pasted \"as is\" from Google's original library:\n      // https://github.com/google/libphonenumber/blob/876268eb1ad6cdc1b7b5bef17fc5e43052702d57/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L3236-L3250\n      // It doesn't check for the \"possibility\" of the original `number`.\n      // I guess it's fine not checking that one. It works as is anyway.\n      if (!isPossibleIncompleteNationalNumber(nationalNumber, metadata)) {\n        // Don't strip the national prefix.\n        return {\n          nationalNumber: number\n        };\n      }\n    }\n  }\n\n  return {\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n} // In some countries, the same digit could be a national prefix\n// or a leading digit of a valid phone number.\n// For example, in Russia, national prefix is `8`,\n// and also `800 555 35 35` is a valid number\n// in which `8` is not a national prefix, but the first digit\n// of a national (significant) number.\n// Same's with Belarus:\n// `82004910060` is a valid national (significant) number,\n// but `2004910060` is not.\n// To support such cases (to prevent the code from always stripping\n// national prefix), a condition is imposed: a national prefix\n// is not extracted when the original number is \"viable\" and the\n// resultant number is not, a \"viable\" national number being the one\n// that matches `national_number_pattern`.\n\nfunction shouldHaveExtractedNationalPrefix(nationalNumberBefore, nationalNumberAfter, metadata) {\n  // The equivalent in Google's code is:\n  // https://github.com/google/libphonenumber/blob/e326fa1fc4283bb05eb35cb3c15c18f98a31af33/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L2969-L3004\n  if (matchesEntirely(nationalNumberBefore, metadata.nationalNumberPattern()) && !matchesEntirely(nationalNumberAfter, metadata.nationalNumberPattern())) {\n    return false;\n  } // This \"is possible\" national number (length) check has been commented out\n  // because it's superceded by the (effectively) same check done in the\n  // `extractNationalNumber()` function after it calls `shouldHaveExtractedNationalPrefix()`.\n  // In other words, why run the same check twice if it could only be run once.\n  // // Check the national (significant) number length after extracting national prefix and carrier code.\n  // // Fixes a minor \"weird behavior\" bug: https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/57\n  // // (Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature).\n  // if (metadata.possibleLengths()) {\n  // \tif (isPossibleIncompleteNationalNumber(nationalNumberBefore, metadata) &&\n  // \t\t!isPossibleIncompleteNationalNumber(nationalNumberAfter, metadata)) {\n  // \t\treturn false\n  // \t}\n  // }\n\n\n  return true;\n}\n\nfunction isPossibleIncompleteNationalNumber(nationalNumber, metadata) {\n  switch (checkNumberLength(nationalNumber, metadata)) {\n    case 'TOO_SHORT':\n    case 'INVALID_LENGTH':\n      // This library ignores \"local-only\" phone numbers (for simplicity).\n      // See the readme for more info on what are \"local-only\" phone numbers.\n      // case 'IS_POSSIBLE_LOCAL_ONLY':\n      return false;\n\n    default:\n      return true;\n  }\n} //# sourceMappingURL=extractNationalNumber.js.map", "map": null, "metadata": {}, "sourceType": "module"}