{"ast": null, "code": "/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\n\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\nexport default cloneRegExp;", "map": null, "metadata": {}, "sourceType": "module"}