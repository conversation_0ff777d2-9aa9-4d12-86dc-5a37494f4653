{"ast": null, "code": "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\n\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n\n  return index;\n}\n\nexport default trimmedEndIndex;", "map": null, "metadata": {}, "sourceType": "module"}