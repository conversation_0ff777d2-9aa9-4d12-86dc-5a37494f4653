{"ast": null, "code": "import Metadata from '../metadata.js';\nimport { VALID_DIGITS } from '../constants.js';\nvar CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])');\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\n  if (!country) {\n    return;\n  } // Check if the number is IDD-prefixed.\n\n\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  var IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix());\n\n  if (number.search(IDDPrefixPattern) !== 0) {\n    return;\n  } // Strip IDD prefix.\n\n\n  number = number.slice(number.match(IDDPrefixPattern)[0].length); // If there're any digits after an IDD prefix,\n  // then those digits are a country calling code.\n  // Since no country code starts with a `0`,\n  // the code below validates that the next digit (if present) is not `0`.\n\n  var matchedGroups = number.match(CAPTURING_DIGIT_PATTERN);\n\n  if (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\n    if (matchedGroups[1] === '0') {\n      return;\n    }\n  }\n\n  return number;\n} //# sourceMappingURL=stripIddPrefix.js.map", "map": null, "metadata": {}, "sourceType": "module"}