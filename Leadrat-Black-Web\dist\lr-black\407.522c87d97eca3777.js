"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[407],{73150:(w,T,r)=>{r.d(T,{_:()=>oe});var e=r(5e3),_=r(93075),C=r(82722),f=r(95698),I=r(39300),g=r(61021),F=r(85598),L=r(49408),v=r(40553),h=r(66844),D=r(32049),y=r(63253),m=r(65620),x=r(71511),p=r(15634),u=r(69808),O=r(24376),P=r(46302),S=r(84617),M=r(17447),R=r(47511),b=r(18995);const B=function(o){return{active:o}};function N(o,i){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",13),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(t),a=l.$implicit,c=l.index,s=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(s.scrollTo(null==a?null:a.name,c))}),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=i.$implicit,n=i.index,l=e.\u0275\u0275nextContext(4);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(2,B,l.currentActive==n)),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",null==t?null:t.name," ")}}function A(o,i){if(1&o&&(e.\u0275\u0275elementStart(0,"div",11)(1,"div",12),e.\u0275\u0275template(2,N,3,4,"ng-container",7),e.\u0275\u0275elementEnd()()),2&o){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.selectedFieldsInfo)}}function U(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"input",27),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControlName",null==t?null:t.controlName)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"enter "+(null==t||null==t.name?null:t.name.toLowerCase()))}}function V(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"input",28),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControlName",null==t?null:t.controlName)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"enter "+(null==t||null==t.name?null:t.name.toLowerCase()))}}function K(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26)(2,"textarea",29),e.\u0275\u0275text(3,"                                                    "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControlName",null==t?null:t.controlName)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"I want to say.....")}}function W(o,i){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"span",30)(3,"input",31),e.\u0275\u0275elementStart(4,"owl-date-time",32,33),e.\u0275\u0275listener("afterPickerOpen",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(6);return e.\u0275\u0275resetView(l.onPickerOpened(l.currentDate))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=e.\u0275\u0275reference(5),n=e.\u0275\u0275nextContext().$implicit,l=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==n?null:n.name)("control",l.leadForm.controls[null==n?null:n.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("owlDateTimeTrigger",t),e.\u0275\u0275advance(1),e.\u0275\u0275property("owlDateTime",t)("formControlName",null==n?null:n.controlName)("owlDateTimeTrigger",t)("placeholder",null!=n&&n.placeholder?null==n?null:n.placeholder:"select "+(null==n||null==n.name?null:n.name.toLowerCase())),e.\u0275\u0275advance(1),e.\u0275\u0275property("pickerType","calendar")}}function k(o,i){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"span",30)(3,"input",31),e.\u0275\u0275elementStart(4,"owl-date-time",34,33),e.\u0275\u0275listener("afterPickerOpen",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(6);return e.\u0275\u0275resetView(l.onPickerOpened(l.currentDate))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=e.\u0275\u0275reference(5),n=e.\u0275\u0275nextContext().$implicit,l=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==n?null:n.name)("control",l.leadForm.controls[null==n?null:n.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("owlDateTimeTrigger",t),e.\u0275\u0275advance(1),e.\u0275\u0275property("owlDateTime",t)("formControlName",null==n?null:n.controlName)("owlDateTimeTrigger",t)("placeholder",null!=n&&n.placeholder?null==n?null:n.placeholder:"select "+(null==n||null==n.name?null:n.name.toLowerCase())),e.\u0275\u0275advance(1),e.\u0275\u0275property("hour12Timer","true")("startAt",l.currentDate)}}function $(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"ng-select",35),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("items",null==t?null:t.List)("formControlName",null==t?null:t.controlName)("virtualScroll",!0)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"select "+(null==t||null==t.name?null:t.name.toLowerCase()))}}function j(o,i){if(1&o&&(e.\u0275\u0275elementStart(0,"div",38),e.\u0275\u0275element(1,"input",39)(2,"span",40),e.\u0275\u0275elementStart(3,"span",41),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()),2&o){const t=i.item,n=i.item$,l=i.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",l,"")("automate-id","item-",l,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t,"")}}function z(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26)(2,"ng-select",36),e.\u0275\u0275template(3,j,5,4,"ng-template",37),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("items",null==t?null:t.List)("formControlName",null==t?null:t.controlName)("virtualScroll",!0)("multiple",!0)("closeOnSelect",!1)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"select "+(null==t||null==t.name?null:t.name.toLowerCase()))}}function H(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form-errors-wrapper",26),e.\u0275\u0275element(2,"input",27),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("label",null==t?null:t.name)("control",n.leadForm.controls[null==t?null:t.controlName]),e.\u0275\u0275advance(1),e.\u0275\u0275property("formControlName",null==t?null:t.controlName)("placeholder",null!=t&&t.placeholder?null==t?null:t.placeholder:"enter "+(null==t||null==t.name?null:t.name.toLowerCase()))}}function G(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",21)(2,"div",6)(3,"div",22),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerStart(5,23),e.\u0275\u0275template(6,U,3,4,"ng-container",24),e.\u0275\u0275template(7,V,3,4,"ng-container",24),e.\u0275\u0275template(8,K,4,4,"ng-container",24),e.\u0275\u0275template(9,W,6,8,"ng-container",24),e.\u0275\u0275template(10,k,6,9,"ng-container",24),e.\u0275\u0275template(11,$,3,6,"ng-container",24),e.\u0275\u0275template(12,z,4,8,"ng-container",24),e.\u0275\u0275template(13,H,3,4,"ng-container",25),e.\u0275\u0275elementContainerEnd(),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&o){const t=i.$implicit;e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",null!=t&&t.isRequiredField?"field-label-req":"field-label"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",null==t?null:t.name," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitch",null==t?null:t.fieldType),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","text"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","number"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","textarea"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","date"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","dateTime"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","singleDropdown"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngSwitchCase","multipleDropdown")}}function J(o,i){if(1&o&&(e.\u0275\u0275elementStart(0,"div",20),e.\u0275\u0275template(1,G,14,10,"ng-container",7),e.\u0275\u0275elementEnd()),2&o){const t=e.\u0275\u0275nextContext().$implicit;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",null==t?null:t.customFields)}}const X=function(o){return{"pb-12":o}},Q=function(o){return{"rotate-270":o}};function Z(o,i){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",14)(2,"div",15)(3,"div",16),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).$implicit,c=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(c.toggleSectionVisibility(a))}),e.\u0275\u0275element(4,"span",17),e.\u0275\u0275elementStart(5,"h6",18),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(7,J,2,1,"div",19),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=i.$implicit,n=i.last;e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(6,X,!n))("id",null==t?null:t.name),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",t.isHide?"":"border-bottom"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(8,Q,t.isHide)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(null==t?null:t.name),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isHide)}}const Y=function(o){return{"pe-none opacity-5":o}};function q(o,i){if(1&o){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",3),e.\u0275\u0275template(2,A,3,1,"div",4),e.\u0275\u0275elementStart(3,"form",5),e.\u0275\u0275listener("scroll",function(l){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.onScroll(l))}),e.\u0275\u0275elementStart(4,"div",6),e.\u0275\u0275template(5,Z,8,10,"ng-container",7),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(6,"div",8)(7,"button",9),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.goToManageLead())}),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"button",10),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.postData())}),e.\u0275\u0275text(11),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&o){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",(null==t.selectedFieldsInfo?null:t.selectedFieldsInfo.length)>1),e.\u0275\u0275advance(1),e.\u0275\u0275property("formGroup",t.leadForm)("ngClass",(null==t.selectedFieldsInfo?null:t.selectedFieldsInfo.length)>1?"h-100-207":"h-100-152"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.selectedFieldsInfo),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,7,"BUTTONS.cancel")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(11,Y,!(null!=t.leadForm&&t.leadForm.dirty))),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(12,9,"BUTTONS.save"))}}function ee(o,i){1&o&&(e.\u0275\u0275elementStart(0,"h1",42),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&o&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.no-fields-selected-yet")))}function te(o,i){if(1&o&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,q,13,13,"ng-container",0),e.\u0275\u0275template(2,ee,3,3,"ng-template",null,2,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementContainerEnd()),2&o){const t=e.\u0275\u0275reference(3),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==n.selectedFieldsInfo?null:n.selectedFieldsInfo.length)("ngIfElse",t)}}function ne(o,i){1&o&&(e.\u0275\u0275elementStart(0,"div",42),e.\u0275\u0275element(1,"application-loader"),e.\u0275\u0275elementEnd())}let oe=(()=>{class o{constructor(t,n,l,a,c,s){this.headerTitle=t,this.store=n,this.router=l,this.activatedRoute=a,this.shareDataService=c,this.fb=s,this.stopper=new e.EventEmitter,this.currentActive=0,this.selectedFieldsInfo=[],this.currentDate=new Date,this.onPickerOpened=g.tK,this.headerTitle.setLangTitle(this.selectedLeadId?"BUTTONS.edit-lead":"BUTTONS.add-lead"),this.shareDataService.URL$.subscribe(d=>{this.receivedCurrentPath=d}),this.store.dispatch(new F.sg)}ngOnInit(){this.store.select(D.Xf).pipe((0,C.R)(this.stopper)).subscribe(t=>{var n,l;this.userBasicDetails=t,this.currentDate=(0,g.Xp)(null===(l=null===(n=this.userBasicDetails)||void 0===n?void 0:n.timeZoneInfo)||void 0===l?void 0:l.baseUTcOffset)}),this.store.select(L.fL).pipe((0,C.R)(this.stopper)).subscribe(t=>{var n;!(null===(n=null==t?void 0:t.info)||void 0===n)&&n.length&&(this.selectedFieldsInfo=null==t?void 0:t.info,this.selectedFieldsInfo=this.selectedFieldsInfo.map(l=>Object.assign(Object.assign({},l),{customFields:[...l.customFields].filter(a=>a.isSelected).sort((a,c)=>(null==a?void 0:a.orderRank)-(null==c?void 0:c.orderRank)),isHide:!1})).filter(l=>l.customFields.length>0).sort((l,a)=>(null==l?void 0:l.orderRank)-(null==a?void 0:a.orderRank)),this.initForm()),this.selectedFieldsIsLoading=null==t?void 0:t.loader}),this.activatedRoute.params.subscribe(t=>{(t||{}).id&&(this.selectedLeadId=t.id,this.store.select(h.hh).pipe((0,f.q)(1)).subscribe(n=>{var l;const a=null===(l=n.filter(c=>this.selectedLeadId===(null==c?void 0:c.id)))||void 0===l?void 0:l[0];this.store.dispatch(a?new v.Svk(Object.assign({},a)):new v.bJB(this.selectedLeadId))}))}),this.store.select(h.BI).pipe((0,C.R)(this.stopper),(0,I.h)(t=>t.id===this.selectedLeadId)).subscribe(t=>{this.selectedLeadInfo=t||{},(0,g.Qr)(this.selectedLeadInfo)||this.patchFormDetails(this.selectedLeadInfo)})}initForm(){const t={};this.selectedFieldsInfo.forEach(n=>{n.customFields.forEach(l=>{t[l.controlName]=[null,l.isRequiredField?_.kI.required:[]]})}),this.leadForm=this.fb.group(t)}patchFormDetails(t){var n;const l=Object.entries(t).reduce((a,[c,s])=>{const d=this.selectedFieldsInfo.flatMap(E=>E.customFields).find(E=>E.id===c);return d&&(a[d.controlName]=s),a},{});null===(n=this.leadForm)||void 0===n||n.patchValue(l)}toggleSectionVisibility(t){t.isHide=!t.isHide}scrollTo(t,n){this.currentActive=n;const l=document.getElementById(t);l&&l.scrollIntoView({behavior:"smooth",block:"start"})}goToManageLead(){return"/invoice"===this.receivedCurrentPath?this.router.navigate(["/invoice"]):(this.store.dispatch(new v.sT3(!0)),this.router.navigate(["leads/manage-leads"]))}onScroll(t){const l=t.target.getBoundingClientRect().top;this.selectedFieldsInfo.forEach((a,c)=>{const s=document.getElementById(a.name);if(s){const d=s.getBoundingClientRect(),le=d.bottom-l-100;d.top-l-100<=0&&le>0&&(this.currentActive=c)}})}postData(){this.leadForm.invalid?(0,g._5)(this.leadForm):this.selectedFieldsInfo.flatMap(a=>a.customFields.filter(c=>c.isSelected)).reduce((a,c)=>{var s;const d=(null===(s=this.leadForm.get(c.controlName))||void 0===s?void 0:s.value)||null;return null!==d&&(a[c.id]=d),a},{})}}return o.\u0275fac=function(t){return new(t||o)(e.\u0275\u0275directiveInject(y.g),e.\u0275\u0275directiveInject(m.yh),e.\u0275\u0275directiveInject(x.F0),e.\u0275\u0275directiveInject(x.gz),e.\u0275\u0275directiveInject(p.u),e.\u0275\u0275directiveInject(_.qu))},o.\u0275cmp=e.\u0275\u0275defineComponent({type:o,selectors:[["custom-lead-form"]],decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["loading",""],["noFieldsSelected",""],[1,"px-30","py-20"],["class","d-flex mb-12",4,"ngIf"],["autocomplete","off",1,"scrollbar",3,"formGroup","ngClass","scroll"],[1,"mr-12"],[4,"ngFor","ngForOf"],[1,"flex-end","px-20","py-16","bg-white"],[1,"btn-gray",3,"click"],[1,"btn-coal","ml-20",3,"ngClass","click"],[1,"d-flex","mb-12"],[1,"border","br-20","bg-white","align-center","user"],[1,"activation",3,"ngClass","click"],[3,"ngClass","id"],[1,"bg-white"],[1,"cursor-pointer","align-center","py-12","px-20",3,"ngClass","click"],[1,"ic-triangle-down","icon","ic-coal","ic-xxxs","mr-6",3,"ngClass"],[1,"text-black-200"],["class","d-flex flex-wrap pl-20 pb-20",4,"ngIf"],[1,"d-flex","flex-wrap","pl-20","pb-20"],[1,"tv-w-20","w-25","tb-w-33","ip-w-50","ph-w-100"],[3,"ngClass"],[3,"ngSwitch"],[4,"ngSwitchCase"],[4,"ngSwitchDefault"],[3,"label","control"],["type","text",3,"formControlName","placeholder"],["type","number",3,"formControlName","placeholder"],["rows","4",3,"formControlName","placeholder"],[1,"icon","ic-calendar","ic-sm","ic-coal","position-absolute","right-20","top-12","cursor-pointer",3,"owlDateTimeTrigger"],["type","text","readonly","","bsDatepicker","",3,"owlDateTime","formControlName","owlDateTimeTrigger","placeholder"],[3,"pickerType","afterPickerOpen"],["dt1",""],[3,"hour12Timer","startAt","afterPickerOpen"],["ResizableDropdown","",3,"items","formControlName","virtualScroll","placeholder"],["ResizableDropdown","",3,"items","formControlName","virtualScroll","multiple","closeOnSelect","placeholder"],["ng-option-tmp",""],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],[1,"checkmark"],[1,"text-truncate-1","break-all"],[1,"flex-center","h-100"]],template:function(t,n){if(1&t&&(e.\u0275\u0275template(0,te,4,2,"ng-container",0),e.\u0275\u0275template(1,ne,2,0,"ng-template",null,1,e.\u0275\u0275templateRefExtractor)),2&t){const l=e.\u0275\u0275reference(2);e.\u0275\u0275property("ngIf",!n.selectedFieldsIsLoading)("ngIfElse",l)}},dependencies:[u.mk,u.sg,u.O5,u.RF,u.n9,u.ED,O.w9,O.ir,P.z,S.sZ,S.BO,S.hV,M.s,R.t,_._Y,_.Fj,_.wV,_.JJ,_.JL,_.sg,_.u,b.X$],encapsulation:2}),o})()},2407:(w,T,r)=>{r.d(T,{_:()=>D}),r(73150),r(6497),r(99219);var f=r(5e3),I=r(18995),g=r(71511);let F=(()=>{class m{constructor(p){this.translate=p,this.currentLang=localStorage.getItem("locale")?localStorage.getItem("locale"):"en"}ngOnInit(){this.translate.setDefaultLang("en"),this.translate.use(this.currentLang)}}return m.\u0275fac=function(p){return new(p||m)(f.\u0275\u0275directiveInject(I.sK))},m.\u0275cmp=f.\u0275\u0275defineComponent({type:m,selectors:[["leads"]],decls:1,vars:0,template:function(p,u){1&p&&f.\u0275\u0275element(0,"router-outlet")},dependencies:[g.lC],encapsulation:2}),m})();var L=r(88923),v=r(11427),h=r(13582);const D=[{path:"",component:F,children:[{path:"",redirectTo:"manage-leads",pathMatch:"full"},{path:"manage-leads",component:L.U},{path:"add-lead",component:h.a},{path:"edit-lead/:id",component:h.a},{path:"bulk-upload",component:v.v}]}]}}]);