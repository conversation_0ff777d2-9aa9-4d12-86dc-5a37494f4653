{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport const subscribeToIterable = iterable => subscriber => {\n  const iterator = iterable[Symbol_iterator]();\n\n  do {\n    let item;\n\n    try {\n      item = iterator.next();\n    } catch (err) {\n      subscriber.error(err);\n      return subscriber;\n    }\n\n    if (item.done) {\n      subscriber.complete();\n      break;\n    }\n\n    subscriber.next(item.value);\n\n    if (subscriber.closed) {\n      break;\n    }\n  } while (true);\n\n  if (typeof iterator.return === 'function') {\n    subscriber.add(() => {\n      if (iterator.return) {\n        iterator.return();\n      }\n    });\n  }\n\n  return subscriber;\n}; //# sourceMappingURL=subscribeToIterable.js.map", "map": null, "metadata": {}, "sourceType": "module"}