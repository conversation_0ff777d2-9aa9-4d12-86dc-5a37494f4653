{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nimport isValidNumber from '../isValid.js';\nimport parseDigits from '../helpers/parseDigits.js';\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js';\nimport Metadata from '../metadata.js';\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js';\nimport { chooseFormatForNumber } from '../format.js';\nimport { startsWith, endsWith } from './util.js';\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\n\nexport default {\n  /**\r\n   * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n   */\n  POSSIBLE: function POSSIBLE(phoneNumber, _ref) {\n    var candidate = _ref.candidate,\n        metadata = _ref.metadata;\n    return true;\n  },\n\n  /**\r\n   * Phone numbers accepted are \"possible\" and \"valid\".\r\n   * Numbers written in national format must have their national-prefix\r\n   * present if it is usually written for a number of this type.\r\n   */\n  VALID: function VALID(phoneNumber, _ref2) {\n    var candidate = _ref2.candidate,\n        defaultCountry = _ref2.defaultCountry,\n        metadata = _ref2.metadata;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata)) {\n      return false;\n    } // Skipped for simplicity.\n    // return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\n\n\n    return true;\n  },\n\n  /**\r\n   * Phone numbers accepted are \"valid\" and\r\n   * are grouped in a possible way for this locale. For example, a US number written as\r\n   * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n   * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol in the national significant number\r\n   * are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use,\r\n   * email the <NAME_EMAIL>.\r\n   */\n  STRICT_GROUPING: function STRICT_GROUPING(phoneNumber, _ref3) {\n    var candidate = _ref3.candidate,\n        defaultCountry = _ref3.defaultCountry,\n        metadata = _ref3.metadata,\n        regExpCache = _ref3.regExpCache;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsRemainGrouped, regExpCache);\n  },\n\n  /**\r\n   * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n   * that we would have formatted it, or as a single block.\r\n   * For example, a US number written as \"650 2530000\" is not accepted\r\n   * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n   * <EMAIL>.\r\n   */\n  EXACT_GROUPING: function EXACT_GROUPING(phoneNumber, _ref4) {\n    var candidate = _ref4.candidate,\n        defaultCountry = _ref4.defaultCountry,\n        metadata = _ref4.metadata,\n        regExpCache = _ref4.regExpCache;\n\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsAreExactlyPresent, regExpCache);\n  }\n};\n\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata) {\n  // The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\n  // national significant number or (2) an extension sign, in which case they always precede the\n  // extension number. We assume a carrier code is more than 1 digit, so the first case has to\n  // have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\n  // or 'X'. We ignore the character if it appears as the last character of the string.\n  for (var index = 0; index < candidate.length - 1; index++) {\n    var charAtIndex = candidate.charAt(index);\n\n    if (charAtIndex === 'x' || charAtIndex === 'X') {\n      var charAtNextIndex = candidate.charAt(index + 1);\n\n      if (charAtNextIndex === 'x' || charAtNextIndex === 'X') {\n        // This is the carrier code case, in which the 'X's always precede the national\n        // significant number.\n        index++;\n\n        if (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH') {\n          return false;\n        } // This is the extension sign case, in which the 'x' or 'X' should always precede the\n        // extension number.\n\n      } else {\n        var ext = parseDigits(candidate.substring(index));\n\n        if (ext) {\n          if (phoneNumber.ext !== ext) {\n            return false;\n          }\n        } else {\n          if (phoneNumber.ext) {\n            return false;\n          }\n        }\n      }\n    }\n  }\n\n  return true;\n}\n\nfunction isNationalPrefixPresentIfRequired(phoneNumber, _ref5) {\n  var defaultCountry = _ref5.defaultCountry,\n      _metadata = _ref5.metadata; // First, check how we deduced the country code. If it was written in international format, then\n  // the national prefix is not required.\n\n  if (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY') {\n    return true;\n  }\n\n  var metadata = new Metadata(_metadata);\n  metadata.selectNumberingPlan(phoneNumber.countryCallingCode);\n  var phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\n    nationalNumber: phoneNumber.nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  }); // Check if a national prefix should be present when formatting this number.\n\n  var nationalNumber = phoneNumber.nationalNumber;\n  var format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber); // To do this, we check that a national prefix formatting rule was present\n  // and that it wasn't just the first-group symbol ($1) with punctuation.\n\n  if (format.nationalPrefixFormattingRule()) {\n    if (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n      // The national-prefix is optional in these cases, so we don't need to check if it was present.\n      return true;\n    }\n\n    if (!format.usesNationalPrefix()) {\n      // National Prefix not needed for this number.\n      return true;\n    }\n\n    return Boolean(phoneNumber.nationalPrefix);\n  }\n\n  return true;\n}\n\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) {\n  var firstSlashInBodyIndex = candidate.indexOf('/');\n\n  if (firstSlashInBodyIndex < 0) {\n    // No slashes, this is okay.\n    return false;\n  } // Now look for a second one.\n\n\n  var secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1);\n\n  if (secondSlashInBodyIndex < 0) {\n    // Only one slash, this is okay.\n    return false;\n  } // If the first slash is after the country calling code, this is permitted.\n\n\n  var candidateHasCountryCode = phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' || phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN';\n\n  if (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode) {\n    // Any more slashes and this is illegal.\n    return candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0;\n  }\n\n  return true;\n}\n\nfunction checkNumberGroupingIsValid(number, candidate, metadata, checkGroups, regExpCache) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var normalizedCandidate = normalizeDigits(candidate, true\n  /* keep non-digits */\n  );\n  var formattedNumberGroups = getNationalNumberGroups(metadata, number, null);\n\n  if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n    return true;\n  } // If this didn't pass, see if there are any alternate formats that match, and try them instead.\n\n\n  var alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode());\n  var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n\n  if (alternateFormats) {\n    for (var _iterator = _createForOfIteratorHelperLoose(alternateFormats.numberFormats()), _step; !(_step = _iterator()).done;) {\n      var alternateFormat = _step.value;\n\n      if (alternateFormat.leadingDigitsPatterns().length > 0) {\n        // There is only one leading digits pattern for alternate formats.\n        var leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0]);\n\n        if (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\n          // Leading digits don't match; try another one.\n          continue;\n        }\n      }\n\n      formattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat);\n\n      if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\n\n\nfunction getNationalNumberGroups(metadata, number, formattingPattern) {\n  throw new Error('This part of code hasn\\'t been ported');\n\n  if (formattingPattern) {\n    // We format the NSN only, and split that according to the separator.\n    var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n    return util.formatNsnUsingPattern(nationalSignificantNumber, formattingPattern, 'RFC3966', metadata).split('-');\n  } // This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\n\n\n  var rfc3966Format = formatNumber(number, 'RFC3966', metadata); // We remove the extension part from the formatted string before splitting it into different\n  // groups.\n\n  var endIndex = rfc3966Format.indexOf(';');\n\n  if (endIndex < 0) {\n    endIndex = rfc3966Format.length;\n  } // The country-code will have a '-' following it.\n\n\n  var startIndex = rfc3966Format.indexOf('-') + 1;\n  return rfc3966Format.slice(startIndex, endIndex).split('-');\n}\n\nfunction allNumberGroupsAreExactlyPresent(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN); // Set this to the last group, skipping it if the number has an extension.\n\n  var candidateNumberGroupIndex = number.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1; // First we check if the national significant number is formatted as a block.\n  // We use contains and not equals, since the national significant number may be present with\n  // a prefix such as a national number prefix, or the country code itself.\n\n  if (candidateGroups.length == 1 || candidateGroups[candidateNumberGroupIndex].contains(util.getNationalSignificantNumber(number))) {\n    return true;\n  } // Starting from the end, go through in reverse, excluding the first group, and check the\n  // candidate and number groups are the same.\n\n\n  var formattedNumberGroupIndex = formattedNumberGroups.length - 1;\n\n  while (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0) {\n    if (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex]) {\n      return false;\n    }\n\n    formattedNumberGroupIndex--;\n    candidateNumberGroupIndex--;\n  } // Now check the first group. There may be a national prefix at the start, so we only check\n  // that the candidate group ends with the formatted number group.\n\n\n  return candidateNumberGroupIndex >= 0 && endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]);\n}\n\nfunction allNumberGroupsRemainGrouped(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var fromIndex = 0;\n\n  if (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY) {\n    // First skip the country code if the normalized candidate contained it.\n    var countryCode = String(number.getCountryCode());\n    fromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length();\n  } // Check each group of consecutive digits are not broken into separate groupings in the\n  // {@code normalizedCandidate} string.\n\n\n  for (var i = 0; i < formattedNumberGroups.length; i++) {\n    // Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\n    // doesn't contain the consecutive digits in formattedNumberGroups[i].\n    fromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex);\n\n    if (fromIndex < 0) {\n      return false;\n    } // Moves {@code fromIndex} forward.\n\n\n    fromIndex += formattedNumberGroups[i].length();\n\n    if (i == 0 && fromIndex < normalizedCandidate.length()) {\n      // We are at the position right after the NDC. We get the region used for formatting\n      // information based on the country code in the phone number, rather than the number itself,\n      // as we do not need to distinguish between different countries with the same country\n      // calling code and this is faster.\n      var region = util.getRegionCodeForCountryCode(number.getCountryCode());\n\n      if (util.getNddPrefixForRegion(region, true) != null && Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\n        // This means there is no formatting symbol after the NDC. In this case, we only\n        // accept the number if there is no formatting symbol at all in the number, except\n        // for extensions. This is only important for countries with national prefixes.\n        var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n        return startsWith(normalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length), nationalSignificantNumber);\n      }\n    }\n  } // The check here makes sure that we haven't mistakenly already used the extension to\n  // match the last group of the subscriber number. Note the extension cannot have\n  // formatting in-between digits.\n\n\n  return normalizedCandidate.slice(fromIndex).contains(number.getExtension());\n} //# sourceMappingURL=Leniency.js.map", "map": null, "metadata": {}, "sourceType": "module"}