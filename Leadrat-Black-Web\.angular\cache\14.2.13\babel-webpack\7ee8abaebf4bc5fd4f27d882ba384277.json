{"ast": null, "code": "import { map } from './map';\nimport { from } from '../observable/from';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function mergeMap(project, resultSelector, concurrent = Number.POSITIVE_INFINITY) {\n  if (typeof resultSelector === 'function') {\n    return source => source.pipe(mergeMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii))), concurrent));\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n\n  return source => source.lift(new MergeMapOperator(project, concurrent));\n}\nexport class MergeMapOperator {\n  constructor(project, concurrent = Number.POSITIVE_INFINITY) {\n    this.project = project;\n    this.concurrent = concurrent;\n  }\n\n  call(observer, source) {\n    return source.subscribe(new MergeMapSubscriber(observer, this.project, this.concurrent));\n  }\n\n}\nexport class MergeMapSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, project, concurrent = Number.POSITIVE_INFINITY) {\n    super(destination);\n    this.project = project;\n    this.concurrent = concurrent;\n    this.hasCompleted = false;\n    this.buffer = [];\n    this.active = 0;\n    this.index = 0;\n  }\n\n  _next(value) {\n    if (this.active < this.concurrent) {\n      this._tryNext(value);\n    } else {\n      this.buffer.push(value);\n    }\n  }\n\n  _tryNext(value) {\n    let result;\n    const index = this.index++;\n\n    try {\n      result = this.project(value, index);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.active++;\n\n    this._innerSub(result);\n  }\n\n  _innerSub(ish) {\n    const innerSubscriber = new SimpleInnerSubscriber(this);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    const innerSubscription = innerSubscribe(ish, innerSubscriber);\n\n    if (innerSubscription !== innerSubscriber) {\n      destination.add(innerSubscription);\n    }\n  }\n\n  _complete() {\n    this.hasCompleted = true;\n\n    if (this.active === 0 && this.buffer.length === 0) {\n      this.destination.complete();\n    }\n\n    this.unsubscribe();\n  }\n\n  notifyNext(innerValue) {\n    this.destination.next(innerValue);\n  }\n\n  notifyComplete() {\n    const buffer = this.buffer;\n    this.active--;\n\n    if (buffer.length > 0) {\n      this._next(buffer.shift());\n    } else if (this.active === 0 && this.hasCompleted) {\n      this.destination.complete();\n    }\n  }\n\n}\nexport const flatMap = mergeMap; //# sourceMappingURL=mergeMap.js.map", "map": null, "metadata": {}, "sourceType": "module"}