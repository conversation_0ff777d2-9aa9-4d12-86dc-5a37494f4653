{"ast": null, "code": "import MapCache from './_MapCache.js';\nimport setCacheAdd from './_setCacheAdd.js';\nimport setCacheHas from './_setCacheHas.js';\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\n\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n  this.__data__ = new MapCache();\n\n  while (++index < length) {\n    this.add(values[index]);\n  }\n} // Add methods to `SetCache`.\n\n\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\nexport default SetCache;", "map": null, "metadata": {}, "sourceType": "module"}