{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { scan } from './scan';\nimport { defer } from '../observable/defer';\nimport { map } from './map';\nexport function timeInterval(scheduler = async) {\n  return source => defer(() => {\n    return source.pipe(scan(({\n      current\n    }, value) => ({\n      value,\n      current: scheduler.now(),\n      last: current\n    }), {\n      current: scheduler.now(),\n      value: undefined,\n      last: undefined\n    }), map(({\n      current,\n      last,\n      value\n    }) => new TimeInterval(value, current - last)));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n\n} //# sourceMappingURL=timeInterval.js.map", "map": null, "metadata": {}, "sourceType": "module"}