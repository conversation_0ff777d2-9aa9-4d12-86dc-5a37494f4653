{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nexport function skipLast(count) {\n  return source => source.lift(new SkipLastOperator(count));\n}\n\nclass SkipLastOperator {\n  constructor(_skipCount) {\n    this._skipCount = _skipCount;\n\n    if (this._skipCount < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n\n  call(subscriber, source) {\n    if (this._skipCount === 0) {\n      return source.subscribe(new Subscriber(subscriber));\n    } else {\n      return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));\n    }\n  }\n\n}\n\nclass SkipLastSubscriber extends Subscriber {\n  constructor(destination, _skipCount) {\n    super(destination);\n    this._skipCount = _skipCount;\n    this._count = 0;\n    this._ring = new Array(_skipCount);\n  }\n\n  _next(value) {\n    const skipCount = this._skipCount;\n    const count = this._count++;\n\n    if (count < skipCount) {\n      this._ring[count] = value;\n    } else {\n      const currentIndex = count % skipCount;\n      const ring = this._ring;\n      const oldValue = ring[currentIndex];\n      ring[currentIndex] = value;\n      this.destination.next(oldValue);\n    }\n  }\n\n} //# sourceMappingURL=skipLast.js.map", "map": null, "metadata": {}, "sourceType": "module"}