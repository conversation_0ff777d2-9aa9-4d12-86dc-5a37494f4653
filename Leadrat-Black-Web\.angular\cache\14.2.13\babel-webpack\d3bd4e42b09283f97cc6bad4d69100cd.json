{"ast": null, "code": "import baseGet from './_baseGet.js';\nimport baseSlice from './_baseSlice.js';\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\n\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nexport default parent;", "map": null, "metadata": {}, "sourceType": "module"}