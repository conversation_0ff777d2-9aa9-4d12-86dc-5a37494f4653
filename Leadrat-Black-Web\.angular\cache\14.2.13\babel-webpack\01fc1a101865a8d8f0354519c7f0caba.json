{"ast": null, "code": "import { Subscriber } from './Subscriber';\nexport class OuterSubscriber extends Subscriber {\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.destination.next(innerValue);\n  }\n\n  notifyError(error, innerSub) {\n    this.destination.error(error);\n  }\n\n  notifyComplete(innerSub) {\n    this.destination.complete();\n  }\n\n} //# sourceMappingURL=OuterSubscriber.js.map", "map": null, "metadata": {}, "sourceType": "module"}