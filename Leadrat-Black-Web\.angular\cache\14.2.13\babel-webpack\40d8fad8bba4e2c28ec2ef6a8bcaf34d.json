{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function schedulePromise(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    sub.add(scheduler.schedule(() => input.then(value => {\n      sub.add(scheduler.schedule(() => {\n        subscriber.next(value);\n        sub.add(scheduler.schedule(() => subscriber.complete()));\n      }));\n    }, err => {\n      sub.add(scheduler.schedule(() => subscriber.error(err)));\n    })));\n    return sub;\n  });\n} //# sourceMappingURL=schedulePromise.js.map", "map": null, "metadata": {}, "sourceType": "module"}