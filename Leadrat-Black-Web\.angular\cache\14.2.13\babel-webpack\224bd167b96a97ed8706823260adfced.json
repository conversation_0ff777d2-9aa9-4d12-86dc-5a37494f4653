{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function canReportError(observer) {\n  while (observer) {\n    const {\n      closed,\n      destination,\n      isStopped\n    } = observer;\n\n    if (closed || isStopped) {\n      return false;\n    } else if (destination && destination instanceof Subscriber) {\n      observer = destination;\n    } else {\n      observer = null;\n    }\n  }\n\n  return true;\n} //# sourceMappingURL=canReportError.js.map", "map": null, "metadata": {}, "sourceType": "module"}