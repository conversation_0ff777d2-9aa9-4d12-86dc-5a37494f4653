{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function sequenceEqual(compareTo, comparator) {\n  return source => source.lift(new SequenceEqualOperator(compareTo, comparator));\n}\nexport class SequenceEqualOperator {\n  constructor(compareTo, comparator) {\n    this.compareTo = compareTo;\n    this.comparator = comparator;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SequenceEqualSubscriber(subscriber, this.compareTo, this.comparator));\n  }\n\n}\nexport class SequenceEqualSubscriber extends Subscriber {\n  constructor(destination, compareTo, comparator) {\n    super(destination);\n    this.compareTo = compareTo;\n    this.comparator = comparator;\n    this._a = [];\n    this._b = [];\n    this._oneComplete = false;\n    this.destination.add(compareTo.subscribe(new SequenceEqualCompareToSubscriber(destination, this)));\n  }\n\n  _next(value) {\n    if (this._oneComplete && this._b.length === 0) {\n      this.emit(false);\n    } else {\n      this._a.push(value);\n\n      this.checkValues();\n    }\n  }\n\n  _complete() {\n    if (this._oneComplete) {\n      this.emit(this._a.length === 0 && this._b.length === 0);\n    } else {\n      this._oneComplete = true;\n    }\n\n    this.unsubscribe();\n  }\n\n  checkValues() {\n    const {\n      _a,\n      _b,\n      comparator\n    } = this;\n\n    while (_a.length > 0 && _b.length > 0) {\n      let a = _a.shift();\n\n      let b = _b.shift();\n\n      let areEqual = false;\n\n      try {\n        areEqual = comparator ? comparator(a, b) : a === b;\n      } catch (e) {\n        this.destination.error(e);\n      }\n\n      if (!areEqual) {\n        this.emit(false);\n      }\n    }\n  }\n\n  emit(value) {\n    const {\n      destination\n    } = this;\n    destination.next(value);\n    destination.complete();\n  }\n\n  nextB(value) {\n    if (this._oneComplete && this._a.length === 0) {\n      this.emit(false);\n    } else {\n      this._b.push(value);\n\n      this.checkValues();\n    }\n  }\n\n  completeB() {\n    if (this._oneComplete) {\n      this.emit(this._a.length === 0 && this._b.length === 0);\n    } else {\n      this._oneComplete = true;\n    }\n  }\n\n}\n\nclass SequenceEqualCompareToSubscriber extends Subscriber {\n  constructor(destination, parent) {\n    super(destination);\n    this.parent = parent;\n  }\n\n  _next(value) {\n    this.parent.nextB(value);\n  }\n\n  _error(err) {\n    this.parent.error(err);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.parent.completeB();\n    this.unsubscribe();\n  }\n\n} //# sourceMappingURL=sequenceEqual.js.map", "map": null, "metadata": {}, "sourceType": "module"}