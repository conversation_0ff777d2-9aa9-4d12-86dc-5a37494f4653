{"ast": null, "code": "import apply from './_apply.js';\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMax = Math.max;\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\n\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? func.length - 1 : start, 0);\n  return function () {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n\n    index = -1;\n    var otherArgs = Array(start + 1);\n\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\n\nexport default overRest;", "map": null, "metadata": {}, "sourceType": "module"}