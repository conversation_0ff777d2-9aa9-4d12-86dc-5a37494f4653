{"ast": null, "code": "import getAllKeys from './_getAllKeys.js';\n/** Used to compose bitmasks for value comparisons. */\n\nvar COMPARE_PARTIAL_FLAG = 1;\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\n\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n\n  var index = objLength;\n\n  while (index--) {\n    var key = objProps[index];\n\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  } // Check that cyclic values are equal.\n\n\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n  var skipCtor = isPartial;\n\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n    } // Recursively compare objects (susceptible to call stack limits).\n\n\n    if (!(compared === undefined ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n      result = false;\n      break;\n    }\n\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor; // Non `Object` object instances with different constructors are not equal.\n\n    if (objCtor != othCtor && 'constructor' in object && 'constructor' in other && !(typeof objCtor == 'function' && objCtor instanceof objCtor && typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nexport default equalObjects;", "map": null, "metadata": {}, "sourceType": "module"}