{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { noop } from '../util/noop';\nimport { isFunction } from '../util/isFunction';\nexport function tap(nextOrObserver, error, complete) {\n  return function tapOperatorFunction(source) {\n    return source.lift(new DoOperator(nextOrObserver, error, complete));\n  };\n}\n\nclass DoOperator {\n  constructor(nextOrObserver, error, complete) {\n    this.nextOrObserver = nextOrObserver;\n    this.error = error;\n    this.complete = complete;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TapSubscriber(subscriber, this.nextOrObserver, this.error, this.complete));\n  }\n\n}\n\nclass TapSubscriber extends Subscriber {\n  constructor(destination, observerOrNext, error, complete) {\n    super(destination);\n    this._tapNext = noop;\n    this._tapError = noop;\n    this._tapComplete = noop;\n    this._tapError = error || noop;\n    this._tapComplete = complete || noop;\n\n    if (isFunction(observerOrNext)) {\n      this._context = this;\n      this._tapNext = observerOrNext;\n    } else if (observerOrNext) {\n      this._context = observerOrNext;\n      this._tapNext = observerOrNext.next || noop;\n      this._tapError = observerOrNext.error || noop;\n      this._tapComplete = observerOrNext.complete || noop;\n    }\n  }\n\n  _next(value) {\n    try {\n      this._tapNext.call(this._context, value);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(value);\n  }\n\n  _error(err) {\n    try {\n      this._tapError.call(this._context, err);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    try {\n      this._tapComplete.call(this._context);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    return this.destination.complete();\n  }\n\n} //# sourceMappingURL=tap.js.map", "map": null, "metadata": {}, "sourceType": "module"}