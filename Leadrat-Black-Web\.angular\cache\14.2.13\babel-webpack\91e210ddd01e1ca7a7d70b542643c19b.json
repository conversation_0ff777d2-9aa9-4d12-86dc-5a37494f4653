{"ast": null, "code": "import isSymbol from './isSymbol.js';\n/** Used as references for various `Number` constants. */\n\nvar NAN = 0 / 0;\n/**\n * The base implementation of `_.toNumber` which doesn't ensure correct\n * conversions of binary, hexadecimal, or octal string values.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n */\n\nfunction baseToNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n\n  if (isSymbol(value)) {\n    return NAN;\n  }\n\n  return +value;\n}\n\nexport default baseToNumber;", "map": null, "metadata": {}, "sourceType": "module"}