{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { map } from './map';\nexport function timestamp(scheduler = async) {\n  return map(value => new Timestamp(value, scheduler.now()));\n}\nexport class Timestamp {\n  constructor(value, timestamp) {\n    this.value = value;\n    this.timestamp = timestamp;\n  }\n\n} //# sourceMappingURL=timestamp.js.map", "map": null, "metadata": {}, "sourceType": "module"}