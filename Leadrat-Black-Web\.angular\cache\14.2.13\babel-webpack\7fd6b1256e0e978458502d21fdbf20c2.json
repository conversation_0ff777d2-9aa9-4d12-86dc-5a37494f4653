{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n\n/**\n * @module watchdog/utils/getsubnodes\n */\n\n/* globals EventTarget, Event */\nexport default function getSubNodes(head, excludedProperties = new Set()) {\n  const nodes = [head]; // @if CK_DEBUG_WATCHDOG // const prevNodeMap = new Map();\n  // Nodes are stored to prevent infinite looping.\n\n  const subNodes = new Set();\n  let nodeIndex = 0;\n\n  while (nodes.length > nodeIndex) {\n    // Incrementing the iterator is much faster than changing size of the array with Array.prototype.shift().\n    const node = nodes[nodeIndex++];\n\n    if (subNodes.has(node) || !shouldNodeBeIncluded(node) || excludedProperties.has(node)) {\n      continue;\n    }\n\n    subNodes.add(node); // Handle arrays, maps, sets, custom collections that implements `[ Symbol.iterator ]()`, etc.\n\n    if (Symbol.iterator in node) {\n      // The custom editor iterators might cause some problems if the editor is crashed.\n      try {\n        for (const n of node) {\n          nodes.push(n); // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( n ) ) {\n          // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( n, node );\n          // @if CK_DEBUG_WATCHDOG // }\n        }\n      } catch (err) {// Do not log errors for broken structures\n        // since we are in the error handling process already.\n        // eslint-disable-line no-empty\n      }\n    } else {\n      for (const key in node) {\n        // We share a reference via the protobuf library within the editors,\n        // hence the shared value should be skipped. Although, it's not a perfect\n        // solution since new places like that might occur in the future.\n        if (key === 'defaultValue') {\n          continue;\n        }\n\n        nodes.push(node[key]); // @if CK_DEBUG_WATCHDOG // if ( !prevNodeMap.has( node[ key ] ) ) {\n        // @if CK_DEBUG_WATCHDOG // \tprevNodeMap.set( node[ key ], node );\n        // @if CK_DEBUG_WATCHDOG // }\n      }\n    }\n  } // @if CK_DEBUG_WATCHDOG // return { subNodes, prevNodeMap } as any;\n\n\n  return subNodes;\n}\n\nfunction shouldNodeBeIncluded(node) {\n  const type = Object.prototype.toString.call(node);\n  const typeOfNode = typeof node;\n  return !(typeOfNode === 'number' || typeOfNode === 'boolean' || typeOfNode === 'string' || typeOfNode === 'symbol' || typeOfNode === 'function' || type === '[object Date]' || type === '[object RegExp]' || type === '[object Module]' || node === undefined || node === null || // This flag is meant to exclude singletons shared across editor instances. So when an error is thrown in one editor,\n  // the other editors connected through the reference to the same singleton are not restarted. This is a temporary workaround\n  // until a better solution is found.\n  // More in https://github.com/ckeditor/ckeditor5/issues/12292.\n  node._watchdogExcluded || // Skip native DOM objects, e.g. Window, nodes, events, etc.\n  node instanceof EventTarget || node instanceof Event);\n}", "map": null, "metadata": {}, "sourceType": "module"}