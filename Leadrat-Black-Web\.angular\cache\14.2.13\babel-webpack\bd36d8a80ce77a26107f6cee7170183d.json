{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AnimationFrameScheduler extends AsyncScheduler {\n  flush(action) {\n    this.active = true;\n    this.scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    let index = -1;\n    let count = actions.length;\n    action = action || actions.shift();\n\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (++index < count && (action = actions.shift()));\n\n    this.active = false;\n\n    if (error) {\n      while (++index < count && (action = actions.shift())) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n} //# sourceMappingURL=AnimationFrameScheduler.js.map", "map": null, "metadata": {}, "sourceType": "module"}