{"ast": null, "code": "import createExtensionPattern from './createExtensionPattern.js'; // Regexp of all known extension prefixes used by different regions followed by\n// 1 or more valid digits, for use when parsing.\n\nvar EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i'); // Strips any extension (as in, the part of the number dialled after the call is\n// connected, usually indicated with extn, ext, x or similar) from the end of\n// the number, and returns it.\n\nexport default function extractExtension(number) {\n  var start = number.search(EXTN_PATTERN);\n\n  if (start < 0) {\n    return {};\n  } // If we find a potential extension, and the number preceding this is a viable\n  // number, we assume it is an extension.\n\n\n  var numberWithoutExtension = number.slice(0, start);\n  var matches = number.match(EXTN_PATTERN);\n  var i = 1;\n\n  while (i < matches.length) {\n    if (matches[i]) {\n      return {\n        number: numberWithoutExtension,\n        ext: matches[i]\n      };\n    }\n\n    i++;\n  }\n} //# sourceMappingURL=extractExtension.js.map", "map": null, "metadata": {}, "sourceType": "module"}