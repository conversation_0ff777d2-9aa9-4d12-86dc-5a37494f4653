"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[376],{26733:(ee,$,o)=>{o.d($,{W:()=>w});var t=o(5e3),n=o(82722),F=o(2976),A=o(51420),R=o(61021),f=o(82667),x=o(97047),W=o(40553),M=o(66844),S=o(97674),K=o(29475),U=o(39830),c=o(61239),B=o(8577),L=o(80877),G=o(78990),N=o(61357),k=o(38576),j=o(83681),z=o(30166),I=o(32049),V=o(92340),X=o(48315),Z=o(38827),u=o(65620),y=o(32496),r=o(18995),d=o(1880),h=o(69808),D=o(63172),C=o(11970),b=o(47511);function T(_,g){if(1&_){const s=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",10)(1,"ag-grid-angular",11,12),t.\u0275\u0275listener("gridReady",function(e){t.\u0275\u0275restoreView(s);const p=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(p.onGridReady(e))}),t.\u0275\u0275elementEnd()()}if(2&_){const s=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("pagination",!0)("paginationPageSize",s.pageSize)("gridOptions",s.gridOptions)("rowData",s.rowData)("suppressPaginationPanel",!0)}}function O(_,g){if(1&_){const s=t.\u0275\u0275getCurrentView();t.\u0275\u0275elementStart(0,"div",13)(1,"pagination",14),t.\u0275\u0275listener("pageChange",function(e){t.\u0275\u0275restoreView(s);const p=t.\u0275\u0275nextContext();return t.\u0275\u0275resetView(p.onPageChange(e))}),t.\u0275\u0275elementEnd()()}if(2&_){const s=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("offset",s.currOffset)("limit",1)("range",1)("size",s.getPages(s.totalUploadedCount,s.pageSize))}}function P(_,g){1&_&&(t.\u0275\u0275elementStart(0,"div",15),t.\u0275\u0275element(1,"application-loader"),t.\u0275\u0275elementEnd())}let w=(()=>{class _{constructor(s,i,e,p,l,a){this.gridOptionsService=s,this.modalService=i,this._store=e,this._notificationService=p,this._translateService=l,this.trackingService=a,this.stopper=new t.EventEmitter,this.pageSize=F.IV1,this.currOffset=0,this.rowData=[],this.s3BucketUrl=V.N.s3ImageBucketURL,this.getPages=R.UQ,this.filtersPayload={pageNumber:1,pageSize:this.pageSize,path:"lead"},this.isDataExcelListLoading=!0,this.gridOptions=this.gridOptionsService.getGridSettings(this),this.defaultColDef=this.gridOptions.defaultColDef,this.gridOptions.rowData=this.rowData,this.initializeGridSettings()}ngOnInit(){var s,i;window.copyFileName=this.copyFileName.bind(this),this._store.select(I.Xf).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.userData=e}),this.trackingService.trackFeature(`Web.${null===(i=null===(s=this.fieldType)||void 0===s?void 0:s.replace(/\s+/g,""))||void 0===i?void 0:i.replace(/^./,e=>null==e?void 0:e.toUpperCase())}.Page.ExcelUploadTracker.Visit`),this._store.dispatch(new z.MI),this._store.select(I.Sh).pipe((0,n.R)(this.stopper)).subscribe(e=>{const p=null==e?void 0:e.map(l=>Object.assign(Object.assign({},l),{fullName:`${l.firstName} ${l.lastName}`}));this.allUsers=p.sort((l,a)=>(!0===a.isActive?1:0)-(!0===l.isActive?1:0))}),"leads"==this.fieldType&&(this._store.select(M.Jx).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(M.Q3).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"data"==this.fieldType&&(this._store.select(x.JX).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(x.C).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"property"==this.fieldType&&(this._store.select(N.oe).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e}),this._store.select(N.bn).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount})),"project"==this.fieldType&&(this._store.select(L.$F).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e}),this._store.select(L.JL).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount})),"project-unit"==this.fieldType&&(this._store.select(L.Si).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e}),this._store.select(L.v9).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount})),"Data Migration"==this.fieldType&&(this._store.select(x.T7).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(x.OM).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"Lead Migration"==this.fieldType&&(this._store.select(M.Eu).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(M.wG).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"Agency Name"==this.fieldType&&(this._store.select(c.KJ).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(c.HW).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"Channel partner"==this.fieldType&&(this._store.select(c.Y1).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(c.qQ).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"Campaign name"==this.fieldType&&(this._store.select(c.dG).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(c.To).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"user"==this.fieldType&&(this._store.select(I.Sg).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(I.XZ).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=e})),"address"==this.fieldType&&(this._store.select(K.f6).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(K.B5).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=null==e?void 0:e.trackerList})),"reference"==this.fieldType&&(this._store.select(j.DR).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.rowData=null==e?void 0:e.items,this.totalUploadedCount=null==e?void 0:e.totalCount}),this._store.select(j.ne).pipe((0,n.R)(this.stopper)).subscribe(e=>{this.isDataExcelListLoading=null==e?void 0:e.excelUploadedList})),this.initializeGridSettings()}initializeGridSettings(){if(this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.columnDefs=[{headerName:"Status",field:"Status",minWidth:85,valueGetter:s=>{var i;return[A.XX[null===(i=s.data)||void 0===i?void 0:i.status]]},cellRenderer:s=>`<p>${s.value}</p>`},{headerName:"Done By",field:"Done By",minWidth:180,valueGetter:s=>{var i,e,p,l,a;return[(0,R.sW)(null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.createdBy,this.allUsers,!0)||"",null!==(e=s.data)&&void 0!==e&&e.createdOn?"At "+(0,R.h5)(null===(p=s.data)||void 0===p?void 0:p.createdOn,null===(a=null===(l=this.userData)||void 0===l?void 0:l.timeZoneInfo)||void 0===a?void 0:a.baseUTcOffset,"fullDateTime"):"--"]},cellRenderer:s=>{var i,e,p,l,a;return`<p class="text-nowrap text-truncate-1 break-all">${s.value[0]}</p>\n            <p class="text-nowrap text-truncate-1 break-all">${s.value[1]}</p>\n            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(e=null===(i=this.userData)||void 0===i?void 0:i.timeZoneInfo)||void 0===e?void 0:e.timeZoneName)&&(null===(p=this.userData)||void 0===p?void 0:p.shouldShowTimeZone)&&s.value[1]?"("+(null===(a=null===(l=this.userData)||void 0===l?void 0:l.timeZoneInfo)||void 0===a?void 0:a.timeZoneName)+")":""}</p>`}},{headerName:"Total Count",field:"Total Count",minWidth:90,filter:!1,valueGetter:s=>{var i;return[null===(i=s.data)||void 0===i?void 0:i.totalCount]},cellRenderer:s=>{var i,e;return 0==(null===(i=s.data)||void 0===i?void 0:i.status)||1==(null===(e=s.data)||void 0===e?void 0:e.status)?"--":`<p>${s.value}</p>`}},{headerName:"Total Uploaded Count",field:"Total Uploaded Count",minWidth:90,filter:!1,valueGetter:s=>{var i;return[null===(i=s.data)||void 0===i?void 0:i.totalUploadedCount]},cellRenderer:s=>{var i,e;return 0==(null===(i=s.data)||void 0===i?void 0:i.status)||1==(null===(e=s.data)||void 0===e?void 0:e.status)?"--":`<p>${s.value}</p>`}},{headerName:"Duplicate Count",field:"Duplicate Count",minWidth:90,filter:!1,valueGetter:s=>{var i;return[null===(i=s.data)||void 0===i?void 0:i.duplicateCount]},cellRenderer:s=>{var i,e;return 0==(null===(i=s.data)||void 0===i?void 0:i.status)||1==(null===(e=s.data)||void 0===e?void 0:e.status)?"--":`<p>${s.value}</p>`}},{headerName:"Invalid Count",field:"Invalid Count",minWidth:90,filter:!1,valueGetter:s=>{var i;return[null===(i=s.data)||void 0===i?void 0:i.invalidCount]},cellRenderer:s=>{var i,e;return 0==(null===(i=s.data)||void 0===i?void 0:i.status)||1==(null===(e=s.data)||void 0===e?void 0:e.status)?"--":`<p>${s.value}</p>`}},{headerName:"File Name",field:"File Name",minWidth:200,filter:!1,cellRenderer:this.fileNameCellRenderer.bind(this),valueGetter:s=>{var i;return[null===(i=s.data)||void 0===i?void 0:i.sheetName]}},{headerName:"Invalid Report/ Error Message",field:"Invalid Report/ Error Message",minWidth:180,filter:!1,valueGetter:s=>{var i,e,p,l,a,m,v,E;return[null!==(i=s.data)&&void 0!==i&&i.invalidDataS3BucketKey?this.s3BucketUrl+(null===(e=s.data)||void 0===e?void 0:e.invalidDataS3BucketKey):"",null===(p=s.data)||void 0===p?void 0:p.message,null===(l=s.data)||void 0===l?void 0:l.status,null!==(a=s.data)&&void 0!==a&&a.invalidProspectS3BucketKey?this.s3BucketUrl+(null===(m=s.data)||void 0===m?void 0:m.invalidProspectS3BucketKey):"",null!==(v=s.data)&&void 0!==v&&v.invalidS3BucketKey?this.s3BucketUrl+(null===(E=s.data)||void 0===E?void 0:E.invalidS3BucketKey):""]},cellRenderer:s=>{let i="";return i="Data Migration"===this.fieldType?s.value[3]:"Agency Name"===this.fieldType||"Channel partner"===this.fieldType||"Campaign name"==this.fieldType?s.value[4]:s.value[0],i?`<a href="${i}" class="btn btn-xxs btn-linear-green text-nowrap flex-center w-150">\n                <span class="icon ic-xxs ic-download"></span>\n                <span class="text-white ml-8">Download Report</span></a>`:4===s.value[2]&&s.value[1]?`<p class="text-danger text-truncate-2 text-sm">${s.value[1]}</p>`:"--"}}],this.gridOptions.context={componentParent:this},"project"!==this.fieldType){const s={headerName:"Unique Count",field:"Unique Count",minWidth:90,filter:!1,valueGetter:i=>{var e,p,l,a;return[null===(e=i.data)||void 0===e?void 0:e.distinctLeadCount,null===(p=i.data)||void 0===p?void 0:p.distinctProspectCount,null===(l=i.data)||void 0===l?void 0:l.distinctUnitCount,null===(a=null==i?void 0:i.data)||void 0===a?void 0:a.distinctCount]},cellRenderer:i=>{var e,p;if(0==(null===(e=i.data)||void 0===e?void 0:e.status)||1==(null===(p=i.data)||void 0===p?void 0:p.status))return"--";let l="";return l="property"==this.fieldType||"leads"==this.fieldType||"Lead Migration"==this.fieldType||"address"==this.fieldType||"reference"==this.fieldType?i.value[0]:"project-unit"==this.fieldType?i.value[2]:"Agency Name"==this.fieldType||"Channel partner"==this.fieldType||"Campaign name"==this.fieldType||"user"==this.fieldType?i.value[3]:i.value[1],`<p>${l}</p>`}};"user"!==this.fieldType&&this.gridOptions.columnDefs.splice(3,0,s)}}fileNameCellRenderer(s){var i;const e=s.value[0]||"";return"leads"==this.fieldType||"Lead Migration"==this.fieldType||"data"==this.fieldType||"Data Migration"==this.fieldType?`\n      <div class="flex-between">\n        <p class="text-truncate-1 break-all">${e}</p>\n        <div title="Copy File Name" class="bg-brown icon-badge" onclick="copyFileName('${null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.s3BucketKey}')">\n          <span class="icon ic-copy-clipboard m-auto ic-xxxs"></span>\n        </div>\n      </div>\n    `:`<p class="text-truncate-1 break-all">${e}</p>`}copyFileName(s){var i;null===(i=navigator.clipboard)||void 0===i||i.writeText(s),this._notificationService.success(this._translateService.instant("GLOBAL.link-copied"))}onGridReady(s){this.gridApi=s.api,s.api.sizeColumnsToFit(),this.gridColumnApi=s.columnApi}onPageChange(s){var i;this.currOffset=s,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:s+1,pageSize:this.pageSize}),this.trackingService.trackFeature(`Web.${null===(i=this.fieldType)||void 0===i?void 0:i.replace(/\s+/g,"").replace(/^./,e=>e.toUpperCase())}.Button.ExcelUploadTrackerpagination.Click`),this.updateTrackerList()}updateTrackerList(){var s,i,e,p,l,a,m,v,E,J,Q,H,Y,q;this.trackingService.trackFeature(`Web.${null===(s=this.fieldType)||void 0===s?void 0:s.replace(/\s+/g,"").replace(/^./,te=>te.toUpperCase())}.Button.ExcelUploadTrackerRefresh.Click`),this._store.dispatch("property"==this.fieldType?new G.Bk(null===(i=this.filtersPayload)||void 0===i?void 0:i.pageNumber,this.filtersPayload.pageSize):"project"==this.fieldType?new B.Zs(null===(e=this.filtersPayload)||void 0===e?void 0:e.pageNumber,this.filtersPayload.pageSize):"leads"==this.fieldType?new W.v5r(null===(p=this.filtersPayload)||void 0===p?void 0:p.pageNumber,this.filtersPayload.pageSize):"project"==this.fieldType?new B.AJ(null===(l=this.filtersPayload)||void 0===l?void 0:l.pageNumber,this.filtersPayload.pageSize):"Data Migration"==this.fieldType?new f.q9(null===(a=this.filtersPayload)||void 0===a?void 0:a.pageNumber,this.filtersPayload.pageSize):"Lead Migration"==this.fieldType?new W.ocl(null===(m=this.filtersPayload)||void 0===m?void 0:m.pageNumber,this.filtersPayload.pageSize):"Agency Name"==this.fieldType?new U.aK(null===(v=this.filtersPayload)||void 0===v?void 0:v.pageNumber,this.filtersPayload.pageSize):"Channel partner"==this.fieldType?new U.j8(null===(E=this.filtersPayload)||void 0===E?void 0:E.pageNumber,this.filtersPayload.pageSize):"Campaign name"==this.fieldType?new U.Gn(null===(J=this.filtersPayload)||void 0===J?void 0:J.pageNumber,this.filtersPayload.pageSize):"user"==this.fieldType?new z.WY(null===(Q=this.filtersPayload)||void 0===Q?void 0:Q.pageNumber,this.filtersPayload.pageSize):"address"==this.fieldType?new S.lt(null===(H=this.filtersPayload)||void 0===H?void 0:H.pageNumber,this.filtersPayload.pageSize):"reference"==this.fieldType?new k.q4(null===(Y=this.filtersPayload)||void 0===Y?void 0:Y.pageNumber,this.filtersPayload.pageSize):new f.aD(null===(q=this.filtersPayload)||void 0===q?void 0:q.pageNumber,this.filtersPayload.pageSize))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return _.\u0275fac=function(s){return new(s||_)(t.\u0275\u0275directiveInject(X.t),t.\u0275\u0275directiveInject(Z.tT),t.\u0275\u0275directiveInject(u.yh),t.\u0275\u0275directiveInject(y.TF),t.\u0275\u0275directiveInject(r.sK),t.\u0275\u0275directiveInject(d.e))},_.\u0275cmp=t.\u0275\u0275defineComponent({type:_,selectors:[["excel-uploaded-status"]],decls:16,vars:10,consts:[[1,"bg-coal","w-100","px-20","py-12","text-white","brtl-10","brtr-10","flex-between"],[1,"fw-semi-bold"],[1,"text-capitalize"],[1,"btn","btn-sm","btn-linear-green","align-center",3,"click"],[1,"ic-refresh","icon","ic-xxs","mr-8","ph-mr-0"],[1,"text-white","text-normal","ph-d-none"],[1,"ic-close-secondary","ic-close-modal","tb-ic-close-secondary",3,"click"],["class","max-h-100-176 scrollbar",4,"ngIf","ngIfElse"],["class","flex-end m-20",4,"ngIf"],["gridLoader",""],[1,"max-h-100-176","scrollbar"],[1,"ag-theme-alpine",3,"pagination","paginationPageSize","gridOptions","rowData","suppressPaginationPanel","gridReady"],["agGrid",""],[1,"flex-end","m-20"],[3,"offset","limit","range","size","pageChange"],[1,"flex-center","h-300"]],template:function(s,i){if(1&s&&(t.\u0275\u0275elementStart(0,"div",0)(1,"h3",1)(2,"span",2),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275text(4),t.\u0275\u0275pipe(5,"translate"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(6,"button",3),t.\u0275\u0275listener("click",function(){return i.updateTrackerList()}),t.\u0275\u0275element(7,"span",4),t.\u0275\u0275elementStart(8,"span",5),t.\u0275\u0275text(9),t.\u0275\u0275pipe(10,"translate"),t.\u0275\u0275elementEnd()(),t.\u0275\u0275elementStart(11,"a",6),t.\u0275\u0275listener("click",function(){return i.modalService.hide()}),t.\u0275\u0275elementEnd()(),t.\u0275\u0275template(12,T,3,5,"div",7),t.\u0275\u0275template(13,O,2,4,"div",8),t.\u0275\u0275template(14,P,2,0,"ng-template",null,9,t.\u0275\u0275templateRefExtractor)),2&s){const e=t.\u0275\u0275reference(15);t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(i.fieldType),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" - ",t.\u0275\u0275pipeBind1(5,6,"LEADS.excel-upload-tracker"),""),t.\u0275\u0275advance(5),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(10,8,"BULK_LEAD.refresh-data")),t.\u0275\u0275advance(3),t.\u0275\u0275property("ngIf",!i.isDataExcelListLoading)("ngIfElse",e),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf",!i.isDataExcelListLoading)}},dependencies:[h.O5,D.N8,C.Q,b.t,r.X$],encapsulation:2}),_})()},35309:(ee,$,o)=>{o.d($,{F:()=>Z});var t=o(5e3),n=o(93075),F=o(82722),A=o(61021),R=o(74775),f=o(42223),x=o(82667),W=o(40553),M=o(97674),S=o(39830),K=o(8577),U=o(78990),c=o(73175),B=o(30166),L=o(32049),G=o(38827),N=o(65620),k=o(69808),j=o(46302),z=o(18995);function I(u,y){if(1&u&&(t.\u0275\u0275elementStart(0,"div",12)(1,"div",13),t.\u0275\u0275element(2,"input",14),t.\u0275\u0275elementStart(3,"label",15),t.\u0275\u0275text(4),t.\u0275\u0275elementEnd()()()),2&u){const r=y.$implicit,d=t.\u0275\u0275nextContext(2);t.\u0275\u0275advance(2),t.\u0275\u0275property("id",(null==r?null:r.value)+"notesTypeOptionRadio")("value",r.value)("formControl",d.notesType),t.\u0275\u0275advance(1),t.\u0275\u0275property("for",(null==r?null:r.value)+"notesTypeOptionRadio"),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate1(" ",r.label," ")}}function V(u,y){if(1&u&&(t.\u0275\u0275elementStart(0,"div",10),t.\u0275\u0275template(1,I,5,5,"div",11),t.\u0275\u0275elementEnd()),2&u){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(1),t.\u0275\u0275property("ngForOf",r.notesTypeOptions)}}function X(u,y){if(1&u&&(t.\u0275\u0275elementStart(0,"div",16)(1,"div",17)(2,"div",18),t.\u0275\u0275text(3),t.\u0275\u0275pipe(4,"translate"),t.\u0275\u0275elementEnd(),t.\u0275\u0275element(5,"div",19),t.\u0275\u0275pipe(6,"translate"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(7,"form-errors-wrapper",20),t.\u0275\u0275element(8,"input",21),t.\u0275\u0275pipe(9,"translate"),t.\u0275\u0275elementEnd()()),2&u){const r=t.\u0275\u0275nextContext();t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(4,5,"GLOBAL.notes-count")),t.\u0275\u0275advance(2),t.\u0275\u0275propertyInterpolate("title",t.\u0275\u0275pipeBind1(6,7,"GLOBAL.between-1-to-100-most-recently")),t.\u0275\u0275advance(2),t.\u0275\u0275property("control",r.notesCount),t.\u0275\u0275advance(1),t.\u0275\u0275propertyInterpolate("placeholder",t.\u0275\u0275pipeBind1(9,9,"GLOBAL.ex-3")),t.\u0275\u0275property("formControl",r.notesCount)}}let Z=(()=>{class u{constructor(r,d){this.modalRef=r,this.store=d,this.stopper=new t.EventEmitter,this.notesType=new n.NI(!1),this.notesCount=new n.NI(3,n.kI.required),this.notesTypeOptions=[{value:!1,label:"Exclude Notes"},{value:!0,label:"Include Notes"}],this.currentDate=new Date}ngOnInit(){this.store.select(L.Xf).pipe((0,F.R)(this.stopper)).subscribe(r=>{var d,h;this.userData=r,this.currentDate=(0,A.Xp)(null===(h=null===(d=this.userData)||void 0===d?void 0:d.timeZoneInfo)||void 0===h?void 0:h.baseUTcOffset)})}getExportLabel(r){return{"lead/new/all":"lead(s)",prospect:"data","prospect/custom-filters":"data","Property/new/all":"property(s)",project:"project(s)",attendance:"attendance","team/id":"member(s)",agency:"agency(s)","user/getallusers":"user(s)",channelpartner:"channel partner(s)",campaign:"campaign(s)",Listing:"listing property(s)"}[r]||"reports"}exportData(){var r,d,h,D,C,b,T,O,P,w,_,g,s,i,e,p,l;let a=Object.assign(Object.assign({},this.payload),{FileName:this.formatDateTime()});switch(null!=a&&a.IsWithTeam&&(a.IsWithTeam=!0),delete a.pageNumber,delete a.pageSize,delete a.PageNumber,delete a.PageSize,Object.entries(a).forEach(([m,v])=>{if(Array.isArray(v)&&void 0!==v[0]){const E=[(0,A.sn)(m,v)];a[m]=E.flat()}}),a.path){case"lead/new/all":a=Object.assign(Object.assign(Object.assign({},a),{AgencyNames:"string"==typeof(null===(r=this.payload)||void 0===r?void 0:r.AgencyNames)?[null===(d=this.payload)||void 0===d?void 0:d.AgencyNames]:null===(h=this.payload)||void 0===h?void 0:h.AgencyNames,StatusIds:"string"==typeof(null===(D=this.payload)||void 0===D?void 0:D.StatusIds)?[null===(C=this.payload)||void 0===C?void 0:C.StatusIds]:null===(b=this.payload)||void 0===b?void 0:b.StatusIds,assignTo:"string"==typeof(null===(T=this.payload)||void 0===T?void 0:T.assignTo)?[null===(O=this.payload)||void 0===O?void 0:O.assignTo]:null===(P=this.payload)||void 0===P?void 0:P.assignTo,Projects:"string"==typeof(null===(w=this.payload)||void 0===w?void 0:w.Projects)?[null===(_=this.payload)||void 0===_?void 0:_.Projects]:null===(g=this.payload)||void 0===g?void 0:g.Projects,SubSources:"string"==typeof(null===(s=this.payload)||void 0===s?void 0:s.SubSources)?[null===(i=this.payload)||void 0===i?void 0:i.SubSources]:null===(e=this.payload)||void 0===e?void 0:e.SubSources,IsWithNotes:this.notesType.value}),!0===this.notesType.value&&{notesCount:this.notesCount.value}),this.store.dispatch(new W.eFb(a));break;case"prospect/custom-filters":case"prospect":a=Object.assign(Object.assign({},a),{SecondLevelFilter:null!=a&&a.SecondLevelFilter?null==a?void 0:a.SecondLevelFilter:0}),this.store.dispatch(new x.tN(a));break;case"project":a=Object.assign(Object.assign({},a),{ProjectType:null===(p=["All","Residential","Commercial","Agricultural"])||void 0===p?void 0:p.indexOf(null===(l=this.payload)||void 0===l?void 0:l.ProjectType)}),this.store.dispatch(new K.Mh(a));break;case"Property/new/all":this.store.dispatch(new U.xQ(a));break;case"attendance":this.store.dispatch(new R.gh(a));break;case"report/user/new/status":this.store.dispatch(new c.Mh4(a));break;case"report/project/status/new":this.store.dispatch(new c.iOh(a));break;case"report/source/status/new":this.store.dispatch(new c.yI5(a));break;case"report/subsource/status/new":this.store.dispatch(new c.y9n(a));break;case"report/agency/status/new":this.store.dispatch(new c.m8k(a));break;case"report/user/meetingandvisit/level1":this.store.dispatch(new c.mB7(a));break;case"report/activity/level9":this.store.dispatch(new c.jHq(a));break;case"datareport/activity":case"datareport/activity/communication":this.store.dispatch(new f.H_(a));break;case"report/substatus/bysubsource/updated":this.store.dispatch(new c.AQ2(a));break;case"report/substatus":this.store.dispatch(new c.iL3(a));break;case"report/user/call-log/new":this.store.dispatch(new c.r_e(a));break;case"report/project/bysubstatus/updated":this.store.dispatch(new c.Nbs(a));break;case"report/datewisesource":this.store.dispatch(new c.Ijz(a));break;case"datareport/user/status":this.store.dispatch(new f.aX(a));break;case"datareport/project/status":this.store.dispatch(new f.NQ(a));break;case"datareport/source/status":this.store.dispatch(new f.w3(a));break;case"datareport/subsource/status":this.store.dispatch(new f.ed(a));break;case"datareport/user/data-call-log":this.store.dispatch(new f.AK(a));break;case"report/uservssource":this.store.dispatch(new c.wo0(a));break;case"report/uservssubsource":this.store.dispatch(new c.Zv6(a));break;case"user/getallusers":this.store.dispatch(new B.Ml(a));break;case"report/user/meetingandvisit/new/level":this.store.dispatch(new c.VV3(a));break;case"agency":this.store.dispatch(new S.Jo(a));break;case"channelpartner":this.store.dispatch(new S.ny(a));break;case"campaign":this.store.dispatch(new S.oK(a));break;case"team/id":this.store.dispatch(new B.h2(a));break;case"report/cityvslead":this.store.dispatch(new c.XIv(a));break;case"Listing":this.store.dispatch(new M.Fh(a))}this.modalRef.hide()}formatDateTime(){const r=new Date(this.currentDate),d=P=>P.toString().padStart(2,"0");return`${d(r.getDate())}-${d(r.getMonth()+1)}-${r.getFullYear().toString().slice(-2)}_${d(r.getHours())}:${d(r.getMinutes())}:${d(r.getSeconds())}`}closePopup(){this.modalRef.hide()}}return u.\u0275fac=function(r){return new(r||u)(t.\u0275\u0275directiveInject(G.UZ),t.\u0275\u0275directiveInject(N.yh))},u.\u0275cmp=t.\u0275\u0275defineComponent({type:u,selectors:[["export-mail"]],decls:24,vars:20,consts:[[1,"p-20"],[1,"ic-close-secondary","ic-close-modal","ip-ic-close-modal",3,"click"],[1,"mt-20","text-center","fw-600"],[1,"text-gray","fw-600","mt-20","text-center"],[1,"header-5","text-coal"],["class","d-flex flex-wrap",4,"ngIf"],["class","w-100",4,"ngIf"],[1,"flex-center","mt-30"],[1,"btn-gray",3,"click"],[1,"btn-coal","ml-10",3,"disabled","click"],[1,"d-flex","flex-wrap"],["class","form-check form-check-inline",4,"ngFor","ngForOf"],[1,"form-check","form-check-inline"],[1,"align-center","mt-8"],["type","radio","name","notesType",1,"radio-check-input","border-remove",3,"id","value","formControl"],[1,"fw-600","text-dark-800","cursor-pointer","text-sm","ml-8",3,"for"],[1,"w-100"],[1,"align-end"],[1,"field-label-req"],[1,"icon","ic-circle-exclamation","ic-slate-110","cursor-pointer","ic-sm","ml-20","mb-6",3,"title"],["label","Notes Count",3,"control"],["type","number","min","1","max","100",3,"placeholder","formControl"]],template:function(r,d){1&r&&(t.\u0275\u0275elementStart(0,"div",0)(1,"a",1),t.\u0275\u0275listener("click",function(){return d.closePopup()}),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(2,"h4",2),t.\u0275\u0275text(3),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(4,"div",3)(5,"div"),t.\u0275\u0275text(6),t.\u0275\u0275pipe(7,"translate"),t.\u0275\u0275elementStart(8,"span",4),t.\u0275\u0275text(9),t.\u0275\u0275pipe(10,"translate"),t.\u0275\u0275elementEnd(),t.\u0275\u0275text(11),t.\u0275\u0275elementStart(12,"span",4),t.\u0275\u0275text(13),t.\u0275\u0275pipe(14,"translate"),t.\u0275\u0275elementEnd()()(),t.\u0275\u0275template(15,V,2,1,"div",5),t.\u0275\u0275template(16,X,10,11,"div",6),t.\u0275\u0275elementStart(17,"div",7)(18,"button",8),t.\u0275\u0275listener("click",function(){return d.closePopup()}),t.\u0275\u0275text(19),t.\u0275\u0275pipe(20,"translate"),t.\u0275\u0275elementEnd(),t.\u0275\u0275elementStart(21,"button",9),t.\u0275\u0275listener("click",function(){return d.exportData()}),t.\u0275\u0275text(22),t.\u0275\u0275pipe(23,"translate"),t.\u0275\u0275elementEnd()()()),2&r&&(t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate1("Are you sure you want to export these ",d.getExportLabel(null==d.payload?null:d.payload.path),"?"),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(7,10,"GLOBAL.clicking-on")),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(10,12,"GLOBAL.confirm")),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate1(" will enable the process of exporting the ",d.getExportLabel(null==d.payload?null:d.payload.path)," in an excel format. You can track the status of the export in "),t.\u0275\u0275advance(2),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(14,14,"GLOBAL.export-tracker")),t.\u0275\u0275advance(2),t.\u0275\u0275property("ngIf","lead/new/all"==d.payload.path),t.\u0275\u0275advance(1),t.\u0275\u0275property("ngIf","lead/new/all"==d.payload.path&&!0===d.notesType.value),t.\u0275\u0275advance(3),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(20,16,"BUTTONS.cancel")),t.\u0275\u0275advance(2),t.\u0275\u0275property("disabled",d.notesCount.invalid&&!0===d.notesType.value&&"lead/new/all"==d.payload.path),t.\u0275\u0275advance(1),t.\u0275\u0275textInterpolate(t.\u0275\u0275pipeBind1(23,18,"BUTTONS.confirm")))},dependencies:[n.Fj,n.wV,n._,n.JJ,n.qQ,n.Fd,k.sg,k.O5,j.z,n.oH,z.X$],encapsulation:2}),u})()}}]);