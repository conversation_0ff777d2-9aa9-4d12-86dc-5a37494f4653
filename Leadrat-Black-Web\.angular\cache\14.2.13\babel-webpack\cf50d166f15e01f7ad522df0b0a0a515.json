{"ast": null, "code": "import { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function withLatestFrom(...args) {\n  return source => {\n    let project;\n\n    if (typeof args[args.length - 1] === 'function') {\n      project = args.pop();\n    }\n\n    const observables = args;\n    return source.lift(new WithLatestFromOperator(observables, project));\n  };\n}\n\nclass WithLatestFromOperator {\n  constructor(observables, project) {\n    this.observables = observables;\n    this.project = project;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WithLatestFromSubscriber(subscriber, this.observables, this.project));\n  }\n\n}\n\nclass WithLatestFromSubscriber extends OuterSubscriber {\n  constructor(destination, observables, project) {\n    super(destination);\n    this.observables = observables;\n    this.project = project;\n    this.toRespond = [];\n    const len = observables.length;\n    this.values = new Array(len);\n\n    for (let i = 0; i < len; i++) {\n      this.toRespond.push(i);\n    }\n\n    for (let i = 0; i < len; i++) {\n      let observable = observables[i];\n      this.add(subscribeToResult(this, observable, undefined, i));\n    }\n  }\n\n  notifyNext(_outerValue, innerValue, outerIndex) {\n    this.values[outerIndex] = innerValue;\n    const toRespond = this.toRespond;\n\n    if (toRespond.length > 0) {\n      const found = toRespond.indexOf(outerIndex);\n\n      if (found !== -1) {\n        toRespond.splice(found, 1);\n      }\n    }\n  }\n\n  notifyComplete() {}\n\n  _next(value) {\n    if (this.toRespond.length === 0) {\n      const args = [value, ...this.values];\n\n      if (this.project) {\n        this._tryProject(args);\n      } else {\n        this.destination.next(args);\n      }\n    }\n  }\n\n  _tryProject(args) {\n    let result;\n\n    try {\n      result = this.project.apply(this, args);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n} //# sourceMappingURL=withLatestFrom.js.map", "map": null, "metadata": {}, "sourceType": "module"}