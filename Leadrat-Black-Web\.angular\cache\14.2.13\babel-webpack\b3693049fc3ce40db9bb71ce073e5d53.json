{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function isEmpty() {\n  return source => source.lift(new IsEmptyOperator());\n}\n\nclass IsEmptyOperator {\n  call(observer, source) {\n    return source.subscribe(new IsEmptySubscriber(observer));\n  }\n\n}\n\nclass IsEmptySubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n\n  notifyComplete(isEmpty) {\n    const destination = this.destination;\n    destination.next(isEmpty);\n    destination.complete();\n  }\n\n  _next(value) {\n    this.notifyComplete(false);\n  }\n\n  _complete() {\n    this.notifyComplete(true);\n  }\n\n} //# sourceMappingURL=isEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module"}