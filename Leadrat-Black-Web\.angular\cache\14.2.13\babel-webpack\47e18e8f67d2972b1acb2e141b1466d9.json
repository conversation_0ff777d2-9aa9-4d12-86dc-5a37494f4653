{"ast": null, "code": "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nexport default setToArray;", "map": null, "metadata": {}, "sourceType": "module"}