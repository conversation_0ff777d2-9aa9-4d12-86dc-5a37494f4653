"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[923],{59543:(Se,_e,d)=>{d.d(_e,{p:()=>Te});var e=d(5e3),k=d(82722),z=d(2976),$=d(85768),O=d(51515),E=d(69294),de=d(40553),me=d(68140),ve=d(80703),G=d(38827),Y=d(65620),he=d(71511),H=d(69808),J=d(24376),ge=d(15662),fe=d(17447),se=d(93075),pe=d(23713),Q=d(18995);function xe(v,V){if(1&v){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const _=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(_.openUnassignModal())}),e.\u0275\u0275element(1,"span",9),e.\u0275\u0275elementEnd()}}function ye(v,V){if(1&v&&(e.\u0275\u0275elementStart(0,"span",10),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&v){const n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",(null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Call)>=100||(null==n.params||null==n.params.data||null==n.params.data.contactRecords?null:n.params.data.contactRecords.Call)>=100?"99+":null!=n.params&&null!=n.params.data&&n.params.data.lead?null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Call:null==n.params||null==n.params.data||null==n.params.data.contactRecords?null:n.params.data.contactRecords.Call,"")}}const Ce=function(v){return{"grid-blur cursor-default":v}};function Z(v,V){if(1&v){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",2),e.\u0275\u0275template(1,xe,2,0,"div",3),e.\u0275\u0275element(2,"share-external",4),e.\u0275\u0275elementStart(3,"div",5),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const _=e.\u0275\u0275nextContext(),P=e.\u0275\u0275reference(2);return e.\u0275\u0275resetView(_.checkToCall(P))}),e.\u0275\u0275element(4,"span",6),e.\u0275\u0275template(5,ye,2,1,"span",7),e.\u0275\u0275elementEnd()()}if(2&v){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.params.data.assignTo==n.EMPTY_GUID),e.\u0275\u0275advance(1),e.\u0275\u0275property("data",null==n.params||null==n.params.value?null:n.params.value[0])("key","ProjectsUnit"===(null==n.params||null==n.params.value||null==n.params.value[4]?null:n.params.value[4][0])?"share-project-units":"Projects"===(null==n.params||null==n.params.value||null==n.params.value[4]?null:n.params.value[4][0])?"share-matching-lead":"share-property")("mailCount",null!=n.params&&null!=n.params.data&&n.params.data.lead?null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Email:null==n.params||null==n.params.data||null==n.params.data.contactRecords?null:n.params.data.contactRecords.Email)("whatsAppCount",null!=n.params&&null!=n.params.data&&n.params.data.lead?null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.WhatsApp:null==n.params||null==n.params.data||null==n.params.data.contactRecords?null:n.params.data.contactRecords.WhatsApp)("contactPhone",n.params.value[1])("contactMail",null==n.params.data||null==n.params.data.lead?null:n.params.data.lead.email)("closeModal",null==n.params||null==n.params.value?null:n.params.value[3])("leadData",null!=n.params&&null!=n.params.data&&n.params.data.lead?null==n.params||null==n.params.data?null:n.params.data.lead:null==n.params?null:n.params.data),e.\u0275\u0275advance(1),e.\u0275\u0275property("title",(null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Call)>0?"Call: "+(null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Call):"Call")("ngClass",e.\u0275\u0275pureFunction1(12,Ce,n.params.data.assignTo==n.EMPTY_GUID)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null!=n.params&&null!=n.params.data&&n.params.data.lead?null==n.params||null==n.params.data||null==n.params.data.lead||null==n.params.data.lead.contactRecords?null:n.params.data.lead.contactRecords.Call:null==n.params||null==n.params.data||null==n.params.data.contactRecords?null:n.params.data.contactRecords.Call)}}function Ee(v,V){if(1&v&&(e.\u0275\u0275elementStart(0,"ng-option",23),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&v){const n=V.$implicit;e.\u0275\u0275property("value",n.phoneNumber),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",n.name,"")}}function Pe(v,V){if(1&v){const n=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"a",11),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.closeModal())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(1,"div",12)(2,"h4",13),e.\u0275\u0275element(3,"ng-lottie",14),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",15),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"h4",16),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div"),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",17)(16,"ng-select",18),e.\u0275\u0275listener("ngModelChange",function(_){e.\u0275\u0275restoreView(n);const P=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(P.assignedToUserId=_)}),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275template(18,Ee,2,2,"ng-option",19),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",20)(20,"button",21),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.closeModal())}),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"button",22),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(n);const _=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(_.ivrClickToCall(_.assignedToUserId))}),e.\u0275\u0275text(24),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275elementEnd()()()}if(2&v){const n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275property("options",n.warn),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(5,11,"LEADS.agent-not-registered")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,13,"LEADS.register-agent-ivr")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,15,"GLOBAL.or")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(14,17,"LEADS.select-agent")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(17,19,"GLOBAL.select-agent")),e.\u0275\u0275property("virtualScroll",!0)("ngModel",n.assignedToUserId),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",n.agentList),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(22,21,"BUTTONS.cancel"),""),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(25,23,"BUTTONS.connect"),"")}}let Te=(()=>{class v{constructor(n,u,_,P){var N,K;this.modalService=n,this.modalRef=u,this.store=_,this.router=P,this.stopper=new e.EventEmitter,this.agents=!1,this.isIVREnabled=!1,this.warn={path:"assets/animations/warning.json"},this.isLoading=!0,this.globalsettingsIVR=0,this.EMPTY_GUID=z.sI9,this.userDetails=localStorage.getItem("userDetails"),this.userPhoneNo=null===(N=JSON.parse(this.userDetails))||void 0===N?void 0:N.phone_number,this.userPermissions=localStorage.getItem("userPermissions"),this.store.select(E.ab).pipe((0,k.R)(this.stopper)).subscribe(T=>{this.ivrAccountCount=T.IVR}),(null===(K=this.userPermissions)||void 0===K?void 0:K.includes("IVRCall"))&&this.ivrAccountCount&&(this.isIVREnabled=!0),this.store.select($.fN).pipe((0,k.R)(this.stopper)).subscribe(T=>{var W;this.globalsettingsIVR=null===(W=null==T?void 0:T.callSettings)||void 0===W?void 0:W.callType}),this.store.select(E.j8).pipe((0,k.R)(this.stopper)).subscribe(T=>{this.agentList=T})}agInit(n){this.params=n,this.projectData=n}checkToCall(n){var u;this.store.dispatch(new O.r2),this.store.dispatch(new O.fr),this.params.data.assignTo!==z.sI9?(this.isIVREnabled&&2===this.globalsettingsIVR&&(null===(u=this.agentList)||void 0===u?void 0:u.length)?this.checkAgent(n):this.openDialerPad(),this.modalService.hide()):this.openUnassignModal()}openUnassignModal(){this.modalRef=this.modalService.show(ve.m,{class:"modal-400 modal-dialog-centered ph-modal-unset"})}checkAgent(n){this.onInitiateCall(1),this.store.dispatch(new O.r2),this.store.select(E.j8).pipe((0,k.R)(this.stopper)).subscribe(u=>{var _;this.agentList=u,this.agentPhoneNo=Object.values(u).filter(P=>{P.phoneNumber==this.userPhoneNo&&(this.agents=!0)}),this.agents?this.ivrClickToCall(this.userPhoneNo):(null===(_=this.agentList)||void 0===_?void 0:_.length)&&0===this.modalService.getModalsCount()&&(this.modalRef=this.modalService.show(n,{class:"modal-400 modal-dialog-centered ph-modal-unset"}))})}ivrClickToCall(n){this.store.dispatch(new O.s_({destination_number:this.params.data.contactNo,agent_number:n})),this.store.dispatch(new me.lt),this.modalRef.hide()}onInitiateCall(n){var u,_,P,N,K,T,W,q,X,ee,te,ne;let ce={id:null!==(u=this.params.data)&&void 0!==u&&u.lead?null===(P=null===(_=this.params.data)||void 0===_?void 0:_.lead)||void 0===P?void 0:P.id:null===(N=this.params.data)||void 0===N?void 0:N.id,contactType:n};this.store.dispatch(new de.h$e(ce.id,ce));const Me={contactType:n,leadId:null!==(K=this.params.data)&&void 0!==K&&K.lead?null===(W=null===(T=this.params.data)||void 0===T?void 0:T.lead)||void 0===W?void 0:W.id:null===(X=null===(q=this.params)||void 0===q?void 0:q.data)||void 0===X?void 0:X.id};null!==(ne=null===(te=null===(ee=this.projectData)||void 0===ee?void 0:ee.value)||void 0===te?void 0:te[4])&&void 0!==ne&&ne[1]&&this.store.dispatch(new de.dFq(Me))}openDialerPad(){var n,u,_;location.href="tel:"+(null===(_=null===(u=null===(n=this.params)||void 0===n?void 0:n.data)||void 0===u?void 0:u.lead)||void 0===_?void 0:_.contactNo),this.onInitiateCall(1)}closeModal(){this.modalRef.hide(),this.assignedToUserId=null}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return v.\u0275fac=function(n){return new(n||v)(e.\u0275\u0275directiveInject(G.tT),e.\u0275\u0275directiveInject(G.UZ),e.\u0275\u0275directiveInject(Y.yh),e.\u0275\u0275directiveInject(he.F0))},v.\u0275cmp=e.\u0275\u0275defineComponent({type:v,selectors:[["leads-share-data"]],decls:3,vars:1,consts:[["class","align-center mt-4",4,"ngIf"],["agent",""],[1,"align-center","mt-4"],["class","bg-dark-blue icon-badge grid-blur cursor-default","id","clkMailLead","data-automate-id","clkMailLead",3,"click",4,"ngIf"],[3,"data","key","mailCount","whatsAppCount","contactPhone","contactMail","closeModal","leadData"],[1,"bg-accent-blue","icon-badge","position-relative",3,"title","ngClass","click"],[1,"icon","ic-Call","m-auto","ic-xxs"],["class","position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6",4,"ngIf"],["id","clkMailLead","data-automate-id","clkMailLead",1,"bg-dark-blue","icon-badge","grid-blur","cursor-default",3,"click"],[1,"icon","ic-mail","m-auto","ic-xxs"],[1,"position-absolute","ntop-14","text-xxs","nright-14","dot","dot-md","bg-red","mr-6"],[1,"ic-close-secondary","ic-close-modal","ip-ic-close-modal",3,"click"],[1,"p-16","fw-600","flex-center-col","mt-20"],[1,"fw-600","text-orange-800","flex-center"],["width","40px","height","40px",1,"mr-4",3,"options"],[1,"mt-20","text-mud"],[1,"my-10","fw-600"],[1,"w-100","mt-8"],["name","agent","ResizableDropdown","",3,"virtualScroll","placeholder","ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],[1,"flex-center","mt-20"],["id","btnAgentCancel","data-automate-id","btnAgentCancel",1,"btn-gray",3,"click"],[1,"btn-coal","ml-8",3,"click"],[3,"value"]],template:function(n,u){1&n&&(e.\u0275\u0275template(0,Z,6,14,"div",0),e.\u0275\u0275template(1,Pe,26,25,"ng-template",null,1,e.\u0275\u0275templateRefExtractor)),2&n&&e.\u0275\u0275property("ngIf",!u.params.data.isArchived)},dependencies:[H.mk,H.sg,H.O5,J.w9,J.jq,ge.t,fe.s,se.JJ,se.On,pe.e$,Q.X$],encapsulation:2}),v})()},57923:(Se,_e,d)=>{d.d(_e,{Y:()=>Ae});var e=d(5e3),k=d(77579),z=d(82722),$=d(2976),O=d(51420),E=d(61021),de=d(59543),me=d(85768),ve=d(40553),G=d(78990),Y=d(61357),he=d(84766),H=d(65620),J=d(38827),ge=d(48315),fe=d(71511),se=d(1880),pe=d(69808),Q=d(24376),xe=d(11970),ye=d(17447),Ce=d(63172),Z=d(93075),Ee=d(18995);function Pe(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1," Project Title: "),e.\u0275\u0275elementContainerEnd())}function Te(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1," Project Unit: "),e.\u0275\u0275elementContainerEnd())}function v(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementContainerEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(2,1,"PROPERTY.PROPERTY_DETAIL.title"),": "))}function V(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275template(2,Pe,2,0,"ng-container",10),e.\u0275\u0275template(3,Te,2,0,"ng-container",10),e.\u0275\u0275template(4,v,3,3,"ng-container",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",39),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.isProject),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isProjectUnit),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isProject&&!t.isProjectUnit),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.isProject||t.isProjectUnit?null==t.params?null:t.params.name:t.params.title)}}function n(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1," Unit Type: "),e.\u0275\u0275elementContainerEnd())}function u(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementContainerEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",e.\u0275\u0275pipeBind1(2,2,"LABEL.property")," ",e.\u0275\u0275pipeBind1(3,4,"LABEL.type"),": "))}function _(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275template(2,n,2,0,"ng-container",10),e.\u0275\u0275template(3,u,4,6,"ng-container",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.isProjectUnit),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isProject&&!t.isProjectUnit),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.isProjectUnit?null==t.params||null==t.params.unitType?null:t.params.unitType.displayName:null==t.params?null:t.params.propertyType.displayName)}}function P(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1," Unit Sub-type "),e.\u0275\u0275elementContainerEnd())}function N(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementContainerEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(2,1,"PROPERTY.sub-type"),": "))}function K(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275template(2,P,2,0,"ng-container",10),e.\u0275\u0275template(3,N,3,3,"ng-container",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.isProjectUnit),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isProject&&!t.isProjectUnit),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.isProjectUnit?null==t.params||null==t.params.unitType||null==t.params.unitType.childType?null:t.params.unitType.childType.displayName:null==t.params.propertyType||null==t.params.propertyType.childType?null:t.params.propertyType.childType.displayName)}}function T(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.isListing?"BR":e.\u0275\u0275pipeBind1(3,2,"PROPERTY.bhk"),": "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.params.noOfBHK)}}function W(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",39),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.isListing?"BR Type":e.\u0275\u0275pipeBind1(3,3,"PROPERTY.bhk")," ",e.\u0275\u0275pipeBind1(4,5,"LABEL.type"),":"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t.BHKType[t.params.bhkType])}}function q(i,p){if(1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,T,6,4,"div",9),e.\u0275\u0275template(2,W,7,7,"div",9),e.\u0275\u0275elementContainerEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.noOfBHK),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.bhkType)}}function X(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,2,"GLOBAL.looking-to")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.EnquiryType[t.params.enquiredFor])}}function ee(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(3,2,"LEAD_FORM.budget"),":"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.params.budget)}}function te(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,2,"GLOBAL.price")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.params.price)}}function ne(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",39),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(3,2,"PROPERTY.PROPERTY_DETAIL.size"),":"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.params.area)}}function ce(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",37)(1,"div",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",40),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(3,2,"LOCATION.location"),":"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.params.location)}}function Me(i,p){1&i&&(e.\u0275\u0275elementStart(0,"span",22),e.\u0275\u0275text(1," Matching Radius"),e.\u0275\u0275elementEnd())}function Le(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",41),e.\u0275\u0275element(1,"input",42)(2,"span",43),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&i){const t=p.item,a=p.item$,l=p.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",l,"")("automate-id","item-",l,""),e.\u0275\u0275property("checked",a.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.label)}}function De(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"ng-option",44),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=p.$implicit;e.\u0275\u0275property("value",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t,"")}}function Re(i,p){1&i&&e.\u0275\u0275element(0,"div",45)}function be(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",46)(1,"ag-grid-angular",47,48),e.\u0275\u0275listener("gridReady",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(o.onGridReady(l))}),e.\u0275\u0275elementEnd()()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("gridOptions",t.gridOptions)("defaultColDef",t.gridOptions.defaultColDef)("pagination",!0)("paginationPageSize",t.pageSize)("rowData",t.rowData)("suppressPaginationPanel",!0)}}function Be(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",49)(1,"div",50),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"pagination",51),e.\u0275\u0275listener("pageChange",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(o.onPageChange(l))}),e.\u0275\u0275elementEnd()()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate7("",e.\u0275\u0275pipeBind1(3,11,"GLOBAL.showing")," ",t.currOffset*t.pageSize+1," ",e.\u0275\u0275pipeBind1(4,13,"GLOBAL.to-small")," ",t.currOffset*t.pageSize+t.pageSize>t.totalMatchCount?t.totalMatchCount:t.currOffset*t.pageSize+t.pageSize," ",e.\u0275\u0275pipeBind1(5,15,"GLOBAL.of-small")," ",t.totalMatchCount," ",e.\u0275\u0275pipeBind1(6,17,"GLOBAL.entries-small"),""),e.\u0275\u0275advance(5),e.\u0275\u0275property("offset",t.currOffset)("limit",1)("range",1)("size",t.getPages(t.totalMatchCount,t.pageSize))}}function we(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"h3",5),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",6),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.modalRef.hide())}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"div",7)(7,"div",8),e.\u0275\u0275template(8,V,7,4,"div",9),e.\u0275\u0275template(9,_,6,3,"div",9),e.\u0275\u0275template(10,K,6,3,"div",9),e.\u0275\u0275template(11,q,3,2,"ng-container",10),e.\u0275\u0275template(12,X,6,4,"div",9),e.\u0275\u0275template(13,ee,6,4,"div",9),e.\u0275\u0275template(14,te,6,4,"div",9),e.\u0275\u0275template(15,ne,6,4,"div",9),e.\u0275\u0275template(16,ce,6,4,"div",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"div",11)(18,"div",12),e.\u0275\u0275elementContainerStart(19),e.\u0275\u0275element(20,"span",13),e.\u0275\u0275elementStart(21,"input",14),e.\u0275\u0275listener("keydown.enter",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.searchTermSubject.next(l.target.value))})("input",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.isEmptyInput(l))})("ngModelChange",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.searchTerm=l)}),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"small",15),e.\u0275\u0275text(24),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd(),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"div",16)(27,"div",17),e.\u0275\u0275template(28,Me,2,0,"span",18),e.\u0275\u0275elementStart(29,"div",19)(30,"ng-select",20),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.filterFunction())})("ngModelChange",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.matchingRadius=l)}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(31,"div",21)(32,"span",22),e.\u0275\u0275text(33),e.\u0275\u0275pipe(34,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(35,"div",23)(36,"ng-select",24),e.\u0275\u0275listener("change",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.onColumnsSelected(l))}),e.\u0275\u0275template(37,Le,4,4,"ng-template",25),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(38,"div",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onSetColumnDefault())}),e.\u0275\u0275elementStart(39,"span",27),e.\u0275\u0275text(40),e.\u0275\u0275pipe(41,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(42,"span",28),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(43,"div",29)(44,"span",30)(45,"span",31),e.\u0275\u0275text(46),e.\u0275\u0275pipe(47,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(48),e.\u0275\u0275pipe(49,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(50,"ng-select",32),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.assignCount())})("ngModelChange",function(l){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.selectedPageSize=l)}),e.\u0275\u0275template(51,De,2,2,"ng-option",33),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(52,Re,1,0,"div",34),e.\u0275\u0275template(53,be,3,6,"div",35),e.\u0275\u0275template(54,Be,8,19,"div",36),e.\u0275\u0275elementEnd()()}if(2&i){const t=e.\u0275\u0275nextContext(),a=e.\u0275\u0275reference(6);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,38,"PROPERTY.matching-leads")),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngIf",t.params.title||t.params.name),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isProjectUnit?null==t.params||null==t.params.unitType?null:t.params.unitType.displayName:null==t.params||null==t.params.propertyType?null:t.params.propertyType.displayName),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isProjectUnit?null==t.params||null==t.params.unitType||null==t.params.unitType.childType?null:t.params.unitType.childType.displayName:null==t.params.propertyType||null==t.params.propertyType.childType?null:t.params.propertyType.childType.displayName),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isProjectUnit?"Residential"==(null==t.params||null==t.params.unitType?null:t.params.unitType.displayName)&&"Plot"!==(null==t.params||null==t.params.unitType||null==t.params.unitType.childType?null:t.params.unitType.childType.displayName):"Residential"==(null==t.params||null==t.params.propertyType?null:t.params.propertyType.displayName)&&"Plot"!==(null==t.params||null==t.params.propertyType||null==t.params.propertyType.childType?null:t.params.propertyType.childType.displayName)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.enquiredFor),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.budget),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.price),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.area),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.params.location),e.\u0275\u0275advance(5),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(22,40,"GLOBAL.type-to-search")),e.\u0275\u0275property("ngModel",t.searchTerm),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("(",e.\u0275\u0275pipeBind1(25,42,"LEADS.lead-search-prompt"),")"),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",!t.matchingRadius),e.\u0275\u0275advance(2),e.\u0275\u0275property("virtualScroll",!0)("items",t.matchingRadiusList)("clearable",!0)("ngModel",t.matchingRadius)("searchable",!1),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(34,44,"BUTTONS.manage-columns"),""),e.\u0275\u0275advance(3),e.\u0275\u0275property("virtualScroll",!0)("items",t.columns)("multiple",!0)("searchable",!1)("closeOnSelect",!1)("ngModel",t.defaultColumns),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(41,46,"GLOBAL.default")),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(47,48,"GLOBAL.show"),""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(49,50,"GLOBAL.entries"),""),e.\u0275\u0275advance(2),e.\u0275\u0275property("virtualScroll",!0)("placeholder",t.pageSize)("ngModel",t.selectedPageSize)("searchable",!1),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.showEntriesSize),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.rowData.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.rowData.length)("ngIfElse",a),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.totalMatchCount)}}function je(i,p){1&i&&(e.\u0275\u0275elementStart(0,"div",52),e.\u0275\u0275element(1,"img",53),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementStart(3,"div",54),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(2,2,"GLOBAL.no-data-found")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(5,4,"PROFILE.no-data-found")))}let Ae=(()=>{class i extends he.y{constructor(t,a,l,o,r,s){super(),this._store=t,this.modalRef=a,this.modalService=l,this.gridOptionsService=o,this.router=r,this.trackingService=s,this.stopper=new e.EventEmitter,this.searchTermSubject=new k.x,this.rowData=[],this.pageSize=$.IV1,this.showEntriesSize=$.gv9,this.filtered_list=[],this.currOffset=0,this.filtersPayload={path:"property/matchingleads",id:null,search:null},this.getPages=E.UQ,this.BHKType=O.uQ,this.EnquiryType=O.w5,this.defaultCurrency="",this.matchingRadiusList=$.jP9,this.isListing=window.location.pathname.includes("listing"),this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.rowData=this.rowData}agInit(t){var a,l,o,r,s,m,c,g,f,x,y,M,h,C,I,S,L,D,R,b,B,w,j,A,U;"Projects"===(null===(a=t.value)||void 0===a?void 0:a[0])?(this.isProject=!0,this.isProjectUnit=!1):"ProjectsUnit"===(null===(l=t.value)||void 0===l?void 0:l[0])?(this.isProjectUnit=!0,this.isProject=!1):(this.isProject=!1,this.isProjectUnit=!1),this.isListing=window.location.pathname.includes("listing"),this.paramsValue=t,this.params=t,this.params=Object.assign(Object.assign({},null===(o=this.params)||void 0===o?void 0:o.data),{budget:null!==(m=null===(s=null===(r=this.params)||void 0===r?void 0:r.data)||void 0===s?void 0:s.monetaryInfo)&&void 0!==m&&m.expectedPrice?(0,E.dP)(null===(f=null===(g=null===(c=this.params)||void 0===c?void 0:c.data)||void 0===g?void 0:g.monetaryInfo)||void 0===f?void 0:f.expectedPrice,(null===(M=null===(y=null===(x=this.params)||void 0===x?void 0:x.data)||void 0===y?void 0:y.monetaryInfo)||void 0===M?void 0:M.currency)||this.defaultCurrency):null,price:null!==(C=null===(h=this.params)||void 0===h?void 0:h.data)&&void 0!==C&&C.price?(0,E.dP)(null===(S=null===(I=this.params)||void 0===I?void 0:I.data)||void 0===S?void 0:S.price,(null===(D=null===(L=this.params)||void 0===L?void 0:L.data)||void 0===D?void 0:D.currency)||this.defaultCurrency):null,area:null!==(R=this.params.data)&&void 0!==R&&R.dimension?(null===(B=null===(b=this.params.data)||void 0===b?void 0:b.dimension)||void 0===B?void 0:B.area)+" "+(null===(j=null===(w=this.params.data)||void 0===w?void 0:w.dimension)||void 0===j?void 0:j.unit):"",location:(0,E.Eh)(null===(U=null===(A=this.params)||void 0===A?void 0:A.data)||void 0===U?void 0:U.address)}),this.searchTermSubject.subscribe(()=>{this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:1}),this.filterFunction()}),this.initializeGridSettings()}get sortOrder(){var t,a;return 1==(null===(t=this.filtersPayload)||void 0===t?void 0:t["SortingCriteria.IsAscending"])?"asc":0==(null===(a=this.filtersPayload)||void 0===a?void 0:a["SortingCriteria.IsAscending"])?"desc":void 0}ngOnInit(){this._store.select(me.fN).pipe((0,z.R)(this.stopper)).subscribe(t=>{this.defaultCurrency=t.countries&&t.countries.length>0?t.countries[0].defaultCurrency:null})}initializeGridSettings(){if(this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.columnDefs=[{headerName:"Lead Name",field:"Lead Name",valueGetter:t=>{var a,l,o;return[this.isProject?null===(a=null==t?void 0:t.data)||void 0===a?void 0:a.name:null===(o=null===(l=null==t?void 0:t.data)||void 0===l?void 0:l.lead)||void 0===o?void 0:o.name]},cellRenderer:t=>`<p class="text-truncate-2 text-sm">${t.value}</p>`},{headerName:this.isProject?"Project Type":"Property Type",field:this.isProject?"projectType":"propertyType",valueGetter:t=>{var a,l,o,r,s,m,c,g,f;return this.isProject?null===(r=null===(o=null===(l=null===(a=null==t?void 0:t.data)||void 0===a?void 0:a.lead)||void 0===l?void 0:l.enquiry)||void 0===o?void 0:o.propertyType)||void 0===r?void 0:r.displayName:(null===(f=null===(g=null===(c=null===(m=null===(s=null==t?void 0:t.data)||void 0===s?void 0:s.lead)||void 0===m?void 0:m.enquiry)||void 0===c?void 0:c.propertyTypes)||void 0===g?void 0:g[0])||void 0===f?void 0:f.displayName)||""},cellRenderer:t=>{var a,l,o,r,s,m,c,g,f,x,y;let M=t.value;return this.isProject?`<p class="text-sm text-truncate-1">\n            ${[null===(r=null===(o=null===(l=null===(a=t.data)||void 0===a?void 0:a.enquiry)||void 0===l?void 0:l.propertyTypes)||void 0===o?void 0:o[0])||void 0===r?void 0:r.displayName,null===(c=null===(m=null===(s=t.data)||void 0===s?void 0:s.enquiry)||void 0===m?void 0:m.propertyTypes)||void 0===c?void 0:c.map(h=>{var C;return null===(C=null==h?void 0:h.childType)||void 0===C?void 0:C.displayName}),null!==(f=null===(g=t.data)||void 0===g?void 0:g.enquiry)&&void 0!==f&&f.bhKs?t.data.enquiry.bhKs.map(h=>(0,E.lS)(h)).join(", "):"",null!==(y=null===(x=t.data)||void 0===x?void 0:x.enquiry)&&void 0!==y&&y.bhkTypes?t.data.enquiry.bhkTypes.map(h=>O.uQ[h]).join(", "):""].filter(h=>h).join(", ")}</p>`:`<p class="text-sm text-truncate-1">${M}</p>`}},{headerName:"Budget",field:"Budget",minWidth:120,valueGetter:t=>{var a,l,o,r,s,m,c,g,f,x,y,M,h,C,I,S,L,D,R,b,B,w,j,A,U,F,ae,le,ie;return[null!==(l=null===(a=null==t?void 0:t.data)||void 0===a?void 0:a.enquiry)&&void 0!==l&&l.lowerBudget?(0,E.dP)(null===(r=null===(o=null==t?void 0:t.data)||void 0===o?void 0:o.enquiry)||void 0===r?void 0:r.lowerBudget,(null===(m=null===(s=null==t?void 0:t.data)||void 0===s?void 0:s.enquiry)||void 0===m?void 0:m.currency)||this.defaultCurrency):null!==(f=null===(g=null===(c=null==t?void 0:t.data)||void 0===c?void 0:c.lead)||void 0===g?void 0:g.enquiry)&&void 0!==f&&f.lowerBudget?(0,E.dP)(null===(M=null===(y=null===(x=null==t?void 0:t.data)||void 0===x?void 0:x.lead)||void 0===y?void 0:y.enquiry)||void 0===M?void 0:M.lowerBudget,(null===(I=null===(C=null===(h=null==t?void 0:t.data)||void 0===h?void 0:h.lead)||void 0===C?void 0:C.enquiry)||void 0===I?void 0:I.currency)||this.defaultCurrency):"",null!==(S=t.data.enquiry)&&void 0!==S&&S.upperBudget?(0,E.dP)(null===(D=null===(L=null==t?void 0:t.data)||void 0===L?void 0:L.enquiry)||void 0===D?void 0:D.upperBudget,(null===(b=null===(R=null==t?void 0:t.data)||void 0===R?void 0:R.enquiry)||void 0===b?void 0:b.currency)||this.defaultCurrency):null!==(j=null===(w=null===(B=null==t?void 0:t.data)||void 0===B?void 0:B.lead)||void 0===w?void 0:w.enquiry)&&void 0!==j&&j.upperBudget?(0,E.dP)(null===(F=null===(U=null===(A=null==t?void 0:t.data)||void 0===A?void 0:A.lead)||void 0===U?void 0:U.enquiry)||void 0===F?void 0:F.upperBudget,(null===(ie=null===(le=null===(ae=null==t?void 0:t.data)||void 0===ae?void 0:ae.lead)||void 0===le?void 0:le.enquiry)||void 0===ie?void 0:ie.currency)||this.defaultCurrency):""]},cellRenderer:t=>`<p class="text-sm text-truncate">${t.value[0]?"Min.: "+t.value[0]:""}</p> <p class="text-sm text-truncate">${t.value[1]?"Max.: "+t.value[1]:""}</p>`},{headerName:"Enquired For",field:"enquiredFor",minWidth:110,valueGetter:t=>{var a,l,o,r,s;const m=this.isProject?null===(l=null===(a=null==t?void 0:t.data)||void 0===a?void 0:a.enquiry)||void 0===l?void 0:l.enquiryTypes:null===(s=null===(r=null===(o=null==t?void 0:t.data)||void 0===o?void 0:o.lead)||void 0===r?void 0:r.enquiry)||void 0===s?void 0:s.enquiryTypes;return m?m.map(c=>"None"===O.w5[c]?"":O.w5[c]):[""]},cellRenderer:t=>`<p>${t.value}</p>`},{headerName:"Enquired Location",field:"Enquired Location",valueGetter:t=>{var a,l,o,r,s;const m=this.isProject?null===(l=null===(a=null==t?void 0:t.data)||void 0===a?void 0:a.enquiry)||void 0===l?void 0:l.addresses:null===(s=null===(r=null===(o=null==t?void 0:t.data)||void 0===o?void 0:o.lead)||void 0===r?void 0:r.enquiry)||void 0===s?void 0:s.addresses;return m?m.map(c=>(0,E.Eh)(c)).join("; "):""},cellRenderer:t=>`<p class="text-sm text-truncate" title='${null==t?void 0:t.value}'>${null==t?void 0:t.value}</p>`,cellClass:"pe-none"},{headerName:"Actions",menuTabs:[],filter:!1,maxWidth:110,minWidth:110,valueGetter:t=>{var a,l,o,r;return[this.isProject?null===(l=null===(a=this.paramsValue)||void 0===a?void 0:a.value)||void 0===l?void 0:l[1]:this.params,null===(o=t.data)||void 0===o?void 0:o.contactNo,null===(r=t.data)||void 0===r?void 0:r.email,()=>{var s;null===(s=this.modalRef)||void 0===s||s.hide()},this.paramsValue.value]},cellRenderer:de.p}],!this.isProject){const t=this.gridOptions.columnDefs.findIndex(r=>"Enquired Location"===r.headerName),a={headerName:"No. Of Match Fields",field:"numberOfMatchField",hide:!0,valueGetter:r=>`${r.data.noOfFieldsMatched}/${r.data.totalNoOfFields}`,cellRenderer:r=>`<p class="text-sm text-truncate">${r.value}</p>`};-1!==t?this.gridOptions.columnDefs.splice(t+1,0,a):this.gridOptions.columnDefs.push(a);const l=this.gridOptions.columnDefs.findIndex(r=>"No. Of Match Fields"===r.headerName),o={headerName:"Percentage Of Match Fields",field:"percentageOfMatchField",hide:!0,valueGetter:r=>r.data.percentageOfFieldsMatched||"",cellRenderer:r=>`<p class="text-sm text-truncate">${r.value}</p>`};-1!==l?this.gridOptions.columnDefs.splice(l+1,0,o):this.gridOptions.columnDefs.push(o)}this.gridOptions.context={componentParent:this}}onGridReady(t){var a,l;this.gridApi=t.api,this.gridColumnApi=t.columnApi,this.columns=this.gridColumnApi.getColumns(),this.columns=this.columns.map(r=>({label:r.getColDef().headerName,value:r})),this.columns=this.columns.slice(2,this.columns.length-1).sort((r,s)=>null==r?void 0:r.label.localeCompare(null==s?void 0:s.label)),this.defaultColumns=this.columns.filter(r=>!0!==r.value.getColDef().hide),this.onColumnMoved&&(this.gridOptions.onColumnMoved=this.onColumnMoved);let o=null===(a=localStorage.getItem("machingPropertyColumn"))||void 0===a?void 0:a.split(",").slice(0,3);if(null!=o&&o.length){let r=null===(l=this.columns)||void 0===l?void 0:l.filter(s=>null==o?void 0:o.includes(s.label));this.defaultColumns=r,this.onColumnsSelected(r)}this.customizeHeader()}customizeHeader(){var t;const a=null===(t=this.gridApi)||void 0===t?void 0:t.getColumnDefs(),l=a.find(o=>{var r;return o.colId===(null===(r=this.filtersPayload)||void 0===r?void 0:r["SortingCriteria.ColumnName"])});l&&(l.sort=this.sortOrder,this.gridApi.setColumnDefs(a))}onColumnMoved(t){var a,l,o=JSON.stringify(null===(l=null===(a=null==t?void 0:t.columnApi)||void 0===a?void 0:a.getColumnState())||void 0===l?void 0:l.map(r=>Object.assign(Object.assign({},r),{sort:null})));localStorage.setItem("machingPropertyColumn",o)}onColumnsSelected(t){var a,l,o;let r=null==t?void 0:t.map(c=>c.label);localStorage.setItem("machingPropertyColumn",null==r?void 0:r.toString()),t||(t=this.defaultColumns);const s=null==t?void 0:t.map(c=>c.value);null===(a=this.gridColumnApi)||void 0===a||a.setColumnsVisible(s,!0);const m=null===(l=this.columns)||void 0===l?void 0:l.filter(c=>!s.includes(c.value));null===(o=this.gridColumnApi)||void 0===o||o.setColumnsVisible(m.map(c=>c.value),!1)}getPathBasedOnParamsValue(){var t;const a=null===(t=this.paramsValue.value)||void 0===t?void 0:t[0];return"Properties"===a?this.isListing?"property/listing/matchingleads":"property/matchingleads":"ProjectsUnit"===a?"project/matchingleads":"project/matchingleadsbyproject"}openMatchLeadsModal(t){var a,l;"Properties"===(null===(a=this.paramsValue.value)||void 0===a?void 0:a[0])?this.trackingService.trackFeature("Web.Property.Button.MatchingLead.Click"):this.trackingService.trackFeature("Web.Project.Matching.Lead.Click");const o=this.getPathBasedOnParamsValue();this.filtersPayload={id:null===(l=this.params)||void 0===l?void 0:l.id,path:o,pageNumber:1,pageSize:10},this._store.dispatch(new G.K1(this.filtersPayload)),this._store.select(Y.Fy).pipe((0,z.R)(this.stopper)).subscribe(r=>{this.rowData=r.items||[],this.totalMatchCount=r.totalCount}),this.modalRef=this.modalService.show(t,{class:"right-modal modal-1000 tb-modal-unset"})}filterFunction(){const t=this.getPathBasedOnParamsValue();this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{path:t,search:this.searchTerm,RadiusInKms:this.matchingRadius?this.matchingRadius:null}),this._store.dispatch(new G.K1(this.filtersPayload)),this._store.select(Y.Fy).pipe((0,z.R)(this.stopper)).subscribe(a=>{this.rowData=a.items||[],this.totalMatchCount=a.totalCount}),this.currOffset=0}assignCount(t){var a;this.pageSize=this.selectedPageSize,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageSize:this.pageSize,pageNumber:1}),this.filterFunction(),this.gridOptions.paginationPageSize=this.pageSize,null===(a=this.gridOptions.api)||void 0===a||a.paginationSetPageSize(this.selectedPageSize),this.gridApi.setRowData([]),this.gridApi.applyTransaction(this.filtered_list.length>0?{add:this.filtered_list}:{add:this.rowData}),this.currOffset=0}onPageChange(t){this.currOffset=t;const a=this.getPathBasedOnParamsValue();this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{path:a,pageSize:this.pageSize,pageNumber:t+1}),this._store.dispatch(new G.K1(this.filtersPayload)),this._store.select(Y.Fy).pipe((0,z.R)(this.stopper)).subscribe(l=>{this.rowData=l.items||[],this.totalMatchCount=l.totalCount})}onSetColumnDefault(){this.defaultColumns=this.columns.filter(t=>1==t.value.getColDef().hide),this.onColumnsSelected(this.defaultColumns)}getMatchingLead(){var t,a,l,o,r,s,m,c,g,f,x,y,M,h,C,I,S,L,D,R,b,B,w,j,A,U,F;const ie=(null===(a=null===(t=this.params)||void 0===t?void 0:t.monetaryInfo)||void 0===a?void 0:a.expectedPrice)||0,Ue=(null===(o=null===(l=this.params)||void 0===l?void 0:l.monetaryInfo)||void 0===o?void 0:o.expectedPrice)||0,Ve=(re=ie,Math.floor(re-.2*re)),Ne=((re,Ie)=>Math.ceil(re+.2*re))(Ue),Oe=O.w5[null===(r=this.params)||void 0===r?void 0:r.enquiredFor];var re;let ue=[];"Rent"===Oe?ue=["Rent"]:"Buy"===Oe?ue=["Sale"]:"Sale"===Oe&&(ue=["Buy"]);let oe={PropertyType:[null===(m=null===(s=this.params)||void 0===s?void 0:s.propertyType)||void 0===m?void 0:m.id],PropertySubType:[null===(f=null===(g=null===(c=this.params)||void 0===c?void 0:c.propertyType)||void 0===g?void 0:g.childType)||void 0===f?void 0:f.id],NoOfBHKs:[null===(x=this.params)||void 0===x?void 0:x.noOfBHK.toString()],States:[null===(M=null===(y=this.params)||void 0===y?void 0:y.address)||void 0===M?void 0:M.state],enquiredFor:ue,MinBudget:Ve,MaxBudget:Ne,pageNumber:1,pageSize:10,Currency:null===(C=null===(h=this.params)||void 0===h?void 0:h.monetaryInfo)||void 0===C?void 0:C.currency};(null===(S=null===(I=this.params)||void 0===I?void 0:I.address)||void 0===S?void 0:S.latitude)&&(null===(D=null===(L=this.params)||void 0===L?void 0:L.address)||void 0===D?void 0:D.longitude)&&(oe.Longitude=null===(b=null===(R=this.params)||void 0===R?void 0:R.address)||void 0===b?void 0:b.longitude,oe.Latitude=null===(w=null===(B=this.params)||void 0===B?void 0:B.address)||void 0===w?void 0:w.latitude,oe.RadiusInKm=10),("Commercial"===(null===(A=null===(j=this.params)||void 0===j?void 0:j.propertyType)||void 0===A?void 0:A.displayName)||"Agricultural"===(null===(F=null===(U=this.params)||void 0===U?void 0:U.propertyType)||void 0===F?void 0:F.displayName))&&(oe.NoOfBHKs=null),this._store.dispatch(new ve.GZG(oe)),this.router.navigate(["leads/manage-leads"]),this.modalRef.hide()}isEmptyInput(t){(""===this.searchTerm||null===this.searchTerm)&&this.searchTermSubject.next("")}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(H.yh),e.\u0275\u0275directiveInject(J.UZ),e.\u0275\u0275directiveInject(J.tT),e.\u0275\u0275directiveInject(ge.t),e.\u0275\u0275directiveInject(fe.F0),e.\u0275\u0275directiveInject(se.e))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["matching-leads"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:7,vars:3,consts:[[1,"text-accent-green","border-bottom-green",3,"click"],["matchingLeadsModal",""],["noData",""],[1,"h-100vh","text-coal"],[1,"flex-between","bg-coal","w-100","px-16","py-12","text-white"],[1,"fw-semi-bold"],[1,"icon","ic-close-secondary","ic-large","cursor-pointer",3,"click"],[1,"px-16","pb-20","h-100-90","scrollbar"],[1,"flex-wrap","w-100","align-center","text-sm","fw-600","text-dark-gray-350"],["class","w-25 tb-w-33 ip-w-50 ph-w-100 d-flex pt-20 pl-8",4,"ngIf"],[4,"ngIf"],[1,"d-flex","ip-flex-col","bg-white","w-100","border-gray","mt-20"],[1,"align-center","px-10","flex-grow-1","border-end","no-validation","py-12","ip-br-0"],[1,"search","icon","ic-search","ic-sm","ic-slate-90","mr-12","ph-mr-4"],["type","text",1,"border-0","outline-0","w-100",3,"placeholder","ngModel","keydown.enter","input","ngModelChange"],[1,"text-muted","text-nowrap"],[1,"d-flex","ip-br-top"],[1,"align-center","position-relative","cursor-pointer","d-flex","border-end","bg-white"],["class","position-absolute left-15 z-index-2 fw-600 text-sm",4,"ngIf"],[1,"w-130","no-validation"],["bindLabel","label","bindValue","value",1,"bg-white","border-0","no-validation",3,"virtualScroll","items","clearable","ngModel","searchable","change","ngModelChange"],[1,"align-center","position-relative","cursor-pointer","d-flex","border-end"],[1,"position-absolute","left-15","z-index-2","fw-600","text-sm"],[1,"show-hide-gray","w-140"],["ResizableDropdown","",1,"bg-white",3,"virtualScroll","items","multiple","searchable","closeOnSelect","ngModel","change"],["ng-option-tmp",""],[1,"bg-coal","text-white","px-10","py-12","ip-w-30px","align-center","cursor-pointer",3,"click"],[1,"ip-d-none"],[1,"ic-refresh","d-none","ip-d-block"],[1,"show-dropdown-white","align-center","position-relative"],[1,"fw-600","position-absolute","left-5","z-index-2"],[1,"tb-d-none"],["bindValue","id","ResizableDropdown","",1,"w-150","tb-w-120px",3,"virtualScroll","placeholder","ngModel","searchable","change","ngModelChange"],["name","showEntriesSize",3,"value",4,"ngFor","ngForOf"],["class","py-4 border-left border-right",4,"ngIf"],["class","matching",4,"ngIf","ngIfElse"],["class","flex-end mt-20",4,"ngIf"],[1,"w-25","tb-w-33","ip-w-50","ph-w-100","d-flex","pt-20","pl-8"],[1,"text-gray-90","text-nowrap"],[1,"ml-10","text-truncate-1","break-all"],[1,"ml-10","text-truncate-1","break-all","ph-w-100px"],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],[1,"checkmark"],["name","showEntriesSize",3,"value"],[1,"py-4","border-left","border-right"],[1,"matching"],[1,"ag-theme-alpine",3,"gridOptions","defaultColDef","pagination","paginationPageSize","rowData","suppressPaginationPanel","gridReady"],["agGrid",""],[1,"flex-end","mt-20"],[1,"mr-10"],[3,"offset","limit","range","size","pageChange"],[1,"flex-center-col","h-100-114"],["src","assets/images/layered-cards.svg",3,"alt"],[1,"header-3","fw-600","text-center"]],template:function(t,a){if(1&t){const l=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"a",0),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const r=e.\u0275\u0275reference(4);return e.\u0275\u0275resetView(a.openMatchLeadsModal(r))}),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(3,we,55,52,"ng-template",null,1,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(5,je,6,6,"ng-template",null,2,e.\u0275\u0275templateRefExtractor)}2&t&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(2,1,"GLOBAL.match"),""))},dependencies:[pe.sg,pe.O5,Q.w9,Q.jq,Q.ir,xe.Q,ye.s,Ce.N8,Z.Fj,Z.JJ,Z.On,Ee.X$],encapsulation:2}),i})()}}]);