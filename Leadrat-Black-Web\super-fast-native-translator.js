const fs = require('fs');

/**
 * Super Fast Native Language Translator
 * Generates proper Malayalam and Bengali translations
 */
class SuperFastNativeTranslator {
    constructor() {
        this.malayalamTranslations = this.getMalayalamTranslations();
        this.bengaliTranslations = this.getBengaliTranslations();
    }

    getMalayalamTranslations() {
        return {
            // Core UI Elements
            "Settings": "ക്രമീകരണങ്ങൾ",
            "Today": "ഇന്ന്",
            "All": "എല്ലാം",
            "New": "പുതിയ",
            "Old": "പഴയ",
            "Overdue": "കാലാവധി കഴിഞ്ഞ",
            "Upcoming": "വരാനിരിക്കുന്ന",
            "Completed": "പൂർത്തിയായി",
            "Not Interested": "താൽപ്പര്യമില്ല",
            "Select": "തിരഞ്ഞെടുക്കുക",
            "Status": "നില",
            "Assigned To": "നിയോഗിച്ചത്",
            "For": "വേണ്ടി",
            "Reset": "പുനഃസജ്ജമാക്കുക",
            "Go": "പോകുക",
            "No": "ഇല്ല",
            "Yes": "അതെ",
            "Lead": "ലീഡ്",
            "Leads": "ലീഡുകൾ",
            "Name": "പേര്",
            "Show": "കാണിക്കുക",
            "Entries": "എൻട്രികൾ",
            "Update": "അപ്ഡേറ്റ്",
            "Details": "വിശദാംശങ്ങൾ",
            "Personal": "വ്യക്തിഗത",
            "Work": "ജോലി",
            "Home": "വീട്",
            "Mobile": "മൊബൈൽ",
            "Email": "ഇമെയിൽ",
            "Phone": "ഫോൺ",
            "Address": "വിലാസം",
            "City": "നഗരം",
            "State": "സംസ്ഥാനം",
            "Country": "രാജ്യം",
            "Pincode": "പിൻകോഡ്",
            "Save": "സേവ്",
            "Cancel": "റദ്ദാക്കുക",
            "Delete": "ഇല്ലാതാക്കുക",
            "Edit": "എഡിറ്റ്",
            "Add": "ചേർക്കുക",
            "Remove": "നീക്കം ചെയ്യുക",
            "Search": "തിരയുക",
            "Filter": "ഫിൽട്ടർ",
            "Sort": "ക്രമീകരിക്കുക",
            "Export": "എക്സ്പോർട്ട്",
            "Import": "ഇമ്പോർട്ട്",
            "Print": "പ്രിന്റ്",
            "Download": "ഡൗൺലോഡ്",
            "Upload": "അപ്ലോഡ്",
            "Submit": "സമർപ്പിക്കുക",
            "Close": "അടയ്ക്കുക",
            "Open": "തുറക്കുക",
            "View": "കാണുക",
            "Preview": "പ്രിവ്യൂ",
            "Next": "അടുത്തത്",
            "Previous": "മുമ്പത്തെ",
            "First": "ആദ്യത്തെ",
            "Last": "അവസാനത്തെ",
            "Page": "പേജ്",
            "Of": "ന്റെ",
            "Total": "ആകെ",
            "Loading": "ലോഡ് ചെയ്യുന്നു",
            "Please wait": "ദയവായി കാത്തിരിക്കുക",
            "Error": "പിശക്",
            "Success": "വിജയം",
            "Warning": "മുന്നറിയിപ്പ്",
            "Info": "വിവരം",
            "Confirm": "സ്ഥിരീകരിക്കുക",
            "Are you sure": "നിങ്ങൾക്ക് ഉറപ്പാണോ",
            "Login": "ലോഗിൻ",
            "Logout": "ലോഗൗട്ട്",
            "Register": "രജിസ്റ്റർ",
            "Forgot password": "പാസ്വേഡ് മറന്നോ",
            "Username": "ഉപയോക്തൃനാമം",
            "Password": "പാസ്വേഡ്",
            "Remember me": "എന്നെ ഓർക്കുക",
            "Dashboard": "ഡാഷ്ബോർഡ്",
            "Profile": "പ്രൊഫൈൽ",
            "Account": "അക്കൗണ്ട്",
            "Notifications": "അറിയിപ്പുകൾ",
            "Messages": "സന്ദേശങ്ങൾ",
            "Help": "സഹായം",
            "Support": "പിന്തുണ",
            "Contact": "ബന്ധപ്പെടുക",
            "About": "കുറിച്ച്",
            "Privacy": "സ്വകാര്യത",
            "Terms": "നിബന്ധനകൾ",
            "Language": "ഭാഷ",
            "Theme": "തീം",
            "Dark": "ഇരുണ്ട",
            "Light": "വെളിച്ചം",
            "Auto": "ഓട്ടോ",

            // Property specific
            "Property": "സ്വത്ത്",
            "Properties": "സ്വത്തുകൾ",
            "Manage Properties": "സ്വത്തുകൾ കൈകാര്യം ചെയ്യുക",
            "Property Details": "സ്വത്ത് വിവരങ്ങൾ",
            "Property Type": "സ്വത്ത് തരം",
            "Property Status": "സ്വത്ത് നില",
            "Property Value": "സ്വത്ത് മൂല്യം",
            "Property Location": "സ്വത്ത് സ്ഥാനം",
            "Property Owner": "സ്വത്ത് ഉടമ",
            "Property Manager": "സ്വത്ത് മാനേജർ",
            "Property Description": "സ്വത്ത് വിവരണം",
            "Property Images": "സ്വത്ത് ചിത്രങ്ങൾ",
            "Property Documents": "സ്വത്ത് രേഖകൾ",
            "Property History": "സ്വത്ത് ചരിത്രം",
            "Property Reports": "സ്വത്ത് റിപ്പോർട്ടുകൾ",
            "Property Search": "സ്വത്ത് തിരയൽ",
            "Property Filter": "സ്വത്ത് ഫിൽട്ടർ",
            "Property List": "സ്വത്ത് പട്ടിക",
            "Property Grid": "സ്വത്ത് ഗ്രിഡ്",
            "Property Map": "സ്വത്ത് മാപ്പ്",

            // Actions
            "Create": "സൃഷ്ടിക്കുക",
            "Read": "വായിക്കുക",
            "Write": "എഴുതുക",
            "Execute": "നടപ്പിലാക്കുക",
            "Modify": "മാറ്റുക",
            "Access": "പ്രവേശനം",
            "Permission": "അനുമതി",
            "Permissions": "അനുമതികൾ",
            "Role": "റോൾ",
            "Roles": "റോളുകൾ",
            "User": "ഉപയോക്താവ്",
            "Users": "ഉപയോക്താക്കൾ",
            "Group": "ഗ്രൂപ്പ്",
            "Groups": "ഗ്രൂപ്പുകൾ",
            "Team": "ടീം",
            "Teams": "ടീമുകൾ",
            "Organization": "സംഘടന",
            "Organizations": "സംഘടനകൾ",
            "Department": "വകുപ്പ്",
            "Departments": "വകുപ്പുകൾ",
            "Branch": "ശാഖ",
            "Branches": "ശാഖകൾ",
            "Office": "ഓഫീസ്",
            "Offices": "ഓഫീസുകൾ",

            // Time and dates
            "Date": "തീയതി",
            "Time": "സമയം",
            "DateTime": "തീയതി സമയം",
            "Created": "സൃഷ്ടിച്ചത്",
            "Modified": "മാറ്റിയത്",
            "Updated": "അപ്ഡേറ്റ് ചെയ്തത്",
            "Deleted": "ഇല്ലാതാക്കിയത്",
            "Active": "സജീവം",
            "Inactive": "നിഷ്ക്രിയം",
            "Enabled": "പ്രവർത്തനക്ഷമം",
            "Disabled": "പ്രവർത്തനരഹിതം",
            "Available": "ലഭ്യം",
            "Unavailable": "ലഭ്യമല്ല",
            "Online": "ഓൺലൈൻ",
            "Offline": "ഓഫ്‌ലൈൻ",
            "Connected": "കണക്റ്റ് ചെയ്തത്",
            "Disconnected": "വിച്ഛേദിച്ചത്",

            // Numbers and pagination
            "Page Size": "പേജ് വലുപ്പം",
            "Page Number": "പേജ് നമ്പർ",
            "Total Pages": "ആകെ പേജുകൾ",
            "Total Records": "ആകെ റെക്കോർഡുകൾ",
            "Records per page": "ഒരു പേജിൽ റെക്കോർഡുകൾ",
            "Showing": "കാണിക്കുന്നു",
            "to": "വരെ",
            "of": "ന്റെ",
            "records": "റെക്കോർഡുകൾ"
        };
    }

    getBengaliTranslations() {
        return {
            // Core UI Elements
            "Settings": "সেটিংস",
            "Today": "আজ",
            "All": "সব",
            "New": "নতুন",
            "Old": "পুরানো",
            "Overdue": "মেয়াদোত্তীর্ণ",
            "Upcoming": "আসন্ন",
            "Completed": "সম্পন্ন",
            "Not Interested": "আগ্রহী নয়",
            "Select": "নির্বাচন করুন",
            "Status": "অবস্থা",
            "Assigned To": "বরাদ্দ করা হয়েছে",
            "For": "জন্য",
            "Reset": "রিসেট",
            "Go": "যান",
            "No": "না",
            "Yes": "হ্যাঁ",
            "Lead": "লিড",
            "Leads": "লিডস",
            "Name": "নাম",
            "Show": "দেখান",
            "Entries": "এন্ট্রি",
            "Update": "আপডেট",
            "Details": "বিস্তারিত",
            "Personal": "ব্যক্তিগত",
            "Work": "কাজ",
            "Home": "বাড়ি",
            "Mobile": "মোবাইল",
            "Email": "ইমেইল",
            "Phone": "ফোন",
            "Address": "ঠিকানা",
            "City": "শহর",
            "State": "রাজ্য",
            "Country": "দেশ",
            "Pincode": "পিনকোড",
            "Save": "সংরক্ষণ",
            "Cancel": "বাতিল",
            "Delete": "মুছুন",
            "Edit": "সম্পাদনা",
            "Add": "যোগ করুন",
            "Remove": "সরান",
            "Search": "অনুসন্ধান",
            "Filter": "ফিল্টার",
            "Sort": "সাজান",
            "Export": "রপ্তানি",
            "Import": "আমদানি",
            "Print": "প্রিন্ট",
            "Download": "ডাউনলোড",
            "Upload": "আপলোড",
            "Submit": "জমা দিন",
            "Close": "বন্ধ",
            "Open": "খুলুন",
            "View": "দেখুন",
            "Preview": "প্রিভিউ",
            "Next": "পরবর্তী",
            "Previous": "পূর্ববর্তী",
            "First": "প্রথম",
            "Last": "শেষ",
            "Page": "পৃষ্ঠা",
            "Of": "এর",
            "Total": "মোট",
            "Loading": "লোড হচ্ছে",
            "Please wait": "অনুগ্রহ করে অপেক্ষা করুন",
            "Error": "ত্রুটি",
            "Success": "সফলতা",
            "Warning": "সতর্কতা",
            "Info": "তথ্য",
            "Confirm": "নিশ্চিত করুন",
            "Are you sure": "আপনি কি নিশ্চিত",
            "Login": "লগইন",
            "Logout": "লগআউট",
            "Register": "নিবন্ধন",
            "Forgot password": "পাসওয়ার্ড ভুলে গেছেন",
            "Username": "ব্যবহারকারীর নাম",
            "Password": "পাসওয়ার্ড",
            "Remember me": "আমাকে মনে রাখুন",
            "Dashboard": "ড্যাশবোর্ড",
            "Profile": "প্রোফাইল",
            "Account": "অ্যাকাউন্ট",
            "Notifications": "বিজ্ঞপ্তি",
            "Messages": "বার্তা",
            "Help": "সাহায্য",
            "Support": "সহায়তা",
            "Contact": "যোগাযোগ",
            "About": "সম্পর্কে",
            "Privacy": "গোপনীয়তা",
            "Terms": "শর্তাবলী",
            "Language": "ভাষা",
            "Theme": "থিম",
            "Dark": "অন্ধকার",
            "Light": "আলো",
            "Auto": "অটো"
        };
    }

    /**
     * Translate text to target language
     */
    translateText(text, targetLang) {
        const translations = targetLang === 'ml' ? this.malayalamTranslations : this.bengaliTranslations;

        // Try exact match first
        if (translations[text]) {
            return translations[text];
        }

        // Try case variations
        const variations = [
            text.toLowerCase(),
            text.toUpperCase(),
            text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
        ];

        for (const variation of variations) {
            if (translations[variation]) {
                return translations[variation];
            }
        }

        // Return original if no translation found
        return text;
    }

    /**
     * Recursively translate JSON object
     */
    translateObject(obj, targetLang) {
        if (typeof obj === 'string') {
            return this.translateText(obj, targetLang);
        } else if (Array.isArray(obj)) {
            return obj.map(item => this.translateObject(item, targetLang));
        } else if (typeof obj === 'object' && obj !== null) {
            const result = {};
            for (const [key, value] of Object.entries(obj)) {
                result[key] = this.translateObject(value, targetLang);
            }
            return result;
        }

        return obj;
    }

    /**
     * Generate language file
     */
    generateLanguage(targetLang) {
        const langName = targetLang === 'ml' ? 'Malayalam' : 'Bengali';
        console.log(`🚀 Generating ${langName} (${targetLang}) with native translations...`);

        const inputFile = 'src/assets/i18n/en.json';
        const outputFile = `src/assets/i18n/${targetLang}.json`;

        try {
            // Load English file
            const enData = JSON.parse(fs.readFileSync(inputFile, 'utf8'));

            // Translate
            const translatedData = this.translateObject(enData, targetLang);

            // Save file
            fs.writeFileSync(outputFile, JSON.stringify(translatedData, null, 2), 'utf8');

            console.log(`✅ ${langName} completed: ${outputFile}`);
            return true;
        } catch (error) {
            console.error(`❌ Error generating ${langName}: ${error.message}`);
            return false;
        }
    }

    /**
     * Generate both languages
     */
    generateBoth() {
        console.log('🚀 Super Fast Native Language Generation');
        console.log('========================================\n');

        const startTime = Date.now();

        const mlSuccess = this.generateLanguage('ml');
        const bnSuccess = this.generateLanguage('bn');

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(1);

        console.log('\n📊 Generation Summary');
        console.log('====================');
        console.log(`${mlSuccess ? '✅' : '❌'} Malayalam (ml)`);
        console.log(`${bnSuccess ? '✅' : '❌'} Bengali (bn)`);
        console.log(`\n⏱️  Total time: ${duration} seconds`);

        if (mlSuccess && bnSuccess) {
            console.log('\n🎉 Both languages generated successfully with native translations!');
        }

        return mlSuccess && bnSuccess;
    }
}

// Main execution
if (require.main === module) {
    const translator = new SuperFastNativeTranslator();

    const args = process.argv.slice(2);

    if (args.length === 0) {
        // Generate both languages
        translator.generateBoth();
    } else {
        // Generate specific language
        const targetLang = args[0];
        if (targetLang === 'ml' || targetLang === 'bn') {
            translator.generateLanguage(targetLang);
        } else {
            console.error(`❌ Unsupported language: ${targetLang}`);
            console.log('Supported languages: ml (Malayalam), bn (Bengali)');
        }
    }
}

module.exports = SuperFastNativeTranslator;