import { Component, OnInit, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { trigger, state, style, transition, animate } from '@angular/animations';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

@Component({
  selector: 'app-language-selector',
  templateUrl: './language-selector.component.html',
  styleUrls: ['./language-selector.component.scss'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('150ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ])
  ]
})
export class LanguageSelectorComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentLanguage: string = 'en';
  isDropdownOpen: boolean = false;

  // Available languages
  languages: Language[] = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸'
    },
    {
      code: 'kn',
      name: 'Kannada',
      nativeName: 'ಕನ್ನಡ',
      flag: '🇮🇳'
    },
    {
      code: 'te',
      name: 'Telugu',
      nativeName: 'తెలుగు',
      flag: '🇮🇳'
    },
    {
      code: 'ta',
      name: 'Tamil',
      nativeName: 'தமிழ்',
      flag: '🇮🇳'
    },
    {
      code: 'bn',
      name: 'Bengali',
      nativeName: 'বাংলা',
      flag: '🇮🇳'
    },
    {
      code: 'ml',
      name: 'Malayalam',
      nativeName: 'മലയാളം',
      flag: '🇮🇳'
    },
    {
      code: 'hi',
      name: 'Hindi',
      nativeName: 'हिन्दी',
      flag: '🇮🇳'
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦'
    },
    {
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷'
    },
    {
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸'
    }
  ];

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    // Get current language from localStorage or default to 'en'
    this.currentLanguage = localStorage.getItem('locale') || 'en';

    // Set up translation service
    this.translate.setDefaultLang('en');
    this.translate.use(this.currentLanguage);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Get current language object
   */
  getCurrentLanguage(): Language {
    return this.languages.find(lang => lang.code === this.currentLanguage) || this.languages[0];
  }

  /**
   * Toggle dropdown visibility
   */
  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  /**
   * Close dropdown
   */
  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  /**
   * Change language
   */
  changeLanguage(languageCode: string): void {
    if (languageCode !== this.currentLanguage) {
      this.currentLanguage = languageCode;

      // Update localStorage
      localStorage.setItem('locale', languageCode);

      // Update translation service
      this.translate.use(languageCode);

      // Close dropdown
      this.closeDropdown();

      // Reload page to ensure all components pick up the new language
      // This is necessary because some components initialize translations in ngOnInit
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
  }

  /**
   * Check if language is available (has translation file)
   */
  isLanguageAvailable(languageCode: string): boolean {
    // For now, return true for all languages
    // In a real implementation, you might want to check if the translation file exists
    return true;
  }

  /**
   * Get language display name
   */
  getLanguageDisplayName(language: Language): string {
    return `${language.flag} ${language.nativeName}`;
  }

  /**
   * Handle click outside to close dropdown
   */
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.language-selector');

    if (dropdown && !dropdown.contains(target)) {
      this.closeDropdown();
    }
  }

  /**
   * TrackBy function for language list
   */
  trackByLanguageCode(index: number, language: Language): string {
    return language.code;
  }
}
