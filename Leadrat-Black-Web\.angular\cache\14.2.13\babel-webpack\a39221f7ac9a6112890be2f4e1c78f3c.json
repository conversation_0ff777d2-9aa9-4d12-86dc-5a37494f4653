{"ast": null, "code": "import baseIsMatch from './_baseIsMatch.js';\nimport getMatchData from './_getMatchData.js';\nimport matchesStrictComparable from './_matchesStrictComparable.js';\n/**\n * The base implementation of `_.matches` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n */\n\nfunction baseMatches(source) {\n  var matchData = getMatchData(source);\n\n  if (matchData.length == 1 && matchData[0][2]) {\n    return matchesStrictComparable(matchData[0][0], matchData[0][1]);\n  }\n\n  return function (object) {\n    return object === source || baseIsMatch(object, source, matchData);\n  };\n}\n\nexport default baseMatches;", "map": null, "metadata": {}, "sourceType": "module"}