{"ast": null, "code": "import root from './_root.js';\n/** Detect free variable `exports`. */\n\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */\n\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */\n\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n/** Built-in value references. */\n\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\n\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n  buffer.copy(result);\n  return result;\n}\n\nexport default cloneBuffer;", "map": null, "metadata": {}, "sourceType": "module"}