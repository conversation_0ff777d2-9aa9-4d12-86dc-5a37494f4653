{"ast": null, "code": "const instanceOfAny = (object, constructors) => constructors.some(c => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods; // This is a function to prevent it throwing up in node environments.\n\nfunction getIdbProxyableTypes() {\n  return idbProxyableTypes || (idbProxyableTypes = [IDBDatabase, IDBObjectStore, IDBIndex, IDBCursor, IDBTransaction]);\n} // This is a function to prevent it throwing up in node environments.\n\n\nfunction getCursorAdvanceMethods() {\n  return cursorAdvanceMethods || (cursorAdvanceMethods = [IDBCursor.prototype.advance, IDBCursor.prototype.continue, IDBCursor.prototype.continuePrimaryKey]);\n}\n\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\n\nfunction promisifyRequest(request) {\n  const promise = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      request.removeEventListener('success', success);\n      request.removeEventListener('error', error);\n    };\n\n    const success = () => {\n      resolve(wrap(request.result));\n      unlisten();\n    };\n\n    const error = () => {\n      reject(request.error);\n      unlisten();\n    };\n\n    request.addEventListener('success', success);\n    request.addEventListener('error', error);\n  });\n  promise.then(value => {\n    // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n    // (see wrapFunction).\n    if (value instanceof IDBCursor) {\n      cursorRequestMap.set(value, request);\n    } // Catching to avoid \"Uncaught Promise exceptions\"\n\n  }).catch(() => {}); // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n  // is because we create many promises from a single IDBRequest.\n\n  reverseTransformCache.set(promise, request);\n  return promise;\n}\n\nfunction cacheDonePromiseForTransaction(tx) {\n  // Early bail if we've already created a done promise for this transaction.\n  if (transactionDoneMap.has(tx)) return;\n  const done = new Promise((resolve, reject) => {\n    const unlisten = () => {\n      tx.removeEventListener('complete', complete);\n      tx.removeEventListener('error', error);\n      tx.removeEventListener('abort', error);\n    };\n\n    const complete = () => {\n      resolve();\n      unlisten();\n    };\n\n    const error = () => {\n      reject(tx.error || new DOMException('AbortError', 'AbortError'));\n      unlisten();\n    };\n\n    tx.addEventListener('complete', complete);\n    tx.addEventListener('error', error);\n    tx.addEventListener('abort', error);\n  }); // Cache it for later retrieval.\n\n  transactionDoneMap.set(tx, done);\n}\n\nlet idbProxyTraps = {\n  get(target, prop, receiver) {\n    if (target instanceof IDBTransaction) {\n      // Special handling for transaction.done.\n      if (prop === 'done') return transactionDoneMap.get(target); // Polyfill for objectStoreNames because of Edge.\n\n      if (prop === 'objectStoreNames') {\n        return target.objectStoreNames || transactionStoreNamesMap.get(target);\n      } // Make tx.store return the only store in the transaction, or undefined if there are many.\n\n\n      if (prop === 'store') {\n        return receiver.objectStoreNames[1] ? undefined : receiver.objectStore(receiver.objectStoreNames[0]);\n      }\n    } // Else transform whatever we get back.\n\n\n    return wrap(target[prop]);\n  },\n\n  set(target, prop, value) {\n    target[prop] = value;\n    return true;\n  },\n\n  has(target, prop) {\n    if (target instanceof IDBTransaction && (prop === 'done' || prop === 'store')) {\n      return true;\n    }\n\n    return prop in target;\n  }\n\n};\n\nfunction replaceTraps(callback) {\n  idbProxyTraps = callback(idbProxyTraps);\n}\n\nfunction wrapFunction(func) {\n  // Due to expected object equality (which is enforced by the caching in `wrap`), we\n  // only create one new func per func.\n  // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n  if (func === IDBDatabase.prototype.transaction && !('objectStoreNames' in IDBTransaction.prototype)) {\n    return function (storeNames, ...args) {\n      const tx = func.call(unwrap(this), storeNames, ...args);\n      transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n      return wrap(tx);\n    };\n  } // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n  // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n  // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n  // with real promises, so each advance methods returns a new promise for the cursor object, or\n  // undefined if the end of the cursor has been reached.\n\n\n  if (getCursorAdvanceMethods().includes(func)) {\n    return function (...args) {\n      // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n      // the original object.\n      func.apply(unwrap(this), args);\n      return wrap(cursorRequestMap.get(this));\n    };\n  }\n\n  return function (...args) {\n    // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n    // the original object.\n    return wrap(func.apply(unwrap(this), args));\n  };\n}\n\nfunction transformCachableValue(value) {\n  if (typeof value === 'function') return wrapFunction(value); // This doesn't return, it just creates a 'done' promise for the transaction,\n  // which is later returned for transaction.done (see idbObjectHandler).\n\n  if (value instanceof IDBTransaction) cacheDonePromiseForTransaction(value);\n  if (instanceOfAny(value, getIdbProxyableTypes())) return new Proxy(value, idbProxyTraps); // Return the same value back if we're not going to transform it.\n\n  return value;\n}\n\nfunction wrap(value) {\n  // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n  // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n  if (value instanceof IDBRequest) return promisifyRequest(value); // If we've already transformed this value before, reuse the transformed value.\n  // This is faster, but it also provides object equality.\n\n  if (transformCache.has(value)) return transformCache.get(value);\n  const newValue = transformCachableValue(value); // Not all types are transformed.\n  // These may be primitive types, so they can't be WeakMap keys.\n\n  if (newValue !== value) {\n    transformCache.set(value, newValue);\n    reverseTransformCache.set(newValue, value);\n  }\n\n  return newValue;\n}\n\nconst unwrap = value => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };", "map": null, "metadata": {}, "sourceType": "module"}