{"ast": null, "code": "import arrayMap from './_arrayMap.js';\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\n\nfunction baseValues(object, props) {\n  return arrayMap(props, function (key) {\n    return object[key];\n  });\n}\n\nexport default baseValues;", "map": null, "metadata": {}, "sourceType": "module"}