{"ast": null, "code": "/**\n * This function is like `baseIndexOf` except that it accepts a comparator.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOfWith(array, value, fromIndex, comparator) {\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (comparator(array[index], value)) {\n      return index;\n    }\n  }\n\n  return -1;\n}\n\nexport default baseIndexOfWith;", "map": null, "metadata": {}, "sourceType": "module"}