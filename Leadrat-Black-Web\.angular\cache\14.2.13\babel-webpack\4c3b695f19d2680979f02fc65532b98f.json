{"ast": null, "code": "import copyObject from './_copyObject.js';\nimport keysIn from './keysIn.js';\n/**\n * The base implementation of `_.assignIn` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\n\nfunction baseAssignIn(object, source) {\n  return object && copyObject(source, keysIn(source), object);\n}\n\nexport default baseAssignIn;", "map": null, "metadata": {}, "sourceType": "module"}