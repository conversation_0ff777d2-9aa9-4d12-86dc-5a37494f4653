{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { Subscriber } from '../Subscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n  return source => {\n    return source.lift(new ThrowIfEmptyOperator(errorFactory));\n  };\n}\n\nclass ThrowIfEmptyOperator {\n  constructor(errorFactory) {\n    this.errorFactory = errorFactory;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ThrowIfEmptySubscriber(subscriber, this.errorFactory));\n  }\n\n}\n\nclass ThrowIfEmptySubscriber extends Subscriber {\n  constructor(destination, errorFactory) {\n    super(destination);\n    this.errorFactory = errorFactory;\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    this.hasValue = true;\n    this.destination.next(value);\n  }\n\n  _complete() {\n    if (!this.hasValue) {\n      let err;\n\n      try {\n        err = this.errorFactory();\n      } catch (e) {\n        err = e;\n      }\n\n      this.destination.error(err);\n    } else {\n      return this.destination.complete();\n    }\n  }\n\n}\n\nfunction defaultErrorFactory() {\n  return new EmptyError();\n} //# sourceMappingURL=throwIfEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module"}