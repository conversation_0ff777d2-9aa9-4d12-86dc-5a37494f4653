{"ast": null, "code": "import Metadata from '../metadata.js';\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\n\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  var possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode);\n\n  if (!possibleCountries) {\n    return [];\n  }\n\n  return possibleCountries.filter(function (country) {\n    return couldNationalNumberBelongToCountry(nationalNumber, country, metadata);\n  });\n}\n\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  _metadata.selectNumberingPlan(country);\n\n  if (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\n    return true;\n  }\n\n  return false;\n} //# sourceMappingURL=getPossibleCountriesForNumber.js.map", "map": null, "metadata": {}, "sourceType": "module"}