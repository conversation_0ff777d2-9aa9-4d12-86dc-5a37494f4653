{"ast": null, "code": "import { innerSubscribe, SimpleInnerSubscriber, SimpleOuterSubscriber } from '../innerSubscribe';\nexport function takeUntil(notifier) {\n  return source => source.lift(new TakeUntilOperator(notifier));\n}\n\nclass TakeUntilOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    const takeUntilSubscriber = new TakeUntilSubscriber(subscriber);\n    const notifierSubscription = innerSubscribe(this.notifier, new SimpleInnerSubscriber(takeUntilSubscriber));\n\n    if (notifierSubscription && !takeUntilSubscriber.seenValue) {\n      takeUntilSubscriber.add(notifierSubscription);\n      return source.subscribe(takeUntilSubscriber);\n    }\n\n    return takeUntilSubscriber;\n  }\n\n}\n\nclass TakeUntilSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.seenValue = false;\n  }\n\n  notifyNext() {\n    this.seenValue = true;\n    this.complete();\n  }\n\n  notifyComplete() {}\n\n} //# sourceMappingURL=takeUntil.js.map", "map": null, "metadata": {}, "sourceType": "module"}