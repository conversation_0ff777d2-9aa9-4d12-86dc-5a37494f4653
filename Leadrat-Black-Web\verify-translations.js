const fs = require('fs');
const path = require('path');

/**
 * Verification script for translation files
 */
class TranslationVerifier {
    constructor() {
        this.languages = {
            'ta': 'Tamil',
            'te': 'Telugu',
            'bn': 'Bengali', 
            'ml': 'Malayalam',
            'kn': 'Kannada'
        };
        this.i18nPath = 'src/assets/i18n';
    }

    /**
     * Verify all translation files
     */
    verifyAll() {
        console.log('Translation Files Verification');
        console.log('==============================\n');

        // Load English reference
        const enPath = path.join(this.i18nPath, 'en.json');
        if (!fs.existsSync(enPath)) {
            console.error('❌ English reference file not found:', enPath);
            return false;
        }

        const enData = JSON.parse(fs.readFileSync(enPath, 'utf8'));
        const enKeys = this.getAllKeys(enData);
        console.log(`📖 English reference: ${enKeys.length} keys\n`);

        let allValid = true;

        // Verify each language
        for (const [code, name] of Object.entries(this.languages)) {
            const isValid = this.verifyLanguage(code, name, enKeys);
            if (!isValid) allValid = false;
        }

        console.log('\n' + '='.repeat(50));
        if (allValid) {
            console.log('🎉 All translation files are valid!');
        } else {
            console.log('⚠️  Some translation files have issues.');
        }

        return allValid;
    }

    /**
     * Verify a specific language file
     */
    verifyLanguage(code, name, enKeys) {
        const filePath = path.join(this.i18nPath, `${code}.json`);
        
        console.log(`🔍 Verifying ${name} (${code})...`);

        if (!fs.existsSync(filePath)) {
            console.log(`   ❌ File not found: ${filePath}`);
            return false;
        }

        try {
            const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            const keys = this.getAllKeys(data);
            
            // Check key count
            const keyCountMatch = keys.length === enKeys.length;
            console.log(`   📊 Keys: ${keys.length}/${enKeys.length} ${keyCountMatch ? '✅' : '❌'}`);

            // Check for missing keys
            const missingKeys = enKeys.filter(key => !keys.includes(key));
            if (missingKeys.length > 0) {
                console.log(`   ⚠️  Missing ${missingKeys.length} keys`);
                if (missingKeys.length <= 5) {
                    missingKeys.forEach(key => console.log(`      - ${key}`));
                } else {
                    console.log(`      - ${missingKeys.slice(0, 3).join(', ')} ... and ${missingKeys.length - 3} more`);
                }
            }

            // Check for extra keys
            const extraKeys = keys.filter(key => !enKeys.includes(key));
            if (extraKeys.length > 0) {
                console.log(`   ℹ️  Extra ${extraKeys.length} keys`);
            }

            // Check file size
            const stats = fs.statSync(filePath);
            const sizeKB = (stats.size / 1024).toFixed(1);
            console.log(`   📁 File size: ${sizeKB} KB`);

            // Sample some translations
            const sampleKeys = ['hello world', 'settings', 'save', 'cancel', 'loading'];
            const samples = this.getSampleTranslations(data, sampleKeys);
            if (samples.length > 0) {
                console.log(`   🔤 Sample translations:`);
                samples.forEach(sample => {
                    console.log(`      ${sample.key}: "${sample.value}"`);
                });
            }

            console.log(`   ${keyCountMatch && missingKeys.length === 0 ? '✅' : '⚠️'} ${name} verification complete\n`);
            
            return keyCountMatch && missingKeys.length === 0;

        } catch (error) {
            console.log(`   ❌ Error reading file: ${error.message}\n`);
            return false;
        }
    }

    /**
     * Get all keys from a nested object
     */
    getAllKeys(obj, prefix = '') {
        let keys = [];
        
        for (const [key, value] of Object.entries(obj)) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                keys = keys.concat(this.getAllKeys(value, fullKey));
            } else {
                keys.push(fullKey);
            }
        }
        
        return keys;
    }

    /**
     * Get sample translations for verification
     */
    getSampleTranslations(obj, sampleKeys) {
        const samples = [];
        
        for (const key of sampleKeys) {
            const value = this.getNestedValue(obj, key);
            if (value && typeof value === 'string') {
                samples.push({ key, value });
            }
        }
        
        return samples;
    }

    /**
     * Get nested value from object using dot notation
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * Generate verification report
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            languages: {},
            summary: {
                total: 0,
                valid: 0,
                invalid: 0
            }
        };

        // Load English reference
        const enPath = path.join(this.i18nPath, 'en.json');
        if (!fs.existsSync(enPath)) {
            report.error = 'English reference file not found';
            return report;
        }

        const enData = JSON.parse(fs.readFileSync(enPath, 'utf8'));
        const enKeys = this.getAllKeys(enData);
        report.referenceKeys = enKeys.length;

        // Check each language
        for (const [code, name] of Object.entries(this.languages)) {
            const filePath = path.join(this.i18nPath, `${code}.json`);
            const langReport = {
                name,
                exists: fs.existsSync(filePath),
                valid: false,
                keys: 0,
                missingKeys: 0,
                extraKeys: 0,
                fileSize: 0
            };

            if (langReport.exists) {
                try {
                    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    const keys = this.getAllKeys(data);
                    const stats = fs.statSync(filePath);

                    langReport.keys = keys.length;
                    langReport.fileSize = stats.size;
                    langReport.missingKeys = enKeys.filter(key => !keys.includes(key)).length;
                    langReport.extraKeys = keys.filter(key => !enKeys.includes(key)).length;
                    langReport.valid = langReport.keys === enKeys.length && langReport.missingKeys === 0;

                    if (langReport.valid) report.summary.valid++;
                    else report.summary.invalid++;

                } catch (error) {
                    langReport.error = error.message;
                    report.summary.invalid++;
                }
            } else {
                report.summary.invalid++;
            }

            report.languages[code] = langReport;
            report.summary.total++;
        }

        return report;
    }
}

// Main execution
if (require.main === module) {
    const verifier = new TranslationVerifier();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--report')) {
        const report = verifier.generateReport();
        console.log(JSON.stringify(report, null, 2));
    } else {
        verifier.verifyAll();
    }
}

module.exports = TranslationVerifier;
