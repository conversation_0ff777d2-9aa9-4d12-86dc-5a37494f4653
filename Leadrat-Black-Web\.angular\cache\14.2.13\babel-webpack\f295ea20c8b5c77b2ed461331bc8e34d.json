{"ast": null, "code": "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\n\nfunction createRange(fromRight) {\n  return function (start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    } // Ensure the sign of `-0` is preserved.\n\n\n    start = toFinite(start);\n\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n\n    step = step === undefined ? start < end ? 1 : -1 : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;", "map": null, "metadata": {}, "sourceType": "module"}