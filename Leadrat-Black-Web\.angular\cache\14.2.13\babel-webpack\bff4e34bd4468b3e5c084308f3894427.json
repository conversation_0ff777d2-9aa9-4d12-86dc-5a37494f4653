{"ast": null, "code": "/** Returns a regular expression quantifier with an upper and lower limit. */\nexport function limit(lower, upper) {\n  if (lower < 0 || upper <= 0 || upper < lower) {\n    throw new TypeError();\n  }\n\n  return \"{\".concat(lower, \",\").concat(upper, \"}\");\n}\n/**\r\n * Trims away any characters after the first match of {@code pattern} in {@code candidate},\r\n * returning the trimmed version.\r\n */\n\nexport function trimAfterFirstMatch(regexp, string) {\n  var index = string.search(regexp);\n\n  if (index >= 0) {\n    return string.slice(0, index);\n  }\n\n  return string;\n}\nexport function startsWith(string, substring) {\n  return string.indexOf(substring) === 0;\n}\nexport function endsWith(string, substring) {\n  return string.indexOf(substring, string.length - substring.length) === string.length - substring.length;\n} //# sourceMappingURL=util.js.map", "map": null, "metadata": {}, "sourceType": "module"}