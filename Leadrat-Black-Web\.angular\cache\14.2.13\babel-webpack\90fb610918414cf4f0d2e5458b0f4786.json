{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function observeOn(scheduler, delay = 0) {\n  return function observeOnOperatorFunction(source) {\n    return source.lift(new ObserveOnOperator(scheduler, delay));\n  };\n}\nexport class ObserveOnOperator {\n  constructor(scheduler, delay = 0) {\n    this.scheduler = scheduler;\n    this.delay = delay;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ObserveOnSubscriber(subscriber, this.scheduler, this.delay));\n  }\n\n}\nexport class ObserveOnSubscriber extends Subscriber {\n  constructor(destination, scheduler, delay = 0) {\n    super(destination);\n    this.scheduler = scheduler;\n    this.delay = delay;\n  }\n\n  static dispatch(arg) {\n    const {\n      notification,\n      destination\n    } = arg;\n    notification.observe(destination);\n    this.unsubscribe();\n  }\n\n  scheduleMessage(notification) {\n    const destination = this.destination;\n    destination.add(this.scheduler.schedule(ObserveOnSubscriber.dispatch, this.delay, new ObserveOnMessage(notification, this.destination)));\n  }\n\n  _next(value) {\n    this.scheduleMessage(Notification.createNext(value));\n  }\n\n  _error(err) {\n    this.scheduleMessage(Notification.createError(err));\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.scheduleMessage(Notification.createComplete());\n    this.unsubscribe();\n  }\n\n}\nexport class ObserveOnMessage {\n  constructor(notification, destination) {\n    this.notification = notification;\n    this.destination = destination;\n  }\n\n} //# sourceMappingURL=observeOn.js.map", "map": null, "metadata": {}, "sourceType": "module"}