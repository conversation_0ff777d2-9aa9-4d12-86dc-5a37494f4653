{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/listing/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\n/** @private */\n\nexport class ServerSentEventsTransport {\n  constructor(httpClient, accessToken, logger, options) {\n    this._httpClient = httpClient;\n    this._accessToken = accessToken;\n    this._logger = logger;\n    this._options = options;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n\n  connect(url, transferFormat) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      Arg.isRequired(url, \"url\");\n      Arg.isRequired(transferFormat, \"transferFormat\");\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n\n      _this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\"); // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\n\n\n      _this._url = url;\n\n      if (_this._accessToken) {\n        url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(_this._accessToken)}`;\n      }\n\n      return new Promise((resolve, reject) => {\n        let opened = false;\n\n        if (transferFormat !== TransferFormat.Text) {\n          reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\n          return;\n        }\n\n        let eventSource;\n\n        if (Platform.isBrowser || Platform.isWebWorker) {\n          eventSource = new _this._options.EventSource(url, {\n            withCredentials: _this._options.withCredentials\n          });\n        } else {\n          // Non-browser passes cookies via the dictionary\n          const cookies = _this._httpClient.getCookieString(url);\n\n          const headers = {};\n          headers.Cookie = cookies;\n          const [name, value] = getUserAgentHeader();\n          headers[name] = value;\n          eventSource = new _this._options.EventSource(url, {\n            withCredentials: _this._options.withCredentials,\n            headers: { ...headers,\n              ..._this._options.headers\n            }\n          });\n        }\n\n        try {\n          eventSource.onmessage = e => {\n            if (_this.onreceive) {\n              try {\n                _this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, _this._options.logMessageContent)}.`);\n\n                _this.onreceive(e.data);\n              } catch (error) {\n                _this._close(error);\n\n                return;\n              }\n            }\n          }; // @ts-ignore: not using event on purpose\n\n\n          eventSource.onerror = e => {\n            // EventSource doesn't give any useful information about server side closes.\n            if (opened) {\n              _this._close();\n            } else {\n              reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\" + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\" + \" If you have multiple servers check that sticky sessions are enabled.\"));\n            }\n          };\n\n          eventSource.onopen = () => {\n            _this._logger.log(LogLevel.Information, `SSE connected to ${_this._url}`);\n\n            _this._eventSource = eventSource;\n            opened = true;\n            resolve();\n          };\n        } catch (e) {\n          reject(e);\n          return;\n        }\n      });\n    })();\n  }\n\n  send(data) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this2._eventSource) {\n        return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n      }\n\n      return sendMessage(_this2._logger, \"SSE\", _this2._httpClient, _this2._url, data, _this2._options);\n    })();\n  }\n\n  stop() {\n    this._close();\n\n    return Promise.resolve();\n  }\n\n  _close(e) {\n    if (this._eventSource) {\n      this._eventSource.close();\n\n      this._eventSource = undefined;\n\n      if (this.onclose) {\n        this.onclose(e);\n      }\n    }\n  }\n\n} //# sourceMappingURL=ServerSentEventsTransport.js.map", "map": null, "metadata": {}, "sourceType": "module"}