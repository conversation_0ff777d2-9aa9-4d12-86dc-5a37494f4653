{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/listing/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortController } from \"./AbortController\";\nimport { HttpError, TimeoutError } from \"./Errors\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\"; // Not exported from 'index', this type is internal.\n\n/** @private */\n\nexport class LongPollingTransport {\n  // This is an internal type, not exported from 'index' so this is really just internal.\n  get pollAborted() {\n    return this._pollAbort.aborted;\n  }\n\n  constructor(httpClient, logger, options) {\n    this._httpClient = httpClient;\n    this._logger = logger;\n    this._pollAbort = new AbortController();\n    this._options = options;\n    this._running = false;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n\n  connect(url, transferFormat) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      Arg.isRequired(url, \"url\");\n      Arg.isRequired(transferFormat, \"transferFormat\");\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n      _this._url = url;\n\n      _this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\"); // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\n\n\n      if (transferFormat === TransferFormat.Binary && typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\") {\n        throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\n      }\n\n      const [name, value] = getUserAgentHeader();\n      const headers = {\n        [name]: value,\n        ..._this._options.headers\n      };\n      const pollOptions = {\n        abortSignal: _this._pollAbort.signal,\n        headers,\n        timeout: 100000,\n        withCredentials: _this._options.withCredentials\n      };\n\n      if (transferFormat === TransferFormat.Binary) {\n        pollOptions.responseType = \"arraybuffer\";\n      } // Make initial long polling request\n      // Server uses first long polling request to finish initializing connection and it returns without data\n\n\n      const pollUrl = `${url}&_=${Date.now()}`;\n\n      _this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n\n      const response = yield _this._httpClient.get(pollUrl, pollOptions);\n\n      if (response.statusCode !== 200) {\n        _this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`); // Mark running as false so that the poll immediately ends and runs the close logic\n\n\n        _this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n        _this._running = false;\n      } else {\n        _this._running = true;\n      }\n\n      _this._receiving = _this._poll(_this._url, pollOptions);\n    })();\n  }\n\n  _poll(url, pollOptions) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        while (_this2._running) {\n          try {\n            const pollUrl = `${url}&_=${Date.now()}`;\n\n            _this2._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n\n            const response = yield _this2._httpClient.get(pollUrl, pollOptions);\n\n            if (response.statusCode === 204) {\n              _this2._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\n\n              _this2._running = false;\n            } else if (response.statusCode !== 200) {\n              _this2._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`); // Unexpected status code\n\n\n              _this2._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n              _this2._running = false;\n            } else {\n              // Process the response\n              if (response.content) {\n                _this2._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, _this2._options.logMessageContent)}.`);\n\n                if (_this2.onreceive) {\n                  _this2.onreceive(response.content);\n                }\n              } else {\n                // This is another way timeout manifest.\n                _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n              }\n            }\n          } catch (e) {\n            if (!_this2._running) {\n              // Log but disregard errors that occur after stopping\n              _this2._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\n            } else {\n              if (e instanceof TimeoutError) {\n                // Ignore timeouts and reissue the poll.\n                _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n              } else {\n                // Close the connection with the error as the result.\n                _this2._closeError = e;\n                _this2._running = false;\n              }\n            }\n          }\n        }\n      } finally {\n        _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\"); // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\n        // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\n\n\n        if (!_this2.pollAborted) {\n          _this2._raiseOnClose();\n        }\n      }\n    })();\n  }\n\n  send(data) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this3._running) {\n        return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n      }\n\n      return sendMessage(_this3._logger, \"LongPolling\", _this3._httpClient, _this3._url, data, _this3._options);\n    })();\n  }\n\n  stop() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\"); // Tell receiving loop to stop, abort any current request, and then wait for it to finish\n\n\n      _this4._running = false;\n\n      _this4._pollAbort.abort();\n\n      try {\n        yield _this4._receiving; // Send DELETE to clean up long polling on the server\n\n        _this4._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${_this4._url}.`);\n\n        const headers = {};\n        const [name, value] = getUserAgentHeader();\n        headers[name] = value;\n        const deleteOptions = {\n          headers: { ...headers,\n            ..._this4._options.headers\n          },\n          timeout: _this4._options.timeout,\n          withCredentials: _this4._options.withCredentials\n        };\n        let error;\n\n        try {\n          yield _this4._httpClient.delete(_this4._url, deleteOptions);\n        } catch (err) {\n          error = err;\n        }\n\n        if (error) {\n          if (error instanceof HttpError) {\n            if (error.statusCode === 404) {\n              _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\n            } else {\n              _this4._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\n            }\n          }\n        } else {\n          _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\n        }\n      } finally {\n        _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\"); // Raise close event here instead of in polling\n        // It needs to happen after the DELETE request is sent\n\n\n        _this4._raiseOnClose();\n      }\n    })();\n  }\n\n  _raiseOnClose() {\n    if (this.onclose) {\n      let logMessage = \"(LongPolling transport) Firing onclose event.\";\n\n      if (this._closeError) {\n        logMessage += \" Error: \" + this._closeError;\n      }\n\n      this._logger.log(LogLevel.Trace, logMessage);\n\n      this.onclose(this._closeError);\n    }\n  }\n\n} //# sourceMappingURL=LongPollingTransport.js.map", "map": null, "metadata": {}, "sourceType": "module"}