{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { empty } from '../observable/empty';\nexport function takeLast(count) {\n  return function takeLastOperatorFunction(source) {\n    if (count === 0) {\n      return empty();\n    } else {\n      return source.lift(new TakeLastOperator(count));\n    }\n  };\n}\n\nclass TakeLastOperator {\n  constructor(total) {\n    this.total = total;\n\n    if (this.total < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TakeLastSubscriber(subscriber, this.total));\n  }\n\n}\n\nclass TakeLastSubscriber extends Subscriber {\n  constructor(destination, total) {\n    super(destination);\n    this.total = total;\n    this.ring = new Array();\n    this.count = 0;\n  }\n\n  _next(value) {\n    const ring = this.ring;\n    const total = this.total;\n    const count = this.count++;\n\n    if (ring.length < total) {\n      ring.push(value);\n    } else {\n      const index = count % total;\n      ring[index] = value;\n    }\n  }\n\n  _complete() {\n    const destination = this.destination;\n    let count = this.count;\n\n    if (count > 0) {\n      const total = this.count >= this.total ? this.total : this.count;\n      const ring = this.ring;\n\n      for (let i = 0; i < total; i++) {\n        const idx = count++ % total;\n        destination.next(ring[idx]);\n      }\n    }\n\n    destination.complete();\n  }\n\n} //# sourceMappingURL=takeLast.js.map", "map": null, "metadata": {}, "sourceType": "module"}