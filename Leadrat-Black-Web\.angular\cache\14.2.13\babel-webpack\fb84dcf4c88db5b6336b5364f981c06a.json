{"ast": null, "code": "import { SubscribeOnObservable } from '../observable/SubscribeOnObservable';\nexport function subscribeOn(scheduler, delay = 0) {\n  return function subscribeOnOperatorFunction(source) {\n    return source.lift(new SubscribeOnOperator(scheduler, delay));\n  };\n}\n\nclass SubscribeOnOperator {\n  constructor(scheduler, delay) {\n    this.scheduler = scheduler;\n    this.delay = delay;\n  }\n\n  call(subscriber, source) {\n    return new SubscribeOnObservable(source, this.delay, this.scheduler).subscribe(subscriber);\n  }\n\n} //# sourceMappingURL=subscribeOn.js.map", "map": null, "metadata": {}, "sourceType": "module"}