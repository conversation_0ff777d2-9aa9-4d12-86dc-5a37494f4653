{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arraySome from './_arraySome.js';\nimport cacheHas from './_cacheHas.js';\n/** Used to compose bitmasks for value comparisons. */\n\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\n\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  } // Check that cyclic values are equal.\n\n\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n\n  var index = -1,\n      result = true,\n      seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache() : undefined;\n  stack.set(array, other);\n  stack.set(other, array); // Ignore non-index properties.\n\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n    }\n\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n\n      result = false;\n      break;\n    } // Recursively compare arrays (susceptible to call stack limits).\n\n\n    if (seen) {\n      if (!arraySome(other, function (othValue, othIndex) {\n        if (!cacheHas(seen, othIndex) && (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n          return seen.push(othIndex);\n        }\n      })) {\n        result = false;\n        break;\n      }\n    } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n      result = false;\n      break;\n    }\n  }\n\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nexport default equalArrays;", "map": null, "metadata": {}, "sourceType": "module"}