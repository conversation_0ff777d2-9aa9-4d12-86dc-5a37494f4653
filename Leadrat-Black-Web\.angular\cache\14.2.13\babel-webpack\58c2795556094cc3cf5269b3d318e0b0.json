{"ast": null, "code": "import copyArray from './_copyArray.js';\nimport isIndex from './_isIndex.js';\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMin = Math.min;\n/**\n * Reorder `array` according to the specified indexes where the element at\n * the first index is assigned as the first element, the element at\n * the second index is assigned as the second element, and so on.\n *\n * @private\n * @param {Array} array The array to reorder.\n * @param {Array} indexes The arranged array indexes.\n * @returns {Array} Returns `array`.\n */\n\nfunction reorder(array, indexes) {\n  var arrLength = array.length,\n      length = nativeMin(indexes.length, arrLength),\n      oldArray = copyArray(array);\n\n  while (length--) {\n    var index = indexes[length];\n    array[length] = isIndex(index, arrLength) ? oldArray[index] : undefined;\n  }\n\n  return array;\n}\n\nexport default reorder;", "map": null, "metadata": {}, "sourceType": "module"}