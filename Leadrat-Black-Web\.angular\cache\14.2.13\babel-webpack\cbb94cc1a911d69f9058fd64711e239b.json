{"ast": null, "code": "import baseUnset from './_baseUnset.js';\nimport isIndex from './_isIndex.js';\n/** Used for built-in method references. */\n\nvar arrayProto = Array.prototype;\n/** Built-in value references. */\n\nvar splice = arrayProto.splice;\n/**\n * The base implementation of `_.pullAt` without support for individual\n * indexes or capturing the removed elements.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {number[]} indexes The indexes of elements to remove.\n * @returns {Array} Returns `array`.\n */\n\nfunction basePullAt(array, indexes) {\n  var length = array ? indexes.length : 0,\n      lastIndex = length - 1;\n\n  while (length--) {\n    var index = indexes[length];\n\n    if (length == lastIndex || index !== previous) {\n      var previous = index;\n\n      if (isIndex(index)) {\n        splice.call(array, index, 1);\n      } else {\n        baseUnset(array, index);\n      }\n    }\n  }\n\n  return array;\n}\n\nexport default basePullAt;", "map": null, "metadata": {}, "sourceType": "module"}