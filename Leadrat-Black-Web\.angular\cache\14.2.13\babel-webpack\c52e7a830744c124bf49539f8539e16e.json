{"ast": null, "code": "import copyObject from './_copyObject.js';\nimport getSymbols from './_getSymbols.js';\n/**\n * Copies own symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\n\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\nexport default copySymbols;", "map": null, "metadata": {}, "sourceType": "module"}