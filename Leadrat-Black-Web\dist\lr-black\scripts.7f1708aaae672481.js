var rr=Object.defineProperty,or=Object.defineProperties,sr=Object.getOwnPropertyDescriptors,qi=Object.getOwnPropertySymbols,ar=Object.prototype.hasOwnProperty,lr=Object.prototype.propertyIsEnumerable,Wi=(R,Z,ne)=>Z in R?rr(R,Z,{enumerable:!0,configurable:!0,writable:!0,value:ne}):R[Z]=ne,Ke=(R,Z)=>{for(var ne in Z||(Z={}))ar.call(Z,ne)&&Wi(R,ne,Z[ne]);if(qi)for(var ne of qi(Z))lr.call(Z,ne)&&Wi(R,ne,Z[ne]);return R},Si=(R,Z)=>or(R,sr(Z));(function(R,Z,ne){var ge,we=R.getElementsByTagName(Z)[0];R.getElementById(ne)||((ge=R.createElement(Z)).id=ne,ge.src="https://connect.facebook.net/en_US/sdk.js",we.parentNode.insertBefore(ge,we))})(document,"script","facebook-jssdk"),window.fbAsyncInit=function(){FB.init({appId:"1491491247883558",cookie:!1,xfbml:!0,version:"v15.0"})},function(R,Z){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=R.document?Z(R,!0):function(ne){if(!ne.document)throw new Error("jQuery requires a window with a document");return Z(ne)}:Z(R)}("undefined"!=typeof window?window:this,function(R,Z){"use strict";var ne=[],ge=Object.getPrototypeOf,we=ne.slice,nt=ne.flat?function(e){return ne.flat.call(e)}:function(e){return ne.concat.apply([],e)},_t=ne.push,Pe=ne.indexOf,Re={},it=Re.toString,je=Re.hasOwnProperty,Ge=je.toString,Jn=Ge.call(Object),ee={},Q=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},Fe=function(e){return null!=e&&e===e.window},K=R.document,xe={type:!0,src:!0,nonce:!0,noModule:!0};function Ie(e,t,i){var r,a,l=(i=i||K).createElement("script");if(l.text=e,t)for(r in xe)(a=t[r]||t.getAttribute&&t.getAttribute(r))&&l.setAttribute(r,a);i.head.appendChild(l).parentNode.removeChild(l)}function Ee(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?Re[it.call(e)]||"object":typeof e}var An="3.7.1 -ajax,-ajax/jsonp,-ajax/load,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-deprecated/ajax-event-alias,-effects,-effects/animatedSelector,-effects/Tween",Ut=/HTML$/i,o=function(e,t){return new o.fn.init(e,t)};function dn(e){var t=!!e&&"length"in e&&e.length,i=Ee(e);return!Q(e)&&!Fe(e)&&("array"===i||0===t||"number"==typeof t&&0<t&&t-1 in e)}function ue(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}o.fn=o.prototype={jquery:An,constructor:o,length:0,toArray:function(){return we.call(this)},get:function(e){return null==e?we.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=o.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return o.each(this,e)},map:function(e){return this.pushStack(o.map(this,function(t,i){return e.call(t,i,t)}))},slice:function(){return this.pushStack(we.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(o.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(o.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,i=+e+(e<0?t:0);return this.pushStack(0<=i&&i<t?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:_t,sort:ne.sort,splice:ne.splice},o.extend=o.fn.extend=function(){var e,t,i,r,a,l,c=arguments[0]||{},v=1,h=arguments.length,x=!1;for("boolean"==typeof c&&(x=c,c=arguments[v]||{},v++),"object"==typeof c||Q(c)||(c={}),v===h&&(c=this,v--);v<h;v++)if(null!=(e=arguments[v]))for(t in e)r=e[t],"__proto__"!==t&&c!==r&&(x&&r&&(o.isPlainObject(r)||(a=Array.isArray(r)))?(i=c[t],l=a&&!Array.isArray(i)?[]:a||o.isPlainObject(i)?i:{},a=!1,c[t]=o.extend(x,l,r)):void 0!==r&&(c[t]=r));return c},o.extend({expando:"jQuery"+(An+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,i;return!(!e||"[object Object]"!==it.call(e)||(t=ge(e))&&("function"!=typeof(i=je.call(t,"constructor")&&t.constructor)||Ge.call(i)!==Jn))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,i){Ie(e,{nonce:t&&t.nonce},i)},each:function(e,t){var i,r=0;if(dn(e))for(i=e.length;r<i&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,i="",r=0,a=e.nodeType;if(!a)for(;t=e[r++];)i+=o.text(t);return 1===a||11===a?e.textContent:9===a?e.documentElement.textContent:3===a||4===a?e.nodeValue:i},makeArray:function(e,t){var i=t||[];return null!=e&&(dn(Object(e))?o.merge(i,"string"==typeof e?[e]:e):_t.call(i,e)),i},inArray:function(e,t,i){return null==t?-1:Pe.call(t,e,i)},isXMLDoc:function(e){var i=e&&(e.ownerDocument||e).documentElement;return!Ut.test(e&&e.namespaceURI||i&&i.nodeName||"HTML")},merge:function(e,t){for(var i=+t.length,r=0,a=e.length;r<i;r++)e[a++]=t[r];return e.length=a,e},grep:function(e,t,i){for(var r=[],a=0,l=e.length,c=!i;a<l;a++)!t(e[a],a)!==c&&r.push(e[a]);return r},map:function(e,t,i){var r,a,l=0,c=[];if(dn(e))for(r=e.length;l<r;l++)null!=(a=t(e[l],l,i))&&c.push(a);else for(l in e)null!=(a=t(e[l],l,i))&&c.push(a);return nt(c)},guid:1,support:ee}),"function"==typeof Symbol&&(o.fn[Symbol.iterator]=ne[Symbol.iterator]),o.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){Re["[object "+t+"]"]=t.toLowerCase()});var Cn=ne.pop,Tn=ne.sort,Zn=ne.splice,oe="[\\x20\\t\\r\\n\\f]",Ae=new RegExp("^"+oe+"+|((?:^|[^\\\\])(?:\\\\.)*)"+oe+"+$","g");o.contains=function(e,t){var i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(e.contains?e.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))};var Ve=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Xe(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}o.escapeSelector=function(e){return(e+"").replace(Ve,Xe)};var ve=K,Mt=_t;!function(){var e,t,i,r,a,l,c,v,h,x,w=Mt,T=o.expando,$=0,S=0,J=at(),te=at(),ae=at(),ke=at(),pe=function(u,y){return u===y&&(a=!0),0},Ce="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ye="(?:\\\\[\\da-fA-F]{1,6}"+oe+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",At="\\["+oe+"*("+ye+")(?:"+oe+"*([*^$|!~]?=)"+oe+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ye+"))|)"+oe+"*\\]",nn=":("+ye+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+At+")*)|.*)\\)|)",Xt=new RegExp(oe+"+","g"),Be=new RegExp("^"+oe+"*,"+oe+"*"),qe=new RegExp("^"+oe+"*([>+~]|"+oe+")"+oe+"*"),rn=new RegExp(oe+"|>"),$t=new RegExp(nn),bn=new RegExp("^"+ye+"$"),on={ID:new RegExp("^#("+ye+")"),CLASS:new RegExp("^\\.("+ye+")"),TAG:new RegExp("^("+ye+"|[*])"),ATTR:new RegExp("^"+At),PSEUDO:new RegExp("^"+nn),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+oe+"*(even|odd|(([+-]|)(\\d*)n|)"+oe+"*(?:([+-]|)"+oe+"*(\\d+)|))"+oe+"*\\)|)","i"),bool:new RegExp("^(?:"+Ce+")$","i"),needsContext:new RegExp("^"+oe+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+oe+"*((?:-\\d)?\\d*)"+oe+"*\\)|)(?=[^-]|$)","i")},_n=/^(?:input|select|textarea|button)$/i,li=/^h\d$/i,ci=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Wn=/[+~]/,gt=new RegExp("\\\\[\\da-fA-F]{1,6}"+oe+"?|\\\\([^\\r\\n\\f])","g"),tt=function(u,y){var b="0x"+u.slice(1)-65536;return y||(b<0?String.fromCharCode(b+65536):String.fromCharCode(b>>10|55296,1023&b|56320))},zn=function(){mt()},_i=di(function(u){return!0===u.disabled&&ue(u,"fieldset")},{dir:"parentNode",next:"legend"});try{w.apply(ne=we.call(ve.childNodes),ve.childNodes)}catch(u){w={apply:function(y,b){Mt.apply(y,we.call(b))},call:function(y){Mt.apply(y,we.call(arguments,1))}}}function fe(u,y,b,_){var C,F,M,B,H,le,G,U=y&&y.ownerDocument,ie=y?y.nodeType:9;if(b=b||[],"string"!=typeof u||!u||1!==ie&&9!==ie&&11!==ie)return b;if(!_&&(mt(y),y=y||l,v)){if(11!==ie&&(H=ci.exec(u)))if(C=H[1]){if(9===ie){if(!(M=y.getElementById(C)))return b;if(M.id===C)return w.call(b,M),b}else if(U&&(M=U.getElementById(C))&&fe.contains(y,M)&&M.id===C)return w.call(b,M),b}else{if(H[2])return w.apply(b,y.getElementsByTagName(u)),b;if((C=H[3])&&y.getElementsByClassName)return w.apply(b,y.getElementsByClassName(C)),b}if(!(ke[u+" "]||h&&h.test(u))){if(G=u,U=y,1===ie&&(rn.test(u)||qe.test(u))){for((U=Wn.test(u)&&xn(y.parentNode)||y)==y&&ee.scope||((B=y.getAttribute("id"))?B=o.escapeSelector(B):y.setAttribute("id",B=T)),F=(le=Xn(u)).length;F--;)le[F]=(B?"#"+B:":scope")+" "+ui(le[F]);G=le.join(",")}try{return w.apply(b,U.querySelectorAll(G)),b}catch(X){ke(u,!0)}finally{B===T&&y.removeAttribute("id")}}}return pi(u.replace(Ae,"$1"),y,b,_)}function at(){var u=[];return function y(b,_){return u.push(b+" ")>t.cacheLength&&delete y[u.shift()],y[b+" "]=_}}function We(u){return u[T]=!0,u}function Pt(u){var y=l.createElement("fieldset");try{return!!u(y)}catch(b){return!1}finally{y.parentNode&&y.parentNode.removeChild(y),y=null}}function lt(u){return function(y){return ue(y,"input")&&y.type===u}}function wn(u){return function(y){return(ue(y,"input")||ue(y,"button"))&&y.type===u}}function Vn(u){return function(y){return"form"in y?y.parentNode&&!1===y.disabled?"label"in y?"label"in y.parentNode?y.parentNode.disabled===u:y.disabled===u:y.isDisabled===u||y.isDisabled!==!u&&_i(y)===u:y.disabled===u:"label"in y&&y.disabled===u}}function Ct(u){return We(function(y){return y=+y,We(function(b,_){for(var C,F=u([],b.length,y),M=F.length;M--;)b[C=F[M]]&&(b[C]=!(_[C]=b[C]))})})}function xn(u){return u&&void 0!==u.getElementsByTagName&&u}function mt(u){var y,b=u?u.ownerDocument||u:ve;return b!=l&&9===b.nodeType&&b.documentElement&&(c=(l=b).documentElement,v=!o.isXMLDoc(l),x=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&ve!=l&&(y=l.defaultView)&&y.top!==y&&y.addEventListener("unload",zn),ee.getById=Pt(function(_){return c.appendChild(_).id=o.expando,!l.getElementsByName||!l.getElementsByName(o.expando).length}),ee.disconnectedMatch=Pt(function(_){return x.call(_,"*")}),ee.scope=Pt(function(){return l.querySelectorAll(":scope")}),ee.cssHas=Pt(function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(_){return!0}}),ee.getById?(t.filter.ID=function(_){var C=_.replace(gt,tt);return function(F){return F.getAttribute("id")===C}},t.find.ID=function(_,C){if(void 0!==C.getElementById&&v){var F=C.getElementById(_);return F?[F]:[]}}):(t.filter.ID=function(_){var C=_.replace(gt,tt);return function(F){var M=void 0!==F.getAttributeNode&&F.getAttributeNode("id");return M&&M.value===C}},t.find.ID=function(_,C){if(void 0!==C.getElementById&&v){var F,M,B,H=C.getElementById(_);if(H){if((F=H.getAttributeNode("id"))&&F.value===_)return[H];for(B=C.getElementsByName(_),M=0;H=B[M++];)if((F=H.getAttributeNode("id"))&&F.value===_)return[H]}return[]}}),t.find.TAG=function(_,C){return void 0!==C.getElementsByTagName?C.getElementsByTagName(_):C.querySelectorAll(_)},t.find.CLASS=function(_,C){if(void 0!==C.getElementsByClassName&&v)return C.getElementsByClassName(_)},h=[],Pt(function(_){var C;c.appendChild(_).innerHTML="<a id='"+T+"' href='' disabled='disabled'></a><select id='"+T+"-\r\\' disabled='disabled'><option selected=''></option></select>",_.querySelectorAll("[selected]").length||h.push("\\["+oe+"*(?:value|"+Ce+")"),_.querySelectorAll("[id~="+T+"-]").length||h.push("~="),_.querySelectorAll("a#"+T+"+*").length||h.push(".#.+[+~]"),_.querySelectorAll(":checked").length||h.push(":checked"),(C=l.createElement("input")).setAttribute("type","hidden"),_.appendChild(C).setAttribute("name","D"),c.appendChild(_).disabled=!0,2!==_.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),(C=l.createElement("input")).setAttribute("name",""),_.appendChild(C),_.querySelectorAll("[name='']").length||h.push("\\["+oe+"*name"+oe+"*="+oe+"*(?:''|\"\")")}),ee.cssHas||h.push(":has"),h=h.length&&new RegExp(h.join("|")),pe=function(_,C){if(_===C)return a=!0,0;var F=!_.compareDocumentPosition-!C.compareDocumentPosition;return F||(1&(F=(_.ownerDocument||_)==(C.ownerDocument||C)?_.compareDocumentPosition(C):1)||!ee.sortDetached&&C.compareDocumentPosition(_)===F?_===l||_.ownerDocument==ve&&fe.contains(ve,_)?-1:C===l||C.ownerDocument==ve&&fe.contains(ve,C)?1:r?Pe.call(r,_)-Pe.call(r,C):0:4&F?-1:1)}),l}for(e in fe.matches=function(u,y){return fe(u,null,null,y)},fe.matchesSelector=function(u,y){if(mt(u),v&&!ke[y+" "]&&(!h||!h.test(y)))try{var b=x.call(u,y);if(b||ee.disconnectedMatch||u.document&&11!==u.document.nodeType)return b}catch(_){ke(y,!0)}return 0<fe(y,l,null,[u]).length},fe.contains=function(u,y){return(u.ownerDocument||u)!=l&&mt(u),o.contains(u,y)},fe.attr=function(u,y){(u.ownerDocument||u)!=l&&mt(u);var b=t.attrHandle[y.toLowerCase()],_=b&&je.call(t.attrHandle,y.toLowerCase())?b(u,y,!v):void 0;return void 0!==_?_:u.getAttribute(y)},fe.error=function(u){throw new Error("Syntax error, unrecognized expression: "+u)},o.uniqueSort=function(u){var y,b=[],_=0,C=0;if(a=!ee.sortStable,r=!ee.sortStable&&we.call(u,0),Tn.call(u,pe),a){for(;y=u[C++];)y===u[C]&&(_=b.push(C));for(;_--;)Zn.call(u,b[_],1)}return r=null,u},o.fn.uniqueSort=function(){return this.pushStack(o.uniqueSort(we.apply(this)))},(t=o.expr={cacheLength:50,createPseudo:We,match:on,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(u){return u[1]=u[1].replace(gt,tt),u[3]=(u[3]||u[4]||u[5]||"").replace(gt,tt),"~="===u[2]&&(u[3]=" "+u[3]+" "),u.slice(0,4)},CHILD:function(u){return u[1]=u[1].toLowerCase(),"nth"===u[1].slice(0,3)?(u[3]||fe.error(u[0]),u[4]=+(u[4]?u[5]+(u[6]||1):2*("even"===u[3]||"odd"===u[3])),u[5]=+(u[7]+u[8]||"odd"===u[3])):u[3]&&fe.error(u[0]),u},PSEUDO:function(u){var y,b=!u[6]&&u[2];return on.CHILD.test(u[0])?null:(u[3]?u[2]=u[4]||u[5]||"":b&&$t.test(b)&&(y=Xn(b,!0))&&(y=b.indexOf(")",b.length-y)-b.length)&&(u[0]=u[0].slice(0,y),u[2]=b.slice(0,y)),u.slice(0,3))}},filter:{TAG:function(u){var y=u.replace(gt,tt).toLowerCase();return"*"===u?function(){return!0}:function(b){return ue(b,y)}},CLASS:function(u){var y=J[u+" "];return y||(y=new RegExp("(^|"+oe+")"+u+"("+oe+"|$)"))&&J(u,function(b){return y.test("string"==typeof b.className&&b.className||void 0!==b.getAttribute&&b.getAttribute("class")||"")})},ATTR:function(u,y,b){return function(_){var C=fe.attr(_,u);return null==C?"!="===y:!y||(C+="","="===y?C===b:"!="===y?C!==b:"^="===y?b&&0===C.indexOf(b):"*="===y?b&&-1<C.indexOf(b):"$="===y?b&&C.slice(-b.length)===b:"~="===y?-1<(" "+C.replace(Xt," ")+" ").indexOf(b):"|="===y&&(C===b||C.slice(0,b.length+1)===b+"-"))}},CHILD:function(u,y,b,_,C){var F="nth"!==u.slice(0,3),M="last"!==u.slice(-4),B="of-type"===y;return 1===_&&0===C?function(H){return!!H.parentNode}:function(H,le,G){var U,ie,X,De,ze,Ne=F!==M?"nextSibling":"previousSibling",$e=H.parentNode,Le=B&&H.nodeName.toLowerCase(),vt=!G&&!B,be=!1;if($e){if(F){for(;Ne;){for(X=H;X=X[Ne];)if(B?ue(X,Le):1===X.nodeType)return!1;ze=Ne="only"===u&&!ze&&"nextSibling"}return!0}if(ze=[M?$e.firstChild:$e.lastChild],M&&vt){for(be=(De=(U=(ie=$e[T]||($e[T]={}))[u]||[])[0]===$&&U[1])&&U[2],X=De&&$e.childNodes[De];X=++De&&X&&X[Ne]||(be=De=0)||ze.pop();)if(1===X.nodeType&&++be&&X===H){ie[u]=[$,De,be];break}}else if(vt&&(be=De=(U=(ie=H[T]||(H[T]={}))[u]||[])[0]===$&&U[1]),!1===be)for(;(X=++De&&X&&X[Ne]||(be=De=0)||ze.pop())&&(!(B?ue(X,Le):1===X.nodeType)||!++be||(vt&&((ie=X[T]||(X[T]={}))[u]=[$,be]),X!==H)););return(be-=C)===_||be%_==0&&0<=be/_}}},PSEUDO:function(u,y){var b,_=t.pseudos[u]||t.setFilters[u.toLowerCase()]||fe.error("unsupported pseudo: "+u);return _[T]?_(y):1<_.length?(b=[u,u,"",y],t.setFilters.hasOwnProperty(u.toLowerCase())?We(function(C,F){for(var M,B=_(C,y),H=B.length;H--;)C[M=Pe.call(C,B[H])]=!(F[M]=B[H])}):function(C){return _(C,0,b)}):_}},pseudos:{not:We(function(u){var y=[],b=[],_=an(u.replace(Ae,"$1"));return _[T]?We(function(C,F,M,B){for(var H,le=_(C,null,B,[]),G=C.length;G--;)(H=le[G])&&(C[G]=!(F[G]=H))}):function(C,F,M){return y[0]=C,_(y,null,M,b),y[0]=null,!b.pop()}}),has:We(function(u){return function(y){return 0<fe(u,y).length}}),contains:We(function(u){return u=u.replace(gt,tt),function(y){return-1<(y.textContent||o.text(y)).indexOf(u)}}),lang:We(function(u){return bn.test(u||"")||fe.error("unsupported lang: "+u),u=u.replace(gt,tt).toLowerCase(),function(y){var b;do{if(b=v?y.lang:y.getAttribute("xml:lang")||y.getAttribute("lang"))return(b=b.toLowerCase())===u||0===b.indexOf(u+"-")}while((y=y.parentNode)&&1===y.nodeType);return!1}}),target:function(u){var y=R.location&&R.location.hash;return y&&y.slice(1)===u.id},root:function(u){return u===c},focus:function(u){return u===function(){try{return l.activeElement}catch(y){}}()&&l.hasFocus()&&!!(u.type||u.href||~u.tabIndex)},enabled:Vn(!1),disabled:Vn(!0),checked:function(u){return ue(u,"input")&&!!u.checked||ue(u,"option")&&!!u.selected},selected:function(u){return!0===u.selected},empty:function(u){for(u=u.firstChild;u;u=u.nextSibling)if(u.nodeType<6)return!1;return!0},parent:function(u){return!t.pseudos.empty(u)},header:function(u){return li.test(u.nodeName)},input:function(u){return _n.test(u.nodeName)},button:function(u){return ue(u,"input")&&"button"===u.type||ue(u,"button")},text:function(u){var y;return ue(u,"input")&&"text"===u.type&&(null==(y=u.getAttribute("type"))||"text"===y.toLowerCase())},first:Ct(function(){return[0]}),last:Ct(function(u,y){return[y-1]}),eq:Ct(function(u,y,b){return[b<0?b+y:b]}),even:Ct(function(u,y){for(var b=0;b<y;b+=2)u.push(b);return u}),odd:Ct(function(u,y){for(var b=1;b<y;b+=2)u.push(b);return u}),lt:Ct(function(u,y,b){var _;for(_=b<0?b+y:y<b?y:b;0<=--_;)u.push(_);return u}),gt:Ct(function(u,y,b){for(var _=b<0?b+y:b;++_<y;)u.push(_);return u})}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=lt(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=wn(e);function Ei(){}function Xn(u,y){var b,_,C,F,M,B,H,le=te[u+" "];if(le)return y?0:le.slice(0);for(M=u,B=[],H=t.preFilter;M;){for(F in b&&!(_=Be.exec(M))||(_&&(M=M.slice(_[0].length)||M),B.push(C=[])),b=!1,(_=qe.exec(M))&&(b=_.shift(),C.push({value:b,type:_[0].replace(Ae," ")}),M=M.slice(b.length)),t.filter)!(_=on[F].exec(M))||H[F]&&!(_=H[F](_))||(b=_.shift(),C.push({value:b,type:F,matches:_}),M=M.slice(b.length));if(!b)break}return y?M.length:M?fe.error(u):te(u,B).slice(0)}function ui(u){for(var y=0,b=u.length,_="";y<b;y++)_+=u[y].value;return _}function di(u,y,b){var _=y.dir,C=y.next,F=C||_,M=b&&"parentNode"===F,B=S++;return y.first?function(H,le,G){for(;H=H[_];)if(1===H.nodeType||M)return u(H,le,G);return!1}:function(H,le,G){var U,ie,X=[$,B];if(G){for(;H=H[_];)if((1===H.nodeType||M)&&u(H,le,G))return!0}else for(;H=H[_];)if(1===H.nodeType||M)if(ie=H[T]||(H[T]={}),C&&ue(H,C))H=H[_]||H;else{if((U=ie[F])&&U[0]===$&&U[1]===B)return X[2]=U[2];if((ie[F]=X)[2]=u(H,le,G))return!0}return!1}}function Un(u){return 1<u.length?function(y,b,_){for(var C=u.length;C--;)if(!u[C](y,b,_))return!1;return!0}:u[0]}function sn(u,y,b,_,C){for(var F,M=[],B=0,H=u.length,le=null!=y;B<H;B++)(F=u[B])&&(b&&!b(F,_,C)||(M.push(F),le&&y.push(B)));return M}function fi(u,y,b,_,C,F){return _&&!_[T]&&(_=fi(_)),C&&!C[T]&&(C=fi(C,F)),We(function(M,B,H,le){var G,U,ie,X,De=[],ze=[],Ne=B.length,$e=M||function(vt,be,ln){for(var yt=0,gi=be.length;yt<gi;yt++)fe(vt,be[yt],ln);return ln}(y||"*",H.nodeType?[H]:H,[]),Le=!u||!M&&y?$e:sn($e,De,u,H,le);if(b?b(Le,X=C||(M?u:Ne||_)?[]:B,H,le):X=Le,_)for(G=sn(X,ze),_(G,[],H,le),U=G.length;U--;)(ie=G[U])&&(X[ze[U]]=!(Le[ze[U]]=ie));if(M){if(C||u){if(C){for(G=[],U=X.length;U--;)(ie=X[U])&&G.push(Le[U]=ie);C(null,X=[],G,le)}for(U=X.length;U--;)(ie=X[U])&&-1<(G=C?Pe.call(M,ie):De[U])&&(M[G]=!(B[G]=ie))}}else X=sn(X===B?X.splice(Ne,X.length):X),C?C(null,B,X,le):w.apply(B,X)})}function hi(u){for(var y,b,_,C=u.length,F=t.relative[u[0].type],M=F||t.relative[" "],B=F?1:0,H=di(function(U){return U===y},M,!0),le=di(function(U){return-1<Pe.call(y,U)},M,!0),G=[function(U,ie,X){var De=!F&&(X||ie!=i)||((y=ie).nodeType?H(U,ie,X):le(U,ie,X));return y=null,De}];B<C;B++)if(b=t.relative[u[B].type])G=[di(Un(G),b)];else{if((b=t.filter[u[B].type].apply(null,u[B].matches))[T]){for(_=++B;_<C&&!t.relative[u[_].type];_++);return fi(1<B&&Un(G),1<B&&ui(u.slice(0,B-1).concat({value:" "===u[B-2].type?"*":""})).replace(Ae,"$1"),b,B<_&&hi(u.slice(B,_)),_<C&&hi(u=u.slice(_)),_<C&&ui(u))}G.push(b)}return Un(G)}function an(u,y){var b,_,C,F,M,B,H=[],le=[],G=ae[u+" "];if(!G){for(y||(y=Xn(u)),b=y.length;b--;)(G=hi(y[b]))[T]?H.push(G):le.push(G);(G=ae(u,(_=le,F=0<(C=H).length,M=0<_.length,B=function(U,ie,X,De,ze){var Ne,$e,Le,vt=0,be="0",ln=U&&[],yt=[],gi=i,Ai=U||M&&t.find.TAG("*",ze),Ci=$+=null==gi?1:Math.random()||.1,Di=Ai.length;for(ze&&(i=ie==l||ie||ze);be!==Di&&null!=(Ne=Ai[be]);be++){if(M&&Ne){for($e=0,ie||Ne.ownerDocument==l||(mt(Ne),X=!v);Le=_[$e++];)if(Le(Ne,ie||l,X)){w.call(De,Ne);break}ze&&($=Ci)}F&&((Ne=!Le&&Ne)&&vt--,U&&ln.push(Ne))}if(vt+=be,F&&be!==vt){for($e=0;Le=C[$e++];)Le(ln,yt,ie,X);if(U){if(0<vt)for(;be--;)ln[be]||yt[be]||(yt[be]=Cn.call(De));yt=sn(yt)}w.apply(De,yt),ze&&!U&&0<yt.length&&1<vt+C.length&&o.uniqueSort(De)}return ze&&($=Ci,i=gi),ln},F?We(B):B))).selector=u}return G}function pi(u,y,b,_){var C,F,M,B,H,le="function"==typeof u&&u,G=!_&&Xn(u=le.selector||u);if(b=b||[],1===G.length){if(2<(F=G[0]=G[0].slice(0)).length&&"ID"===(M=F[0]).type&&9===y.nodeType&&v&&t.relative[F[1].type]){if(!(y=(t.find.ID(M.matches[0].replace(gt,tt),y)||[])[0]))return b;le&&(y=y.parentNode),u=u.slice(F.shift().value.length)}for(C=on.needsContext.test(u)?0:F.length;C--&&!t.relative[B=(M=F[C]).type];)if((H=t.find[B])&&(_=H(M.matches[0].replace(gt,tt),Wn.test(F[0].type)&&xn(y.parentNode)||y))){if(F.splice(C,1),!(u=_.length&&ui(F)))return w.apply(b,_),b;break}}return(le||an(u,G))(_,y,!v,b,!y||Wn.test(u)&&xn(y.parentNode)||y),b}Ei.prototype=t.filters=t.pseudos,t.setFilters=new Ei,ee.sortStable=T.split("").sort(pe).join("")===T,mt(),ee.sortDetached=Pt(function(u){return 1&u.compareDocumentPosition(l.createElement("fieldset"))}),o.find=fe,o.expr[":"]=o.expr.pseudos,o.unique=o.uniqueSort,fe.compile=an,fe.select=pi,fe.setDocument=mt,fe.tokenize=Xn,fe.escape=o.escapeSelector,fe.getText=o.text,fe.isXML=o.isXMLDoc,fe.selectors=o.expr,fe.support=o.support,fe.uniqueSort=o.uniqueSort}();var Ue=function(e,t,i){for(var r=[],a=void 0!==i;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&o(e).is(i))break;r.push(e)}return r},Tt=function(e,t){for(var i=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&i.push(e);return i},kt=o.expr.match.needsContext,O=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Ht(e,t,i){return Q(t)?o.grep(e,function(r,a){return!!t.call(r,a,r)!==i}):t.nodeType?o.grep(e,function(r){return r===t!==i}):"string"!=typeof t?o.grep(e,function(r){return-1<Pe.call(t,r)!==i}):o.filter(t,e,i)}o.filter=function(e,t,i){var r=t[0];return i&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?o.find.matchesSelector(r,e)?[r]:[]:o.find.matches(e,o.grep(t,function(a){return 1===a.nodeType}))},o.fn.extend({find:function(e){var t,i,r=this.length,a=this;if("string"!=typeof e)return this.pushStack(o(e).filter(function(){for(t=0;t<r;t++)if(o.contains(a[t],this))return!0}));for(i=this.pushStack([]),t=0;t<r;t++)o.find(e,a[t],i);return 1<r?o.uniqueSort(i):i},filter:function(e){return this.pushStack(Ht(this,e||[],!1))},not:function(e){return this.pushStack(Ht(this,e||[],!0))},is:function(e){return!!Ht(this,"string"==typeof e&&kt.test(e)?o(e):e||[],!1).length}});var Ot,kn=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(o.fn.init=function(e,t,i){var r,a;if(!e)return this;if(i=i||Ot,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:kn.exec(e))||!r[1]&&t)return!t||t.jquery?(t||i).find(e):this.constructor(t).find(e);if(r[1]){if(o.merge(this,o.parseHTML(r[1],(t=t instanceof o?t[0]:t)&&t.nodeType?t.ownerDocument||t:K,!0)),O.test(r[1])&&o.isPlainObject(t))for(r in t)Q(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(a=K.getElementById(r[2]))&&(this[0]=a,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):Q(e)?void 0!==i.ready?i.ready(e):e(o):o.makeArray(e,this)}).prototype=o.fn,Ot=o(K);var rt=/^(?:parents|prev(?:Until|All))/,Rt={children:!0,contents:!0,next:!0,prev:!0};function Ye(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}o.fn.extend({has:function(e){var t=o(e,this),i=t.length;return this.filter(function(){for(var r=0;r<i;r++)if(o.contains(this,t[r]))return!0})},closest:function(e,t){var i,r=0,a=this.length,l=[],c="string"!=typeof e&&o(e);if(!kt.test(e))for(;r<a;r++)for(i=this[r];i&&i!==t;i=i.parentNode)if(i.nodeType<11&&(c?-1<c.index(i):1===i.nodeType&&o.find.matchesSelector(i,e))){l.push(i);break}return this.pushStack(1<l.length?o.uniqueSort(l):l)},index:function(e){return e?"string"==typeof e?Pe.call(o(e),this[0]):Pe.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(o.uniqueSort(o.merge(this.get(),o(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),o.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return Ue(e,"parentNode")},parentsUntil:function(e,t,i){return Ue(e,"parentNode",i)},next:function(e){return Ye(e,"nextSibling")},prev:function(e){return Ye(e,"previousSibling")},nextAll:function(e){return Ue(e,"nextSibling")},prevAll:function(e){return Ue(e,"previousSibling")},nextUntil:function(e,t,i){return Ue(e,"nextSibling",i)},prevUntil:function(e,t,i){return Ue(e,"previousSibling",i)},siblings:function(e){return Tt((e.parentNode||{}).firstChild,e)},children:function(e){return Tt(e.firstChild)},contents:function(e){return null!=e.contentDocument&&ge(e.contentDocument)?e.contentDocument:(ue(e,"template")&&(e=e.content||e),o.merge([],e.childNodes))}},function(e,t){o.fn[e]=function(i,r){var a=o.map(this,t,i);return"Until"!==e.slice(-5)&&(r=i),r&&"string"==typeof r&&(a=o.filter(r,a)),1<this.length&&(Rt[e]||o.uniqueSort(a),rt.test(e)&&a.reverse()),this.pushStack(a)}});var ut=/[^\x20\t\r\n\f]+/g;function W(e){return e}function St(e){throw e}function On(e,t,i,r){var a;try{e&&Q(a=e.promise)?a.call(e).done(t).fail(i):e&&Q(a=e.then)?a.call(e,t,i):t.apply(void 0,[e].slice(r))}catch(l){i.apply(void 0,[l])}}o.Callbacks=function(e){var i;e="string"==typeof e?(i={},o.each(e.match(ut)||[],function($,S){i[S]=!0}),i):o.extend({},e);var r,a,l,c,v=[],h=[],x=-1,w=function(){for(c=c||e.once,l=r=!0;h.length;x=-1)for(a=h.shift();++x<v.length;)!1===v[x].apply(a[0],a[1])&&e.stopOnFalse&&(x=v.length,a=!1);e.memory||(a=!1),r=!1,c&&(v=a?[]:"")},T={add:function(){return v&&(a&&!r&&(x=v.length-1,h.push(a)),function $(S){o.each(S,function(J,te){Q(te)?e.unique&&T.has(te)||v.push(te):te&&te.length&&"string"!==Ee(te)&&$(te)})}(arguments),a&&!r&&w()),this},remove:function(){return o.each(arguments,function($,S){for(var J;-1<(J=o.inArray(S,v,J));)v.splice(J,1),J<=x&&x--}),this},has:function($){return $?-1<o.inArray($,v):0<v.length},empty:function(){return v&&(v=[]),this},disable:function(){return c=h=[],v=a="",this},disabled:function(){return!v},lock:function(){return c=h=[],a||r||(v=a=""),this},locked:function(){return!!c},fireWith:function($,S){return c||(S=[$,(S=S||[]).slice?S.slice():S],h.push(S),r||w()),this},fire:function(){return T.fireWith(this,arguments),this},fired:function(){return!!l}};return T},o.extend({Deferred:function(e){var t=[["notify","progress",o.Callbacks("memory"),o.Callbacks("memory"),2],["resolve","done",o.Callbacks("once memory"),o.Callbacks("once memory"),0,"resolved"],["reject","fail",o.Callbacks("once memory"),o.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},catch:function(l){return r.then(null,l)},pipe:function(){var l=arguments;return o.Deferred(function(c){o.each(t,function(v,h){var x=Q(l[h[4]])&&l[h[4]];a[h[1]](function(){var w=x&&x.apply(this,arguments);w&&Q(w.promise)?w.promise().progress(c.notify).done(c.resolve).fail(c.reject):c[h[0]+"With"](this,x?[w]:arguments)})}),l=null}).promise()},then:function(l,c,v){var h=0;function x(w,T,$,S){return function(){var J=this,te=arguments,ae=function(){var pe,Ce;if(!(w<h)){if((pe=$.apply(J,te))===T.promise())throw new TypeError("Thenable self-resolution");Q(Ce=pe&&("object"==typeof pe||"function"==typeof pe)&&pe.then)?S?Ce.call(pe,x(h,T,W,S),x(h,T,St,S)):(h++,Ce.call(pe,x(h,T,W,S),x(h,T,St,S),x(h,T,W,T.notifyWith))):($!==W&&(J=void 0,te=[pe]),(S||T.resolveWith)(J,te))}},ke=S?ae:function(){try{ae()}catch(pe){o.Deferred.exceptionHook&&o.Deferred.exceptionHook(pe,ke.error),h<=w+1&&($!==St&&(J=void 0,te=[pe]),T.rejectWith(J,te))}};w?ke():(o.Deferred.getErrorHook?ke.error=o.Deferred.getErrorHook():o.Deferred.getStackHook&&(ke.error=o.Deferred.getStackHook()),R.setTimeout(ke))}}return o.Deferred(function(w){t[0][3].add(x(0,w,Q(v)?v:W,w.notifyWith)),t[1][3].add(x(0,w,Q(l)?l:W)),t[2][3].add(x(0,w,Q(c)?c:St))}).promise()},promise:function(l){return null!=l?o.extend(l,r):r}},a={};return o.each(t,function(l,c){var v=c[2],h=c[5];r[c[1]]=v.add,h&&v.add(function(){i=h},t[3-l][2].disable,t[3-l][3].disable,t[0][2].lock,t[0][3].lock),v.add(c[3].fire),a[c[0]]=function(){return a[c[0]+"With"](this===a?void 0:this,arguments),this},a[c[0]+"With"]=v.fireWith}),r.promise(a),e&&e.call(a,a),a},when:function(e){var t=arguments.length,i=t,r=Array(i),a=we.call(arguments),l=o.Deferred(),c=function(v){return function(h){r[v]=this,a[v]=1<arguments.length?we.call(arguments):h,--t||l.resolveWith(r,a)}};if(t<=1&&(On(e,l.done(c(i)).resolve,l.reject,!t),"pending"===l.state()||Q(a[i]&&a[i].then)))return l.then();for(;i--;)On(a[i],c(i),l.reject);return l.promise()}});var mi=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;o.Deferred.exceptionHook=function(e,t){R.console&&R.console.warn&&e&&mi.test(e.name)&&R.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},o.readyException=function(e){R.setTimeout(function(){throw e})};var Sn=o.Deferred();function wt(){K.removeEventListener("DOMContentLoaded",wt),R.removeEventListener("load",wt),o.ready()}o.fn.ready=function(e){return Sn.then(e).catch(function(t){o.readyException(t)}),this},o.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--o.readyWait:o.isReady)||(o.isReady=!0)!==e&&0<--o.readyWait||Sn.resolveWith(K,[o])}}),o.ready.then=Sn.then,"complete"===K.readyState||"loading"!==K.readyState&&!K.documentElement.doScroll?R.setTimeout(o.ready):(K.addEventListener("DOMContentLoaded",wt),R.addEventListener("load",wt));var ot=function(e,t,i,r,a,l,c){var v=0,h=e.length,x=null==i;if("object"===Ee(i))for(v in a=!0,i)ot(e,t,v,i[v],!0,l,c);else if(void 0!==r&&(a=!0,Q(r)||(c=!0),x&&(c?(t.call(e,r),t=null):(x=t,t=function(w,T,$){return x.call(o(w),$)})),t))for(;v<h;v++)t(e[v],i,c?r:r.call(e[v],v,t(e[v],i)));return a?e:x?t.call(e):h?t(e[0],i):l},Yt=/^-ms-/,Ft=/-([a-z])/g;function vi(e,t){return t.toUpperCase()}function dt(e){return e.replace(Yt,"ms-").replace(Ft,vi)}var Qt=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Kt(){this.expando=o.expando+Kt.uid++}Kt.uid=1,Kt.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Qt(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,i){var r,a=this.cache(e);if("string"==typeof t)a[dt(t)]=i;else for(r in t)a[dt(r)]=t[r];return a},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][dt(t)]},access:function(e,t,i){return void 0===t||t&&"string"==typeof t&&void 0===i?this.get(e,t):(this.set(e,t,i),void 0!==i?i:t)},remove:function(e,t){var i,r=e[this.expando];if(void 0!==r){if(void 0!==t)for((i=(t=Array.isArray(t)?t.map(dt):(t=dt(t))in r?[t]:t.match(ut)||[]).length);i--;)delete r[t[i]];(void 0===t||o.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!o.isEmptyObject(t)}};var z=new Kt,Me=new Kt,Dn=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,fn=/[A-Z]/g;function st(e,t,i){var r,a;if(void 0===i&&1===e.nodeType)if(r="data-"+t.replace(fn,"-$&").toLowerCase(),"string"==typeof(i=e.getAttribute(r))){try{i="true"===(a=i)||"false"!==a&&("null"===a?null:a===+a+""?+a:Dn.test(a)?JSON.parse(a):a)}catch(l){}Me.set(e,t,i)}else i=void 0;return i}o.extend({hasData:function(e){return Me.hasData(e)||z.hasData(e)},data:function(e,t,i){return Me.access(e,t,i)},removeData:function(e,t){Me.remove(e,t)},_data:function(e,t,i){return z.access(e,t,i)},_removeData:function(e,t){z.remove(e,t)}}),o.fn.extend({data:function(e,t){var i,r,a,l=this[0],c=l&&l.attributes;if(void 0===e){if(this.length&&(a=Me.get(l),1===l.nodeType&&!z.get(l,"hasDataAttrs"))){for(i=c.length;i--;)c[i]&&0===(r=c[i].name).indexOf("data-")&&(r=dt(r.slice(5)),st(l,r,a[r]));z.set(l,"hasDataAttrs",!0)}return a}return"object"==typeof e?this.each(function(){Me.set(this,e)}):ot(this,function(v){var h;if(l&&void 0===v)return void 0!==(h=Me.get(l,e))||void 0!==(h=st(l,e))?h:void 0;this.each(function(){Me.set(this,e,v)})},null,t,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){Me.remove(this,e)})}}),o.extend({queue:function(e,t,i){var r;if(e)return r=z.get(e,t=(t||"fx")+"queue"),i&&(!r||Array.isArray(i)?r=z.access(e,t,o.makeArray(i)):r.push(i)),r||[]},dequeue:function(e,t){var i=o.queue(e,t=t||"fx"),r=i.length,a=i.shift(),l=o._queueHooks(e,t);"inprogress"===a&&(a=i.shift(),r--),a&&("fx"===t&&i.unshift("inprogress"),delete l.stop,a.call(e,function(){o.dequeue(e,t)},l)),!r&&l&&l.empty.fire()},_queueHooks:function(e,t){var i=t+"queueHooks";return z.get(e,i)||z.access(e,i,{empty:o.Callbacks("once memory").add(function(){z.remove(e,[t+"queue",i])})})}}),o.fn.extend({queue:function(e,t){var i=2;return"string"!=typeof e&&(t=e,e="fx",i--),arguments.length<i?o.queue(this[0],e):void 0===t?this:this.each(function(){var r=o.queue(this,e,t);o._queueHooks(this,e),"fx"===e&&"inprogress"!==r[0]&&o.dequeue(this,e)})},dequeue:function(e){return this.each(function(){o.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var i,r=1,a=o.Deferred(),l=this,c=this.length,v=function(){--r||a.resolveWith(l,[l])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";c--;)(i=z.get(l[c],e+"queueHooks"))&&i.empty&&(r++,i.empty.add(v));return v(),a.promise(t)}});var Ln=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,xt=new RegExp("^(?:([+-])=|)("+Ln+")([a-z%]*)$","i"),_e=["Top","Right","Bottom","Left"],Oe=K.documentElement,Je=function(e){return o.contains(e.ownerDocument,e)},ei={composed:!0};Oe.getRootNode&&(Je=function(e){return o.contains(e.ownerDocument,e)||e.getRootNode(ei)===e.ownerDocument});var Gt=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&Je(e)&&"none"===o.css(e,"display")},Nn={};function $n(e,t){for(var i,r,a,l,c,v,h,x=[],w=0,T=e.length;w<T;w++)(r=e[w]).style&&(i=r.style.display,t?("none"===i&&(x[w]=z.get(r,"display")||null,x[w]||(r.style.display="")),""===r.style.display&&Gt(r)&&(x[w]=(h=c=l=void 0,c=(a=r).ownerDocument,(h=Nn[v=a.nodeName])||(l=c.body.appendChild(c.createElement(v)),h=o.css(l,"display"),l.parentNode.removeChild(l),"none"===h&&(h="block"),Nn[v]=h)))):"none"!==i&&(x[w]="none",z.set(r,"display",i)));for(w=0;w<T;w++)null!=x[w]&&(e[w].style.display=x[w]);return e}o.fn.extend({show:function(){return $n(this,!0)},hide:function(){return $n(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Gt(this)?o(this).show():o(this).hide()})}});var Qe,Jt,Bt=/^(?:checkbox|radio)$/i,hn=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,qt=/^$|^module$|\/(?:java|ecma)script/i;Qe=K.createDocumentFragment().appendChild(K.createElement("div")),(Jt=K.createElement("input")).setAttribute("type","radio"),Jt.setAttribute("checked","checked"),Jt.setAttribute("name","t"),Qe.appendChild(Jt),ee.checkClone=Qe.cloneNode(!0).cloneNode(!0).lastChild.checked,Qe.innerHTML="<textarea>x</textarea>",ee.noCloneChecked=!!Qe.cloneNode(!0).lastChild.defaultValue,Qe.innerHTML="<option></option>",ee.option=!!Qe.lastChild;var He={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Se(e,t){var i;return i=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&ue(e,t)?o.merge([e],i):i}function pn(e,t){for(var i=0,r=e.length;i<r;i++)z.set(e[i],"globalEval",!t||z.get(t[i],"globalEval"))}He.tbody=He.tfoot=He.colgroup=He.caption=He.thead,He.th=He.td,ee.option||(He.optgroup=He.option=[1,"<select multiple='multiple'>","</select>"]);var Pn=/<|&#?\w+;/;function ti(e,t,i,r,a){for(var l,c,v,h,x,w,T=t.createDocumentFragment(),$=[],S=0,J=e.length;S<J;S++)if((l=e[S])||0===l)if("object"===Ee(l))o.merge($,l.nodeType?[l]:l);else if(Pn.test(l)){for(c=c||T.appendChild(t.createElement("div")),v=(hn.exec(l)||["",""])[1].toLowerCase(),c.innerHTML=(h=He[v]||He._default)[1]+o.htmlPrefilter(l)+h[2],w=h[0];w--;)c=c.lastChild;o.merge($,c.childNodes),(c=T.firstChild).textContent=""}else $.push(t.createTextNode(l));for(T.textContent="",S=0;l=$[S++];)if(r&&-1<o.inArray(l,r))a&&a.push(l);else if(x=Je(l),c=Se(T.appendChild(l),"script"),x&&pn(c),i)for(w=0;l=c[w++];)qt.test(l.type||"")&&i.push(l);return T}var ni=/^([^.]*)(?:\.(.+)|)/;function Dt(){return!0}function Ze(){return!1}function ft(e,t,i,r,a,l){var c,v;if("object"==typeof t){for(v in"string"!=typeof i&&(r=r||i,i=void 0),t)ft(e,v,i,r,t[v],l);return e}if(null==r&&null==a?(a=i,r=i=void 0):null==a&&("string"==typeof i?(a=r,r=void 0):(a=r,r=i,i=void 0)),!1===a)a=Ze;else if(!a)return e;return 1===l&&(c=a,(a=function(h){return o().off(h),c.apply(this,arguments)}).guid=c.guid||(c.guid=o.guid++)),e.each(function(){o.event.add(this,t,a,r,i)})}function gn(e,t,i){i?(z.set(e,t,!1),o.event.add(e,t,{namespace:!1,handler:function(r){var a,l=z.get(this,t);if(1&r.isTrigger&&this[t]){if(l)(o.event.special[t]||{}).delegateType&&r.stopPropagation();else if(l=we.call(arguments),z.set(this,t,l),this[t](),a=z.get(this,t),z.set(this,t,!1),l!==a)return r.stopImmediatePropagation(),r.preventDefault(),a}else l&&(z.set(this,t,o.event.trigger(l[0],l.slice(1),this)),r.stopPropagation(),r.isImmediatePropagationStopped=Dt)}})):void 0===z.get(e,t)&&o.event.add(e,t,Dt)}o.event={global:{},add:function(e,t,i,r,a){var l,c,v,h,x,w,T,$,S,J,te,ae=z.get(e);if(Qt(e))for(i.handler&&(i=(l=i).handler,a=l.selector),a&&o.find.matchesSelector(Oe,a),i.guid||(i.guid=o.guid++),(h=ae.events)||(h=ae.events=Object.create(null)),(c=ae.handle)||(c=ae.handle=function(ke){return void 0!==o&&o.event.triggered!==ke.type?o.event.dispatch.apply(e,arguments):void 0}),x=(t=(t||"").match(ut)||[""]).length;x--;)S=te=(v=ni.exec(t[x])||[])[1],J=(v[2]||"").split(".").sort(),S&&(T=o.event.special[S]||{},T=o.event.special[S=(a?T.delegateType:T.bindType)||S]||{},w=o.extend({type:S,origType:te,data:r,handler:i,guid:i.guid,selector:a,needsContext:a&&o.expr.match.needsContext.test(a),namespace:J.join(".")},l),($=h[S])||(($=h[S]=[]).delegateCount=0,T.setup&&!1!==T.setup.call(e,r,J,c)||e.addEventListener&&e.addEventListener(S,c)),T.add&&(T.add.call(e,w),w.handler.guid||(w.handler.guid=i.guid)),a?$.splice($.delegateCount++,0,w):$.push(w),o.event.global[S]=!0)},remove:function(e,t,i,r,a){var l,c,v,h,x,w,T,$,S,J,te,ae=z.hasData(e)&&z.get(e);if(ae&&(h=ae.events)){for(x=(t=(t||"").match(ut)||[""]).length;x--;)if(S=te=(v=ni.exec(t[x])||[])[1],J=(v[2]||"").split(".").sort(),S){for(T=o.event.special[S]||{},$=h[S=(r?T.delegateType:T.bindType)||S]||[],v=v[2]&&new RegExp("(^|\\.)"+J.join("\\.(?:.*\\.|)")+"(\\.|$)"),c=l=$.length;l--;)w=$[l],!a&&te!==w.origType||i&&i.guid!==w.guid||v&&!v.test(w.namespace)||r&&r!==w.selector&&("**"!==r||!w.selector)||($.splice(l,1),w.selector&&$.delegateCount--,T.remove&&T.remove.call(e,w));c&&!$.length&&(T.teardown&&!1!==T.teardown.call(e,J,ae.handle)||o.removeEvent(e,S,ae.handle),delete h[S])}else for(S in h)o.event.remove(e,S+t[x],i,r,!0);o.isEmptyObject(h)&&z.remove(e,"handle events")}},dispatch:function(e){var t,i,r,a,l,c,v=new Array(arguments.length),h=o.event.fix(e),x=(z.get(this,"events")||Object.create(null))[h.type]||[],w=o.event.special[h.type]||{};for(v[0]=h,t=1;t<arguments.length;t++)v[t]=arguments[t];if(h.delegateTarget=this,!w.preDispatch||!1!==w.preDispatch.call(this,h)){for(c=o.event.handlers.call(this,h,x),t=0;(a=c[t++])&&!h.isPropagationStopped();)for(h.currentTarget=a.elem,i=0;(l=a.handlers[i++])&&!h.isImmediatePropagationStopped();)h.rnamespace&&!1!==l.namespace&&!h.rnamespace.test(l.namespace)||(h.handleObj=l,h.data=l.data,void 0!==(r=((o.event.special[l.origType]||{}).handle||l.handler).apply(a.elem,v))&&!1===(h.result=r)&&(h.preventDefault(),h.stopPropagation()));return w.postDispatch&&w.postDispatch.call(this,h),h.result}},handlers:function(e,t){var i,r,a,l,c,v=[],h=t.delegateCount,x=e.target;if(h&&x.nodeType&&!("click"===e.type&&1<=e.button))for(;x!==this;x=x.parentNode||this)if(1===x.nodeType&&("click"!==e.type||!0!==x.disabled)){for(l=[],c={},i=0;i<h;i++)void 0===c[a=(r=t[i]).selector+" "]&&(c[a]=r.needsContext?-1<o(a,this).index(x):o.find(a,this,null,[x]).length),c[a]&&l.push(r);l.length&&v.push({elem:x,handlers:l})}return x=this,h<t.length&&v.push({elem:x,handlers:t.slice(h)}),v},addProp:function(e,t){Object.defineProperty(o.Event.prototype,e,{enumerable:!0,configurable:!0,get:Q(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(i){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:i})}})},fix:function(e){return e[o.expando]?e:new o.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Bt.test(t.type)&&t.click&&ue(t,"input")&&gn(t,"click",!0),!1},trigger:function(e){var t=this||e;return Bt.test(t.type)&&t.click&&ue(t,"input")&&gn(t,"click"),!0},_default:function(e){var t=e.target;return Bt.test(t.type)&&t.click&&ue(t,"input")&&z.get(t,"click")||ue(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},o.removeEvent=function(e,t,i){e.removeEventListener&&e.removeEventListener(t,i)},o.Event=function(e,t){if(!(this instanceof o.Event))return new o.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Dt:Ze,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&o.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[o.expando]=!0},o.Event.prototype={constructor:o.Event,isDefaultPrevented:Ze,isPropagationStopped:Ze,isImmediatePropagationStopped:Ze,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Dt,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Dt,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Dt,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},o.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},o.event.addProp),o.each({focus:"focusin",blur:"focusout"},function(e,t){function i(r){if(K.documentMode){var a=z.get(this,"handle"),l=o.event.fix(r);l.type="focusin"===r.type?"focus":"blur",l.isSimulated=!0,a(r),l.target===l.currentTarget&&a(l)}else o.event.simulate(t,r.target,o.event.fix(r))}o.event.special[e]={setup:function(){var r;if(gn(this,e,!0),!K.documentMode)return!1;(r=z.get(this,t))||this.addEventListener(t,i),z.set(this,t,(r||0)+1)},trigger:function(){return gn(this,e),!0},teardown:function(){var r;if(!K.documentMode)return!1;(r=z.get(this,t)-1)?z.set(this,t,r):(this.removeEventListener(t,i),z.remove(this,t))},_default:function(r){return z.get(r.target,e)},delegateType:t},o.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,a=K.documentMode?this:r,l=z.get(a,t);l||(K.documentMode?this.addEventListener(t,i):r.addEventListener(e,i,!0)),z.set(a,t,(l||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,a=K.documentMode?this:r,l=z.get(a,t)-1;l?z.set(a,t,l):(K.documentMode?this.removeEventListener(t,i):r.removeEventListener(e,i,!0),z.remove(a,t))}}}),o.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){o.event.special[e]={delegateType:t,bindType:t,handle:function(i){var r,a=i.relatedTarget,l=i.handleObj;return a&&(a===this||o.contains(this,a))||(i.type=l.origType,r=l.handler.apply(this,arguments),i.type=t),r}}}),o.fn.extend({on:function(e,t,i,r){return ft(this,e,t,i,r)},one:function(e,t,i,r){return ft(this,e,t,i,r,1)},off:function(e,t,i){var r,a;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,o(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(a in e)this.off(a,t,e[a]);return this}return!1!==t&&"function"!=typeof t||(i=t,t=void 0),!1===i&&(i=Ze),this.each(function(){o.event.remove(this,e,i,t)})}});var yi=/<script|<style|<link/i,jn=/checked\s*(?:[^=]|=\s*.checked.)/i,bi=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function ii(e,t){return ue(e,"table")&&ue(11!==t.nodeType?t:t.firstChild,"tr")&&o(e).children("tbody")[0]||e}function mn(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Wt(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Et(e,t){var i,r,a,l,c,v;if(1===t.nodeType){if(z.hasData(e)&&(v=z.get(e).events))for(a in z.remove(t,"handle events"),v)for(i=0,r=v[a].length;i<r;i++)o.event.add(t,a,v[a][i]);Me.hasData(e)&&(l=Me.access(e),c=o.extend({},l),Me.set(t,c))}}function zt(e,t,i,r){t=nt(t);var a,l,c,v,h,x,w=0,T=e.length,$=T-1,S=t[0],J=Q(S);if(J||1<T&&"string"==typeof S&&!ee.checkClone&&jn.test(S))return e.each(function(te){var ae=e.eq(te);J&&(t[0]=S.call(this,te,ae.html())),zt(ae,t,i,r)});if(T&&(l=(a=ti(t,e[0].ownerDocument,!1,e,r)).firstChild,1===a.childNodes.length&&(a=l),l||r)){for(v=(c=o.map(Se(a,"script"),mn)).length;w<T;w++)h=a,w!==$&&(h=o.clone(h,!0,!0),v&&o.merge(c,Se(h,"script"))),i.call(e[w],h,w);if(v)for(x=c[c.length-1].ownerDocument,o.map(c,Wt),w=0;w<v;w++)qt.test((h=c[w]).type||"")&&!z.access(h,"globalEval")&&o.contains(x,h)&&(h.src&&"module"!==(h.type||"").toLowerCase()?o._evalUrl&&!h.noModule&&o._evalUrl(h.src,{nonce:h.nonce||h.getAttribute("nonce")},x):Ie(h.textContent.replace(bi,""),h,x))}return e}function Zt(e,t,i){for(var r,a=t?o.filter(t,e):e,l=0;null!=(r=a[l]);l++)i||1!==r.nodeType||o.cleanData(Se(r)),r.parentNode&&(i&&Je(r)&&pn(Se(r,"script")),r.parentNode.removeChild(r));return e}o.extend({htmlPrefilter:function(e){return e},clone:function(e,t,i){var r,a,l,c,v,h,x,w=e.cloneNode(!0),T=Je(e);if(!(ee.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||o.isXMLDoc(e)))for(c=Se(w),r=0,a=(l=Se(e)).length;r<a;r++)v=l[r],"input"===(x=(h=c[r]).nodeName.toLowerCase())&&Bt.test(v.type)?h.checked=v.checked:"input"!==x&&"textarea"!==x||(h.defaultValue=v.defaultValue);if(t)if(i)for(l=l||Se(e),c=c||Se(w),r=0,a=l.length;r<a;r++)Et(l[r],c[r]);else Et(e,w);return 0<(c=Se(w,"script")).length&&pn(c,!T&&Se(e,"script")),w},cleanData:function(e){for(var t,i,r,a=o.event.special,l=0;void 0!==(i=e[l]);l++)if(Qt(i)){if(t=i[z.expando]){if(t.events)for(r in t.events)a[r]?o.event.remove(i,r):o.removeEvent(i,r,t.handle);i[z.expando]=void 0}i[Me.expando]&&(i[Me.expando]=void 0)}}}),o.fn.extend({detach:function(e){return Zt(this,e,!0)},remove:function(e){return Zt(this,e)},text:function(e){return ot(this,function(t){return void 0===t?o.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,e,arguments.length)},append:function(){return zt(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||ii(this,e).appendChild(e)})},prepend:function(){return zt(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=ii(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return zt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return zt(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(o.cleanData(Se(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return o.clone(this,e,t)})},html:function(e){return ot(this,function(t){var i=this[0]||{},r=0,a=this.length;if(void 0===t&&1===i.nodeType)return i.innerHTML;if("string"==typeof t&&!yi.test(t)&&!He[(hn.exec(t)||["",""])[1].toLowerCase()]){t=o.htmlPrefilter(t);try{for(;r<a;r++)1===(i=this[r]||{}).nodeType&&(o.cleanData(Se(i,!1)),i.innerHTML=t);i=0}catch(l){}}i&&this.empty().append(t)},null,e,arguments.length)},replaceWith:function(){var e=[];return zt(this,arguments,function(t){var i=this.parentNode;o.inArray(this,e)<0&&(o.cleanData(Se(this)),i&&i.replaceChild(t,this))},e)}}),o.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){o.fn[e]=function(i){for(var r,a=[],l=o(i),c=l.length-1,v=0;v<=c;v++)r=v===c?this:this.clone(!0),o(l[v])[t](r),_t.apply(a,r.get());return this.pushStack(a)}});var In=new RegExp("^("+Ln+")(?!px)[a-z%]+$","i"),Mn=/^--/,ht=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=R),t.getComputedStyle(e)},vn=function(e,t,i){var r,a,l={};for(a in t)l[a]=e.style[a],e.style[a]=t[a];for(a in r=i.call(e),t)e.style[a]=l[a];return r},Lt=new RegExp(_e.join("|"),"i");function et(e,t,i){var r,a,l,c,v=Mn.test(t),h=e.style;return(i=i||ht(e))&&(c=i.getPropertyValue(t)||i[t],v&&c&&(c=c.replace(Ae,"$1")||void 0),""!==c||Je(e)||(c=o.style(e,t)),!ee.pixelBoxStyles()&&In.test(c)&&Lt.test(t)&&(r=h.width,a=h.minWidth,l=h.maxWidth,h.minWidth=h.maxWidth=h.width=c,c=i.width,h.width=r,h.minWidth=a,h.maxWidth=l)),void 0!==c?c+"":c}function ri(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(x){h.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",x.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Oe.appendChild(h).appendChild(x);var w=R.getComputedStyle(x);i="1%"!==w.top,v=12===t(w.marginLeft),x.style.right="60%",l=36===t(w.right),r=36===t(w.width),x.style.position="absolute",a=12===t(x.offsetWidth/3),Oe.removeChild(h),x=null}}function t(w){return Math.round(parseFloat(w))}var i,r,a,l,c,v,h=K.createElement("div"),x=K.createElement("div");x.style&&(x.style.backgroundClip="content-box",x.cloneNode(!0).style.backgroundClip="",ee.clearCloneStyle="content-box"===x.style.backgroundClip,o.extend(ee,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),l},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),v},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var w,T,$,S;return null==c&&(w=K.createElement("table"),T=K.createElement("tr"),$=K.createElement("div"),w.style.cssText="position:absolute;left:-11111px;border-collapse:separate",T.style.cssText="box-sizing:content-box;border:1px solid",T.style.height="1px",$.style.height="9px",$.style.display="block",Oe.appendChild(w).appendChild(T).appendChild($),S=R.getComputedStyle(T),c=parseInt(S.height,10)+parseInt(S.borderTopWidth,10)+parseInt(S.borderBottomWidth,10)===T.offsetHeight,Oe.removeChild(w)),c}}))}();var Hn=["Webkit","Moz","ms"],Rn=K.createElement("div").style,oi={};function si(e){return o.cssProps[e]||oi[e]||(e in Rn?e:oi[e]=function(i){for(var r=i[0].toUpperCase()+i.slice(1),a=Hn.length;a--;)if((i=Hn[a]+r)in Rn)return i}(e)||e)}var Vt,yn,Fn=/^(none|table(?!-c[ea]).+)/,ai={position:"absolute",visibility:"hidden",display:"block"},Nt={letterSpacing:"0",fontWeight:"400"};function pt(e,t,i){var r=xt.exec(t);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):t}function Bn(e,t,i,r,a,l){var c="width"===t?1:0,v=0,h=0,x=0;if(i===(r?"border":"content"))return 0;for(;c<4;c+=2)"margin"===i&&(x+=o.css(e,i+_e[c],!0,a)),r?("content"===i&&(h-=o.css(e,"padding"+_e[c],!0,a)),"margin"!==i&&(h-=o.css(e,"border"+_e[c]+"Width",!0,a))):(h+=o.css(e,"padding"+_e[c],!0,a),"padding"!==i?h+=o.css(e,"border"+_e[c]+"Width",!0,a):v+=o.css(e,"border"+_e[c]+"Width",!0,a));return!r&&0<=l&&(h+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-l-h-v-.5))||0),h+x}function en(e,t,i){var r=ht(e),a=(!ee.boxSizingReliable()||i)&&"border-box"===o.css(e,"boxSizing",!1,r),l=a,c=et(e,t,r),v="offset"+t[0].toUpperCase()+t.slice(1);if(In.test(c)){if(!i)return c;c="auto"}return(!ee.boxSizingReliable()&&a||!ee.reliableTrDimensions()&&ue(e,"tr")||"auto"===c||!parseFloat(c)&&"inline"===o.css(e,"display",!1,r))&&e.getClientRects().length&&(a="border-box"===o.css(e,"boxSizing",!1,r),(l=v in e)&&(c=e[v])),(c=parseFloat(c)||0)+Bn(e,t,i||(a?"border":"content"),l,r,c)+"px"}o.extend({cssHooks:{opacity:{get:function(e,t){if(t){var i=et(e,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,i,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,l,c,v=dt(t),h=Mn.test(t),x=e.style;if(h||(t=si(v)),c=o.cssHooks[t]||o.cssHooks[v],void 0===i)return c&&"get"in c&&void 0!==(a=c.get(e,!1,r))?a:x[t];"string"==(l=typeof i)&&(a=xt.exec(i))&&a[1]&&(i=function(w,T,$,S){var J,te,ae=20,ke=function(){return o.css(w,T,"")},pe=ke(),Ce=$&&$[3]||(o.cssNumber[T]?"":"px"),ye=w.nodeType&&(o.cssNumber[T]||"px"!==Ce&&+pe)&&xt.exec(o.css(w,T));if(ye&&ye[3]!==Ce){for(Ce=Ce||ye[3],ye=+(pe/=2)||1;ae--;)o.style(w,T,ye+Ce),(1-te)*(1-(te=ke()/pe||.5))<=0&&(ae=0),ye/=te;o.style(w,T,(ye*=2)+Ce),$=$||[]}return $&&(ye=+ye||+pe||0,J=$[1]?ye+($[1]+1)*$[2]:+$[2]),J}(e,t,a),l="number"),null!=i&&i==i&&("number"!==l||h||(i+=a&&a[3]||(o.cssNumber[v]?"":"px")),ee.clearCloneStyle||""!==i||0!==t.indexOf("background")||(x[t]="inherit"),c&&"set"in c&&void 0===(i=c.set(e,i,r))||(h?x.setProperty(t,i):x[t]=i))}},css:function(e,t,i,r){var a,l,c,v=dt(t);return Mn.test(t)||(t=si(v)),(c=o.cssHooks[t]||o.cssHooks[v])&&"get"in c&&(a=c.get(e,!0,i)),void 0===a&&(a=et(e,t,r)),"normal"===a&&t in Nt&&(a=Nt[t]),""===i||i?(l=parseFloat(a),!0===i||isFinite(l)?l||0:a):a}}),o.each(["height","width"],function(e,t){o.cssHooks[t]={get:function(i,r,a){if(r)return!Fn.test(o.css(i,"display"))||i.getClientRects().length&&i.getBoundingClientRect().width?en(i,t,a):vn(i,ai,function(){return en(i,t,a)})},set:function(i,r,a){var l,c=ht(i),v=!ee.scrollboxSize()&&"absolute"===c.position,h=(v||a)&&"border-box"===o.css(i,"boxSizing",!1,c),x=a?Bn(i,t,a,h,c):0;return h&&v&&(x-=Math.ceil(i["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(c[t])-Bn(i,t,"border",!1,c)-.5)),x&&(l=xt.exec(r))&&"px"!==(l[3]||"px")&&(i.style[t]=r,r=o.css(i,t)),pt(0,r,x)}}}),o.cssHooks.marginLeft=ri(ee.reliableMarginLeft,function(e,t){if(t)return(parseFloat(et(e,"marginLeft"))||e.getBoundingClientRect().left-vn(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),o.each({margin:"",padding:"",border:"Width"},function(e,t){o.cssHooks[e+t]={expand:function(i){for(var r=0,a={},l="string"==typeof i?i.split(" "):[i];r<4;r++)a[e+_e[r]+t]=l[r]||l[r-2]||l[0];return a}},"margin"!==e&&(o.cssHooks[e+t].set=pt)}),o.fn.extend({css:function(e,t){return ot(this,function(i,r,a){var l,c,v={},h=0;if(Array.isArray(r)){for(l=ht(i),c=r.length;h<c;h++)v[r[h]]=o.css(i,r[h],!1,l);return v}return void 0!==a?o.style(i,r,a):o.css(i,r)},e,t,1<arguments.length)}}),o.fn.delay=function(e,t){return e=o.fx&&o.fx.speeds[e]||e,this.queue(t=t||"fx",function(i,r){var a=R.setTimeout(i,e);r.stop=function(){R.clearTimeout(a)}})},Vt=K.createElement("input"),yn=K.createElement("select").appendChild(K.createElement("option")),Vt.type="checkbox",ee.checkOn=""!==Vt.value,ee.optSelected=yn.selected,(Vt=K.createElement("input")).value="t",Vt.type="radio",ee.radioValue="t"===Vt.value;var qn,tn=o.expr.attrHandle;o.fn.extend({attr:function(e,t){return ot(this,o.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){o.removeAttr(this,e)})}}),o.extend({attr:function(e,t,i){var r,a,l=e.nodeType;if(3!==l&&8!==l&&2!==l)return void 0===e.getAttribute?o.prop(e,t,i):(1===l&&o.isXMLDoc(e)||(a=o.attrHooks[t.toLowerCase()]||(o.expr.match.bool.test(t)?qn:void 0)),void 0!==i?null===i?void o.removeAttr(e,t):a&&"set"in a&&void 0!==(r=a.set(e,i,t))?r:(e.setAttribute(t,i+""),i):a&&"get"in a&&null!==(r=a.get(e,t))?r:null==(r=o.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!ee.radioValue&&"radio"===t&&ue(e,"input")){var i=e.value;return e.setAttribute("type",t),i&&(e.value=i),t}}}},removeAttr:function(e,t){var i,r=0,a=t&&t.match(ut);if(a&&1===e.nodeType)for(;i=a[r++];)e.removeAttribute(i)}}),qn={set:function(e,t,i){return!1===t?o.removeAttr(e,i):e.setAttribute(i,i),i}},o.each(o.expr.match.bool.source.match(/\w+/g),function(e,t){var i=tn[t]||o.find.attr;tn[t]=function(r,a,l){var c,v,h=a.toLowerCase();return l||(v=tn[h],tn[h]=c,c=null!=i(r,a,l)?h:null,tn[h]=v),c}});var d=/^(?:input|select|textarea|button)$/i,f=/^(?:a|area)$/i;function m(e){return(e.match(ut)||[]).join(" ")}function E(e){return e.getAttribute&&e.getAttribute("class")||""}function A(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(ut)||[]}o.fn.extend({prop:function(e,t){return ot(this,o.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[o.propFix[e]||e]})}}),o.extend({prop:function(e,t,i){var r,a,l=e.nodeType;if(3!==l&&8!==l&&2!==l)return 1===l&&o.isXMLDoc(e)||(a=o.propHooks[t=o.propFix[t]||t]),void 0!==i?a&&"set"in a&&void 0!==(r=a.set(e,i,t))?r:e[t]=i:a&&"get"in a&&null!==(r=a.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=o.find.attr(e,"tabindex");return t?parseInt(t,10):d.test(e.nodeName)||f.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),ee.optSelected||(o.propHooks.selected={get:function(e){return null},set:function(e){}}),o.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){o.propFix[this.toLowerCase()]=this}),o.fn.extend({addClass:function(e){var t,i,r,a,l,c;return Q(e)?this.each(function(v){o(this).addClass(e.call(this,v,E(this)))}):(t=A(e)).length?this.each(function(){if(r=E(this),i=1===this.nodeType&&" "+m(r)+" "){for(l=0;l<t.length;l++)i.indexOf(" "+(a=t[l])+" ")<0&&(i+=a+" ");c=m(i),r!==c&&this.setAttribute("class",c)}}):this},removeClass:function(e){var t,i,r,a,l,c;return Q(e)?this.each(function(v){o(this).removeClass(e.call(this,v,E(this)))}):arguments.length?(t=A(e)).length?this.each(function(){if(r=E(this),i=1===this.nodeType&&" "+m(r)+" "){for(l=0;l<t.length;l++)for(a=t[l];-1<i.indexOf(" "+a+" ");)i=i.replace(" "+a+" "," ");c=m(i),r!==c&&this.setAttribute("class",c)}}):this:this.attr("class","")},toggleClass:function(e,t){var i,r,a,l,c=typeof e,v="string"===c||Array.isArray(e);return Q(e)?this.each(function(h){o(this).toggleClass(e.call(this,h,E(this),t),t)}):"boolean"==typeof t&&v?t?this.addClass(e):this.removeClass(e):(i=A(e),this.each(function(){if(v)for(l=o(this),a=0;a<i.length;a++)l.hasClass(r=i[a])?l.removeClass(r):l.addClass(r);else void 0!==e&&"boolean"!==c||((r=E(this))&&z.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":z.get(this,"__className__")||""))}))},hasClass:function(e){var t,i,r=0;for(t=" "+e+" ";i=this[r++];)if(1===i.nodeType&&-1<(" "+m(E(i))+" ").indexOf(t))return!0;return!1}});var L=/\r/g;o.fn.extend({val:function(e){var t,i,r,a=this[0];return arguments.length?(r=Q(e),this.each(function(l){var c;1===this.nodeType&&(null==(c=r?e.call(this,l,o(this).val()):e)?c="":"number"==typeof c?c+="":Array.isArray(c)&&(c=o.map(c,function(v){return null==v?"":v+""})),(t=o.valHooks[this.type]||o.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,c,"value")||(this.value=c))})):a?(t=o.valHooks[a.type]||o.valHooks[a.nodeName.toLowerCase()])&&"get"in t&&void 0!==(i=t.get(a,"value"))?i:"string"==typeof(i=a.value)?i.replace(L,""):null==i?"":i:void 0}}),o.extend({valHooks:{option:{get:function(e){var t=o.find.attr(e,"value");return null!=t?t:m(o.text(e))}},select:{get:function(e){var t,i,r,a=e.options,l=e.selectedIndex,c="select-one"===e.type,v=c?null:[],h=c?l+1:a.length;for(r=l<0?h:c?l:0;r<h;r++)if(((i=a[r]).selected||r===l)&&!i.disabled&&(!i.parentNode.disabled||!ue(i.parentNode,"optgroup"))){if(t=o(i).val(),c)return t;v.push(t)}return v},set:function(e,t){for(var i,r,a=e.options,l=o.makeArray(t),c=a.length;c--;)((r=a[c]).selected=-1<o.inArray(o.valHooks.option.get(r),l))&&(i=!0);return i||(e.selectedIndex=-1),l}}}}),o.each(["radio","checkbox"],function(){o.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<o.inArray(o(e).val(),t)}},ee.checkOn||(o.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),o.parseXML=function(e){var t,i;if(!e||"string"!=typeof e)return null;try{t=(new R.DOMParser).parseFromString(e,"text/xml")}catch(r){}return i=t&&t.getElementsByTagName("parsererror")[0],t&&!i||o.error("Invalid XML: "+(i?o.map(i.childNodes,function(r){return r.textContent}).join("\n"):e)),t};var j=/^(?:focusinfocus|focusoutblur)$/,N=function(e){e.stopPropagation()};o.extend(o.event,{trigger:function(e,t,i,r){var a,l,c,v,h,x,w,T,$=[i||K],S=je.call(e,"type")?e.type:e,J=je.call(e,"namespace")?e.namespace.split("."):[];if(l=T=c=i=i||K,3!==i.nodeType&&8!==i.nodeType&&!j.test(S+o.event.triggered)&&(-1<S.indexOf(".")&&(S=(J=S.split(".")).shift(),J.sort()),h=S.indexOf(":")<0&&"on"+S,(e=e[o.expando]?e:new o.Event(S,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=J.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+J.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),t=null==t?[e]:o.makeArray(t,[e]),w=o.event.special[S]||{},r||!w.trigger||!1!==w.trigger.apply(i,t))){if(!r&&!w.noBubble&&!Fe(i)){for(j.test((v=w.delegateType||S)+S)||(l=l.parentNode);l;l=l.parentNode)$.push(l),c=l;c===(i.ownerDocument||K)&&$.push(c.defaultView||c.parentWindow||R)}for(a=0;(l=$[a++])&&!e.isPropagationStopped();)T=l,e.type=1<a?v:w.bindType||S,(x=(z.get(l,"events")||Object.create(null))[e.type]&&z.get(l,"handle"))&&x.apply(l,t),(x=h&&l[h])&&x.apply&&Qt(l)&&(e.result=x.apply(l,t),!1===e.result&&e.preventDefault());return e.type=S,r||e.isDefaultPrevented()||w._default&&!1!==w._default.apply($.pop(),t)||!Qt(i)||h&&Q(i[S])&&!Fe(i)&&((c=i[h])&&(i[h]=null),o.event.triggered=S,e.isPropagationStopped()&&T.addEventListener(S,N),i[S](),e.isPropagationStopped()&&T.removeEventListener(S,N),o.event.triggered=void 0,c&&(i[h]=c)),e.result}},simulate:function(e,t,i){var r=o.extend(new o.Event,i,{type:e,isSimulated:!0});o.event.trigger(r,null,t)}}),o.fn.extend({trigger:function(e,t){return this.each(function(){o.event.trigger(e,t,this)})},triggerHandler:function(e,t){var i=this[0];if(i)return o.event.trigger(e,t,i,!0)}});var D,V=/\[\]$/,I=/\r?\n/g,de=/^(?:submit|button|image|reset|file)$/i,he=/^(?:input|select|textarea|keygen)/i;function Y(e,t,i,r){var a;if(Array.isArray(t))o.each(t,function(l,c){i||V.test(e)?r(e,c):Y(e+"["+("object"==typeof c&&null!=c?l:"")+"]",c,i,r)});else if(i||"object"!==Ee(t))r(e,t);else for(a in t)Y(e+"["+a+"]",t[a],i,r)}o.param=function(e,t){var i,r=[],a=function(l,c){var v=Q(c)?c():c;r[r.length]=encodeURIComponent(l)+"="+encodeURIComponent(null==v?"":v)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!o.isPlainObject(e))o.each(e,function(){a(this.name,this.value)});else for(i in e)Y(i,e[i],t,a);return r.join("&")},o.fn.extend({serialize:function(){return o.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=o.prop(this,"elements");return e?o.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!o(this).is(":disabled")&&he.test(this.nodeName)&&!de.test(e)&&(this.checked||!Bt.test(e))}).map(function(e,t){var i=o(this).val();return null==i?null:Array.isArray(i)?o.map(i,function(r){return{name:t.name,value:r.replace(I,"\r\n")}}):{name:t.name,value:i.replace(I,"\r\n")}}).get()}}),o.fn.extend({wrapAll:function(e){var t;return this[0]&&(Q(e)&&(e=e.call(this[0])),t=o(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var i=this;i.firstElementChild;)i=i.firstElementChild;return i}).append(this)),this},wrapInner:function(e){return Q(e)?this.each(function(t){o(this).wrapInner(e.call(this,t))}):this.each(function(){var t=o(this),i=t.contents();i.length?i.wrapAll(e):t.append(e)})},wrap:function(e){var t=Q(e);return this.each(function(i){o(this).wrapAll(t?e.call(this,i):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){o(this).replaceWith(this.childNodes)}),this}}),o.expr.pseudos.hidden=function(e){return!o.expr.pseudos.visible(e)},o.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},ee.createHTMLDocument=((D=K.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===D.childNodes.length),o.parseHTML=function(e,t,i){return"string"!=typeof e?[]:("boolean"==typeof t&&(i=t,t=!1),t||(ee.createHTMLDocument?((r=(t=K.implementation.createHTMLDocument("")).createElement("base")).href=K.location.href,t.head.appendChild(r)):t=K),l=!i&&[],(a=O.exec(e))?[t.createElement(a[1])]:(a=ti([e],t,l),l&&l.length&&o(l).remove(),o.merge([],a.childNodes)));var r,a,l},o.offset={setOffset:function(e,t,i){var r,a,l,c,v,h,x=o.css(e,"position"),w=o(e),T={};"static"===x&&(e.style.position="relative"),v=w.offset(),l=o.css(e,"top"),h=o.css(e,"left"),("absolute"===x||"fixed"===x)&&-1<(l+h).indexOf("auto")?(c=(r=w.position()).top,a=r.left):(c=parseFloat(l)||0,a=parseFloat(h)||0),Q(t)&&(t=t.call(e,i,o.extend({},v))),null!=t.top&&(T.top=t.top-v.top+c),null!=t.left&&(T.left=t.left-v.left+a),"using"in t?t.using.call(e,T):w.css(T)}},o.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(a){o.offset.setOffset(this,e,a)});var t,i,r=this[0];return r?r.getClientRects().length?{top:(t=r.getBoundingClientRect()).top+(i=r.ownerDocument.defaultView).pageYOffset,left:t.left+i.pageXOffset}:{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,i,r=this[0],a={top:0,left:0};if("fixed"===o.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),i=r.ownerDocument,e=r.offsetParent||i.documentElement;e&&(e===i.body||e===i.documentElement)&&"static"===o.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((a=o(e).offset()).top+=o.css(e,"borderTopWidth",!0),a.left+=o.css(e,"borderLeftWidth",!0))}return{top:t.top-a.top-o.css(r,"marginTop",!0),left:t.left-a.left-o.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===o.css(e,"position");)e=e.offsetParent;return e||Oe})}}),o.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var i="pageYOffset"===t;o.fn[e]=function(r){return ot(this,function(a,l,c){var v;if(Fe(a)?v=a:9===a.nodeType&&(v=a.defaultView),void 0===c)return v?v[t]:a[l];v?v.scrollTo(i?v.pageXOffset:c,i?c:v.pageYOffset):a[l]=c},e,r,arguments.length)}}),o.each(["top","left"],function(e,t){o.cssHooks[t]=ri(ee.pixelPosition,function(i,r){if(r)return r=et(i,t),In.test(r)?o(i).position()[t]+"px":r})}),o.each({Height:"height",Width:"width"},function(e,t){o.each({padding:"inner"+e,content:t,"":"outer"+e},function(i,r){o.fn[r]=function(a,l){var c=arguments.length&&(i||"boolean"!=typeof a),v=i||(!0===a||!0===l?"margin":"border");return ot(this,function(h,x,w){var T;return Fe(h)?0===r.indexOf("outer")?h["inner"+e]:h.document.documentElement["client"+e]:9===h.nodeType?(T=h.documentElement,Math.max(h.body["scroll"+e],T["scroll"+e],h.body["offset"+e],T["offset"+e],T["client"+e])):void 0===w?o.css(h,x,v):o.style(h,x,w,v)},t,c?a:void 0,c)}})}),o.fn.extend({bind:function(e,t,i){return this.on(e,null,t,i)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,i,r){return this.on(t,e,i,r)},undelegate:function(e,t,i){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",i)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),o.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){o.fn[t]=function(i,r){return 0<arguments.length?this.on(t,null,i,r):this.trigger(t)}});var me=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;o.proxy=function(e,t){var i,r,a;if("string"==typeof t&&(i=e[t],t=e,e=i),Q(e))return r=we.call(arguments,2),(a=function(){return e.apply(t||this,r.concat(we.call(arguments)))}).guid=e.guid=e.guid||o.guid++,a},o.holdReady=function(e){e?o.readyWait++:o.ready(!0)},o.isArray=Array.isArray,o.parseJSON=JSON.parse,o.nodeName=ue,o.isFunction=Q,o.isWindow=Fe,o.camelCase=dt,o.type=Ee,o.now=Date.now,o.isNumeric=function(e){var t=o.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},o.trim=function(e){return null==e?"":(e+"").replace(me,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return o});var se=R.jQuery,re=R.$;return o.noConflict=function(e){return R.$===o&&(R.$=re),e&&R.jQuery===o&&(R.jQuery=se),o},void 0===Z&&(R.jQuery=R.$=o),o}),function(R,Z){"object"==typeof exports&&"undefined"!=typeof module?Z(exports):"function"==typeof define&&define.amd?define(["exports"],Z):Z((R="undefined"!=typeof globalThis?globalThis:R||self).Popper={})}(this,function(R){"use strict";function Z(d){if(null==d)return window;if("[object Window]"!==d.toString()){var f=d.ownerDocument;return f&&f.defaultView||window}return d}function ne(d){return d instanceof Z(d).Element||d instanceof Element}function ge(d){return d instanceof Z(d).HTMLElement||d instanceof HTMLElement}function we(d){return"undefined"!=typeof ShadowRoot&&(d instanceof Z(d).ShadowRoot||d instanceof ShadowRoot)}var nt=Math.max,_t=Math.min,Pe=Math.round;function Re(){var d=navigator.userAgentData;return null!=d&&d.brands&&Array.isArray(d.brands)?d.brands.map(function(f){return f.brand+"/"+f.version}).join(" "):navigator.userAgent}function it(){return!/^((?!chrome|android).)*safari/i.test(Re())}function je(d,f,m){void 0===f&&(f=!1),void 0===m&&(m=!1);var E=d.getBoundingClientRect(),A=1,L=1;f&&ge(d)&&(A=d.offsetWidth>0&&Pe(E.width)/d.offsetWidth||1,L=d.offsetHeight>0&&Pe(E.height)/d.offsetHeight||1);var N=(ne(d)?Z(d):window).visualViewport,D=!it()&&m,V=(E.left+(D&&N?N.offsetLeft:0))/A,I=(E.top+(D&&N?N.offsetTop:0))/L,de=E.width/A,he=E.height/L;return{width:de,height:he,top:I,right:V+de,bottom:I+he,left:V,x:V,y:I}}function Ge(d){var f=Z(d);return{scrollLeft:f.pageXOffset,scrollTop:f.pageYOffset}}function Q(d){return d?(d.nodeName||"").toLowerCase():null}function Fe(d){return((ne(d)?d.ownerDocument:d.document)||window.document).documentElement}function K(d){return je(Fe(d)).left+Ge(d).scrollLeft}function xe(d){return Z(d).getComputedStyle(d)}function Ie(d){var f=xe(d);return/auto|scroll|overlay|hidden/.test(f.overflow+f.overflowY+f.overflowX)}function An(d,f,m){void 0===m&&(m=!1);var E=ge(f),A=ge(f)&&function Ee(d){var f=d.getBoundingClientRect(),m=Pe(f.width)/d.offsetWidth||1,E=Pe(f.height)/d.offsetHeight||1;return 1!==m||1!==E}(f),L=Fe(f),j=je(d,A,m),N={scrollLeft:0,scrollTop:0},D={x:0,y:0};return(E||!E&&!m)&&(("body"!==Q(f)||Ie(L))&&(N=function ee(d){return d!==Z(d)&&ge(d)?function Jn(d){return{scrollLeft:d.scrollLeft,scrollTop:d.scrollTop}}(d):Ge(d)}(f)),ge(f)?((D=je(f,!0)).x+=f.clientLeft,D.y+=f.clientTop):L&&(D.x=K(L))),{x:j.left+N.scrollLeft-D.x,y:j.top+N.scrollTop-D.y,width:j.width,height:j.height}}function Ut(d){var f=je(d),m=d.offsetWidth,E=d.offsetHeight;return Math.abs(f.width-m)<=1&&(m=f.width),Math.abs(f.height-E)<=1&&(E=f.height),{x:d.offsetLeft,y:d.offsetTop,width:m,height:E}}function o(d){return"html"===Q(d)?d:d.assignedSlot||d.parentNode||(we(d)?d.host:null)||Fe(d)}function dn(d){return["html","body","#document"].indexOf(Q(d))>=0?d.ownerDocument.body:ge(d)&&Ie(d)?d:dn(o(d))}function ue(d,f){var m;void 0===f&&(f=[]);var E=dn(d),A=E===(null==(m=d.ownerDocument)?void 0:m.body),L=Z(E),j=A?[L].concat(L.visualViewport||[],Ie(E)?E:[]):E,N=f.concat(j);return A?N:N.concat(ue(o(j)))}function Cn(d){return["table","td","th"].indexOf(Q(d))>=0}function Tn(d){return ge(d)&&"fixed"!==xe(d).position?d.offsetParent:null}function oe(d){for(var f=Z(d),m=Tn(d);m&&Cn(m)&&"static"===xe(m).position;)m=Tn(m);return m&&("html"===Q(m)||"body"===Q(m)&&"static"===xe(m).position)?f:m||function Zn(d){var f=/firefox/i.test(Re());if(/Trident/i.test(Re())&&ge(d)&&"fixed"===xe(d).position)return null;var A=o(d);for(we(A)&&(A=A.host);ge(A)&&["html","body"].indexOf(Q(A))<0;){var L=xe(A);if("none"!==L.transform||"none"!==L.perspective||"paint"===L.contain||-1!==["transform","perspective"].indexOf(L.willChange)||f&&"filter"===L.willChange||f&&L.filter&&"none"!==L.filter)return A;A=A.parentNode}return null}(d)||f}var Ae="top",Ve="bottom",Xe="right",ve="left",Mt="auto",Ue=[Ae,Ve,Xe,ve],Tt="start",kt="end",Ht="viewport",Ot="popper",rt=Ue.reduce(function(d,f){return d.concat([f+"-"+Tt,f+"-"+kt])},[]),Rt=[].concat(Ue,[Mt]).reduce(function(d,f){return d.concat([f,f+"-"+Tt,f+"-"+kt])},[]),Yt=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Ft(d){var f=new Map,m=new Set,E=[];function A(L){m.add(L.name),[].concat(L.requires||[],L.requiresIfExists||[]).forEach(function(N){if(!m.has(N)){var D=f.get(N);D&&A(D)}}),E.push(L)}return d.forEach(function(L){f.set(L.name,L)}),d.forEach(function(L){m.has(L.name)||A(L)}),E}function dt(d){var f;return function(){return f||(f=new Promise(function(m){Promise.resolve().then(function(){f=void 0,m(d())})})),f}}function Me(d,f){var m=f.getRootNode&&f.getRootNode();if(d.contains(f))return!0;if(m&&we(m)){var E=f;do{if(E&&d.isSameNode(E))return!0;E=E.parentNode||E.host}while(E)}return!1}function Dn(d){return Object.assign({},d,{left:d.x,top:d.y,right:d.x+d.width,bottom:d.y+d.height})}function st(d,f,m){return f===Ht?Dn(function Kt(d,f){var m=Z(d),E=Fe(d),A=m.visualViewport,L=E.clientWidth,j=E.clientHeight,N=0,D=0;if(A){L=A.width,j=A.height;var V=it();(V||!V&&"fixed"===f)&&(N=A.offsetLeft,D=A.offsetTop)}return{width:L,height:j,x:N+K(d),y:D}}(d,m)):ne(f)?function fn(d,f){var m=je(d,!1,"fixed"===f);return m.top=m.top+d.clientTop,m.left=m.left+d.clientLeft,m.bottom=m.top+d.clientHeight,m.right=m.left+d.clientWidth,m.width=d.clientWidth,m.height=d.clientHeight,m.x=m.left,m.y=m.top,m}(f,m):Dn(function z(d){var f,m=Fe(d),E=Ge(d),A=null==(f=d.ownerDocument)?void 0:f.body,L=nt(m.scrollWidth,m.clientWidth,A?A.scrollWidth:0,A?A.clientWidth:0),j=nt(m.scrollHeight,m.clientHeight,A?A.scrollHeight:0,A?A.clientHeight:0),N=-E.scrollLeft+K(d),D=-E.scrollTop;return"rtl"===xe(A||m).direction&&(N+=nt(m.clientWidth,A?A.clientWidth:0)-L),{width:L,height:j,x:N,y:D}}(Fe(d)))}function _e(d){return d.split("-")[0]}function Oe(d){return d.split("-")[1]}function Je(d){return["top","bottom"].indexOf(d)>=0?"x":"y"}function ei(d){var D,f=d.reference,m=d.element,E=d.placement,A=E?_e(E):null,L=E?Oe(E):null,j=f.x+f.width/2-m.width/2,N=f.y+f.height/2-m.height/2;switch(A){case Ae:D={x:j,y:f.y-m.height};break;case Ve:D={x:j,y:f.y+f.height};break;case Xe:D={x:f.x+f.width,y:N};break;case ve:D={x:f.x-m.width,y:N};break;default:D={x:f.x,y:f.y}}var V=A?Je(A):null;if(null!=V){var I="y"===V?"height":"width";switch(L){case Tt:D[V]=D[V]-(f[I]/2-m[I]/2);break;case kt:D[V]=D[V]+(f[I]/2-m[I]/2)}}return D}function Nn(d){return Object.assign({},{top:0,right:0,bottom:0,left:0},d)}function $n(d,f){return f.reduce(function(m,E){return m[E]=d,m},{})}function Qe(d,f){void 0===f&&(f={});var E=f.placement,A=void 0===E?d.placement:E,L=f.strategy,j=void 0===L?d.strategy:L,N=f.boundary,D=void 0===N?"clippingParents":N,V=f.rootBoundary,I=void 0===V?Ht:V,de=f.elementContext,he=void 0===de?Ot:de,Y=f.altBoundary,me=void 0!==Y&&Y,se=f.padding,re=void 0===se?0:se,e=Nn("number"!=typeof re?re:$n(re,Ue)),i=d.rects.popper,r=d.elements[me?he===Ot?"reference":Ot:he],a=function xt(d,f,m,E){var A="clippingParents"===f?function Ln(d){var f=ue(o(d)),E=["absolute","fixed"].indexOf(xe(d).position)>=0&&ge(d)?oe(d):d;return ne(E)?f.filter(function(A){return ne(A)&&Me(A,E)&&"body"!==Q(A)}):[]}(d):[].concat(f),L=[].concat(A,[m]),N=L.reduce(function(D,V){var I=st(d,V,E);return D.top=nt(I.top,D.top),D.right=_t(I.right,D.right),D.bottom=_t(I.bottom,D.bottom),D.left=nt(I.left,D.left),D},st(d,L[0],E));return N.width=N.right-N.left,N.height=N.bottom-N.top,N.x=N.left,N.y=N.top,N}(ne(r)?r:r.contextElement||Fe(d.elements.popper),D,I,j),l=je(d.elements.reference),c=ei({reference:l,element:i,strategy:"absolute",placement:A}),v=Dn(Object.assign({},i,c)),h=he===Ot?v:l,x={top:a.top-h.top+e.top,bottom:h.bottom-a.bottom+e.bottom,left:a.left-h.left+e.left,right:h.right-a.right+e.right},w=d.modifiersData.offset;if(he===Ot&&w){var T=w[A];Object.keys(x).forEach(function($){var S=[Xe,Ve].indexOf($)>=0?1:-1,J=[Ae,Ve].indexOf($)>=0?"y":"x";x[$]+=T[J]*S})}return x}var Jt={placement:"bottom",modifiers:[],strategy:"absolute"};function Bt(){for(var d=arguments.length,f=new Array(d),m=0;m<d;m++)f[m]=arguments[m];return!f.some(function(E){return!(E&&"function"==typeof E.getBoundingClientRect)})}function hn(d){void 0===d&&(d={});var m=d.defaultModifiers,E=void 0===m?[]:m,A=d.defaultOptions,L=void 0===A?Jt:A;return function(N,D,V){void 0===V&&(V=L);var I={placement:"bottom",orderedModifiers:[],options:Object.assign({},Jt,L),modifiersData:{},elements:{reference:N,popper:D},attributes:{},styles:{}},de=[],he=!1,Y={state:I,setOptions:function(e){var t="function"==typeof e?e(I.options):e;se(),I.options=Object.assign({},L,I.options,t),I.scrollParents={reference:ne(N)?ue(N):N.contextElement?ue(N.contextElement):[],popper:ue(D)};var i=function vi(d){var f=Ft(d);return Yt.reduce(function(m,E){return m.concat(f.filter(function(A){return A.phase===E}))},[])}(function Qt(d){var f=d.reduce(function(m,E){var A=m[E.name];return m[E.name]=A?Object.assign({},A,E,{options:Object.assign({},A.options,E.options),data:Object.assign({},A.data,E.data)}):E,m},{});return Object.keys(f).map(function(m){return f[m]})}([].concat(E,I.options.modifiers)));return I.orderedModifiers=i.filter(function(r){return r.enabled}),function me(){I.orderedModifiers.forEach(function(re){var t=re.options,r=re.effect;if("function"==typeof r){var a=r({state:I,name:re.name,instance:Y,options:void 0===t?{}:t});de.push(a||function(){})}})}(),Y.update()},forceUpdate:function(){if(!he){var e=I.elements,t=e.reference,i=e.popper;if(Bt(t,i)){I.rects={reference:An(t,oe(i),"fixed"===I.options.strategy),popper:Ut(i)},I.reset=!1,I.placement=I.options.placement,I.orderedModifiers.forEach(function(x){return I.modifiersData[x.name]=Object.assign({},x.data)});for(var r=0;r<I.orderedModifiers.length;r++)if(!0!==I.reset){var a=I.orderedModifiers[r],l=a.fn,c=a.options;"function"==typeof l&&(I=l({state:I,options:void 0===c?{}:c,name:a.name,instance:Y})||I)}else I.reset=!1,r=-1}}},update:dt(function(){return new Promise(function(re){Y.forceUpdate(),re(I)})}),destroy:function(){se(),he=!0}};if(!Bt(N,D))return Y;function se(){de.forEach(function(re){return re()}),de=[]}return Y.setOptions(V).then(function(re){!he&&V.onFirstUpdate&&V.onFirstUpdate(re)}),Y}}var qt={passive:!0},Se={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function He(d){var f=d.state,m=d.instance,E=d.options,A=E.scroll,L=void 0===A||A,j=E.resize,N=void 0===j||j,D=Z(f.elements.popper),V=[].concat(f.scrollParents.reference,f.scrollParents.popper);return L&&V.forEach(function(I){I.addEventListener("scroll",m.update,qt)}),N&&D.addEventListener("resize",m.update,qt),function(){L&&V.forEach(function(I){I.removeEventListener("scroll",m.update,qt)}),N&&D.removeEventListener("resize",m.update,qt)}},data:{}},Pn={name:"popperOffsets",enabled:!0,phase:"read",fn:function pn(d){var f=d.state;f.modifiersData[d.name]=ei({reference:f.rects.reference,element:f.rects.popper,strategy:"absolute",placement:f.placement})},data:{}},ti={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Dt(d){var f,m=d.popper,E=d.popperRect,A=d.placement,L=d.variation,j=d.offsets,N=d.position,D=d.gpuAcceleration,V=d.adaptive,I=d.roundOffsets,de=d.isFixed,he=j.x,Y=void 0===he?0:he,me=j.y,se=void 0===me?0:me,re="function"==typeof I?I({x:Y,y:se}):{x:Y,y:se};Y=re.x,se=re.y;var e=j.hasOwnProperty("x"),t=j.hasOwnProperty("y"),i=ve,r=Ae,a=window;if(V){var l=oe(m),c="clientHeight",v="clientWidth";l===Z(m)&&"static"!==xe(l=Fe(m)).position&&"absolute"===N&&(c="scrollHeight",v="scrollWidth"),(A===Ae||(A===ve||A===Xe)&&L===kt)&&(r=Ve,se-=(de&&l===a&&a.visualViewport?a.visualViewport.height:l[c])-E.height,se*=D?1:-1),A!==ve&&(A!==Ae&&A!==Ve||L!==kt)||(i=Xe,Y-=(de&&l===a&&a.visualViewport?a.visualViewport.width:l[v])-E.width,Y*=D?1:-1)}var $,w=Object.assign({position:N},V&&ti),T=!0===I?function ni(d,f){var E=d.y,A=f.devicePixelRatio||1;return{x:Pe(d.x*A)/A||0,y:Pe(E*A)/A||0}}({x:Y,y:se},Z(m)):{x:Y,y:se};return Y=T.x,se=T.y,Object.assign({},w,D?(($={})[r]=t?"0":"",$[i]=e?"0":"",$.transform=(a.devicePixelRatio||1)<=1?"translate("+Y+"px, "+se+"px)":"translate3d("+Y+"px, "+se+"px, 0)",$):((f={})[r]=t?se+"px":"",f[i]=e?Y+"px":"",f.transform="",f))}var ft={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function Ze(d){var f=d.state,m=d.options,E=m.gpuAcceleration,A=void 0===E||E,L=m.adaptive,j=void 0===L||L,N=m.roundOffsets,D=void 0===N||N,V={placement:_e(f.placement),variation:Oe(f.placement),popper:f.elements.popper,popperRect:f.rects.popper,gpuAcceleration:A,isFixed:"fixed"===f.options.strategy};null!=f.modifiersData.popperOffsets&&(f.styles.popper=Object.assign({},f.styles.popper,Dt(Object.assign({},V,{offsets:f.modifiersData.popperOffsets,position:f.options.strategy,adaptive:j,roundOffsets:D})))),null!=f.modifiersData.arrow&&(f.styles.arrow=Object.assign({},f.styles.arrow,Dt(Object.assign({},V,{offsets:f.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:D})))),f.attributes.popper=Object.assign({},f.attributes.popper,{"data-popper-placement":f.placement})},data:{}},jn={name:"applyStyles",enabled:!0,phase:"write",fn:function gn(d){var f=d.state;Object.keys(f.elements).forEach(function(m){var E=f.styles[m]||{},A=f.attributes[m]||{},L=f.elements[m];!ge(L)||!Q(L)||(Object.assign(L.style,E),Object.keys(A).forEach(function(j){var N=A[j];!1===N?L.removeAttribute(j):L.setAttribute(j,!0===N?"":N)}))})},effect:function yi(d){var f=d.state,m={popper:{position:f.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(f.elements.popper.style,m.popper),f.styles=m,f.elements.arrow&&Object.assign(f.elements.arrow.style,m.arrow),function(){Object.keys(f.elements).forEach(function(E){var A=f.elements[E],L=f.attributes[E]||{},N=Object.keys(f.styles.hasOwnProperty(E)?f.styles[E]:m[E]).reduce(function(D,V){return D[V]="",D},{});!ge(A)||!Q(A)||(Object.assign(A.style,N),Object.keys(L).forEach(function(D){A.removeAttribute(D)}))})}},requires:["computeStyles"]},mn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function ii(d){var f=d.state,E=d.name,A=d.options.offset,L=void 0===A?[0,0]:A,j=Rt.reduce(function(I,de){return I[de]=function bi(d,f,m){var E=_e(d),A=[ve,Ae].indexOf(E)>=0?-1:1,L="function"==typeof m?m(Object.assign({},f,{placement:d})):m,j=L[0],N=L[1];return j=j||0,N=(N||0)*A,[ve,Xe].indexOf(E)>=0?{x:N,y:j}:{x:j,y:N}}(de,f.rects,L),I},{}),N=j[f.placement],V=N.y;null!=f.modifiersData.popperOffsets&&(f.modifiersData.popperOffsets.x+=N.x,f.modifiersData.popperOffsets.y+=V),f.modifiersData[E]=j}},Wt={left:"right",right:"left",bottom:"top",top:"bottom"};function Et(d){return d.replace(/left|right|bottom|top/g,function(f){return Wt[f]})}var zt={start:"end",end:"start"};function Zt(d){return d.replace(/start|end/g,function(f){return zt[f]})}var vn={name:"flip",enabled:!0,phase:"main",fn:function ht(d){var f=d.state,m=d.options,E=d.name;if(!f.modifiersData[E]._skip){for(var A=m.mainAxis,L=void 0===A||A,j=m.altAxis,N=void 0===j||j,D=m.fallbackPlacements,V=m.padding,I=m.boundary,de=m.rootBoundary,he=m.altBoundary,Y=m.flipVariations,me=void 0===Y||Y,se=m.allowedAutoPlacements,re=f.options.placement,e=_e(re),i=D||(e!==re&&me?function Mn(d){if(_e(d)===Mt)return[];var f=Et(d);return[Zt(d),f,Zt(f)]}(re):[Et(re)]),r=[re].concat(i).reduce(function(Xt,Be){return Xt.concat(_e(Be)===Mt?function In(d,f){void 0===f&&(f={});var A=f.boundary,L=f.rootBoundary,j=f.padding,N=f.flipVariations,D=f.allowedAutoPlacements,V=void 0===D?Rt:D,I=Oe(f.placement),de=I?N?rt:rt.filter(function(me){return Oe(me)===I}):Ue,he=de.filter(function(me){return V.indexOf(me)>=0});0===he.length&&(he=de);var Y=he.reduce(function(me,se){return me[se]=Qe(d,{placement:se,boundary:A,rootBoundary:L,padding:j})[_e(se)],me},{});return Object.keys(Y).sort(function(me,se){return Y[me]-Y[se]})}(f,{placement:Be,boundary:I,rootBoundary:de,padding:V,flipVariations:me,allowedAutoPlacements:se}):Be)},[]),a=f.rects.reference,l=f.rects.popper,c=new Map,v=!0,h=r[0],x=0;x<r.length;x++){var w=r[x],T=_e(w),$=Oe(w)===Tt,S=[Ae,Ve].indexOf(T)>=0,J=S?"width":"height",te=Qe(f,{placement:w,boundary:I,rootBoundary:de,altBoundary:he,padding:V}),ae=S?$?Xe:ve:$?Ve:Ae;a[J]>l[J]&&(ae=Et(ae));var ke=Et(ae),pe=[];if(L&&pe.push(te[T]<=0),N&&pe.push(te[ae]<=0,te[ke]<=0),pe.every(function(Xt){return Xt})){h=w,v=!1;break}c.set(w,pe)}if(v)for(var ye=function(Be){var qe=r.find(function(rn){var $t=c.get(rn);if($t)return $t.slice(0,Be).every(function(bn){return bn})});if(qe)return h=qe,"break"},At=me?3:1;At>0&&"break"!==ye(At);At--);f.placement!==h&&(f.modifiersData[E]._skip=!0,f.placement=h,f.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function et(d,f,m){return nt(d,_t(f,m))}var Rn={name:"preventOverflow",enabled:!0,phase:"main",fn:function Hn(d){var f=d.state,m=d.options,E=d.name,A=m.mainAxis,L=void 0===A||A,j=m.altAxis,N=void 0!==j&&j,he=m.tether,Y=void 0===he||he,me=m.tetherOffset,se=void 0===me?0:me,re=Qe(f,{boundary:m.boundary,rootBoundary:m.rootBoundary,padding:m.padding,altBoundary:m.altBoundary}),e=_e(f.placement),t=Oe(f.placement),i=!t,r=Je(e),a=function Lt(d){return"x"===d?"y":"x"}(r),l=f.modifiersData.popperOffsets,c=f.rects.reference,v=f.rects.popper,h="function"==typeof se?se(Object.assign({},f.rects,{placement:f.placement})):se,x="number"==typeof h?{mainAxis:h,altAxis:h}:Object.assign({mainAxis:0,altAxis:0},h),w=f.modifiersData.offset?f.modifiersData.offset[f.placement]:null,T={x:0,y:0};if(l){if(L){var $,S="y"===r?Ae:ve,J="y"===r?Ve:Xe,te="y"===r?"height":"width",ae=l[r],ke=ae+re[S],pe=ae-re[J],Ce=Y?-v[te]/2:0,ye=t===Tt?c[te]:v[te],At=t===Tt?-v[te]:-c[te],nn=f.elements.arrow,Xt=Y&&nn?Ut(nn):{width:0,height:0},Be=f.modifiersData["arrow#persistent"]?f.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},qe=Be[S],rn=Be[J],$t=et(0,c[te],Xt[te]),bn=i?c[te]/2-Ce-$t-qe-x.mainAxis:ye-$t-qe-x.mainAxis,on=i?-c[te]/2+Ce+$t+rn+x.mainAxis:At+$t+rn+x.mainAxis,_n=f.elements.arrow&&oe(f.elements.arrow),ci=null!=($=null==w?void 0:w[r])?$:0,gt=ae+on-ci,tt=et(Y?_t(ke,ae+bn-ci-(_n?"y"===r?_n.clientTop||0:_n.clientLeft||0:0)):ke,ae,Y?nt(pe,gt):pe);l[r]=tt,T[r]=tt-ae}if(N){var zn,at=l[a],We="y"===a?"height":"width",Pt=at+re["x"===r?Ae:ve],lt=at-re["x"===r?Ve:Xe],wn=-1!==[Ae,ve].indexOf(e),Vn=null!=(zn=null==w?void 0:w[a])?zn:0,Ct=wn?Pt:at-c[We]-v[We]-Vn+x.altAxis,xn=wn?at+c[We]+v[We]-Vn-x.altAxis:lt,mt=Y&&wn?function ri(d,f,m){var E=et(d,f,m);return E>m?m:E}(Ct,at,xn):et(Y?Ct:Pt,at,Y?xn:lt);l[a]=mt,T[a]=mt-at}f.modifiersData[E]=T}},requiresIfExists:["offset"]},yn={name:"arrow",enabled:!0,phase:"main",fn:function si(d){var f,m=d.state,E=d.name,A=d.options,L=m.elements.arrow,j=m.modifiersData.popperOffsets,N=_e(m.placement),D=Je(N),I=[ve,Xe].indexOf(N)>=0?"height":"width";if(L&&j){var de=function(f,m){return Nn("number"!=typeof(f="function"==typeof f?f(Object.assign({},m.rects,{placement:m.placement})):f)?f:$n(f,Ue))}(A.padding,m),he=Ut(L),Y="y"===D?Ae:ve,me="y"===D?Ve:Xe,se=m.rects.reference[I]+m.rects.reference[D]-j[D]-m.rects.popper[I],re=j[D]-m.rects.reference[D],e=oe(L),t=e?"y"===D?e.clientHeight||0:e.clientWidth||0:0,l=t/2-he[I]/2+(se/2-re/2),c=et(de[Y],l,t-he[I]-de[me]);m.modifiersData[E]=((f={})[D]=c,f.centerOffset=c-l,f)}},effect:function Vt(d){var f=d.state,E=d.options.element,A=void 0===E?"[data-popper-arrow]":E;null!=A&&("string"==typeof A&&!(A=f.elements.popper.querySelector(A))||!Me(f.elements.popper,A)||(f.elements.arrow=A))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Fn(d,f,m){return void 0===m&&(m={x:0,y:0}),{top:d.top-f.height-m.y,right:d.right-f.width+m.x,bottom:d.bottom-f.height+m.y,left:d.left-f.width-m.x}}function ai(d){return[Ae,Xe,Ve,ve].some(function(f){return d[f]>=0})}var pt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function Nt(d){var f=d.state,m=d.name,E=f.rects.reference,A=f.rects.popper,L=f.modifiersData.preventOverflow,j=Qe(f,{elementContext:"reference"}),N=Qe(f,{altBoundary:!0}),D=Fn(j,E),V=Fn(N,A,L),I=ai(D),de=ai(V);f.modifiersData[m]={referenceClippingOffsets:D,popperEscapeOffsets:V,isReferenceHidden:I,hasPopperEscaped:de},f.attributes.popper=Object.assign({},f.attributes.popper,{"data-popper-reference-hidden":I,"data-popper-escaped":de})}},en=hn({defaultModifiers:[Se,Pn,ft,jn]}),qn=[Se,Pn,ft,jn,mn,vn,Rn,yn,pt],tn=hn({defaultModifiers:qn});R.applyStyles=jn,R.arrow=yn,R.computeStyles=ft,R.createPopper=tn,R.createPopperLite=en,R.defaultModifiers=qn,R.detectOverflow=Qe,R.eventListeners=Se,R.flip=vn,R.hide=pt,R.offset=mn,R.popperGenerator=hn,R.popperOffsets=Pn,R.preventOverflow=Rn,Object.defineProperty(R,"__esModule",{value:!0})}),function(R,Z){"object"==typeof exports&&"undefined"!=typeof module?module.exports=Z(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],Z):(R="undefined"!=typeof globalThis?globalThis:R||self).bootstrap=Z(R.Popper)}(this,function(R){"use strict";const ne=function Z(g){const n=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(g)for(const s in g)if("default"!==s){const p=Object.getOwnPropertyDescriptor(g,s);Object.defineProperty(n,s,p.get?p:{enumerable:!0,get:()=>g[s]})}return n.default=g,Object.freeze(n)}(R),ge=new Map,we={set(g,n,s){ge.has(g)||ge.set(g,new Map);const p=ge.get(g);p.has(n)||0===p.size?p.set(n,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(p.keys())[0]}.`)},get:(g,n)=>ge.has(g)&&ge.get(g).get(n)||null,remove(g,n){if(!ge.has(g))return;const s=ge.get(g);s.delete(n),0===s.size&&ge.delete(g)}},nt="transitionend",_t=g=>(g&&window.CSS&&window.CSS.escape&&(g=g.replace(/#([^\s"#']+)/g,(n,s)=>`#${CSS.escape(s)}`)),g),Pe=g=>{g.dispatchEvent(new Event(nt))},Re=g=>!(!g||"object"!=typeof g)&&(void 0!==g.jquery&&(g=g[0]),void 0!==g.nodeType),it=g=>Re(g)?g.jquery?g[0]:g:"string"==typeof g&&g.length>0?document.querySelector(_t(g)):null,je=g=>{if(!Re(g)||0===g.getClientRects().length)return!1;const n="visible"===getComputedStyle(g).getPropertyValue("visibility"),s=g.closest("details:not([open])");if(!s)return n;if(s!==g){const p=g.closest("summary");if(p&&p.parentNode!==s||null===p)return!1}return n},Ge=g=>!g||g.nodeType!==Node.ELEMENT_NODE||!!g.classList.contains("disabled")||(void 0!==g.disabled?g.disabled:g.hasAttribute("disabled")&&"false"!==g.getAttribute("disabled")),Jn=g=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof g.getRootNode){const n=g.getRootNode();return n instanceof ShadowRoot?n:null}return g instanceof ShadowRoot?g:g.parentNode?Jn(g.parentNode):null},ee=()=>{},Fe=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,K=[],xe=()=>"rtl"===document.documentElement.dir,Ie=g=>{var n;n=()=>{const s=Fe();if(s){const p=g.NAME,k=s.fn[p];s.fn[p]=g.jQueryInterface,s.fn[p].Constructor=g,s.fn[p].noConflict=()=>(s.fn[p]=k,g.jQueryInterface)}},"loading"===document.readyState?(K.length||document.addEventListener("DOMContentLoaded",()=>{for(const s of K)s()}),K.push(n)):n()},Ee=(g,n=[],s=g)=>"function"==typeof g?g(...n):s,An=(g,n,s=!0)=>{if(!s)return void Ee(g);const p=(q=>{if(!q)return 0;let{transitionDuration:ce,transitionDelay:Te}=window.getComputedStyle(q);const ct=Number.parseFloat(ce),bt=Number.parseFloat(Te);return ct||bt?(ce=ce.split(",")[0],Te=Te.split(",")[0],1e3*(Number.parseFloat(ce)+Number.parseFloat(Te))):0})(n)+5;let k=!1;const P=({target:q})=>{q===n&&(k=!0,n.removeEventListener(nt,P),Ee(g))};n.addEventListener(nt,P),setTimeout(()=>{k||Pe(n)},p)},Ut=(g,n,s,p)=>{const k=g.length;let P=g.indexOf(n);return-1===P?!s&&p?g[k-1]:g[0]:(P+=s?1:-1,p&&(P=(P+k)%k),g[Math.max(0,Math.min(P,k-1))])},o=/[^.]*(?=\..*)\.|.*/,dn=/\..*/,ue=/::\d+$/,Cn={};let Tn=1;const Zn={mouseenter:"mouseover",mouseleave:"mouseout"},oe=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Ae(g,n){return n&&`${n}::${Tn++}`||g.uidEvent||Tn++}function Ve(g){const n=Ae(g);return g.uidEvent=n,Cn[n]=Cn[n]||{},Cn[n]}function Xe(g,n,s=null){return Object.values(g).find(p=>p.callable===n&&p.delegationSelector===s)}function ve(g,n,s){const p="string"==typeof n,k=p?s:n||s;let P=kt(g);return oe.has(P)||(P=g),[p,k,P]}function Mt(g,n,s,p,k){if("string"!=typeof n||!g)return;let[P,q,ce]=ve(n,s,p);var Kn;n in Zn&&(Kn=q,q=function(It){if(!It.relatedTarget||It.relatedTarget!==It.delegateTarget&&!It.delegateTarget.contains(It.relatedTarget))return Kn.call(this,It)});const Te=Ve(g),ct=Te[ce]||(Te[ce]={}),bt=Xe(ct,q,P?s:null);if(bt)return void(bt.oneOff=bt.oneOff&&k);const jt=Ae(q,n.replace(o,"")),cn=P?function(un,Kn,It){return function xi(Oi){const nr=un.querySelectorAll(Kn);for(let{target:Gn}=Oi;Gn&&Gn!==this;Gn=Gn.parentNode)for(const ir of nr)if(ir===Gn)return Ht(Oi,{delegateTarget:Gn}),xi.oneOff&&O.off(un,Oi.type,Kn,It),It.apply(Gn,[Oi])}}(g,s,q):function(un,Kn){return function It(xi){return Ht(xi,{delegateTarget:un}),It.oneOff&&O.off(un,xi.type,Kn),Kn.apply(un,[xi])}}(g,q);cn.delegationSelector=P?s:null,cn.callable=q,cn.oneOff=k,cn.uidEvent=jt,ct[jt]=cn,g.addEventListener(ce,cn,P)}function Ue(g,n,s,p,k){const P=Xe(n[s],p,k);P&&(g.removeEventListener(s,P,Boolean(k)),delete n[s][P.uidEvent])}function Tt(g,n,s,p){const k=n[s]||{};for(const[P,q]of Object.entries(k))P.includes(p)&&Ue(g,n,s,q.callable,q.delegationSelector)}function kt(g){return g=g.replace(dn,""),Zn[g]||g}const O={on(g,n,s,p){Mt(g,n,s,p,!1)},one(g,n,s,p){Mt(g,n,s,p,!0)},off(g,n,s,p){if("string"!=typeof n||!g)return;const[k,P,q]=ve(n,s,p),ce=q!==n,Te=Ve(g),ct=Te[q]||{},bt=n.startsWith(".");if(void 0===P){if(bt)for(const jt of Object.keys(Te))Tt(g,Te,jt,n.slice(1));for(const[jt,cn]of Object.entries(ct)){const un=jt.replace(ue,"");ce&&!n.includes(un)||Ue(g,Te,q,cn.callable,cn.delegationSelector)}}else{if(!Object.keys(ct).length)return;Ue(g,Te,q,P,k?s:null)}},trigger(g,n,s){if("string"!=typeof n||!g)return null;const p=Fe();let k=null,P=!0,q=!0,ce=!1;n!==kt(n)&&p&&(k=p.Event(n,s),p(g).trigger(k),P=!k.isPropagationStopped(),q=!k.isImmediatePropagationStopped(),ce=k.isDefaultPrevented());const Te=Ht(new Event(n,{bubbles:P,cancelable:!0}),s);return ce&&Te.preventDefault(),q&&g.dispatchEvent(Te),Te.defaultPrevented&&k&&k.preventDefault(),Te}};function Ht(g,n={}){for(const[s,p]of Object.entries(n))try{g[s]=p}catch(k){Object.defineProperty(g,s,{configurable:!0,get:()=>p})}return g}function Ot(g){if("true"===g)return!0;if("false"===g)return!1;if(g===Number(g).toString())return Number(g);if(""===g||"null"===g)return null;if("string"!=typeof g)return g;try{return JSON.parse(decodeURIComponent(g))}catch(n){return g}}function kn(g){return g.replace(/[A-Z]/g,n=>`-${n.toLowerCase()}`)}const rt={setDataAttribute(g,n,s){g.setAttribute(`data-bs-${kn(n)}`,s)},removeDataAttribute(g,n){g.removeAttribute(`data-bs-${kn(n)}`)},getDataAttributes(g){if(!g)return{};const n={},s=Object.keys(g.dataset).filter(p=>p.startsWith("bs")&&!p.startsWith("bsConfig"));for(const p of s){let k=p.replace(/^bs/,"");k=k.charAt(0).toLowerCase()+k.slice(1,k.length),n[k]=Ot(g.dataset[p])}return n},getDataAttribute:(g,n)=>Ot(g.getAttribute(`data-bs-${kn(n)}`))};class Rt{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(n){return n=this._mergeConfigObj(n),n=this._configAfterMerge(n),this._typeCheckConfig(n),n}_configAfterMerge(n){return n}_mergeConfigObj(n,s){const p=Re(s)?rt.getDataAttribute(s,"config"):{};return Ke(Ke(Ke(Ke({},this.constructor.Default),"object"==typeof p?p:{}),Re(s)?rt.getDataAttributes(s):{}),"object"==typeof n?n:{})}_typeCheckConfig(n,s=this.constructor.DefaultType){for(const[k,P]of Object.entries(s)){const q=n[k],ce=Re(q)?"element":null==(p=q)?`${p}`:Object.prototype.toString.call(p).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(P).test(ce))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${k}" provided type "${ce}" but expected type "${P}".`)}var p}}class Ye extends Rt{constructor(n,s){super(),(n=it(n))&&(this._element=n,this._config=this._getConfig(s),we.set(this._element,this.constructor.DATA_KEY,this))}dispose(){we.remove(this._element,this.constructor.DATA_KEY),O.off(this._element,this.constructor.EVENT_KEY);for(const n of Object.getOwnPropertyNames(this))this[n]=null}_queueCallback(n,s,p=!0){An(n,s,p)}_getConfig(n){return n=this._mergeConfigObj(n,this._element),n=this._configAfterMerge(n),this._typeCheckConfig(n),n}static getInstance(n){return we.get(it(n),this.DATA_KEY)}static getOrCreateInstance(n,s={}){return this.getInstance(n)||new this(n,"object"==typeof s?s:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(n){return`${n}${this.EVENT_KEY}`}}const ut=g=>{let n=g.getAttribute("data-bs-target");if(!n||"#"===n){let s=g.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),n=s&&"#"!==s?s.trim():null}return n?n.split(",").map(s=>_t(s)).join(","):null},W={find:(g,n=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(n,g)),findOne:(g,n=document.documentElement)=>Element.prototype.querySelector.call(n,g),children:(g,n)=>[].concat(...g.children).filter(s=>s.matches(n)),parents(g,n){const s=[];let p=g.parentNode.closest(n);for(;p;)s.push(p),p=p.parentNode.closest(n);return s},prev(g,n){let s=g.previousElementSibling;for(;s;){if(s.matches(n))return[s];s=s.previousElementSibling}return[]},next(g,n){let s=g.nextElementSibling;for(;s;){if(s.matches(n))return[s];s=s.nextElementSibling}return[]},focusableChildren(g){const n=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(s=>`${s}:not([tabindex^="-"])`).join(",");return this.find(n,g).filter(s=>!Ge(s)&&je(s))},getSelectorFromElement(g){const n=ut(g);return n&&W.findOne(n)?n:null},getElementFromSelector(g){const n=ut(g);return n?W.findOne(n):null},getMultipleElementsFromSelector(g){const n=ut(g);return n?W.find(n):[]}},St=(g,n="hide")=>{const p=g.NAME;O.on(document,`click.dismiss${g.EVENT_KEY}`,`[data-bs-dismiss="${p}"]`,function(k){if(["A","AREA"].includes(this.tagName)&&k.preventDefault(),Ge(this))return;const P=W.getElementFromSelector(this)||this.closest(`.${p}`);g.getOrCreateInstance(P)[n]()})},On=".bs.alert",mi=`close${On}`,Sn=`closed${On}`;class wt extends Ye{static get NAME(){return"alert"}close(){if(O.trigger(this._element,mi).defaultPrevented)return;this._element.classList.remove("show");const n=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),O.trigger(this._element,Sn),this.dispose()}static jQueryInterface(n){return this.each(function(){const s=wt.getOrCreateInstance(this);if("string"==typeof n){if(void 0===s[n]||n.startsWith("_")||"constructor"===n)throw new TypeError(`No method named "${n}"`);s[n](this)}})}}St(wt,"close"),Ie(wt);const ot='[data-bs-toggle="button"]';class Yt extends Ye{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(n){return this.each(function(){const s=Yt.getOrCreateInstance(this);"toggle"===n&&s[n]()})}}O.on(document,"click.bs.button.data-api",ot,g=>{g.preventDefault();const n=g.target.closest(ot);Yt.getOrCreateInstance(n).toggle()}),Ie(Yt);const Ft=".bs.swipe",vi=`touchstart${Ft}`,dt=`touchmove${Ft}`,Qt=`touchend${Ft}`,Kt=`pointerdown${Ft}`,z=`pointerup${Ft}`,Me={endCallback:null,leftCallback:null,rightCallback:null},Dn={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class fn extends Rt{constructor(n,s){super(),this._element=n,n&&fn.isSupported()&&(this._config=this._getConfig(s),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Me}static get DefaultType(){return Dn}static get NAME(){return"swipe"}dispose(){O.off(this._element,Ft)}_start(n){this._supportPointerEvents?this._eventIsPointerPenTouch(n)&&(this._deltaX=n.clientX):this._deltaX=n.touches[0].clientX}_end(n){this._eventIsPointerPenTouch(n)&&(this._deltaX=n.clientX-this._deltaX),this._handleSwipe(),Ee(this._config.endCallback)}_move(n){this._deltaX=n.touches&&n.touches.length>1?0:n.touches[0].clientX-this._deltaX}_handleSwipe(){const n=Math.abs(this._deltaX);if(n<=40)return;const s=n/this._deltaX;this._deltaX=0,s&&Ee(s>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(O.on(this._element,Kt,n=>this._start(n)),O.on(this._element,z,n=>this._end(n)),this._element.classList.add("pointer-event")):(O.on(this._element,vi,n=>this._start(n)),O.on(this._element,dt,n=>this._move(n)),O.on(this._element,Qt,n=>this._end(n)))}_eventIsPointerPenTouch(n){return this._supportPointerEvents&&("pen"===n.pointerType||"touch"===n.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const st=".bs.carousel",Ln=".data-api",xt="next",_e="prev",Oe="left",Je="right",ei=`slide${st}`,Gt=`slid${st}`,Nn=`keydown${st}`,$n=`mouseenter${st}`,Qe=`mouseleave${st}`,Jt=`dragstart${st}`,Bt=`load${st}${Ln}`,hn=`click${st}${Ln}`,qt="carousel",He="active",ti={ArrowLeft:Je,ArrowRight:Oe},ni={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Dt={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ze extends Ye{constructor(n,s){super(n,s),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=W.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===qt&&this.cycle()}static get Default(){return ni}static get DefaultType(){return Dt}static get NAME(){return"carousel"}next(){this._slide(xt)}nextWhenVisible(){!document.hidden&&je(this._element)&&this.next()}prev(){this._slide(_e)}pause(){this._isSliding&&Pe(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?O.one(this._element,Gt,()=>this.cycle()):this.cycle())}to(n){const s=this._getItems();if(n>s.length-1||n<0)return;if(this._isSliding)return void O.one(this._element,Gt,()=>this.to(n));const p=this._getItemIndex(this._getActive());p!==n&&this._slide(n>p?xt:_e,s[n])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(n){return n.defaultInterval=n.interval,n}_addEventListeners(){this._config.keyboard&&O.on(this._element,Nn,n=>this._keydown(n)),"hover"===this._config.pause&&(O.on(this._element,$n,()=>this.pause()),O.on(this._element,Qe,()=>this._maybeEnableCycle())),this._config.touch&&fn.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of W.find(".carousel-item img",this._element))O.on(s,Jt,p=>p.preventDefault());this._swipeHelper=new fn(this._element,{leftCallback:()=>this._slide(this._directionToOrder(Oe)),rightCallback:()=>this._slide(this._directionToOrder(Je)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}})}_keydown(n){if(/input|textarea/i.test(n.target.tagName))return;const s=ti[n.key];s&&(n.preventDefault(),this._slide(this._directionToOrder(s)))}_getItemIndex(n){return this._getItems().indexOf(n)}_setActiveIndicatorElement(n){if(!this._indicatorsElement)return;const s=W.findOne(".active",this._indicatorsElement);s.classList.remove(He),s.removeAttribute("aria-current");const p=W.findOne(`[data-bs-slide-to="${n}"]`,this._indicatorsElement);p&&(p.classList.add(He),p.setAttribute("aria-current","true"))}_updateInterval(){const n=this._activeElement||this._getActive();if(!n)return;const s=Number.parseInt(n.getAttribute("data-bs-interval"),10);this._config.interval=s||this._config.defaultInterval}_slide(n,s=null){if(this._isSliding)return;const p=this._getActive(),k=n===xt,P=s||Ut(this._getItems(),p,k,this._config.wrap);if(P===p)return;const q=this._getItemIndex(P),ce=jt=>O.trigger(this._element,jt,{relatedTarget:P,direction:this._orderToDirection(n),from:this._getItemIndex(p),to:q});if(ce(ei).defaultPrevented||!p||!P)return;const Te=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(q),this._activeElement=P;const ct=k?"carousel-item-start":"carousel-item-end",bt=k?"carousel-item-next":"carousel-item-prev";P.classList.add(bt),p.classList.add(ct),P.classList.add(ct),this._queueCallback(()=>{P.classList.remove(ct,bt),P.classList.add(He),p.classList.remove(He,bt,ct),this._isSliding=!1,ce(Gt)},p,this._isAnimated()),Te&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return W.findOne(".active.carousel-item",this._element)}_getItems(){return W.find(".carousel-item",this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(n){return xe()?n===Oe?_e:xt:n===Oe?xt:_e}_orderToDirection(n){return xe()?n===_e?Oe:Je:n===_e?Je:Oe}static jQueryInterface(n){return this.each(function(){const s=Ze.getOrCreateInstance(this,n);if("number"!=typeof n){if("string"==typeof n){if(void 0===s[n]||n.startsWith("_")||"constructor"===n)throw new TypeError(`No method named "${n}"`);s[n]()}}else s.to(n)})}}O.on(document,hn,"[data-bs-slide], [data-bs-slide-to]",function(g){const n=W.getElementFromSelector(this);if(!n||!n.classList.contains(qt))return;g.preventDefault();const s=Ze.getOrCreateInstance(n),p=this.getAttribute("data-bs-slide-to");return p?(s.to(p),void s._maybeEnableCycle()):"next"===rt.getDataAttribute(this,"slide")?(s.next(),void s._maybeEnableCycle()):(s.prev(),void s._maybeEnableCycle())}),O.on(window,Bt,()=>{const g=W.find('[data-bs-ride="carousel"]');for(const n of g)Ze.getOrCreateInstance(n)}),Ie(Ze);const ft=".bs.collapse",gn=`show${ft}`,yi=`shown${ft}`,jn=`hide${ft}`,bi=`hidden${ft}`,ii=`click${ft}.data-api`,mn="show",Wt="collapse",Et="collapsing",zt=`:scope .${Wt} .${Wt}`,Zt='[data-bs-toggle="collapse"]',In={parent:null,toggle:!0},Mn={parent:"(null|element)",toggle:"boolean"};class ht extends Ye{constructor(n,s){super(n,s),this._isTransitioning=!1,this._triggerArray=[];const p=W.find(Zt);for(const k of p){const P=W.getSelectorFromElement(k),q=W.find(P).filter(ce=>ce===this._element);null!==P&&q.length&&this._triggerArray.push(k)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return In}static get DefaultType(){return Mn}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let n=[];if(this._config.parent&&(n=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(k=>k!==this._element).map(k=>ht.getOrCreateInstance(k,{toggle:!1}))),n.length&&n[0]._isTransitioning||O.trigger(this._element,gn).defaultPrevented)return;for(const k of n)k.hide();const s=this._getDimension();this._element.classList.remove(Wt),this._element.classList.add(Et),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const p=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Et),this._element.classList.add(Wt,mn),this._element.style[s]="",O.trigger(this._element,yi)},this._element,!0),this._element.style[s]=`${this._element[p]}px`}hide(){if(this._isTransitioning||!this._isShown()||O.trigger(this._element,jn).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,this._element.classList.add(Et),this._element.classList.remove(Wt,mn);for(const s of this._triggerArray){const p=W.getElementFromSelector(s);p&&!this._isShown(p)&&this._addAriaAndCollapsedClass([s],!1)}this._isTransitioning=!0,this._element.style[n]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Et),this._element.classList.add(Wt),O.trigger(this._element,bi)},this._element,!0)}_isShown(n=this._element){return n.classList.contains(mn)}_configAfterMerge(n){return n.toggle=Boolean(n.toggle),n.parent=it(n.parent),n}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const n=this._getFirstLevelChildren(Zt);for(const s of n){const p=W.getElementFromSelector(s);p&&this._addAriaAndCollapsedClass([s],this._isShown(p))}}_getFirstLevelChildren(n){const s=W.find(zt,this._config.parent);return W.find(n,this._config.parent).filter(p=>!s.includes(p))}_addAriaAndCollapsedClass(n,s){if(n.length)for(const p of n)p.classList.toggle("collapsed",!s),p.setAttribute("aria-expanded",s)}static jQueryInterface(n){const s={};return"string"==typeof n&&/show|hide/.test(n)&&(s.toggle=!1),this.each(function(){const p=ht.getOrCreateInstance(this,s);if("string"==typeof n){if(void 0===p[n])throw new TypeError(`No method named "${n}"`);p[n]()}})}}O.on(document,ii,Zt,function(g){("A"===g.target.tagName||g.delegateTarget&&"A"===g.delegateTarget.tagName)&&g.preventDefault();for(const n of W.getMultipleElementsFromSelector(this))ht.getOrCreateInstance(n,{toggle:!1}).toggle()}),Ie(ht);const vn="dropdown",Lt=".bs.dropdown",et=".data-api",ri="ArrowUp",Hn="ArrowDown",Rn=`hide${Lt}`,oi=`hidden${Lt}`,si=`show${Lt}`,Vt=`shown${Lt}`,yn=`click${Lt}${et}`,Fn=`keydown${Lt}${et}`,ai=`keyup${Lt}${et}`,Nt="show",pt='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Bn=`${pt}.show`,en=".dropdown-menu",qn=xe()?"top-end":"top-start",tn=xe()?"top-start":"top-end",d=xe()?"bottom-end":"bottom-start",f=xe()?"bottom-start":"bottom-end",m=xe()?"left-start":"right-start",E=xe()?"right-start":"left-start",A={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},L={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class j extends Ye{constructor(n,s){super(n,s),this._popper=null,this._parent=this._element.parentNode,this._menu=W.next(this._element,en)[0]||W.prev(this._element,en)[0]||W.findOne(en,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return A}static get DefaultType(){return L}static get NAME(){return vn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Ge(this._element)||this._isShown())return;const n={relatedTarget:this._element};if(!O.trigger(this._element,si,n).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const s of[].concat(...document.body.children))O.on(s,"mouseover",ee);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Nt),this._element.classList.add(Nt),O.trigger(this._element,Vt,n)}}hide(){!Ge(this._element)&&this._isShown()&&this._completeHide({relatedTarget:this._element})}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(n){if(!O.trigger(this._element,Rn,n).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))O.off(s,"mouseover",ee);this._popper&&this._popper.destroy(),this._menu.classList.remove(Nt),this._element.classList.remove(Nt),this._element.setAttribute("aria-expanded","false"),rt.removeDataAttribute(this._menu,"popper"),O.trigger(this._element,oi,n)}}_getConfig(n){if("object"==typeof(n=super._getConfig(n)).reference&&!Re(n.reference)&&"function"!=typeof n.reference.getBoundingClientRect)throw new TypeError(`${vn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return n}_createPopper(){if(void 0===ne)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let n=this._element;"parent"===this._config.reference?n=this._parent:Re(this._config.reference)?n=it(this._config.reference):"object"==typeof this._config.reference&&(n=this._config.reference);const s=this._getPopperConfig();this._popper=ne.createPopper(n,this._menu,s)}_isShown(){return this._menu.classList.contains(Nt)}_getPlacement(){const n=this._parent;if(n.classList.contains("dropend"))return m;if(n.classList.contains("dropstart"))return E;if(n.classList.contains("dropup-center"))return"top";if(n.classList.contains("dropdown-center"))return"bottom";const s="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return n.classList.contains("dropup")?s?tn:qn:s?f:d}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:n}=this._config;return"string"==typeof n?n.split(",").map(s=>Number.parseInt(s,10)):"function"==typeof n?s=>n(s,this._element):n}_getPopperConfig(){const n={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(rt.setDataAttribute(this._menu,"popper","static"),n.modifiers=[{name:"applyStyles",enabled:!1}]),Ke(Ke({},n),Ee(this._config.popperConfig,[n]))}_selectMenuItem({key:n,target:s}){const p=W.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(k=>je(k));p.length&&Ut(p,s,n===Hn,!p.includes(s)).focus()}static jQueryInterface(n){return this.each(function(){const s=j.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n])throw new TypeError(`No method named "${n}"`);s[n]()}})}static clearMenus(n){if(2===n.button||"keyup"===n.type&&"Tab"!==n.key)return;const s=W.find(Bn);for(const p of s){const k=j.getInstance(p);if(!k||!1===k._config.autoClose)continue;const P=n.composedPath(),q=P.includes(k._menu);if(P.includes(k._element)||"inside"===k._config.autoClose&&!q||"outside"===k._config.autoClose&&q||k._menu.contains(n.target)&&("keyup"===n.type&&"Tab"===n.key||/input|select|option|textarea|form/i.test(n.target.tagName)))continue;const ce={relatedTarget:k._element};"click"===n.type&&(ce.clickEvent=n),k._completeHide(ce)}}static dataApiKeydownHandler(n){const s=/input|textarea/i.test(n.target.tagName),p="Escape"===n.key,k=[ri,Hn].includes(n.key);if(!k&&!p||s&&!p)return;n.preventDefault();const P=this.matches(pt)?this:W.prev(this,pt)[0]||W.next(this,pt)[0]||W.findOne(pt,n.delegateTarget.parentNode),q=j.getOrCreateInstance(P);if(k)return n.stopPropagation(),q.show(),void q._selectMenuItem(n);q._isShown()&&(n.stopPropagation(),q.hide(),P.focus())}}O.on(document,Fn,pt,j.dataApiKeydownHandler),O.on(document,Fn,en,j.dataApiKeydownHandler),O.on(document,yn,j.clearMenus),O.on(document,ai,j.clearMenus),O.on(document,yn,pt,function(g){g.preventDefault(),j.getOrCreateInstance(this).toggle()}),Ie(j);const N="backdrop",D="show",V=`mousedown.bs.${N}`,I={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},de={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class he extends Rt{constructor(n){super(),this._config=this._getConfig(n),this._isAppended=!1,this._element=null}static get Default(){return I}static get DefaultType(){return de}static get NAME(){return N}show(n){if(!this._config.isVisible)return void Ee(n);this._append();this._getElement().classList.add(D),this._emulateAnimation(()=>{Ee(n)})}hide(n){this._config.isVisible?(this._getElement().classList.remove(D),this._emulateAnimation(()=>{this.dispose(),Ee(n)})):Ee(n)}dispose(){this._isAppended&&(O.off(this._element,V),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const n=document.createElement("div");n.className=this._config.className,this._config.isAnimated&&n.classList.add("fade"),this._element=n}return this._element}_configAfterMerge(n){return n.rootElement=it(n.rootElement),n}_append(){if(this._isAppended)return;const n=this._getElement();this._config.rootElement.append(n),O.on(n,V,()=>{Ee(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(n){An(n,this._getElement(),this._config.isAnimated)}}const Y=".bs.focustrap",me=`focusin${Y}`,se=`keydown.tab${Y}`,re="backward",e={autofocus:!0,trapElement:null},t={autofocus:"boolean",trapElement:"element"};class i extends Rt{constructor(n){super(),this._config=this._getConfig(n),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return e}static get DefaultType(){return t}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),O.off(document,Y),O.on(document,me,n=>this._handleFocusin(n)),O.on(document,se,n=>this._handleKeydown(n)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,O.off(document,Y))}_handleFocusin(n){const{trapElement:s}=this._config;if(n.target===document||n.target===s||s.contains(n.target))return;const p=W.focusableChildren(s);0===p.length?s.focus():this._lastTabNavDirection===re?p[p.length-1].focus():p[0].focus()}_handleKeydown(n){"Tab"===n.key&&(this._lastTabNavDirection=n.shiftKey?re:"forward")}}const r=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",a=".sticky-top",l="padding-right",c="margin-right";class v{constructor(){this._element=document.body}getWidth(){const n=document.documentElement.clientWidth;return Math.abs(window.innerWidth-n)}hide(){const n=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,l,s=>s+n),this._setElementAttributes(r,l,s=>s+n),this._setElementAttributes(a,c,s=>s-n)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,l),this._resetElementAttributes(r,l),this._resetElementAttributes(a,c)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(n,s,p){const k=this.getWidth();this._applyManipulationCallback(n,P=>{if(P!==this._element&&window.innerWidth>P.clientWidth+k)return;this._saveInitialAttribute(P,s);const q=window.getComputedStyle(P).getPropertyValue(s);P.style.setProperty(s,`${p(Number.parseFloat(q))}px`)})}_saveInitialAttribute(n,s){const p=n.style.getPropertyValue(s);p&&rt.setDataAttribute(n,s,p)}_resetElementAttributes(n,s){this._applyManipulationCallback(n,p=>{const k=rt.getDataAttribute(p,s);null!==k?(rt.removeDataAttribute(p,s),p.style.setProperty(s,k)):p.style.removeProperty(s)})}_applyManipulationCallback(n,s){if(Re(n))s(n);else for(const p of W.find(n,this._element))s(p)}}const h=".bs.modal",x=`hide${h}`,w=`hidePrevented${h}`,T=`hidden${h}`,$=`show${h}`,S=`shown${h}`,J=`resize${h}`,te=`click.dismiss${h}`,ae=`mousedown.dismiss${h}`,ke=`keydown.dismiss${h}`,pe=`click${h}.data-api`,Ce="modal-open",At="modal-static",nn={backdrop:!0,focus:!0,keyboard:!0},Xt={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Be extends Ye{constructor(n,s){super(n,s),this._dialog=W.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new v,this._addEventListeners()}static get Default(){return nn}static get DefaultType(){return Xt}static get NAME(){return"modal"}toggle(n){return this._isShown?this.hide():this.show(n)}show(n){this._isShown||this._isTransitioning||O.trigger(this._element,$,{relatedTarget:n}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ce),this._adjustDialog(),this._backdrop.show(()=>this._showElement(n)))}hide(){this._isShown&&!this._isTransitioning&&(O.trigger(this._element,x).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove("show"),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){O.off(window,h),O.off(this._dialog,h),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new he({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new i({trapElement:this._element})}_showElement(n){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const s=W.findOne(".modal-body",this._dialog);s&&(s.scrollTop=0),this._element.classList.add("show"),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,O.trigger(this._element,S,{relatedTarget:n})},this._dialog,this._isAnimated())}_addEventListeners(){O.on(this._element,ke,n=>{"Escape"===n.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),O.on(window,J,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),O.on(this._element,ae,n=>{O.one(this._element,te,s=>{this._element===n.target&&this._element===s.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ce),this._resetAdjustments(),this._scrollBar.reset(),O.trigger(this._element,T)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(O.trigger(this._element,w).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;"hidden"===s||this._element.classList.contains(At)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(At),this._queueCallback(()=>{this._element.classList.remove(At),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const n=this._element.scrollHeight>document.documentElement.clientHeight,s=this._scrollBar.getWidth(),p=s>0;if(p&&!n){const k=xe()?"paddingLeft":"paddingRight";this._element.style[k]=`${s}px`}if(!p&&n){const k=xe()?"paddingRight":"paddingLeft";this._element.style[k]=`${s}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(n,s){return this.each(function(){const p=Be.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===p[n])throw new TypeError(`No method named "${n}"`);p[n](s)}})}}O.on(document,pe,'[data-bs-toggle="modal"]',function(g){const n=W.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&g.preventDefault(),O.one(n,$,p=>{p.defaultPrevented||O.one(n,T,()=>{je(this)&&this.focus()})});const s=W.findOne(".modal.show");s&&Be.getInstance(s).hide(),Be.getOrCreateInstance(n).toggle(this)}),St(Be),Ie(Be);const qe=".bs.offcanvas",rn=".data-api",$t=`load${qe}${rn}`,on="showing",li=".offcanvas.show",ci=`show${qe}`,Wn=`shown${qe}`,gt=`hide${qe}`,tt=`hidePrevented${qe}`,zn=`hidden${qe}`,_i=`resize${qe}`,fe=`click${qe}${rn}`,at=`keydown.dismiss${qe}`,We={backdrop:!0,keyboard:!0,scroll:!1},Pt={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class lt extends Ye{constructor(n,s){super(n,s),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return We}static get DefaultType(){return Pt}static get NAME(){return"offcanvas"}toggle(n){return this._isShown?this.hide():this.show(n)}show(n){this._isShown||O.trigger(this._element,ci,{relatedTarget:n}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new v).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(on),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add("show"),this._element.classList.remove(on),O.trigger(this._element,Wn,{relatedTarget:n})},this._element,!0))}hide(){this._isShown&&(O.trigger(this._element,gt).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add("hiding"),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove("show","hiding"),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new v).reset(),O.trigger(this._element,zn)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const n=Boolean(this._config.backdrop);return new he({className:"offcanvas-backdrop",isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?()=>{"static"!==this._config.backdrop?this.hide():O.trigger(this._element,tt)}:null})}_initializeFocusTrap(){return new i({trapElement:this._element})}_addEventListeners(){O.on(this._element,at,n=>{"Escape"===n.key&&(this._config.keyboard?this.hide():O.trigger(this._element,tt))})}static jQueryInterface(n){return this.each(function(){const s=lt.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n]||n.startsWith("_")||"constructor"===n)throw new TypeError(`No method named "${n}"`);s[n](this)}})}}O.on(document,fe,'[data-bs-toggle="offcanvas"]',function(g){const n=W.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&g.preventDefault(),Ge(this))return;O.one(n,zn,()=>{je(this)&&this.focus()});const s=W.findOne(li);s&&s!==n&&lt.getInstance(s).hide(),lt.getOrCreateInstance(n).toggle(this)}),O.on(window,$t,()=>{for(const g of W.find(li))lt.getOrCreateInstance(g).show()}),O.on(window,_i,()=>{for(const g of W.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(g).position&&lt.getOrCreateInstance(g).hide()}),St(lt),Ie(lt);const wn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Vn=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Ct=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,xn=(g,n)=>{const s=g.nodeName.toLowerCase();return n.includes(s)?!Vn.has(s)||Boolean(Ct.test(g.nodeValue)):n.filter(p=>p instanceof RegExp).some(p=>p.test(s))},mt={allowList:wn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Ei={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Xn={entry:"(string|element|function|null)",selector:"(string|element)"};class ui extends Rt{constructor(n){super(),this._config=this._getConfig(n)}static get Default(){return mt}static get DefaultType(){return Ei}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(n=>this._resolvePossibleFunction(n)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(n){return this._checkContent(n),this._config.content=Ke(Ke({},this._config.content),n),this}toHtml(){const n=document.createElement("div");n.innerHTML=this._maybeSanitize(this._config.template);for(const[k,P]of Object.entries(this._config.content))this._setContent(n,P,k);const s=n.children[0],p=this._resolvePossibleFunction(this._config.extraClass);return p&&s.classList.add(...p.split(" ")),s}_typeCheckConfig(n){super._typeCheckConfig(n),this._checkContent(n.content)}_checkContent(n){for(const[s,p]of Object.entries(n))super._typeCheckConfig({selector:s,entry:p},Xn)}_setContent(n,s,p){const k=W.findOne(p,n);k&&((s=this._resolvePossibleFunction(s))?Re(s)?this._putElementInTemplate(it(s),k):this._config.html?k.innerHTML=this._maybeSanitize(s):k.textContent=s:k.remove())}_maybeSanitize(n){return this._config.sanitize?function(s,p,k){if(!s.length)return s;if(k&&"function"==typeof k)return k(s);const P=(new window.DOMParser).parseFromString(s,"text/html"),q=[].concat(...P.body.querySelectorAll("*"));for(const ce of q){const Te=ce.nodeName.toLowerCase();if(!Object.keys(p).includes(Te)){ce.remove();continue}const ct=[].concat(...ce.attributes),bt=[].concat(p["*"]||[],p[Te]||[]);for(const jt of ct)xn(jt,bt)||ce.removeAttribute(jt.nodeName)}return P.body.innerHTML}(n,this._config.allowList,this._config.sanitizeFn):n}_resolvePossibleFunction(n){return Ee(n,[this])}_putElementInTemplate(n,s){if(this._config.html)return s.innerHTML="",void s.append(n);s.textContent=n.textContent}}const di=new Set(["sanitize","allowList","sanitizeFn"]),Un="fade",sn="show",hi="hide.bs.modal",an="hover",pi="focus",u={AUTO:"auto",TOP:"top",RIGHT:xe()?"left":"right",BOTTOM:"bottom",LEFT:xe()?"right":"left"},y={allowList:wn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},b={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class _ extends Ye{constructor(n,s){if(void 0===ne)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(n,s),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return y}static get DefaultType(){return b}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),O.off(this._element.closest(".modal"),hi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const n=O.trigger(this._element,this.constructor.eventName("show")),s=(Jn(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(n.defaultPrevented||!s)return;this._disposePopper();const p=this._getTipElement();this._element.setAttribute("aria-describedby",p.getAttribute("id"));const{container:k}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(k.append(p),O.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(p),p.classList.add(sn),"ontouchstart"in document.documentElement)for(const P of[].concat(...document.body.children))O.on(P,"mouseover",ee);this._queueCallback(()=>{O.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!O.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(sn),"ontouchstart"in document.documentElement)for(const n of[].concat(...document.body.children))O.off(n,"mouseover",ee);this._activeTrigger.click=!1,this._activeTrigger[pi]=!1,this._activeTrigger[an]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),O.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(n){const s=this._getTemplateFactory(n).toHtml();if(!s)return null;s.classList.remove(Un,sn),s.classList.add(`bs-${this.constructor.NAME}-auto`);const p=(k=>{do{k+=Math.floor(1e6*Math.random())}while(document.getElementById(k));return k})(this.constructor.NAME).toString();return s.setAttribute("id",p),this._isAnimated()&&s.classList.add(Un),s}setContent(n){this._newContent=n,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(n){return this._templateFactory?this._templateFactory.changeContent(n):this._templateFactory=new ui(Si(Ke({},this._config),{content:n,extraClass:this._resolvePossibleFunction(this._config.customClass)})),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(n){return this.constructor.getOrCreateInstance(n.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Un)}_isShown(){return this.tip&&this.tip.classList.contains(sn)}_createPopper(n){const s=Ee(this._config.placement,[this,n,this._element]),p=u[s.toUpperCase()];return ne.createPopper(this._element,n,this._getPopperConfig(p))}_getOffset(){const{offset:n}=this._config;return"string"==typeof n?n.split(",").map(s=>Number.parseInt(s,10)):"function"==typeof n?s=>n(s,this._element):n}_resolvePossibleFunction(n){return Ee(n,[this._element])}_getPopperConfig(n){const s={placement:n,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:p=>{this._getTipElement().setAttribute("data-popper-placement",p.state.placement)}}]};return Ke(Ke({},s),Ee(this._config.popperConfig,[s]))}_setListeners(){const n=this._config.trigger.split(" ");for(const s of n)if("click"===s)O.on(this._element,this.constructor.eventName("click"),this._config.selector,p=>{this._initializeOnDelegatedTarget(p).toggle()});else if("manual"!==s){const p=this.constructor.eventName(s===an?"mouseenter":"focusin"),k=this.constructor.eventName(s===an?"mouseleave":"focusout");O.on(this._element,p,this._config.selector,P=>{const q=this._initializeOnDelegatedTarget(P);q._activeTrigger["focusin"===P.type?pi:an]=!0,q._enter()}),O.on(this._element,k,this._config.selector,P=>{const q=this._initializeOnDelegatedTarget(P);q._activeTrigger["focusout"===P.type?pi:an]=q._element.contains(P.relatedTarget),q._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},O.on(this._element.closest(".modal"),hi,this._hideModalHandler)}_fixTitle(){const n=this._element.getAttribute("title");n&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",n),this._element.setAttribute("data-bs-original-title",n),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(n,s){clearTimeout(this._timeout),this._timeout=setTimeout(n,s)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(n){const s=rt.getDataAttributes(this._element);for(const p of Object.keys(s))di.has(p)&&delete s[p];return n=Ke(Ke({},s),"object"==typeof n&&n?n:{}),n=this._mergeConfigObj(n),n=this._configAfterMerge(n),this._typeCheckConfig(n),n}_configAfterMerge(n){return n.container=!1===n.container?document.body:it(n.container),"number"==typeof n.delay&&(n.delay={show:n.delay,hide:n.delay}),"number"==typeof n.title&&(n.title=n.title.toString()),"number"==typeof n.content&&(n.content=n.content.toString()),n}_getDelegateConfig(){const n={};for(const[s,p]of Object.entries(this._config))this.constructor.Default[s]!==p&&(n[s]=p);return n.selector=!1,n.trigger="manual",n}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(n){return this.each(function(){const s=_.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n])throw new TypeError(`No method named "${n}"`);s[n]()}})}}Ie(_);const C=Si(Ke({},_.Default),{content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"}),F=Si(Ke({},_.DefaultType),{content:"(null|string|element|function)"});class M extends _{static get Default(){return C}static get DefaultType(){return F}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(n){return this.each(function(){const s=M.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n])throw new TypeError(`No method named "${n}"`);s[n]()}})}}Ie(M);const B=".bs.scrollspy",H=`activate${B}`,le=`click${B}`,G=`load${B}.data-api`,U="active",ie="[href]",X=".nav-link",De=`${X}, .nav-item > ${X}, .list-group-item`,ze={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ne={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class $e extends Ye{constructor(n,s){super(n,s),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return ze}static get DefaultType(){return Ne}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const n of this._observableSections.values())this._observer.observe(n)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(n){return n.target=it(n.target)||document.body,n.rootMargin=n.offset?`${n.offset}px 0px -30%`:n.rootMargin,"string"==typeof n.threshold&&(n.threshold=n.threshold.split(",").map(s=>Number.parseFloat(s))),n}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(O.off(this._config.target,le),O.on(this._config.target,le,ie,n=>{const s=this._observableSections.get(n.target.hash);if(s){n.preventDefault();const p=this._rootElement||window,k=s.offsetTop-this._element.offsetTop;if(p.scrollTo)return void p.scrollTo({top:k,behavior:"smooth"});p.scrollTop=k}}))}_getNewObserver(){return new IntersectionObserver(s=>this._observerCallback(s),{root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin})}_observerCallback(n){const s=q=>this._targetLinks.get(`#${q.target.id}`),p=q=>{this._previousScrollData.visibleEntryTop=q.target.offsetTop,this._process(s(q))},k=(this._rootElement||document.documentElement).scrollTop,P=k>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=k;for(const q of n){if(!q.isIntersecting){this._activeTarget=null,this._clearActiveClass(s(q));continue}const ce=q.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(P&&ce){if(p(q),!k)return}else P||ce||p(q)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const n=W.find(ie,this._config.target);for(const s of n){if(!s.hash||Ge(s))continue;const p=W.findOne(decodeURI(s.hash),this._element);je(p)&&(this._targetLinks.set(decodeURI(s.hash),s),this._observableSections.set(s.hash,p))}}_process(n){this._activeTarget!==n&&(this._clearActiveClass(this._config.target),this._activeTarget=n,n.classList.add(U),this._activateParents(n),O.trigger(this._element,H,{relatedTarget:n}))}_activateParents(n){if(n.classList.contains("dropdown-item"))W.findOne(".dropdown-toggle",n.closest(".dropdown")).classList.add(U);else for(const s of W.parents(n,".nav, .list-group"))for(const p of W.prev(s,De))p.classList.add(U)}_clearActiveClass(n){n.classList.remove(U);const s=W.find(`${ie}.${U}`,n);for(const p of s)p.classList.remove(U)}static jQueryInterface(n){return this.each(function(){const s=$e.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n]||n.startsWith("_")||"constructor"===n)throw new TypeError(`No method named "${n}"`);s[n]()}})}}O.on(window,G,()=>{for(const g of W.find('[data-bs-spy="scroll"]'))$e.getOrCreateInstance(g)}),Ie($e);const Le=".bs.tab",vt=`hide${Le}`,be=`hidden${Le}`,ln=`show${Le}`,yt=`shown${Le}`,gi=`click${Le}`,Ai=`keydown${Le}`,Ci=`load${Le}`,Di="ArrowLeft",ji="ArrowRight",zi="ArrowUp",Ii="ArrowDown",Li="Home",Mi="End",Yn="active",Ni="show",Ri=".dropdown-toggle",$i=`:not(${Ri})`,Fi='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Pi=`.nav-link${$i}, .list-group-item${$i}, [role="tab"]${$i}, ${Fi}`,Vi=`.${Yn}[data-bs-toggle="tab"], .${Yn}[data-bs-toggle="pill"], .${Yn}[data-bs-toggle="list"]`;class Qn extends Ye{constructor(n){super(n),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),O.on(this._element,Ai,s=>this._keydown(s)))}static get NAME(){return"tab"}show(){const n=this._element;if(this._elemIsActive(n))return;const s=this._getActiveElem(),p=s?O.trigger(s,vt,{relatedTarget:n}):null;O.trigger(n,ln,{relatedTarget:s}).defaultPrevented||p&&p.defaultPrevented||(this._deactivate(s,n),this._activate(n,s))}_activate(n,s){n&&(n.classList.add(Yn),this._activate(W.getElementFromSelector(n)),this._queueCallback(()=>{"tab"===n.getAttribute("role")?(n.removeAttribute("tabindex"),n.setAttribute("aria-selected",!0),this._toggleDropDown(n,!0),O.trigger(n,yt,{relatedTarget:s})):n.classList.add(Ni)},n,n.classList.contains("fade")))}_deactivate(n,s){n&&(n.classList.remove(Yn),n.blur(),this._deactivate(W.getElementFromSelector(n)),this._queueCallback(()=>{"tab"===n.getAttribute("role")?(n.setAttribute("aria-selected",!1),n.setAttribute("tabindex","-1"),this._toggleDropDown(n,!1),O.trigger(n,be,{relatedTarget:s})):n.classList.remove(Ni)},n,n.classList.contains("fade")))}_keydown(n){if(![Di,ji,zi,Ii,Li,Mi].includes(n.key))return;n.stopPropagation(),n.preventDefault();const s=this._getChildren().filter(k=>!Ge(k));let p;if([Li,Mi].includes(n.key))p=s[n.key===Li?0:s.length-1];else{const k=[ji,Ii].includes(n.key);p=Ut(s,n.target,k,!0)}p&&(p.focus({preventScroll:!0}),Qn.getOrCreateInstance(p).show())}_getChildren(){return W.find(Pi,this._parent)}_getActiveElem(){return this._getChildren().find(n=>this._elemIsActive(n))||null}_setInitialAttributes(n,s){this._setAttributeIfNotExists(n,"role","tablist");for(const p of s)this._setInitialAttributesOnChild(p)}_setInitialAttributesOnChild(n){n=this._getInnerElement(n);const s=this._elemIsActive(n),p=this._getOuterElement(n);n.setAttribute("aria-selected",s),p!==n&&this._setAttributeIfNotExists(p,"role","presentation"),s||n.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(n,"role","tab"),this._setInitialAttributesOnTargetPanel(n)}_setInitialAttributesOnTargetPanel(n){const s=W.getElementFromSelector(n);s&&(this._setAttributeIfNotExists(s,"role","tabpanel"),n.id&&this._setAttributeIfNotExists(s,"aria-labelledby",`${n.id}`))}_toggleDropDown(n,s){const p=this._getOuterElement(n);if(!p.classList.contains("dropdown"))return;const k=(P,q)=>{const ce=W.findOne(P,p);ce&&ce.classList.toggle(q,s)};k(Ri,Yn),k(".dropdown-menu",Ni),p.setAttribute("aria-expanded",s)}_setAttributeIfNotExists(n,s,p){n.hasAttribute(s)||n.setAttribute(s,p)}_elemIsActive(n){return n.classList.contains(Yn)}_getInnerElement(n){return n.matches(Pi)?n:W.findOne(Pi,n)}_getOuterElement(n){return n.closest(".nav-item, .list-group-item")||n}static jQueryInterface(n){return this.each(function(){const s=Qn.getOrCreateInstance(this);if("string"==typeof n){if(void 0===s[n]||n.startsWith("_")||"constructor"===n)throw new TypeError(`No method named "${n}"`);s[n]()}})}}O.on(document,gi,Fi,function(g){["A","AREA"].includes(this.tagName)&&g.preventDefault(),Ge(this)||Qn.getOrCreateInstance(this).show()}),O.on(window,Ci,()=>{for(const g of W.find(Vi))Qn.getOrCreateInstance(g)}),Ie(Qn);const En=".bs.toast",Xi=`mouseover${En}`,Ui=`mouseout${En}`,Yi=`focusin${En}`,Qi=`focusout${En}`,Ki=`hide${En}`,Gi=`hidden${En}`,Ji=`show${En}`,Zi=`shown${En}`,Ti="show",ki="showing",er={animation:"boolean",autohide:"boolean",delay:"number"},tr={animation:!0,autohide:!0,delay:5e3};class wi extends Ye{constructor(n,s){super(n,s),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return tr}static get DefaultType(){return er}static get NAME(){return"toast"}show(){O.trigger(this._element,Ji).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),this._element.classList.add(Ti,ki),this._queueCallback(()=>{this._element.classList.remove(ki),O.trigger(this._element,Zi),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(O.trigger(this._element,Ki).defaultPrevented||(this._element.classList.add(ki),this._queueCallback(()=>{this._element.classList.add("hide"),this._element.classList.remove(ki,Ti),O.trigger(this._element,Gi)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Ti),super.dispose()}isShown(){return this._element.classList.contains(Ti)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(n,s){switch(n.type){case"mouseover":case"mouseout":this._hasMouseInteraction=s;break;case"focusin":case"focusout":this._hasKeyboardInteraction=s}if(s)return void this._clearTimeout();const p=n.relatedTarget;this._element===p||this._element.contains(p)||this._maybeScheduleHide()}_setListeners(){O.on(this._element,Xi,n=>this._onInteraction(n,!0)),O.on(this._element,Ui,n=>this._onInteraction(n,!1)),O.on(this._element,Yi,n=>this._onInteraction(n,!0)),O.on(this._element,Qi,n=>this._onInteraction(n,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(n){return this.each(function(){const s=wi.getOrCreateInstance(this,n);if("string"==typeof n){if(void 0===s[n])throw new TypeError(`No method named "${n}"`);s[n](this)}})}}return St(wi),Ie(wi),{Alert:wt,Button:Yt,Carousel:Ze,Collapse:ht,Dropdown:j,Modal:Be,Offcanvas:lt,Popover:M,ScrollSpy:$e,Tab:Qn,Toast:wi,Tooltip:_}});