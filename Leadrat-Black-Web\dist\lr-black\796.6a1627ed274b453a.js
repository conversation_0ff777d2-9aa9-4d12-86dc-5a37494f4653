"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[796],{91507:(ie,Z,n)=>{n.d(Z,{T:()=>xe});var e=n(5e3),v=n(93075),H=n(96170),U=n.n(H),E=n(82722),w=n(35684),V=n(95698),A=n(2976),j=n(51420),I=n(61021),X=n(96494),$=n(82667),B=n(63887),K=n(16088),f=n(85768),s=n(40553),b=n(66844),P=n(52107),N=n(79857),G=n(8577),q=n(80877),ne=n(78990),ee=n(61357),F=n(32049),z=n(27536),r=n(51894),p=n(38827),_=n(65620),C=n(17012),g=n(32496),O=n(69808),l=n(24376),o=n(46302),u=n(17447),S=n(18995);function M(d,L){if(1&d&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",58),e.\u0275\u0275element(2,"input",59),e.\u0275\u0275elementStart(3,"label",60),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&d){const t=L.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("id","inpSelectedUser",t,""),e.\u0275\u0275property("value",t),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("for","inpSelectedUser",t,""),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t," ")}}const y=function(d){return{"pe-none blinking":d}};function W(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"h5",61),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",11)(5,"form-errors-wrapper",62)(6,"ng-select",63),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.projectPropertyChange("Project"))}),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,6,"GLOBAL.select-project")),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",t.emailForm.controls.selectedProject),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(7,8,"GLOBAL.select-project")),e.\u0275\u0275property("virtualScroll",!0)("ngClass",e.\u0275\u0275pureFunction1(10,y,t.projectListIsLoading))("items",t.allProjectList)}}function D(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"h5",61),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",11)(5,"form-errors-wrapper",64)(6,"ng-select",65),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.projectPropertyChange("Property"))}),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,6,"GLOBAL.select-property")),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",t.emailForm.controls.selectedProperty),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(7,8,"GLOBAL.select-property")),e.\u0275\u0275property("virtualScroll",!0)("ngClass",e.\u0275\u0275pureFunction1(10,y,t.propertyListIsLoading))("items",t.allPropertyList)}}function ae(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",66),e.\u0275\u0275text(2),e.\u0275\u0275elementStart(3,"span",67),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(t).index,i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.removeToAddress(h))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=L.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function Q(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",76),e.\u0275\u0275text(2),e.\u0275\u0275elementStart(3,"div",77),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(t).index,i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.removeCCFromList(h))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=L.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function k(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",68)(1,"div",15),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",69)(5,"div",70)(6,"form-errors-wrapper",71)(7,"div",6),e.\u0275\u0275template(8,Q,4,1,"ng-container",7),e.\u0275\u0275elementStart(9,"div",72)(10,"input",73),e.\u0275\u0275listener("keydown.enter",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.addCCToList())}),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"small",74),e.\u0275\u0275text(13," (press enter to add)"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",75),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.ccEnabled=!m.ccEnabled)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",18),e.\u0275\u0275element(16,"div",19),e.\u0275\u0275elementEnd()()()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,4,"GLOBAL.cc")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.emailForm.controls.cc),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.ccList),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(11,6,"GLOBAL.ex-pampanagmailcom"))}}function pe(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",76),e.\u0275\u0275text(2),e.\u0275\u0275elementStart(3,"div",77),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(t).index,i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.removeBCCFromList(h))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=L.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function me(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",68)(1,"div",15),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",69)(5,"div",70)(6,"form-errors-wrapper",78)(7,"div",6),e.\u0275\u0275template(8,pe,4,1,"ng-container",7),e.\u0275\u0275elementStart(9,"div",72)(10,"input",79),e.\u0275\u0275listener("keydown.enter",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.addBCCToList())}),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"small",74),e.\u0275\u0275text(13," (press enter to add)"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(14,"div",75),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.bccEnabled=!m.bccEnabled)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",18),e.\u0275\u0275element(16,"div",19),e.\u0275\u0275elementEnd()()()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,4,"GLOBAL.bcc")),e.\u0275\u0275advance(4),e.\u0275\u0275property("control",t.emailForm.controls.bcc),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.bccList),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(11,6,"GLOBAL.ex-mounikagmailcom"))}}const le=function(){return{standalone:!0}};function _e(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",80)(1,"textarea",81),e.\u0275\u0275listener("ngModelChange",function(m){e.\u0275\u0275restoreView(t);const h=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(h.template=m)}),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(2,4,"GLOBAL.ex-dear-nichola-ferrellas-per-")),e.\u0275\u0275property("ngModel",t.template)("ngModelOptions",e.\u0275\u0275pureFunction0(6,le))("value",t.template)}}function ue(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",83)(2,"h5",84),e.\u0275\u0275text(3," select lead to preview:"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"ng-select",85),e.\u0275\u0275listener("ngModelChange",function(m){e.\u0275\u0275restoreView(t);const h=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(h.selectedLead=m)}),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(7,"div",86),e.\u0275\u0275elementContainerEnd()}if(2&d){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(4),e.\u0275\u0275propertyInterpolate2("placeholder","",e.\u0275\u0275pipeBind1(5,6,"GLOBAL.select")," ",e.\u0275\u0275pipeBind1(6,8,"BULK_LEAD.template"),""),e.\u0275\u0275property("virtualScroll",!0)("items",t.bulkData)("ngModel",t.selectedLead)("ngModelOptions",e.\u0275\u0275pureFunction0(10,le))}}function he(d,L){if(1&d&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,ue,8,11,"ng-container",9),e.\u0275\u0275elementStart(2,"div",80),e.\u0275\u0275element(3,"textarea",82),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Lead"===t.emailForm.get("selectedOption").value),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(4,3,"GLOBAL.ex-dear-nichola-ferrellas-per-")),e.\u0275\u0275property("value",t.messagePreview)}}function ve(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",92)(1,"div",93),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(3,"div",94),e.\u0275\u0275elementStart(4,"div",95),e.\u0275\u0275listener("click",function(){const h=e.\u0275\u0275restoreView(t).index,i=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(i.deleteFiles(h,1))}),e.\u0275\u0275elementEnd()()}if(2&d){const t=L.$implicit;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.name)}}function ge(d,L){if(1&d&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275element(1,"div",90),e.\u0275\u0275elementStart(2,"div",6),e.\u0275\u0275template(3,ve,5,1,"div",91),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&d){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.files)}}function fe(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",87)(1,"div",88),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.showFiles=!m.showFiles)}),e.\u0275\u0275elementStart(2,"div"),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementStart(5,"b"),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"div",89),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,ge,4,1,"ng-container",9),e.\u0275\u0275elementEnd()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.view")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==t.files?null:t.files.length),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,6,"GLOBAL.attachments")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",t.showFiles)}}function Ee(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",104),e.\u0275\u0275listener("click",function(m){const i=e.\u0275\u0275restoreView(t).$implicit,c=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(c.addVariable(i,m))}),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()}if(2&d){const t=L.$implicit,a=L.index;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",a+1+". "+t," ")}}function Ce(d,L){if(1&d){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",96)(1,"div",97)(2,"div",70)(3,"div",98),e.\u0275\u0275element(4,"div",99),e.\u0275\u0275elementStart(5,"h5",100),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",101),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const m=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(m.isShowVariablePopup=!1)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"div",102),e.\u0275\u0275template(9,Ee,2,1,"div",103),e.\u0275\u0275elementEnd()()()}if(2&d){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275property("ngClass",t.isData?"ic-address-card-solid":"ic-secondary-filter-solid"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.isData?"Data":"Lead"),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",t.variables)}}const be=function(d){return{"pe-none opacity-50":d}};let xe=(()=>{class d{constructor(t,a,m,h,i){this.modalRef=t,this._store=a,this.tenantService=m,this._notificationService=h,this.fb=i,this.stopper=new e.EventEmitter,this.isTemplatesLoading=!0,this.tenantId=(0,I.u9)(),this.tenantName="",this.isTenantNameLoading=!0,this.message="",this.ccEnabled=!1,this.bccEnabled=!1,this.ccMail="",this.bccMail="",this.isShowVariablePopup=!1,this.isShowLeadPreview=!1,this.showFiles=!1,this.variables=[],this.template="",this.displayEmail="",this.templatePreview="",this.fileFormat="Supported format: JPG, PNG, MP4, GIF and PDF",this.fileMessage=!0,this.files=[],this.toList=[],this.ccList=[],this.bccList=[],this.selectedFiles=[],this.allUserList=[],this.templatesList=["Lead","Project","Property"],this.Editor=U(),this.currentDate=new Date,this.isCampaignListLoading=!0}ngOnInit(){var t,a,m,h;this._store.select(F.Xf).pipe((0,E.R)(this.stopper)).subscribe(i=>{var c,T;this.userData=i,this.currentDate=(0,I.Xp)(null===(T=null===(c=this.userData)||void 0===c?void 0:c.timeZoneInfo)||void 0===T?void 0:T.baseUTcOffset)}),this.bulkData=null===(a=null===(t=this.data)||void 0===t?void 0:t.bulkData)||void 0===a?void 0:a.filter(i=>i.email),this.shareType=null===(m=this.data)||void 0===m?void 0:m.shareType,this.isData=null===(h=this.data)||void 0===h?void 0:h.isData,this.toList=this.bulkData.map(i=>null==i?void 0:i.email),this.emailForm=this.fb.group({from:[null,v.kI.required],cc:["",X.w.emailValidatorMinLength],bcc:["",X.w.emailValidatorMinLength],subject:["",v.kI.required],selectedOption:["Lead"],selectedProject:[null],selectedProperty:[null],selectCampaign:[null,v.kI.required]}),this._store.dispatch(new s.J_S),this._store.select(b.vM).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.campaigns=null==i?void 0:i.filter(c=>c).slice().sort((c,T)=>c.localeCompare(T))}),this._store.select(b.Ji).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.isCampaignListLoading=i}),this.emailForm.get("selectedOption").valueChanges.subscribe(i=>{var c;this.selectedTemplate=null,this.template="",this.emailForm.get("selectedProject").reset(),this.emailForm.get("selectedProperty").reset(),this.emailForm.get("selectedProject").clearValidators(),this.emailForm.get("selectedProperty").clearValidators(),"Lead"===i?(this._store.dispatch(new z.G7(0)),this.variables=null!==(c=this.globalSettings)&&void 0!==c&&c.isDualOwnershipEnabled?A.GEj.filter(T=>"#Assign To#"!==T).sort():A.GEj.filter(T=>"#Primary Owner#"!==T&&"#Secondary Owner#"!==T).sort()):"Project"===i?(this.emailForm.get("selectedProject").setValidators(v.kI.required),this.emailForm.get("selectedProject").markAsUntouched(),this._store.dispatch(new G.v1),this._store.dispatch(new z.G7(5)),this.variables=A.tgO.sort()):"Property"===i&&(this.emailForm.get("selectedProperty").setValidators(v.kI.required),this.emailForm.get("selectedProperty").markAsUntouched(),this._store.dispatch(new ne.z1),this._store.dispatch(new z.G7(6)),this.variables=A.aQs.sort()),this.emailForm.get("selectedProject").updateValueAndValidity(),this.emailForm.get("selectedProperty").updateValueAndValidity()}),this._store.dispatch(new z.G7(0)),this._store.select(f.fN).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.globalSettings=i,this.defaultCurrency=i.countries&&i.countries.length>0?i.countries[0].defaultCurrency:null;const c=new Set;null!=i&&i.isDualOwnershipEnabled?c.add("#Assign To#"):(c.add("#Primary Owner#"),c.add("#Secondary Owner#")),null!=i&&i.isCustomLeadFormEnabled?(c.add("#No Of BHK#"),c.add("#BHK Type#")):(c.add("#BR#"),c.add("#Beds#"),c.add("#Baths#"),c.add("#Furnish Status#"),c.add("#Preferred Floor#"),c.add("#Offering Type#"),c.add("#Secondary Owner#"),c.add("#Property Area#"),c.add("#Net Area#"),c.add("#Unit Number or Name#"),c.add("#Cluster Name#"),c.add("#Nationality#")),this.variables=A.GEj.filter(T=>!c.has(T)).sort()}),this._store.select(K.ZF).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.EmailList=i,this.fromList=i}),this._store.dispatch(new P.dg),this._store.select(N.WV).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.areaSizeUnits=i||[]}),this._store.select(r.gW).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.isTemplatesLoading=null==i?void 0:i.isTemplatesLoading,""!=i&&null!=i&&(this.templates=i.templates.filter(c=>c).slice().sort((c,T)=>c.title.localeCompare(T.title)))}),this._store.select(F.Sh).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.allUserList=i}),this.tenantId&&this.tenantService.getTenantById(this.tenantId).subscribe(i=>{this.tenantName=i.data.name,this.isTenantNameLoading=!1}),this._store.select(q.Bf).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.allProjectList=null==i?void 0:i.filter(c=>c.name).slice().sort((c,T)=>c.name.localeCompare(T.name))}),this._store.select(q.e1).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.projectListIsLoading=i}),this._store.select(ee.ij).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.allPropertyList=i.slice().sort((c,T)=>(c.name||"").localeCompare(T.name||""))}),this._store.select(ee.vR).pipe((0,E.R)(this.stopper)).subscribe(i=>{this.propertyListIsLoading=i}),this._store.select(ee.b1).pipe((0,E.R)(this.stopper)).subscribe(i=>{var c;i&&(this.propertyData=Object.assign(Object.assign({},i),{leadName:null===(c=this.data)||void 0===c?void 0:c.name}))}),this._store.select(q.rI).pipe((0,E.R)(this.stopper)).subscribe(i=>{var c;i&&(this.projectData=Object.assign(Object.assign({},i),{leadName:null===(c=this.data)||void 0===c?void 0:c.name}))})}sendMessage(){var t,a;if(!this.emailForm.valid)return void(0,I._5)(this.emailForm);if(""===this.template.trim())return void this._notificationService.warn("Email Body Required");if(this.ccEnabled&&this.emailForm.get("cc").valid&&this.ccList.push(this.emailForm.get("cc").value),this.bccEnabled&&this.emailForm.get("bcc").valid&&this.bccList.push(this.emailForm.get("bcc").value),this.files.reduce((x,R)=>x+R.size,0)>26214400)return void this._notificationService.warn("File size should not exceed 25MB");let h=null===(t=JSON.parse(localStorage.getItem("userDetails")))||void 0===t?void 0:t.sub,i=this.emailForm.value,c=[];const T=this.ccList.filter(x=>x),oe=this.bccList.filter(x=>x);this.bulkData&&this.bulkData.forEach(x=>{var R,te,de;const ce={Project:()=>(0,I.$p)(this.template,this.projectData,this.areaSizeUnits,this.tenantName,"","",this.allUserList,"share-project",this.userData),Property:()=>(0,I.j1)(this.template,this.propertyData,this.areaSizeUnits,this.tenantName,"","",!0,"share-property",this.userData,this.currentDate),Lead:()=>(0,I.fC)(this.template,x,this.tenantName,this.defaultCurrency,"","",this.allUserList,this.userData,this.currentDate)},De=(null===(de=null===(te=ce[null===(R=this.emailForm.get("selectedOption"))||void 0===R?void 0:R.value])||void 0===te?void 0:te.call(ce))||void 0===de?void 0:de.replace(/\\n|\n/g,"<br>"))||"",Se={CurrentUserId:h,LeadId:x.id,CampaignName:i.selectCampaign,From:i.from.from,ServerName:i.from.serverName,Port:i.from.port,UserName:i.from.userName,Password:i.from.password,Priority:1,To:[null==x?void 0:x.email],Body:De,Subject:i.subject,CC:[...T],BCC:[...oe],IsData:this.isData};c=[...c,Se]});const J={contactType:j.uC[this.shareType],message:this.message},se=null===(a=this.bulkData)||void 0===a?void 0:a.map(x=>null==x?void 0:x.id);this.bulkData&&this.isData?J.prospectIds=se:!this.bulkData&&this.isData?J.prospectId=this.data.id:this.bulkData&&!this.isData?J.leadIds=se:!this.bulkData&&!this.isData&&(J.leadId=this.data.id),this.bulkData&&this.isData?this._store.dispatch(new $.FT(J)):!this.bulkData&&this.isData?this._store.dispatch(new $.uJ(J)):this.bulkData&&!this.isData?this._store.dispatch(new s.Dfj(J)):!this.bulkData&&!this.isData&&this._store.dispatch(new s.dFq(J));let Y={contactType:j.uC[this.shareType]};this.bulkData?Y.ids=se:Y.id=this.data.id;const re=()=>{var x,R;this._store.dispatch(new B.$b([...c],this.files)),null===(R=null===(x=this.data)||void 0===x?void 0:x.selectedNodes)||void 0===R||R.forEach(te=>{null==te||te.setSelected(!1)}),this.modalRef.hide()};if(this.bulkData&&this.isData){const x=this._store.select(R=>R.dataManagement.allData).pipe((0,E.R)(this.stopper),(0,w.T)(1),(0,V.q)(1)).subscribe(()=>{re(),x.unsubscribe()});this._store.dispatch(new $.Zn(Y))}else if(!this.bulkData&&this.isData){const x=this._store.select(R=>R.dataManagement.allData).pipe((0,E.R)(this.stopper),(0,w.T)(1),(0,V.q)(1)).subscribe(()=>{re(),x.unsubscribe()});this._store.dispatch(new $.eS(Y.id,Y))}else if(this.bulkData&&!this.isData){const x=this._store.select(R=>R.lead.leadsCommunication).pipe((0,E.R)(this.stopper),(0,w.T)(1),(0,V.q)(1)).subscribe(()=>{re(),x.unsubscribe()});this._store.dispatch(new s.Pas(Y))}else if(!this.bulkData&&!this.isData){const x=this._store.select(R=>R.lead.leadsCommunication).pipe((0,E.R)(this.stopper),(0,w.T)(1),(0,V.q)(1)).subscribe(()=>{re(),x.unsubscribe()});this._store.dispatch(new s.h$e(Y.id,Y))}}addVariable(t){if(this.isShowLeadPreview)return void(this.isShowVariablePopup=!1);const a=" "+t,m=document.getElementById("txtLeadMsg"),h=m.selectionStart;let i=m.value.substring(0,h),c=m.value.substring(h);null==h&&(i=m.value,c=" "),m.value=i+a+c,m.selectionStart=h+a.length,m.selectionEnd=h+a.length,this.template=m.value,this.isShowVariablePopup=!1}onFileSelection(t){let a=t.target.files;this.selectedFile=a,this.files=[...this.files,...a]}templateChange(){if(!this.selectedTemplate)return this.emailForm.get("subject").patchValue(null),void(this.template="");this.emailForm.get("subject").patchValue(this.selectedTemplate.title),this.template=(this.selectedTemplate.header?this.selectedTemplate.header+"\n":"")+`${this.selectedTemplate.message}`+(this.selectedTemplate.footer?"\n"+this.selectedTemplate.footer:"")}get messagePreview(){var t,a,m;return"Project"===this.emailForm.get("selectedOption").value?null===(t=(0,I.$p)(this.template,this.projectData,this.areaSizeUnits,this.tenantName,"","",this.allUserList,"share-project",this.userData))||void 0===t?void 0:t.replace(/\\n/g,"\n"):"Property"===this.emailForm.get("selectedOption").value?null===(a=(0,I.j1)(this.template,this.propertyData,this.areaSizeUnits,this.tenantName,"","",!0,"share-property",this.userData,this.currentDate))||void 0===a?void 0:a.replace(/\\n/g,"\n"):this.selectedLead?null===(m=(0,I.fC)(this.template,this.selectedLead,this.tenantName,this.defaultCurrency,"","",this.allUserList,this.userData,this.currentDate))||void 0===m?void 0:m.replace(/\\n/g,"\n"):""}toggleLeadPreview(){this.isShowLeadPreview=!this.isShowLeadPreview}deleteFiles(t,a){this.files.splice(t,a)}addCCToList(){const t=this.emailForm.get("cc").value;t&&this.emailForm.get("cc").valid&&(this.ccList=[...this.ccList,t],this.emailForm.get("cc").setValue(null))}addBCCToList(){const t=this.emailForm.get("bcc").value;t&&this.emailForm.get("bcc").valid&&(this.bccList=[...this.bccList,t],this.emailForm.get("bcc").setValue(null))}removeCCFromList(t){var a;t>=0&&t<(null===(a=this.ccList)||void 0===a?void 0:a.length)&&this.ccList.splice(t,1)}removeBCCFromList(t){var a;t>=0&&t<(null===(a=this.bccList)||void 0===a?void 0:a.length)&&this.bccList.splice(t,1)}removeToAddress(t){var a;t>=0&&t<(null===(a=this.toList)||void 0===a?void 0:a.length)&&(this.toList.splice(t,1),this.bulkData.splice(t,1))}projectPropertyChange(t){(this.emailForm.get("selectedProject").value||this.emailForm.get("selectedProperty").value)&&this._store.dispatch("Project"===t?new G.RU(this.emailForm.get("selectedProject").value):new ne.Xx(this.emailForm.get("selectedProperty").value))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return d.\u0275fac=function(t){return new(t||d)(e.\u0275\u0275directiveInject(p.UZ),e.\u0275\u0275directiveInject(_.yh),e.\u0275\u0275directiveInject(C.a),e.\u0275\u0275directiveInject(g.TF),e.\u0275\u0275directiveInject(v.qu))},d.\u0275cmp=e.\u0275\u0275defineComponent({type:d,selectors:[["bulk-leads-email-share"]],decls:105,vars:81,consts:[[1,"flex-between","bg-coal","w-100","px-20","py-12","text-white"],[1,"fw-semi-bold"],[1,"icon","ic-close-secondary","ic-large","cursor-pointer",3,"click"],[1,"h-100-110","scrollbar","ip-min-w-350","ip-max-w-350"],[1,"p-10","w-100",3,"formGroup"],[1,"mt-12"],[1,"d-flex","flex-wrap"],[4,"ngFor","ngForOf"],[1,"no-validation"],[4,"ngIf"],[1,"field-label-req","text-dark-800"],[1,"flex-col","w-100","mt-4","mb-8","border","br-5"],["label","Campaign",3,"control"],["bindLabel","name","addTagText","Create New Campaign","bindValue","id","formControlName","selectCampaign","ResizableDropdown","",1,"w-100",3,"virtualScroll","ngClass","items","addTag","placeholder"],[1,"d-flex","text-nowrap"],[1,"field-label","text-dark-800","w-60"],[1,"flex-col","w-100","mt-4"],["bindLabel","title","ResizableDropdown","",1,"bg-white","border-0","w-100",3,"virtualScroll","ngClass","items","ngModel","ngModelOptions","placeholder","ngModelChange","change"],[1,"w-100"],[1,"border-bottom","ml-12"],[1,"d-flex","text-nowrap","mt-12","position-relative"],[1,"w-60"],[1,"w-100","flex-col","mt-4"],["label","From",3,"control"],["bindLabel","from","formControlName","from","ResizableDropdown","",1,"bg-white","border-0","w-100",3,"items","placeholder"],[1,"text-nowrap","align-center","position-relative","mt-8"],[1,"flex-col","w-100"],[1,"position-relative","mt-12"],[1,"w-80pr","ip-w-60"],[1,"d-flex","fw-700","header-5","text-accent-green","position-absolute","right-10","bottom-10","bg-white","px-10"],[1,"mr-30","cursor-pointer",3,"click"],[1,"cursor-pointer",3,"click"],["class","d-flex text-nowrap mt-12",4,"ngIf"],[1,"w-68"],[1,"w-100","flex-col","no-validation","mt-4"],["label","Subject",3,"control"],["type","text","formControlName","subject",1,"bg-white","border-0",3,"placeholder"],[1,"flex-between","mt-20"],[1,"field-label","mt-0","text-dark-800","w-60","text-nowrap"],[1,"checkbox-container","my-4",3,"change"],["type","checkbox"],[1,"checkmark"],[1,"border-bottom"],["class","mt-16 form-group no-validation",4,"ngIf"],["class","p-10",4,"ngIf"],[1,"position-fixed","p-12","bottom-0","border-top","flex-between","w-100","bg-white"],[1,"align-center","ph-flex-col"],[1,"position-relative"],[1,"my-4"],[1,"btn-coal",3,"ngClass","click"],["class","position-absolute bg-light-pearl w-460 ip-max-w-340 br-10 z-index-2 left-0 bottom-40",4,"ngIf"],[1,"btn-coal","ml-10","ph-ml-0",3,"click"],[1,"icon","ic-xxs","ic-paper-clip","mr-10"],["type","file",2,"display","none",3,"change"],["attachFiles",""],[1,"flex-end"],[1,"mr-20","fw-semi-bold","text-mud","cursor-pointer",3,"click"],[1,"btn-coal",3,"click"],[1,"form-check","form-check-inline","mr-10","mb-12","bg-light-pearl","br-20","p-10","mt-10"],["type","radio","name","templatesListOption","formControlName","selectedOption",1,"radio-check-input",3,"id","value"],[1,"text-dark-gray","cursor-pointer","text-large","text-sm","ml-6",3,"for"],[1,"field-label-req","text-dark-800","w-100px"],["label","Project",3,"control"],["bindLabel","name","bindValue","id","formControlName","selectedProject","ResizableDropdown","",1,"w-100",3,"virtualScroll","ngClass","items","placeholder","change"],["label","Property",3,"control"],["bindLabel","title","bindValue","id","formControlName","selectedProperty","ResizableDropdown","",1,"w-100",3,"virtualScroll","ngClass","items","placeholder","change"],[1,"br-15","px-10","py-6","bg-ash","mr-4","mb-4"],[1,"ic-cancel","ic-dark","icon","ic-x-xs","mr-4","cursor-pointer",3,"click"],[1,"d-flex","text-nowrap","mt-12"],[1,"w-100","flex-col"],[1,"flex-between"],["label","CC",1,"w-100",3,"control"],[1,"flex-between","flex-grow-1"],["type","text","formControlName","cc",1,"bg-white","border-0","mt-4",3,"placeholder","keydown.enter"],[1,"text-muted","text-nowrap","mr-10"],[1,"icon","ic-close","ic-sm","ic-red-350","cursor-pointer",3,"click"],[1,"align-center","br-15","px-10","py-6","bg-ash","mr-4","mb-4"],[1,"icon","ic-x-xs","ic-cancel","ic-gray","ml-6","cursor-pointer",3,"click"],["label","BCC",1,"w-100",3,"control"],["type","text","formControlName","bcc",1,"bg-white","border-0","mt-4",3,"placeholder","keydown.enter"],[1,"mt-16","form-group","no-validation"],["rows","8","id","txtLeadMsg","data-automate-id","txtLeadMsg",1,"scrollbar","border-0",3,"ngModel","ngModelOptions","value","placeholder","ngModelChange"],["rows","8","disabled","true","id","txtLeadMsg","data-automate-id","txtLeadMsg",1,"scrollbar","border-0",3,"value","placeholder"],[1,"flex-center","mx-auto","no-validation","ng-select-sm","mt-10"],[1,"text-nowrap","mr-10"],["bindLabel","name","ResizableDropdown","",1,"w-250",3,"virtualScroll","items","ngModel","ngModelOptions","placeholder","ngModelChange"],[1,"border-bottom","my-10","mx-50"],[1,"p-10"],[1,"flex-between","text-accent-green","cursor-pointer",3,"click"],[1,"icon","ic-accent-green","ic-x-xs","ic-triangle-down"],[1,"my-10","border-bottom"],["class","align-center p-10 bg-light-pearl br-4 mr-10 mt-4",4,"ngFor","ngForOf"],[1,"align-center","p-10","bg-light-pearl","br-4","mr-10","mt-4"],[1,"text-truncate-1"],[1,"border","h-10","mx-10"],[1,"icon","ic-delete","ic-xxs","ic-red-350","cursor-pointer",3,"click"],[1,"position-absolute","bg-light-pearl","w-460","ip-max-w-340","br-10","z-index-2","left-0","bottom-40"],[1,"br-4","p-12","bg-slate","border"],[1,"align-center"],[1,"icon","ic-xxs","mr-4","ic-coal",3,"ngClass"],[1,"fw-600","text-coal"],[1,"icon","ic-close","ic-sm","ic-coal","cursor-pointer",3,"click"],[1,"pt-12","d-flex","flex-wrap","w-100","max-h-100-300","scrollbar","mt-10"],["class","py-4 px-8 border br-10 mr-8 mb-8 cursor-pointer",3,"click",4,"ngFor","ngForOf"],[1,"py-4","px-8","border","br-10","mr-8","mb-8","cursor-pointer",3,"click"]],template:function(t,a){if(1&t){const m=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",0)(1,"h3",1),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",2),e.\u0275\u0275listener("click",function(){return a.modalRef.hide()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"div",3)(5,"div",4)(6,"div",5)(7,"div",6),e.\u0275\u0275template(8,M,5,4,"ng-container",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275template(10,W,8,12,"ng-container",9),e.\u0275\u0275template(11,D,8,12,"ng-container",9),e.\u0275\u0275elementStart(12,"h5",10),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(15,"div",11)(16,"form-errors-wrapper",12),e.\u0275\u0275element(17,"ng-select",13),e.\u0275\u0275pipe(18,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"div",14)(20,"h5",15),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"div",16)(24,"ng-select",17),e.\u0275\u0275listener("ngModelChange",function(i){return a.selectedTemplate=i})("change",function(){return a.templateChange()}),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275pipe(26,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(27,"div",18),e.\u0275\u0275element(28,"div",19),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(29,"div",20)(30,"div",21)(31,"div",10),e.\u0275\u0275text(32),e.\u0275\u0275pipe(33,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(34,"div",22)(35,"form-errors-wrapper",23),e.\u0275\u0275element(36,"ng-select",24),e.\u0275\u0275pipe(37,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"div",18),e.\u0275\u0275element(39,"div",19),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(40,"div",25)(41,"div",21)(42,"div",10),e.\u0275\u0275text(43),e.\u0275\u0275pipe(44,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(45,"div",26)(46,"div",27)(47,"div",28)(48,"div",6),e.\u0275\u0275template(49,ae,4,1,"ng-container",7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(50,"div",29)(51,"div",30),e.\u0275\u0275listener("click",function(){return a.ccEnabled=!a.ccEnabled}),e.\u0275\u0275text(52),e.\u0275\u0275pipe(53,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(54,"div",31),e.\u0275\u0275listener("click",function(){return a.bccEnabled=!a.bccEnabled}),e.\u0275\u0275text(55),e.\u0275\u0275pipe(56,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(57,"div",18),e.\u0275\u0275element(58,"div",19),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(59,k,17,8,"div",32),e.\u0275\u0275template(60,me,17,8,"div",32),e.\u0275\u0275elementStart(61,"div",20)(62,"div",33)(63,"div",10),e.\u0275\u0275text(64),e.\u0275\u0275pipe(65,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(66,"div",34)(67,"form-errors-wrapper",35),e.\u0275\u0275element(68,"input",36),e.\u0275\u0275pipe(69,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(70,"div",18),e.\u0275\u0275element(71,"div",19),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementStart(72,"div",37)(73,"div",38),e.\u0275\u0275text(74),e.\u0275\u0275pipe(75,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(76,"label",39),e.\u0275\u0275listener("change",function(){return a.toggleLeadPreview()}),e.\u0275\u0275element(77,"input",40)(78,"span",41),e.\u0275\u0275text(79,"Preview mail content "),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(80,"div",42),e.\u0275\u0275template(81,_e,3,7,"div",43),e.\u0275\u0275template(82,he,5,5,"ng-container",9),e.\u0275\u0275template(83,fe,11,8,"div",44),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(84,"div",45)(85,"div",46)(86,"div",47)(87,"div",48)(88,"button",49),e.\u0275\u0275listener("click",function(){return a.isShowVariablePopup=a.isShowLeadPreview?a.isShowVariablePopup:!a.isShowVariablePopup}),e.\u0275\u0275elementStart(89,"span"),e.\u0275\u0275text(90),e.\u0275\u0275pipe(91,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(92,Ce,10,3,"div",50),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(93,"div",51),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(m);const i=e.\u0275\u0275reference(97);return e.\u0275\u0275resetView(i.click())}),e.\u0275\u0275element(94,"span",52),e.\u0275\u0275text(95,"Attach Files "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(96,"input",53,54),e.\u0275\u0275listener("change",function(i){return a.onFileSelection(i)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(98,"div",55)(99,"u",56),e.\u0275\u0275listener("click",function(){return a.modalRef.hide()}),e.\u0275\u0275text(100),e.\u0275\u0275pipe(101,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(102,"button",57),e.\u0275\u0275listener("click",function(){return a.sendMessage()}),e.\u0275\u0275text(103),e.\u0275\u0275pipe(104,"translate"),e.\u0275\u0275elementEnd()()()}2&t&&(e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Send ",null==a.data?null:a.data.shareType," message"),e.\u0275\u0275advance(3),e.\u0275\u0275property("formGroup",a.emailForm),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",a.templatesList),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf","Project"===a.emailForm.get("selectedOption").value),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Property"===a.emailForm.get("selectedOption").value),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(14,42,"GLOBAL.select-campaign")),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",a.emailForm.controls.selectCampaign),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(18,44,"GLOBAL.select-campaigncreate-campaign")),e.\u0275\u0275property("virtualScroll",!0)("ngClass",e.\u0275\u0275pureFunction1(74,y,a.isCampaignListLoading))("items",a.campaigns)("addTag",!0),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(22,46,"BULK_LEAD.template"),":"),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate2("placeholder","",e.\u0275\u0275pipeBind1(25,48,"GLOBAL.select")," ",e.\u0275\u0275pipeBind1(26,50,"BULK_LEAD.template"),""),e.\u0275\u0275property("virtualScroll",!0)("ngClass",e.\u0275\u0275pureFunction1(76,y,a.isTemplatesLoading))("items",a.templates)("ngModel",a.selectedTemplate)("ngModelOptions",e.\u0275\u0275pureFunction0(78,le)),e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(33,52,"GLOBAL.from")),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",a.emailForm.controls.from),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(37,54,"GLOBAL.ex-pmounimnkgmailcom")),e.\u0275\u0275property("items",a.fromList),e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(44,56,"GLOBAL.to")),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngForOf",a.toList),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(53,58,"GLOBAL.cc")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(56,60,"GLOBAL.bcc")),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",a.ccEnabled),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.bccEnabled),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(65,62,"GLOBAL.subject")),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",a.emailForm.controls.subject),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(69,64,"GLOBAL.type-here")),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(75,66,"GLOBAL.content")),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngIf",!a.isShowLeadPreview),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.isShowLeadPreview),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==a.files?null:a.files.length),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(79,be,a.isShowLeadPreview)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" # Select ",e.\u0275\u0275pipeBind1(91,68,"GLOBAL.variable"),""),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",a.isShowVariablePopup),e.\u0275\u0275advance(8),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(101,70,"BUTTONS.cancel")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(104,72,"GLOBAL.send-email")))},dependencies:[v.Fj,v._,v.JJ,v.JL,v.On,O.mk,O.sg,O.O5,l.w9,o.z,v.sg,v.u,u.s,S.X$],encapsulation:2}),d})()},84660:(ie,Z,n)=>{n.d(Z,{R:()=>C});var e=n(5e3),v=n(93075),H=n(68671),U=n(82722),E=n(2976),w=n(61021),V=n(85768),A=n(40553),j=n(66844),I=n(8377),X=n(25946),$=n(38827),B=n(65620),K=n(1880),f=n(69808),s=n(24376),b=n(46302),P=n(21718),N=n(17447),G=n(18995);const q=["contactNoInput"];function ne(g,O){if(1&g&&(e.\u0275\u0275elementStart(0,"div",20)(1,"div",13),e.\u0275\u0275text(2," Head Variable"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"form-errors-wrapper",21),e.\u0275\u0275element(4,"input",22),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()()),2&g){const l=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",1==(null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.whatsAppHeaderTypes)&&(null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.headerValuesCount)>0?"field-label-req":"field-label"),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",l.shareDetailForm.controls.headerVariable),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(5,4,"GLOBAL.ex-text")),e.\u0275\u0275property("required",1==(null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.whatsAppHeaderTypes)&&(null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.headerValuesCount)>0)}}function ee(g,O){if(1&g&&(e.\u0275\u0275elementStart(0,"div",27),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&g){const l=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" Body Variable(s) should be ",null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.bodyValuesCount,"")}}function F(g,O){if(1&g&&(e.\u0275\u0275elementStart(0,"div",20)(1,"div",4),e.\u0275\u0275text(2),e.\u0275\u0275elementStart(3,"span",23),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"form-errors-wrapper",24),e.\u0275\u0275element(7,"input",25),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(9,ee,2,1,"div",26),e.\u0275\u0275elementEnd()),2&g){const l=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Body Variable(s) - ",null==l.shareDetailForm.controls.template||null==l.shareDetailForm.controls.template.value?null:l.shareDetailForm.controls.template.value.bodyValuesCount," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(5,5,"GLOBAL.with-comma-separated")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",l.shareDetailForm.controls.bodyVariables),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(8,7,"GLOBAL.ex-name-time-company-name")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!l.isBodyVarCountValid&&(null==l.shareDetailForm.controls.bodyVariables?null:l.shareDetailForm.controls.bodyVariables.value))}}function z(g,O){if(1&g){const l=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",28),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const u=e.\u0275\u0275nextContext(),S=e.\u0275\u0275reference(43);return e.\u0275\u0275resetView(u.updateBulkShare(S))}),e.\u0275\u0275text(1,"Send Test"),e.\u0275\u0275elementEnd()}}function r(g,O){if(1&g){const l=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",28),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const u=e.\u0275\u0275nextContext(),S=e.\u0275\u0275reference(43);return e.\u0275\u0275resetView(u.updateBulkShare(S))}),e.\u0275\u0275text(1,"Send Bulk"),e.\u0275\u0275elementEnd()}}function p(g,O){if(1&g){const l=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",29)(1,"a",30),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const u=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(u.modalRef.hide())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"h5",31),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",32)(6,"button",17),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const u=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(u.modalRef.hide())}),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"button",28),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(l);const u=e.\u0275\u0275nextContext();return u.testTemplateSent=!0,e.\u0275\u0275resetView(u.modalRef.hide())}),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd()()()}2&g&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,3,"GLOBAL.did-you-receive-message")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,5,"GLOBAL.no")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,7,"GLOBAL.yes")))}const _=function(g){return{"blinking pe-none":g}};let C=(()=>{class g{constructor(l,o,u,S,M){var y;this.modalService=l,this.store=o,this.modalRef=u,this.fb=S,this.trackingService=M,this.stopper=new e.EventEmitter,this.templates=[],this.testTemplateSent=!1,this.isBodyVarCountValid=!1,this.preferredCountries=["in"],this.campaigns=[],this.isCampaignListLoading=!1;const W=localStorage.getItem("userDetails");this.loggedInPhoneNo=null===(y=JSON.parse(W))||void 0===y?void 0:y.phone_number,this.shareDetailForm=this.fb.group({testContactNo:[this.loggedInPhoneNo.toString(),this.contactNumberValidator()],template:[null,v.kI.required],campaignName:[null,v.kI.required],fileName:[""],headerVariable:[""],bodyVariables:[""]})}ngOnInit(){this.store.dispatch(new I.vD),this.store.dispatch(new A.J_S),this.store.select(j.vM).pipe((0,U.R)(this.stopper)).subscribe(l=>{this.campaigns=null==l?void 0:l.filter(o=>o).slice().sort((o,u)=>o.localeCompare(u))}),this.store.select(j.Ji).pipe((0,U.R)(this.stopper)).subscribe(l=>{this.isCampaignListLoading=l}),this.store.select(V.fN).pipe((0,U.R)(this.stopper)).subscribe(l=>{var o;this.preferredCountries=null!=l&&l.hasInternationalSupport&&null!==(o=null==l?void 0:l.countries)&&void 0!==o&&o.length?[l.countries[0].code.toLowerCase()]:["in"]}),this.store.select(X.Yr).pipe((0,U.R)(this.stopper)).subscribe(l=>{""!=l&&null!=l&&(this.templates=l.templates)}),this.shareDetailForm.get("template").valueChanges.subscribe(l=>{var o,u,S,M;this.bodyValuesCount=(null===(u=null===(o=this.shareDetailForm.controls.template)||void 0===o?void 0:o.value)||void 0===u?void 0:u.bodyValuesCount)>=1,this.fileNameReq=4===(null===(M=null===(S=this.shareDetailForm.controls.template)||void 0===S?void 0:S.value)||void 0===M?void 0:M.whatsAppHeaderTypes),this.shareDetailForm.controls.bodyVariables.setValue(""),this.bodyValuesCount?(0,w.fN)(E.$iz,this.shareDetailForm,"bodyVariables",[v.kI.required]):(0,w.fN)(E.qLM,this.shareDetailForm,"bodyVariables"),this.fileNameReq?(0,w.fN)(E.$iz,this.shareDetailForm,"fileName",[v.kI.required]):(0,w.fN)(E.qLM,this.shareDetailForm,"fileName")}),this.shareDetailForm.get("bodyVariables").valueChanges.subscribe(l=>{var o,u,S,M,y,W,D;this.isBodyVarCountValid=0==(null===(M=null===(S=null===(u=null===(o=this.shareDetailForm)||void 0===o?void 0:o.controls)||void 0===u?void 0:u.template)||void 0===S?void 0:S.value)||void 0===M?void 0:M.bodyValuesCount)||(null===(y=l.split(","))||void 0===y?void 0:y.length)==(null===(D=null===(W=this.shareDetailForm.controls.template)||void 0===W?void 0:W.value)||void 0===D?void 0:D.bodyValuesCount)&&!l.split(",").some(ae=>""===ae)})}getSelectedCountryCodeContactNo(){var l;return null===(l=this.contactNoInput)||void 0===l?void 0:l.selectedCountry}contactNumberValidator(){let l="IN";return o=>{var u,S,M;const y=document.querySelector(".contactNoInput > div > input");if(!(null!==(u=null==y?void 0:y.value)&&void 0!==u&&u.length||null!=o&&o.value))return{required:!0};l=null===(S=this.getSelectedCountryCodeContactNo())||void 0===S?void 0:S.dialCode;try{return(0,H.t)((null===(M=this.contactNoInput)||void 0===M?void 0:M.value)||(null==o?void 0:o.value),l)?null:{validatePhoneNumber:!0}}catch(W){return{validatePhoneNumber:!0}}}}updateBulkShare(l){var o,u,S,M,y,W;if(!this.shareDetailForm.valid||!this.isBodyVarCountValid)return void(0,w._5)(this.shareDetailForm);const D=this.shareDetailForm.value,ae=null===(S=null!==(o=this.data)&&void 0!==o&&o.isData?null===(u=this.data)||void 0===u?void 0:u.bulkData:this.data)||void 0===S?void 0:S.map(k=>({id:null==k?void 0:k.id,contactNo:null==k?void 0:k.contactNo,name:null==k?void 0:k.name}));let Q=Object.assign(Object.assign({},null==D?void 0:D.template),{headerValue:null===(M=null==D?void 0:D.template)||void 0===M?void 0:M.headerValuesCount.toString(),bodyValues:null==D?void 0:D.bodyVariables.split(","),fileName:null==D?void 0:D.fileName,campaignName:null==D?void 0:D.campaignName,isData:null===(y=this.data)||void 0===y?void 0:y.isData});this.testTemplateSent?(Q=Object.assign(Object.assign({},Q),{leadContactInfoDtos:[...ae]}),this.store.dispatch(new I.tu(Q))):(Q=Object.assign(Object.assign({},Q),{leadContactInfoDtos:[{contactNo:null==D?void 0:D.testContactNo}],isTestMessage:!0}),this.store.dispatch(new I.Fh(Q))),null===(W=this.selectedNodes)||void 0===W||W.forEach(k=>{null==k||k.setSelected(!1)}),this.testTemplateSent||(this.modalRef=this.modalService.show(l,{class:"modal-300 modal-dialog-centered"})),this.trackingService.trackFeature(this.testTemplateSent?"Web.Leads.Button.Sendbulk.Click":"Web.Leads.Button.sendtest.Click")}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return g.\u0275fac=function(l){return new(l||g)(e.\u0275\u0275directiveInject($.tT),e.\u0275\u0275directiveInject(B.yh),e.\u0275\u0275directiveInject($.UZ),e.\u0275\u0275directiveInject(v.qu),e.\u0275\u0275directiveInject(K.e))},g.\u0275cmp=e.\u0275\u0275defineComponent({type:g,selectors:[["lead-bulk-share"]],viewQuery:function(l,o){if(1&l&&e.\u0275\u0275viewQuery(q,5),2&l){let u;e.\u0275\u0275queryRefresh(u=e.\u0275\u0275loadQuery())&&(o.contactNoInput=u.first)}},decls:44,vars:58,consts:[[1,"bg-light-pearl","h-100vh","bg-triangle-pattern"],[1,"flex-between","bg-coal","w-100","px-16","py-12","text-white"],[1,"icon","ic-close-secondary","ic-large","cursor-pointer",3,"click"],[1,"px-12",3,"formGroup"],[1,"field-label-req"],["label","campaign",3,"control"],["ResizableDropdown","","addTagText","Create New Campaign","formControlName","campaignName",3,"virtualScroll","items","closeOnSelect","ngClass","addTag","placeholder"],["label","Template",3,"control"],["bindLabel","name","formControlName","template","ResizableDropdown","",3,"virtualScroll","items","placeholder","change"],["label","Contact No",3,"control"],["formControlName","testContactNo",1,"no-validation","contactNoInput",3,"preferredCountries","enablePlaceholder","enableSearch","placeholder"],["contactNoInput",""],["class","position-relative",4,"ngIf"],[3,"ngClass"],["label","File Name",3,"control"],["type","text","formControlName","fileName",3,"placeholder"],[1,"flex-center","mt-20"],[1,"btn-gray","mr-20",3,"click"],["class","btn-coal",3,"click",4,"ngIf"],["ConfirmationModal",""],[1,"position-relative"],["label","Header Variable",3,"control"],["type","text","formControlName","headerVariable",3,"placeholder","required"],[1,"ml-4","text-sm"],["label","Body Variable(s)",3,"control"],["type","text","formControlName","bodyVariables",3,"placeholder"],["class","error-message",4,"ngIf"],[1,"error-message"],[1,"btn-coal",3,"click"],[1,"px-40","py-30","flex-center-col"],[1,"ic-close-secondary","ic-close-modal","ip-ic-close-modal",3,"click"],[1,"fw-600","text-center"],[1,"flex-center","mt-24"]],template:function(l,o){1&l&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"h4"),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",2),e.\u0275\u0275listener("click",function(){return o.modalService.hide()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"form",3)(7,"h5",4),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"form-errors-wrapper",5),e.\u0275\u0275element(11,"ng-select",6),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"h5",4),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275pipe(16,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"form-errors-wrapper",7)(18,"ng-select",8),e.\u0275\u0275listener("change",function(){return o.trackingService.trackFeature("Web.Leads.Options.Selecttemplate.Click")}),e.\u0275\u0275pipe(19,"translate"),e.\u0275\u0275pipe(20,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"h5",4),e.\u0275\u0275text(22),e.\u0275\u0275pipe(23,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"form-errors-wrapper",9),e.\u0275\u0275element(25,"ngx-mat-intl-tel-input",10,11),e.\u0275\u0275pipe(27,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(28,ne,6,6,"div",12),e.\u0275\u0275template(29,F,10,9,"div",12),e.\u0275\u0275elementStart(30,"h5",13),e.\u0275\u0275text(31),e.\u0275\u0275pipe(32,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(33,"form-errors-wrapper",14),e.\u0275\u0275element(34,"input",15),e.\u0275\u0275pipe(35,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(36,"div",16)(37,"button",17),e.\u0275\u0275listener("click",function(){return o.modalService.hide(),o.trackingService.trackFeature("Web.Leads.Button.cancel.Click")}),e.\u0275\u0275text(38),e.\u0275\u0275pipe(39,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(40,z,2,0,"button",18),e.\u0275\u0275template(41,r,2,0,"button",18),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(42,p,12,9,"ng-template",null,19,e.\u0275\u0275templateRefExtractor)),2&l&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,32,"GLOBAL.bulk-whatsapp")),e.\u0275\u0275advance(3),e.\u0275\u0275property("formGroup",o.shareDetailForm),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(9,34,"GLOBAL.select")," Campaign"),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",o.shareDetailForm.controls.campaignName),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("placeholder","",e.\u0275\u0275pipeBind1(12,36,"GLOBAL.select"),"/Create Campaign"),e.\u0275\u0275property("virtualScroll",!0)("items",o.campaigns)("closeOnSelect",!0)("ngClass",e.\u0275\u0275pureFunction1(56,_,o.isCampaignListLoading))("addTag",!0),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(15,38,"GLOBAL.select")," ",e.\u0275\u0275pipeBind1(16,40,"BULK_LEAD.template"),""),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",o.shareDetailForm.controls.template),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate2("placeholder","",e.\u0275\u0275pipeBind1(19,42,"GLOBAL.select")," ",e.\u0275\u0275pipeBind1(20,44,"BULK_LEAD.template"),""),e.\u0275\u0275property("virtualScroll",!0)("items",o.templates),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(23,46,"GLOBAL.test-contact-no")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",o.shareDetailForm.controls.testContactNo),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(27,48,"GLOBAL.ex-9133xxxxxx")),e.\u0275\u0275property("preferredCountries",o.preferredCountries)("enablePlaceholder",!0)("enableSearch",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",1==(null==o.shareDetailForm.controls.template||null==o.shareDetailForm.controls.template.value?null:o.shareDetailForm.controls.template.value.whatsAppHeaderTypes)&&(null==o.shareDetailForm.controls.template||null==o.shareDetailForm.controls.template.value?null:o.shareDetailForm.controls.template.value.headerValuesCount)>0),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==o.shareDetailForm.controls.template?null:o.shareDetailForm.controls.template.value)&&o.bodyValuesCount),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",o.fileNameReq?"field-label-req":"field-label"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(32,50,"GLOBAL.file-name")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",o.shareDetailForm.controls.fileName),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(35,52,"GLOBAL.file-name")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(39,54,"GLOBAL.cancel")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!o.testTemplateSent),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",o.testTemplateSent))},dependencies:[v._Y,v.Fj,v.JJ,v.JL,v.Q7,f.mk,f.O5,s.w9,b.z,v.sg,v.u,P.d,N.s,G.X$],encapsulation:2}),g})()},80444:(ie,Z,n)=>{n.d(Z,{X:()=>ee});var e=n(5e3),v=n(82722),H=n(2976),U=n(61021),E=n(82667),w=n(97047),V=n(40553),A=n(66844),j=n(32049),I=n(92340),X=n(48315),$=n(38827),B=n(65620),K=n(1880),f=n(69808),s=n(63172),b=n(11970),P=n(47511),N=n(18995);function G(F,z){if(1&F){const r=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ag-grid-angular",11,12),e.\u0275\u0275listener("gridReady",function(_){e.\u0275\u0275restoreView(r);const C=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(C.onGridReady(_))}),e.\u0275\u0275elementEnd()}if(2&F){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("pagination",!0)("paginationPageSize",r.pageSize)("gridOptions",r.gridOptions)("rowData",r.rowData)("suppressPaginationPanel",!0)}}function q(F,z){if(1&F){const r=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"pagination",13),e.\u0275\u0275listener("pageChange",function(_){e.\u0275\u0275restoreView(r);const C=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(C.onPageChange(_))}),e.\u0275\u0275elementEnd()}if(2&F){const r=e.\u0275\u0275nextContext();e.\u0275\u0275property("offset",r.currOffset)("limit",1)("range",1)("size",r.getPages(r.totalReqCount,r.pageSize))}}function ne(F,z){1&F&&(e.\u0275\u0275elementStart(0,"div",14),e.\u0275\u0275element(1,"application-loader"),e.\u0275\u0275elementEnd())}let ee=(()=>{class F{constructor(r,p,_,C){this.gridOptionsService=r,this.modalService=p,this._store=_,this.trackingService=C,this.stopper=new e.EventEmitter,this.pageSize=H.IV1,this.currOffset=0,this.rowData=[],this.s3BucketUrl=I.N.s3ImageBucketURL,this.getPages=U.UQ,this.filtersPayload={pageNumber:1,pageSize:this.pageSize,path:"lead"},this.gridOptions=this.gridOptionsService.getGridSettings(this),this.defaultColDef=this.gridOptions.defaultColDef,this.gridOptions.rowData=this.rowData,this.initializeGridSettings()}ngOnInit(){this.trackingService.trackFeature(`Web.${this.isDataManagement?"Data":"Leads".replace(/\s+/g,"").replace(/^./,r=>r.toUpperCase())}.Page.ExportTracker.Visit`),this.isDataManagement?(this._store.select(w.fL).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.rowData=null==r?void 0:r.items,this.totalReqCount=null==r?void 0:r.totalCount}),this._store.select(w.e4).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.isDataExportStatusLoading=r})):this._store.select(A.xw).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.rowData=null==r?void 0:r.items,this.totalReqCount=null==r?void 0:r.totalCount}),this._store.select(A.Ax).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.isDataExportStatusLoading=r}),this._store.select(j.Xf).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.userData=r})}initializeGridSettings(){this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.columnDefs=[{headerName:"Export By",field:"Export By",minWidth:180,filter:!1,valueGetter:r=>{var p,_;return[null===(_=null===(p=r.data)||void 0===p?void 0:p.exportedUser)||void 0===_?void 0:_.name]},cellRenderer:r=>`<p class="text-truncate-2">${r.value}</p>`},{headerName:"Requested on",field:"Requested on",minWidth:150,valueGetter:r=>{var p,_,C;return[(0,U.h5)(null===(p=r.data)||void 0===p?void 0:p.createdOn,null===(C=null===(_=this.userData)||void 0===_?void 0:_.timeZoneInfo)||void 0===C?void 0:C.baseUTcOffset,"fullDateTime")]},cellRenderer:r=>{var p,_,C,g,O;return`<p class="text-nowrap">${r.value}</p>\n             <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(_=null===(p=this.userData)||void 0===p?void 0:p.timeZoneInfo)||void 0===_?void 0:_.timeZoneName)&&(null===(C=this.userData)||void 0===C?void 0:C.shouldShowTimeZone)&&r.value?"("+(null===(O=null===(g=this.userData)||void 0===g?void 0:g.timeZoneInfo)||void 0===O?void 0:O.timeZoneName)+")":""}</p>`}},{headerName:"Last Modified on",field:"Last Modified on",minWidth:150,valueGetter:r=>{var p,_,C;return[(0,U.h5)(null===(p=r.data)||void 0===p?void 0:p.lastModifiedOn,null===(C=null===(_=this.userData)||void 0===_?void 0:_.timeZoneInfo)||void 0===C?void 0:C.baseUTcOffset,"fullDateTime")]},cellRenderer:r=>{var p,_,C,g,O;return`<p class="text-nowrap">${r.value}</p>\n             <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(_=null===(p=this.userData)||void 0===p?void 0:p.timeZoneInfo)||void 0===_?void 0:_.timeZoneName)&&(null===(C=this.userData)||void 0===C?void 0:C.shouldShowTimeZone)&&r.value?"("+(null===(O=null===(g=this.userData)||void 0===g?void 0:g.timeZoneInfo)||void 0===O?void 0:O.timeZoneName)+")":""}</p>`}},{headerName:"Total Count",field:"Total Count",maxWidth:100,filter:!1,valueGetter:r=>{var p;return[null===(p=r.data)||void 0===p?void 0:p.count]},cellRenderer:r=>`<p>${r.value}</p>`},{headerName:"File Name",field:"File Name",minWidth:100,filter:!1,valueGetter:r=>{var p;return[null===(p=r.data)||void 0===p?void 0:p.fileName]},cellRenderer:r=>`<p class="text-truncate-1 break-all">${r.value}</p>`},{headerName:"Excel File",field:"Status",maxWidth:180,minWidth:180,filter:!1,valueGetter:r=>{var p,_;return[null!==(p=r.data)&&void 0!==p&&p.s3BucketKey?null===(_=r.data)||void 0===_?void 0:_.s3BucketKey:""]},cellRenderer:r=>r.value[0]?`<a href="${r.value}" class="btn btn-xxs btn-linear-green text-nowrap flex-center w-150">\n            <span class="icon ic-xxs ic-download"></span>\n            <span class="text-white ml-8">Ready to Download</span></a>`:""}],this.gridOptions.context={componentParent:this}}onGridReady(r){this.gridApi=r.api,r.api.sizeColumnsToFit(),this.gridColumnApi=r.columnApi}onPageChange(r){this.currOffset=r,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:r+1,pageSize:this.pageSize}),this.trackingService.trackFeature(`Web.${this.isDataManagement?"Data":"Leads".replace(/\s+/g,"").replace(/^./,p=>p.toUpperCase())}.Button.ExportTrackerPagination.Click`),this.updateTrackerList()}updateTrackerList(){var r,p,_,C;this.trackingService.trackFeature(`Web.${this.isDataManagement?"Data":"Leads".replace(/\s+/g,"").replace(/^./,g=>g.toUpperCase())}.Button.ExportTrackerRefresh.Click`),this._store.dispatch(this.isDataManagement?new E.R_(null===(r=this.filtersPayload)||void 0===r?void 0:r.pageNumber,null===(p=this.filtersPayload)||void 0===p?void 0:p.pageSize):new V.xc_(null===(_=this.filtersPayload)||void 0===_?void 0:_.pageNumber,null===(C=this.filtersPayload)||void 0===C?void 0:C.pageSize))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return F.\u0275fac=function(r){return new(r||F)(e.\u0275\u0275directiveInject(X.t),e.\u0275\u0275directiveInject($.tT),e.\u0275\u0275directiveInject(B.yh),e.\u0275\u0275directiveInject(K.e))},F.\u0275cmp=e.\u0275\u0275defineComponent({type:F,selectors:[["export-leads-tracker"]],decls:15,vars:7,consts:[[1,"bg-coal","w-100","px-20","py-12","text-white","brtl-10","brtr-10","flex-between"],[1,"fw-semi-bold"],[1,"btn","btn-sm","btn-linear-green","align-center",3,"click"],[1,"ic-refresh","icon","ic-xxs","mr-8","ph-mr-0"],[1,"text-white","text-normal","ph-d-none"],[1,"ic-close-secondary","ic-close-modal","tb-ic-close-secondary",3,"click"],[1,"max-h-100-176","scrollbar"],["class","ag-theme-alpine",3,"pagination","paginationPageSize","gridOptions","rowData","suppressPaginationPanel","gridReady",4,"ngIf","ngIfElse"],[1,"flex-end","m-20"],[3,"offset","limit","range","size","pageChange",4,"ngIf"],["gridLoader",""],[1,"ag-theme-alpine",3,"pagination","paginationPageSize","gridOptions","rowData","suppressPaginationPanel","gridReady"],["agGrid",""],[3,"offset","limit","range","size","pageChange"],[1,"flex-center","h-100","mt-60"]],template:function(r,p){if(1&r&&(e.\u0275\u0275elementStart(0,"div",0)(1,"h3",1),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"button",2),e.\u0275\u0275listener("click",function(){return p.updateTrackerList()}),e.\u0275\u0275element(4,"span",3),e.\u0275\u0275elementStart(5,"span",4),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"a",5),e.\u0275\u0275listener("click",function(){return p.modalService.hide()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"div",6),e.\u0275\u0275template(10,G,2,5,"ag-grid-angular",7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",8),e.\u0275\u0275template(12,q,1,4,"pagination",9),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(13,ne,2,0,"ng-template",null,10,e.\u0275\u0275templateRefExtractor)),2&r){const _=e.\u0275\u0275reference(14);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",p.isDataManagement?"Data":"Leads"," - Export Tracker"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(7,5,"BULK_LEAD.refresh-data")),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",!p.isDataExportStatusLoading)("ngIfElse",_),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!p.isDataExportStatusLoading)}},dependencies:[f.O5,s.N8,b.Q,P.t,N.X$],encapsulation:2}),F})()},5927:(ie,Z,n)=>{n.d(Z,{w:()=>$});var e=n(5e3),v=n(6536),H=n(52230),U=n(65620),E=n(38827),w=n(93075),V=n(69808),A=n(18995);function j(B,K){if(1&B){const f=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",11)(1,"div",12),e.\u0275\u0275listener("click",function(b){const N=e.\u0275\u0275restoreView(f).$implicit,G=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(G.onSelect(b,N))}),e.\u0275\u0275elementStart(2,"h6",13),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",14)(5,"div",15)(6,"div",16),e.\u0275\u0275listener("click",function(b){const N=e.\u0275\u0275restoreView(f).$implicit,G=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(G.onEditFilter(b,N))}),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275element(8,"span",17),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"div",18),e.\u0275\u0275elementStart(10,"div",19),e.\u0275\u0275listener("click",function(b){const N=e.\u0275\u0275restoreView(f).$implicit,G=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(G.onDeleteFilter(b,N))}),e.\u0275\u0275element(11,"span",20),e.\u0275\u0275elementEnd()()()()()}if(2&B){const f=K.$implicit,s=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(f.name),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",s.isMobileView?"":"hover-container"),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("title",e.\u0275\u0275pipeBind1(7,3,"GLOBAL.edit"))}}function I(B,K){if(1&B&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",9),e.\u0275\u0275template(2,j,12,5,"div",10),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&B){const f=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",f.showFilters?"max-h-100-380":"max-h-100-332"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",f.filteredFilters)}}function X(B,K){1&B&&(e.\u0275\u0275elementStart(0,"div",21)(1,"h6",22),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd()()),2&B&&(e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,"GLOBAL.no-saved-filters-found")))}let $=(()=>{class B{constructor(f,s,b){this._store=f,this.modalService=s,this.modalRef=b,this.isMobileView=!1,this.showFilters=!1,this.editFilter=new e.EventEmitter,this.selectFilter=new e.EventEmitter,this.closeFilter=new e.EventEmitter,this.filteredFilters=[],this.searchTerm=""}ngOnInit(){console.log(this.showFilters),this.filteredFilters=this.filters}onSearch(){const f=this.searchTerm.toLowerCase().replace(/\s+/g,"");this.filteredFilters=this.filters.filter(s=>s.name.toLowerCase().replace(/\s+/g,"").includes(f))}onEditFilter(f,s){f.stopPropagation();const b=null!=s&&s.filterCriteria?JSON.parse(s.filterCriteria):{},P=Object.assign(Object.assign({},s),{filterCriteria:b,id:null==s?void 0:s.id,name:null==s?void 0:s.name});this.editFilter.emit(P)}onSelect(f,s){f.stopPropagation();const b=null!=s&&s.filterCriteria?JSON.parse(s.filterCriteria):{},P=Object.assign(Object.assign({},s),{filterCriteria:b,id:null==s?void 0:s.id,name:null==s?void 0:s.name});this.selectFilter.emit(P)}onDeleteFilter(f,s){var b;f.stopPropagation(),this.modalRef&&this.modalRef.hide(),this.modalRef=this.modalService.show(H._,Object.assign({},{class:"modal-350 top-modal",initialState:{message:"GLOBAL.user-confirmation",confirmType:"delete",title:null==s?void 0:s.name,fieldType:"filter"}})),null!==(b=this.modalRef)&&void 0!==b&&b.onHide&&this.modalRef.onHide.subscribe(N=>{"confirmed"==N&&(this._store.dispatch(new v.UV(null==s?void 0:s.id)),this.onClose(f))})}onClose(f){f.stopPropagation(),this.closeFilter.emit()}}return B.\u0275fac=function(f){return new(f||B)(e.\u0275\u0275directiveInject(U.yh),e.\u0275\u0275directiveInject(E.tT),e.\u0275\u0275directiveInject(E.UZ))},B.\u0275cmp=e.\u0275\u0275defineComponent({type:B,selectors:[["saved-filter"]],inputs:{filters:"filters",isMobileView:"isMobileView",showFilters:"showFilters"},outputs:{editFilter:"editFilter",selectFilter:"selectFilter",closeFilter:"closeFilter"},decls:14,vars:10,consts:[["id","dropdown-element",1,"w-240","bg-white","box-shadow-10","br-10","z-index-1021","position-absolute",3,"ngClass","click"],[1,"flex-between","bg-coal","p-12","text-white"],[1,"icon","ic-close","ic-white","ic-sm","cursor-pointer",3,"click"],[1,"px-12","pb-8"],[1,"bg-white","align-center","mt-10","px-20","no-validation","py-6","br-20","border"],[1,"border-0","outline-0","w-100","bg-white","ml-4",3,"placeholder","ngModel","ngModelChange","input"],[1,"justify-end","search","icon","ic-search-solid","ic-xxs","ic-slate-90"],[4,"ngIf","ngIfElse"],["noFilter",""],[1,"border","br-6","mt-10","scrollbar","max-h-100-332",3,"ngClass"],["class","hovered-card",4,"ngFor","ngForOf"],[1,"hovered-card"],[1,"flex-between","hover-container","cursor-pointer","py-12","hover-bg","w-100","border-bottom",3,"click"],[1,"ml-12","my-4","text-sm"],[3,"ngClass"],[1,"d-flex"],[1,"ml-4","icon-badge","icon-width",3,"title","click"],["id","clkSavedFilterEdit","data-automate-id","clkSavedFilterEdit",1,"icon","ic-dark","ic-pen-solid","m-auto","ic-xxs"],[1,"border","mt-2","h-16","mx-4"],["title","Delete",1,"ml-4","icon-badge","icon-width",3,"click"],["id","clkSavedFilterDelete","data-automate-id","clkSavedFilterDelete",1,"icon","ic-dark","ic-trash","m-auto","ic-xxs"],[1,"flex-center","min-h-240"],[1,"text-center","text-sm"]],template:function(f,s){if(1&f&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("click",function(P){return P.stopPropagation()}),e.\u0275\u0275elementStart(1,"div",1)(2,"h5"),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",2),e.\u0275\u0275listener("click",function(P){return s.onClose(P)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"div",3)(7,"div",4)(8,"input",5),e.\u0275\u0275listener("ngModelChange",function(P){return s.searchTerm=P})("input",function(){return s.onSearch()}),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(10,"span",6),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(11,I,3,2,"ng-container",7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(12,X,4,3,"ng-template",null,8,e.\u0275\u0275templateRefExtractor)),2&f){const b=e.\u0275\u0275reference(13);e.\u0275\u0275property("ngClass",s.isMobileView?"top-40 nright-0":s.showFilters?"top-95 nright-10":"top-65 nright-10"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,6,"GLOBAL.saved-filter")),e.\u0275\u0275advance(5),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(9,8,"GLOBAL.type-to-search")),e.\u0275\u0275property("ngModel",s.searchTerm),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",null==s.filteredFilters?null:s.filteredFilters.length)("ngIfElse",b)}},dependencies:[w.Fj,w.JJ,w.On,V.mk,V.sg,V.O5,A.X$],encapsulation:2}),B})()},83905:(ie,Z,n)=>{n.d(Z,{z:()=>H});var e=n(86805),v=n(70930);function H(U,E){const w="object"==typeof E;return new Promise((V,A)=>{const j=new v.Hp({next:I=>{V(I),j.unsubscribe()},error:A,complete:()=>{w?V(E.defaultValue):A(new e.K)}});U.subscribe(j)})}}}]);