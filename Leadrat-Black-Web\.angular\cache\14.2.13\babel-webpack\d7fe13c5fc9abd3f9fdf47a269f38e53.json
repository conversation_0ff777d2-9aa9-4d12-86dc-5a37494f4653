{"ast": null, "code": "import isSymbol from './isSymbol.js';\n/** Used as references for the maximum length and index of an array. */\n\nvar MAX_ARRAY_LENGTH = 4294967295,\n    MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeFloor = Math.floor,\n    nativeMin = Math.min;\n/**\n * The base implementation of `_.sortedIndexBy` and `_.sortedLastIndexBy`\n * which invokes `iteratee` for `value` and each element of `array` to compute\n * their sort ranking. The iteratee is invoked with one argument; (value).\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {Function} iteratee The iteratee invoked per element.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\n\nfunction baseSortedIndexBy(array, value, iteratee, retHighest) {\n  var low = 0,\n      high = array == null ? 0 : array.length;\n\n  if (high === 0) {\n    return 0;\n  }\n\n  value = iteratee(value);\n  var valIsNaN = value !== value,\n      valIsNull = value === null,\n      valIsSymbol = isSymbol(value),\n      valIsUndefined = value === undefined;\n\n  while (low < high) {\n    var mid = nativeFloor((low + high) / 2),\n        computed = iteratee(array[mid]),\n        othIsDefined = computed !== undefined,\n        othIsNull = computed === null,\n        othIsReflexive = computed === computed,\n        othIsSymbol = isSymbol(computed);\n\n    if (valIsNaN) {\n      var setLow = retHighest || othIsReflexive;\n    } else if (valIsUndefined) {\n      setLow = othIsReflexive && (retHighest || othIsDefined);\n    } else if (valIsNull) {\n      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);\n    } else if (valIsSymbol) {\n      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);\n    } else if (othIsNull || othIsSymbol) {\n      setLow = false;\n    } else {\n      setLow = retHighest ? computed <= value : computed < value;\n    }\n\n    if (setLow) {\n      low = mid + 1;\n    } else {\n      high = mid;\n    }\n  }\n\n  return nativeMin(high, MAX_ARRAY_INDEX);\n}\n\nexport default baseSortedIndexBy;", "map": null, "metadata": {}, "sourceType": "module"}