{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function sample(notifier) {\n  return source => source.lift(new SampleOperator(notifier));\n}\n\nclass SampleOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    const sampleSubscriber = new SampleSubscriber(subscriber);\n    const subscription = source.subscribe(sampleSubscriber);\n    subscription.add(innerSubscribe(this.notifier, new SimpleInnerSubscriber(sampleSubscriber)));\n    return subscription;\n  }\n\n}\n\nclass SampleSubscriber extends SimpleOuterSubscriber {\n  constructor() {\n    super(...arguments);\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    this.value = value;\n    this.hasValue = true;\n  }\n\n  notifyNext() {\n    this.emitValue();\n  }\n\n  notifyComplete() {\n    this.emitValue();\n  }\n\n  emitValue() {\n    if (this.hasValue) {\n      this.hasValue = false;\n      this.destination.next(this.value);\n    }\n  }\n\n} //# sourceMappingURL=sample.js.map", "map": null, "metadata": {}, "sourceType": "module"}