"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[575],{48575:(T,v,n)=>{n.r(v),n.d(v,{LeadsModule:()=>b});var E=n(69808),w=n(40520),g=n(93075),y=n(33315),_=n(71511),l=n(5e3);Symbol("MainQueueId"),new Array(256).fill(0).map((o,t)=>("0"+t.toString(16)).slice(-2));let x=(()=>{class o{}return o.\u0275fac=function(e){return new(e||o)},o.\u0275mod=l.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=l.\u0275\u0275defineInjector({imports:[[g.u5,E.ez]]}),o})();var m=n(18995),O=n(63172),F=n(6162),I=n(23713),A=n(21718),p=n(51190),R=n(2407),W=n(96616),N=n(90810);n(73150),n(6497),n(88923),n(99219);let b=(()=>{class o{}return o.\u0275fac=function(e){return new(e||o)},o.\u0275mod=l.\u0275\u0275defineNgModule({type:o}),o.\u0275inj=l.\u0275\u0275defineInjector({providers:[m.sK,W.c],imports:[E.ez,_.Bz.forChild(R._),N.m,O.sF,g.UX,g.u5,A.d,F.l1,y.Y4,x,I.CT.forRoot({player:p.xd}),m.aw.forChild({loader:{provide:m.Zw,useFactory:p.gS,deps:[w.eN]}})]}),o})()}}]);