{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\n// it's a very new API right now.\n// Not exported from index.\n\n/** @private */\nexport class AbortController {\n  constructor() {\n    this._isAborted = false;\n    this.onabort = null;\n  }\n\n  abort() {\n    if (!this._isAborted) {\n      this._isAborted = true;\n\n      if (this.onabort) {\n        this.onabort();\n      }\n    }\n  }\n\n  get signal() {\n    return this;\n  }\n\n  get aborted() {\n    return this._isAborted;\n  }\n\n} //# sourceMappingURL=AbortController.js.map", "map": null, "metadata": {}, "sourceType": "module"}