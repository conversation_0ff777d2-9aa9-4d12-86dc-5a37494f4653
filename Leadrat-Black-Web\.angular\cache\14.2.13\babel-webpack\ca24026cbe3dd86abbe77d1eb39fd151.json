{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return source => source.lift(new ThrottleOperator(durationSelector, !!config.leading, !!config.trailing));\n}\n\nclass ThrottleOperator {\n  constructor(durationSelector, leading, trailing) {\n    this.durationSelector = durationSelector;\n    this.leading = leading;\n    this.trailing = trailing;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));\n  }\n\n}\n\nclass ThrottleSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, durationSelector, _leading, _trailing) {\n    super(destination);\n    this.destination = destination;\n    this.durationSelector = durationSelector;\n    this._leading = _leading;\n    this._trailing = _trailing;\n    this._hasValue = false;\n  }\n\n  _next(value) {\n    this._hasValue = true;\n    this._sendValue = value;\n\n    if (!this._throttled) {\n      if (this._leading) {\n        this.send();\n      } else {\n        this.throttle(value);\n      }\n    }\n  }\n\n  send() {\n    const {\n      _hasValue,\n      _sendValue\n    } = this;\n\n    if (_hasValue) {\n      this.destination.next(_sendValue);\n      this.throttle(_sendValue);\n    }\n\n    this._hasValue = false;\n    this._sendValue = undefined;\n  }\n\n  throttle(value) {\n    const duration = this.tryDurationSelector(value);\n\n    if (!!duration) {\n      this.add(this._throttled = innerSubscribe(duration, new SimpleInnerSubscriber(this)));\n    }\n  }\n\n  tryDurationSelector(value) {\n    try {\n      return this.durationSelector(value);\n    } catch (err) {\n      this.destination.error(err);\n      return null;\n    }\n  }\n\n  throttlingDone() {\n    const {\n      _throttled,\n      _trailing\n    } = this;\n\n    if (_throttled) {\n      _throttled.unsubscribe();\n    }\n\n    this._throttled = undefined;\n\n    if (_trailing) {\n      this.send();\n    }\n  }\n\n  notifyNext() {\n    this.throttlingDone();\n  }\n\n  notifyComplete() {\n    this.throttlingDone();\n  }\n\n} //# sourceMappingURL=throttle.js.map", "map": null, "metadata": {}, "sourceType": "module"}