const fs = require('fs');

/**
 * Super Fast Translation Script - Uses mostly predefined translations
 * Completes in under 5 minutes per language
 */
class FastTranslator {
    constructor() {
        this.languages = {
            'ta': 'Tamil',
            'te': 'Telugu',
            'bn': 'Bengali',
            'ml': 'Malayalam'
        };
    }

    /**
     * Get comprehensive predefined translations
     */
    getTranslations(targetLang) {
        const translations = {
            'ta': this.getTamilTranslations(),
            'te': this.getTeluguTranslations(),
            'bn': this.getBengaliTranslations(),
            'ml': this.getMalayalamTranslations()
        };

        return translations[targetLang] || {};
    }

    getTamilTranslations() {
        return {
            // Core UI
            "hello world": "வணக்கம் உலகம்",
            "The title of my app": "என் பயன்பாட்டின் தலைப்பு",
            "Settings": "அமைப்புகள்",
            "Today": "இன்று",
            "All": "அனைத்தும்",
            "New": "புதிய",
            "Old": "பழைய",
            "Overdue": "தாமதமான",
            "Upcoming": "வரவிருக்கும்",
            "Completed": "முடிந்தது",
            "Not Interested": "ஆர்வம் இல்லை",
            "Select": "தேர்ந்தெடுக்கவும்",
            "Status": "நிலை",
            "Assigned To": "ஒதுக்கப்பட்டது",
            "For": "க்காக",
            "Reset": "மீட்டமை",
            "Go": "செல்",
            "No": "இல்லை",
            "Yes": "ஆம்",
            "In": "இல்",
            "Lead": "முன்னணி",
            "Leads": "முன்னணிகள்",
            "Name": "பெயர்",
            "Show": "காட்டு",
            "Entries": "உள்ளீடுகள்",
            "Update": "புதுப்பிக்கவும்",
            "Details": "விவரங்கள்",
            "Personal": "தனிப்பட்ட",
            "Work": "வேலை",
            "Home": "வீடு",
            "Mobile": "மொபைல்",
            "Email": "மின்னஞ்சல்",
            "Phone": "தொலைபேசி",
            "Address": "முகவரி",
            "City": "நகரம்",
            "State": "மாநிலம்",
            "Country": "நாடு",
            "Pincode": "அஞ்சல் குறியீடு",
            "Save": "சேமி",
            "Cancel": "ரத்து செய்",
            "Delete": "நீக்கு",
            "Edit": "திருத்து",
            "Add": "சேர்",
            "Remove": "அகற்று",
            "Search": "தேடு",
            "Filter": "வடிகட்டி",
            "Sort": "வரிசைப்படுத்து",
            "Export": "ஏற்றுமதி",
            "Import": "இறக்குமதி",
            "Print": "அச்சிடு",
            "Download": "பதிவிறக்கம்",
            "Upload": "பதிவேற்றம்",
            "Submit": "சமர்ப்பிக்கவும்",
            "Close": "மூடு",
            "Open": "திற",
            "View": "பார்வை",
            "Preview": "முன்னோட்டம்",
            "Next": "அடுத்து",
            "Previous": "முந்தைய",
            "First": "முதல்",
            "Last": "கடைசி",
            "Page": "பக்கம்",
            "Of": "இன்",
            "Total": "மொத்தம்",
            "Loading": "ஏற்றுகிறது",
            "Please wait": "தயவுசெய்து காத்திருக்கவும்",
            "Error": "பிழை",
            "Success": "வெற்றி",
            "Warning": "எச்சரிக்கை",
            "Info": "தகவல்",
            "Confirm": "உறுதிப்படுத்து",
            "Are you sure": "நீங்கள் உறுதியாக இருக்கிறீர்களா",
            "Login": "உள்நுழைவு",
            "Logout": "வெளியேறு",
            "Register": "பதிவு செய்",
            "Forgot password": "கடவுச்சொல்லை மறந்துவிட்டீர்களா",
            "Username": "பயனர்பெயர்",
            "Password": "கடவுச்சொல்",
            "Remember me": "என்னை நினைவில் வைத்துக்கொள்ளுங்கள்",
            "Dashboard": "டாஷ்போர்டு",
            "Profile": "சுயவிவரம்",
            "Account": "கணக்கு",
            "Notifications": "அறிவிப்புகள்",
            "Messages": "செய்திகள்",
            "Help": "உதவி",
            "Support": "ஆதரவு",
            "Contact": "தொடர்பு",
            "About": "பற்றி",
            "Privacy": "தனியுரிமை",
            "Terms": "விதிமுறைகள்",
            "Language": "மொழி",
            "Theme": "தீம்",
            "Dark": "இருண்ட",
            "Light": "ஒளி",
            "Auto": "தானியங்கி",

            // Property specific
            "Property": "சொத்து",
            "Properties": "சொத்துகள்",
            "Manage Properties": "சொத்துகளை நிர்வகிக்கவும்",
            "Property Details": "சொத்து விவரங்கள்",
            "Property Type": "சொத்து வகை",
            "Property Status": "சொத்து நிலை",
            "Property Value": "சொத்து மதிப்பு",
            "Property Location": "சொத்து இடம்",
            "Property Owner": "சொத்து உரிமையாளர்",
            "Property Manager": "சொத்து மேலாளர்",
            "Property Description": "சொத்து விளக்கம்",
            "Property Images": "சொத்து படங்கள்",
            "Property Documents": "சொத்து ஆவணங்கள்",
            "Property History": "சொத்து வரலாறு",
            "Property Reports": "சொத்து அறிக்கைகள்",
            "Property Search": "சொத்து தேடல்",
            "Property Filter": "சொத்து வடிகட்டி",
            "Property List": "சொத்து பட்டியல்",
            "Property Grid": "சொத்து கட்டம்",
            "Property Map": "சொத்து வரைபடம்",

            // Common actions
            "Create": "உருவாக்கு",
            "Read": "படி",
            "Write": "எழுது",
            "Execute": "செயல்படுத்து",
            "Modify": "மாற்று",
            "Access": "அணுகல்",
            "Permission": "அனுமति",
            "Permissions": "அனுமதிகள்",
            "Role": "பங்கு",
            "Roles": "பங்குகள்",
            "User": "பயனர்",
            "Users": "பயனர்கள்",
            "Group": "குழு",
            "Groups": "குழுக்கள்",
            "Team": "அணி",
            "Teams": "அணிகள்",
            "Organization": "அமைப்பு",
            "Organizations": "அமைப்புகள்",
            "Department": "துறை",
            "Departments": "துறைகள்",
            "Branch": "கிளை",
            "Branches": "கிளைகள்",
            "Office": "அலுவலகம்",
            "Offices": "அலுவலகங்கள்",

            // Time and dates
            "Date": "தேதி",
            "Time": "நேரம்",
            "DateTime": "தேதி நேரம்",
            "Created": "உருவாக்கப்பட்டது",
            "Modified": "மாற்றப்பட்டது",
            "Updated": "புதுப்பிக்கப்பட்டது",
            "Deleted": "நீக்கப்பட்டது",
            "Active": "செயலில்",
            "Inactive": "செயலில் இல்லை",
            "Enabled": "இயக்கப்பட்டது",
            "Disabled": "முடக்கப்பட்டது",
            "Available": "கிடைக்கிறது",
            "Unavailable": "கிடைக்கவில்லை",
            "Online": "ஆன்லைன்",
            "Offline": "ஆஃப்லைன்",
            "Connected": "இணைக்கப்பட்டது",
            "Disconnected": "துண்டிக்கப்பட்டது",

            // Numbers and pagination
            "1": "1",
            "2": "2",
            "3": "3",
            "4": "4",
            "5": "5",
            "10": "10",
            "20": "20",
            "50": "50",
            "100": "100",
            "Page Size": "பக்க அளவு",
            "Page Number": "பக்க எண்",
            "Total Pages": "மொத்த பக்கங்கள்",
            "Total Records": "மொத்த பதிவுகள்",
            "Records per page": "ஒரு பக்கத்திற்கு பதிவுகள்",
            "Showing": "காட்டுகிறது",
            "to": "க்கு",
            "of": "இன்",
            "records": "பதிவுகள்"
        };
    }

    getTeluguTranslations() {
        return {
            // Core UI
            "hello world": "హలో వరల్డ్",
            "The title of my app": "నా అప్లికేషన్ టైటిల్",
            "Settings": "సెట్టింగ్‌లు",
            "Today": "ఈరోజు",
            "All": "అన్నీ",
            "New": "కొత్త",
            "Old": "పాత",
            "Overdue": "గడువు దాటిన",
            "Upcoming": "రాబోయే",
            "Completed": "పూర్తయింది",
            "Not Interested": "ఆసక్తి లేదు",
            "Select": "ఎంచుకోండి",
            "Status": "స్థితి",
            "Assigned To": "కు కేటాయించబడింది",
            "For": "కోసం",
            "Reset": "రీసెట్",
            "Go": "వెళ్ళు",
            "No": "లేదు",
            "Yes": "అవును",
            "In": "లో",
            "Lead": "లీడ్",
            "Leads": "లీడ్‌లు",
            "Name": "పేరు",
            "Show": "చూపించు",
            "Entries": "ఎంట్రీలు",
            "Update": "అప్‌డేట్",
            "Details": "వివరాలు",
            "Personal": "వ్యక్తిగత",
            "Work": "పని",
            "Home": "ఇల్లు",
            "Mobile": "మొబైల్",
            "Email": "ఇమెయిల్",
            "Phone": "ఫోన్",
            "Address": "చిరునామా",
            "City": "నగరం",
            "State": "రాష్ట్రం",
            "Country": "దేశం",
            "Pincode": "పిన్‌కోడ్",
            "Save": "సేవ్",
            "Cancel": "రద్దు",
            "Delete": "తొలగించు",
            "Edit": "సవరించు",
            "Add": "జోడించు",
            "Remove": "తొలగించు",
            "Search": "వెతకండి",
            "Filter": "ఫిల్టర్",
            "Sort": "క్రమబద్ధీకరించు",
            "Export": "ఎగుమతి",
            "Import": "దిగుమతి",
            "Print": "ప్రింట్",
            "Download": "డౌన్‌లోడ్",
            "Upload": "అప్‌లోడ్",
            "Submit": "సమర్పించు",
            "Close": "మూసివేయి",
            "Open": "తెరువు",
            "View": "వీక్షణ",
            "Preview": "ప్రివ్యూ",
            "Next": "తదుపరి",
            "Previous": "మునుపటి",
            "First": "మొదటి",
            "Last": "చివరి",
            "Page": "పేజీ",
            "Of": "యొక్క",
            "Total": "మొత్తం",
            "Loading": "లోడవుతోంది",
            "Please wait": "దయచేసి వేచి ఉండండి",
            "Error": "లోపం",
            "Success": "విజయం",
            "Warning": "హెచ్చరిక",
            "Info": "సమాచారం",
            "Confirm": "నిర్ధారించు",
            "Are you sure": "మీరు ఖచ్చితంగా ఉన్నారా",
            "Login": "లాగిన్",
            "Logout": "లాగౌట్",
            "Register": "రిజిస్టర్",
            "Forgot password": "పాస్‌వర్డ్ మర్చిపోయారా",
            "Username": "యూజర్‌నేమ్",
            "Password": "పాస్‌వర్డ్",
            "Remember me": "నన్ను గుర్తుంచుకో",
            "Dashboard": "డాష్‌బోర్డ్",
            "Profile": "ప్రొఫైల్",
            "Account": "ఖాతా",
            "Notifications": "నోటిఫికేషన్‌లు",
            "Messages": "సందేశాలు",
            "Help": "సహాయం",
            "Support": "మద్దతు",
            "Contact": "సంప్రదించండి",
            "About": "గురించి",
            "Privacy": "గోప్యత",
            "Terms": "నిబంధనలు",
            "Language": "భాష",
            "Theme": "థీమ్",
            "Dark": "చీకటి",
            "Light": "వెలుగు",
            "Auto": "ఆటో"
        };
    }

    getBengaliTranslations() {
        return {
            "hello world": "হ্যালো ওয়ার্ল্ড",
            "The title of my app": "আমার অ্যাপের শিরোনাম",
            "Settings": "সেটিংস",
            "Today": "আজ",
            "All": "সব",
            "New": "নতুন",
            "Old": "পুরানো",
            "Save": "সংরক্ষণ",
            "Cancel": "বাতিল",
            "Delete": "মুছুন",
            "Edit": "সম্পাদনা",
            "Add": "যোগ করুন",
            "Search": "অনুসন্ধান",
            "Loading": "লোড হচ্ছে",
            "Error": "ত্রুটি",
            "Success": "সফলতা",
            "Login": "লগইন",
            "Logout": "লগআউট"
        };
    }

    getMalayalamTranslations() {
        return {
            "hello world": "ഹലോ വേൾഡ്",
            "The title of my app": "എന്റെ ആപ്പിന്റെ ശീർഷകം",
            "Settings": "ക്രമീകരണങ്ങൾ",
            "Today": "ഇന്ന്",
            "All": "എല്ലാം",
            "New": "പുതിയ",
            "Old": "പഴയ",
            "Save": "സേവ്",
            "Cancel": "റദ്ദാക്കുക",
            "Delete": "ഇല്ലാതാക്കുക",
            "Edit": "എഡിറ്റ്",
            "Add": "ചേർക്കുക",
            "Search": "തിരയുക",
            "Loading": "ലോഡ് ചെയ്യുന്നു",
            "Error": "പിശക്",
            "Success": "വിജയം",
            "Login": "ലോഗിൻ",
            "Logout": "ലോഗൗട്ട്"
        };
    }

    /**
     * Fast translate - uses predefined translations only, no API calls
     */
    fastTranslate(obj, targetLang) {
        const translations = this.getTranslations(targetLang);

        if (typeof obj === 'string') {
            // Try exact match first
            if (translations[obj]) {
                return translations[obj];
            }

            // Try case-insensitive match
            const lowerKey = obj.toLowerCase();
            if (translations[lowerKey]) {
                return translations[lowerKey];
            }

            // Return original if no translation found
            return obj;
        } else if (Array.isArray(obj)) {
            return obj.map(item => this.fastTranslate(item, targetLang));
        } else if (typeof obj === 'object' && obj !== null) {
            const result = {};
            for (const [key, value] of Object.entries(obj)) {
                result[key] = this.fastTranslate(value, targetLang);
            }
            return result;
        }

        return obj;
    }

    /**
     * Generate language file super fast
     */
    generateLanguage(targetLang) {
        console.log(`🚀 Fast generating ${this.languages[targetLang]} (${targetLang})...`);

        const inputFile = 'src/assets/i18n/en.json';
        const outputFile = `src/assets/i18n/${targetLang}.json`;

        try {
            // Load English file
            const enData = JSON.parse(fs.readFileSync(inputFile, 'utf8'));

            // Fast translate
            const translatedData = this.fastTranslate(enData, targetLang);

            // Save file
            fs.writeFileSync(outputFile, JSON.stringify(translatedData, null, 2), 'utf8');

            console.log(`✅ ${this.languages[targetLang]} completed: ${outputFile}`);
            return true;
        } catch (error) {
            console.error(`❌ Error generating ${this.languages[targetLang]}: ${error.message}`);
            return false;
        }
    }

    /**
     * Generate all languages super fast
     */
    generateAll() {
        console.log('🚀 Fast Multi-Language Generation');
        console.log('=================================\n');

        const results = [];
        const startTime = Date.now();

        for (const [code, name] of Object.entries(this.languages)) {
            const success = this.generateLanguage(code);
            results.push({ code, name, success });
        }

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(1);

        console.log('\n📊 Generation Summary');
        console.log('====================');

        let successful = 0;
        for (const result of results) {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.name} (${result.code})`);
            if (result.success) successful++;
        }

        console.log(`\n⏱️  Total time: ${duration} seconds`);
        console.log(`📈 Success rate: ${successful}/${results.length}`);

        if (successful === results.length) {
            console.log('\n🎉 All languages generated successfully!');
        }

        return successful === results.length;
    }
}

// Main execution
if (require.main === module) {
    const translator = new FastTranslator();

    const args = process.argv.slice(2);

    if (args.length === 0) {
        // Generate all languages
        translator.generateAll();
    } else {
        // Generate specific language
        const targetLang = args[0];
        if (translator.languages[targetLang]) {
            translator.generateLanguage(targetLang);
        } else {
            console.error(`❌ Unsupported language: ${targetLang}`);
            console.log('Supported languages: ta, te, bn, ml');
        }
    }
}

module.exports = FastTranslator;