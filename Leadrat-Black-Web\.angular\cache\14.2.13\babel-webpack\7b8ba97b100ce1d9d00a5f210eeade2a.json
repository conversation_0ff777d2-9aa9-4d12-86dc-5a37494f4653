{"ast": null, "code": "import metaMap from './_metaMap.js';\nimport noop from './noop.js';\n/**\n * Gets metadata for `func`.\n *\n * @private\n * @param {Function} func The function to query.\n * @returns {*} Returns the metadata for `func`.\n */\n\nvar getData = !metaMap ? noop : function (func) {\n  return metaMap.get(func);\n};\nexport default getData;", "map": null, "metadata": {}, "sourceType": "module"}