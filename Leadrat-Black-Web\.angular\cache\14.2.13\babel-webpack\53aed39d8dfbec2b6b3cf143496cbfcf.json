{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js';\nimport ParseError from './ParseError.js';\nimport { isSupportedCountry } from './metadata.js';\nexport default function parsePhoneNumber(text, options, metadata) {\n  // Validate `defaultCountry`.\n  if (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\n    options = _objectSpread(_objectSpread({}, options), {}, {\n      defaultCountry: undefined\n    });\n  } // Parse phone number.\n\n\n  try {\n    return parsePhoneNumberWithError(text, options, metadata);\n  } catch (error) {\n    /* istanbul ignore else */\n    if (error instanceof ParseError) {//\n    } else {\n      throw error;\n    }\n  }\n} //# sourceMappingURL=parsePhoneNumber_.js.map", "map": null, "metadata": {}, "sourceType": "module"}