{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function exhaust() {\n  return source => source.lift(new SwitchFirstOperator());\n}\n\nclass SwitchFirstOperator {\n  call(subscriber, source) {\n    return source.subscribe(new SwitchFirstSubscriber(subscriber));\n  }\n\n}\n\nclass SwitchFirstSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.hasCompleted = false;\n    this.hasSubscription = false;\n  }\n\n  _next(value) {\n    if (!this.hasSubscription) {\n      this.hasSubscription = true;\n      this.add(innerSubscribe(value, new SimpleInnerSubscriber(this)));\n    }\n  }\n\n  _complete() {\n    this.hasCompleted = true;\n\n    if (!this.hasSubscription) {\n      this.destination.complete();\n    }\n  }\n\n  notifyComplete() {\n    this.hasSubscription = false;\n\n    if (this.hasCompleted) {\n      this.destination.complete();\n    }\n  }\n\n} //# sourceMappingURL=exhaust.js.map", "map": null, "metadata": {}, "sourceType": "module"}