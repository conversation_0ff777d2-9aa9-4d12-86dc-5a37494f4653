{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function every(predicate, thisArg) {\n  return source => source.lift(new EveryOperator(predicate, thisArg, source));\n}\n\nclass EveryOperator {\n  constructor(predicate, thisArg, source) {\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n  }\n\n  call(observer, source) {\n    return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));\n  }\n\n}\n\nclass EverySubscriber extends Subscriber {\n  constructor(destination, predicate, thisArg, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n    this.index = 0;\n    this.thisArg = thisArg || this;\n  }\n\n  notifyComplete(everyValueMatch) {\n    this.destination.next(everyValueMatch);\n    this.destination.complete();\n  }\n\n  _next(value) {\n    let result = false;\n\n    try {\n      result = this.predicate.call(this.thisArg, value, this.index++, this.source);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    if (!result) {\n      this.notifyComplete(false);\n    }\n  }\n\n  _complete() {\n    this.notifyComplete(true);\n  }\n\n} //# sourceMappingURL=every.js.map", "map": null, "metadata": {}, "sourceType": "module"}