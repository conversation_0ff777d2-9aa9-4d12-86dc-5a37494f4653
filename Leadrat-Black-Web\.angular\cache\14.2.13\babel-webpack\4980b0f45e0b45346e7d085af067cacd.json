{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { Platform } from \"./Utils\";\n/** @private */\n\nexport function configureFetch(obj) {\n  // Node added a fetch implementation to the global scope starting in v18.\n  // We need to add a cookie jar in node to be able to share cookies with WebSocket\n  if (typeof fetch === \"undefined\" || Platform.isNode) {\n    // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    obj._jar = new (require(\"tough-cookie\").CookieJar)();\n\n    if (typeof fetch === \"undefined\") {\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      obj._fetchType = require(\"node-fetch\");\n    } else {\n      // Use fetch from Node if available\n      obj._fetchType = fetch;\n    } // node-fetch doesn't have a nice API for getting and setting cookies\n    // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n\n\n    obj._fetchType = require(\"fetch-cookie\")(obj._fetchType, obj._jar);\n    return true;\n  }\n\n  return false;\n}\n/** @private */\n\nexport function configureAbortController(obj) {\n  if (typeof AbortController === \"undefined\") {\n    // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\n    obj._abortControllerType = require(\"abort-controller\");\n    return true;\n  }\n\n  return false;\n}\n/** @private */\n\nexport function getWS() {\n  return require(\"ws\");\n}\n/** @private */\n\nexport function getEventSource() {\n  return require(\"eventsource\");\n} //# sourceMappingURL=DynamicImports.js.map", "map": null, "metadata": {}, "sourceType": "module"}