{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function refCount() {\n  return function refCountOperatorFunction(source) {\n    return source.lift(new RefCountOperator(source));\n  };\n}\n\nclass RefCountOperator {\n  constructor(connectable) {\n    this.connectable = connectable;\n  }\n\n  call(subscriber, source) {\n    const {\n      connectable\n    } = this;\n    connectable._refCount++;\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      refCounter.connection = connectable.connect();\n    }\n\n    return subscription;\n  }\n\n}\n\nclass RefCountSubscriber extends Subscriber {\n  constructor(destination, connectable) {\n    super(destination);\n    this.connectable = connectable;\n  }\n\n  _unsubscribe() {\n    const {\n      connectable\n    } = this;\n\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n\n    this.connectable = null;\n    const refCount = connectable._refCount;\n\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n\n    connectable._refCount = refCount - 1;\n\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n\n    const {\n      connection\n    } = this;\n    const sharedConnection = connectable._connection;\n    this.connection = null;\n\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n\n} //# sourceMappingURL=refCount.js.map", "map": null, "metadata": {}, "sourceType": "module"}