{"ast": null, "code": "// When phone numbers are written in `RFC3966` format — `\"tel:+12133734253\"` —\n// they can have their \"calling code\" part written separately in a `phone-context` parameter.\n// Example: `\"tel:12133734253;phone-context=+1\"`.\n// This function parses the full phone number from the local number and the `phone-context`\n// when the `phone-context` contains a `+` sign.\nimport { VALID_DIGITS // PLUS_CHARS\n} from '../constants.js';\nexport var PLUS_SIGN = '+';\nvar RFC3966_VISUAL_SEPARATOR_ = '[\\\\-\\\\.\\\\(\\\\)]?';\nvar RFC3966_PHONE_DIGIT_ = '(' + '[' + VALID_DIGITS + ']' + '|' + RFC3966_VISUAL_SEPARATOR_ + ')';\nvar RFC3966_GLOBAL_NUMBER_DIGITS_ = '^' + '\\\\' + PLUS_SIGN + RFC3966_PHONE_DIGIT_ + '*' + '[' + VALID_DIGITS + ']' + RFC3966_PHONE_DIGIT_ + '*' + '$';\n/**\r\n * Regular expression of valid global-number-digits for the phone-context\r\n * parameter, following the syntax defined in RFC3966.\r\n */\n\nvar RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_ = new RegExp(RFC3966_GLOBAL_NUMBER_DIGITS_, 'g'); // In this port of Google's library, we don't accept alpha characters in phone numbers.\n// const ALPHANUM_ = VALID_ALPHA_ + VALID_DIGITS\n\nvar ALPHANUM_ = VALID_DIGITS;\nvar RFC3966_DOMAINLABEL_ = '[' + ALPHANUM_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*';\nvar VALID_ALPHA_ = 'a-zA-Z';\nvar RFC3966_TOPLABEL_ = '[' + VALID_ALPHA_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*';\nvar RFC3966_DOMAINNAME_ = '^(' + RFC3966_DOMAINLABEL_ + '\\\\.)*' + RFC3966_TOPLABEL_ + '\\\\.?$';\n/**\r\n * Regular expression of valid domainname for the phone-context parameter,\r\n * following the syntax defined in RFC3966.\r\n */\n\nvar RFC3966_DOMAINNAME_PATTERN_ = new RegExp(RFC3966_DOMAINNAME_, 'g');\nexport var RFC3966_PREFIX_ = 'tel:';\nexport var RFC3966_PHONE_CONTEXT_ = ';phone-context=';\nexport var RFC3966_ISDN_SUBADDRESS_ = ';isub=';\n/**\r\n * Extracts the value of the phone-context parameter of `numberToExtractFrom`,\r\n * following the syntax defined in RFC3966.\r\n *\r\n * @param {string} numberToExtractFrom\r\n * @return {string|null} the extracted string (possibly empty), or `null` if no phone-context parameter is found.\r\n */\n\nexport default function extractPhoneContext(numberToExtractFrom) {\n  var indexOfPhoneContext = numberToExtractFrom.indexOf(RFC3966_PHONE_CONTEXT_); // If no phone-context parameter is present\n\n  if (indexOfPhoneContext < 0) {\n    return null;\n  }\n\n  var phoneContextStart = indexOfPhoneContext + RFC3966_PHONE_CONTEXT_.length; // If phone-context parameter is empty\n\n  if (phoneContextStart >= numberToExtractFrom.length) {\n    return '';\n  }\n\n  var phoneContextEnd = numberToExtractFrom.indexOf(';', phoneContextStart); // If phone-context is not the last parameter\n\n  if (phoneContextEnd >= 0) {\n    return numberToExtractFrom.substring(phoneContextStart, phoneContextEnd);\n  } else {\n    return numberToExtractFrom.substring(phoneContextStart);\n  }\n}\n/**\r\n * Returns whether the value of phoneContext follows the syntax defined in RFC3966.\r\n *\r\n * @param {string|null} phoneContext\r\n * @return {boolean}\r\n */\n\nexport function isPhoneContextValid(phoneContext) {\n  if (phoneContext === null) {\n    return true;\n  }\n\n  if (phoneContext.length === 0) {\n    return false;\n  } // Does phone-context value match pattern of global-number-digits or domainname.\n\n\n  return RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_.test(phoneContext) || RFC3966_DOMAINNAME_PATTERN_.test(phoneContext);\n} //# sourceMappingURL=extractPhoneContext.js.map", "map": null, "metadata": {}, "sourceType": "module"}