{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let config;\n\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    config = configOrBufferSize;\n  } else {\n    config = {\n      bufferSize: configOrBufferSize,\n      windowTime,\n      refCount: false,\n      scheduler\n    };\n  }\n\n  return source => source.lift(shareReplayOperator(config));\n}\n\nfunction shareReplayOperator({\n  bufferSize = Number.POSITIVE_INFINITY,\n  windowTime = Number.POSITIVE_INFINITY,\n  refCount: useRefCount,\n  scheduler\n}) {\n  let subject;\n  let refCount = 0;\n  let subscription;\n  let hasError = false;\n  let isComplete = false;\n  return function shareReplayOperation(source) {\n    refCount++;\n    let innerSub;\n\n    if (!subject || hasError) {\n      hasError = false;\n      subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n      innerSub = subject.subscribe(this);\n      subscription = source.subscribe({\n        next(value) {\n          subject.next(value);\n        },\n\n        error(err) {\n          hasError = true;\n          subject.error(err);\n        },\n\n        complete() {\n          isComplete = true;\n          subscription = undefined;\n          subject.complete();\n        }\n\n      });\n\n      if (isComplete) {\n        subscription = undefined;\n      }\n    } else {\n      innerSub = subject.subscribe(this);\n    }\n\n    this.add(() => {\n      refCount--;\n      innerSub.unsubscribe();\n      innerSub = undefined;\n\n      if (subscription && !isComplete && useRefCount && refCount === 0) {\n        subscription.unsubscribe();\n        subscription = undefined;\n        subject = undefined;\n      }\n    });\n  };\n} //# sourceMappingURL=shareReplay.js.map", "map": null, "metadata": {}, "sourceType": "module"}