{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\n\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = typeof Ctor == 'function' && Ctor.prototype || objectProto;\n  return value === proto;\n}\n\nexport default isPrototype;", "map": null, "metadata": {}, "sourceType": "module"}