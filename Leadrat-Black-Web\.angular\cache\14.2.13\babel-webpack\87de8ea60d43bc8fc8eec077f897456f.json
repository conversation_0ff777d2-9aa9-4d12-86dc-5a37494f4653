{"ast": null, "code": "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n} // https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\n\n\nvar Node = /*#__PURE__*/_createClass(function Node(key, value) {\n  var next = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var prev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  _classCallCheck(this, Node);\n\n  this.key = key;\n  this.value = value;\n  this.next = next;\n  this.prev = prev;\n});\n\nvar LRUCache = /*#__PURE__*/function () {\n  //set default limit of 10 if limit is not passed.\n  function LRUCache() {\n    var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n\n    _classCallCheck(this, LRUCache);\n\n    this.size = 0;\n    this.limit = limit;\n    this.head = null;\n    this.tail = null;\n    this.cache = {};\n  } // Write Node to head of LinkedList\n  // update cache with Node key and Node reference\n\n\n  _createClass(LRUCache, [{\n    key: \"put\",\n    value: function put(key, value) {\n      this.ensureLimit();\n\n      if (!this.head) {\n        this.head = this.tail = new Node(key, value);\n      } else {\n        var node = new Node(key, value, this.head);\n        this.head.prev = node;\n        this.head = node;\n      } //Update the cache map\n\n\n      this.cache[key] = this.head;\n      this.size++;\n    } // Read from cache map and make that node as new Head of LinkedList\n\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      if (this.cache[key]) {\n        var value = this.cache[key].value; // node removed from it's position and cache\n\n        this.remove(key); // write node again to the head of LinkedList to make it most recently used\n\n        this.put(key, value);\n        return value;\n      }\n\n      console.log(\"Item not available in cache for key \".concat(key));\n    }\n  }, {\n    key: \"ensureLimit\",\n    value: function ensureLimit() {\n      if (this.size === this.limit) {\n        this.remove(this.tail.key);\n      }\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(key) {\n      var node = this.cache[key];\n\n      if (node.prev !== null) {\n        node.prev.next = node.next;\n      } else {\n        this.head = node.next;\n      }\n\n      if (node.next !== null) {\n        node.next.prev = node.prev;\n      } else {\n        this.tail = node.prev;\n      }\n\n      delete this.cache[key];\n      this.size--;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = null;\n      this.tail = null;\n      this.size = 0;\n      this.cache = {};\n    } // // Invokes the callback function with every node of the chain and the index of the node.\n    // forEach(fn) {\n    //   let node = this.head;\n    //   let counter = 0;\n    //   while (node) {\n    //     fn(node, counter);\n    //     node = node.next;\n    //     counter++;\n    //   }\n    // }\n    // // To iterate over LRU with a 'for...of' loop\n    // *[Symbol.iterator]() {\n    //   let node = this.head;\n    //   while (node) {\n    //     yield node;\n    //     node = node.next;\n    //   }\n    // }\n\n  }]);\n\n  return LRUCache;\n}();\n\nexport { LRUCache as default }; //# sourceMappingURL=LRUCache.js.map", "map": null, "metadata": {}, "sourceType": "module"}