"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[618],{97618:(ee,F,l)=>{l.r(F),l.d(F,{LoginModule:()=>q});var c=l(69808),T=l(23713),B=l(40520),s=l(93075),m=l(18995),g=l(71511),e=l(5e3),_=l(82722),u=l(61021),h=l(47507),E=l(69131),f=l(65620),S=l(1880),P=l(46302);const I=function(i){return{"pe-none disabled":i}};function b(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",1)(2,"div")(3,"h4",2),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",3),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",4),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",5),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"form-errors-wrapper")(15,"input",6),e.\u0275\u0275listener("keyup.enter",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275reference(18);return e.\u0275\u0275resetView(o.click())})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.userName=o)}),e.\u0275\u0275pipe(16,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"h4",7,8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.verifyUsername(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.ForgotPasswordSubmit.Click"))}),e.\u0275\u0275text(19),e.\u0275\u0275pipe(20,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"div",9)(22,"div",10),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.navigateToLogin(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.GoBackToLogin.Click"))}),e.\u0275\u0275element(23,"span",11),e.\u0275\u0275elementStart(24,"h5",12),e.\u0275\u0275text(25),e.\u0275\u0275pipe(26,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(5,9,"AUTH.forgot-password"),"?"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,11,"AUTH.password-reset")),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(10,13,"GLOBAL.laptop")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(13,15,"AUTH.user-name")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(16,17,"GLOBAL.enter-your-username")),e.\u0275\u0275property("ngModel",t.userName),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(23,I,!t.userName)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(20,19,"BUTTONS.submit")," "),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(26,21,"AUTH.go-back-login"))}}function M(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",13)(2,"div")(3,"h4",14),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(6,"img",15),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div")(9,"h5",16),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"div",17)(13,"div"),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275pipe(16,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"p",18),e.\u0275\u0275text(18),e.\u0275\u0275pipe(19,"translate"),e.\u0275\u0275pipe(20,"translate"),e.\u0275\u0275pipe(21,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"div"),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"slice"),e.\u0275\u0275pipe(25,"slice"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"div",19)(27,"div"),e.\u0275\u0275text(28),e.\u0275\u0275pipe(29,"translate"),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"p",18),e.\u0275\u0275text(32),e.\u0275\u0275pipe(33,"translate"),e.\u0275\u0275pipe(34,"translate"),e.\u0275\u0275pipe(35,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(36,"div"),e.\u0275\u0275text(37),e.\u0275\u0275pipe(38,"slice"),e.\u0275\u0275pipe(39,"slice"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(40,"h4",20),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.getOtp(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.GetOTP.Click"))}),e.\u0275\u0275text(41),e.\u0275\u0275pipe(42,"translate"),e.\u0275\u0275pipe(43,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(5,19,"AUTH.recover-account")),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(7,21,"GLOBAL.laptop")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(11,23,"AUTH.recovery-options"),":"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(15,25,"GLOBAL.your")," ",e.\u0275\u0275pipeBind1(16,27,"SHARE.email"),""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate3("",e.\u0275\u0275pipeBind1(19,29,"AUTH.otp-to")," ",e.\u0275\u0275pipeBind1(20,31,"GLOBAL.your")," ",e.\u0275\u0275pipeBind1(21,33,"SHARE.email"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind3(24,35,null==t.userData?null:t.userData.email,0,1),"******",e.\u0275\u0275pipeBind2(25,39,null==t.userData?null:t.userData.email,null==t.userData?null:t.userData.email.indexOf("@")),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(29,42,"GLOBAL.your")," ",e.\u0275\u0275pipeBind1(30,44,"USER.phone-number"),""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate3("",e.\u0275\u0275pipeBind1(33,46,"AUTH.otp-to")," ",e.\u0275\u0275pipeBind1(34,48,"GLOBAL.your")," ",e.\u0275\u0275pipeBind1(35,50,"USER.phone-number"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate2(" ",e.\u0275\u0275pipeBind3(38,52,null==t.userData?null:t.userData.phoneNumber,0,4),"*******",e.\u0275\u0275pipeBind2(39,56,null==t.userData?null:t.userData.phoneNumber,(null==t.userData?null:t.userData.phoneNumber.length)-2)," "),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate2(" ",e.\u0275\u0275pipeBind1(42,59,"AUTH.get")," ",e.\u0275\u0275pipeBind1(43,61,"LEAD_FORM.otp"),"")}}function A(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1("00:",t.formattedSeconds,"")}}function V(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",13)(2,"div")(3,"h4",2),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"h6",21),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementStart(9,"div",22),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(14,"img",23),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"div",24)(17,"input",25),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"digit-2"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[0]=o)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"input",26),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"digit-3","digit-1"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[1]=o)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(19,"input",27),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"digit-4","digit-2"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[2]=o)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"div"),e.\u0275\u0275text(21,"-"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"input",28),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"digit-5","digit-3"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[3]=o)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"input",29),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"digit-6","digit-4"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[4]=o)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"input",30),e.\u0275\u0275listener("keyup",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otpClickEvent(o,"","digit-5"))})("ngModelChange",function(o){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.otp[5]=o)})("keyup.enter",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275reference(37);return e.\u0275\u0275resetView(o.click())}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(25,"div",31)(26,"div",32),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"translate"),e.\u0275\u0275pipe(29,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(30,"h5",33),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.resendOtp(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.ResendOTP.Click"))}),e.\u0275\u0275elementStart(31,"u"),e.\u0275\u0275text(32),e.\u0275\u0275pipe(33,"translate"),e.\u0275\u0275pipe(34,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(35,A,2,1,"div",0),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(36,"h4",34,8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.verifyOtp(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.SubmitOTP.Click"))}),e.\u0275\u0275text(38),e.\u0275\u0275pipe(39,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(5,18,"AUTH.enter-otp")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(8,20,"AUTH.we-sent-an")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(11,22,"LEAD_FORM.otp")," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(13,24,"AUTH.to-email-and-phone"),""),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(15,26,"GLOBAL.otp")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",t.otp[0]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.otp[1]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.otp[2]),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",t.otp[3]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.otp[4]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.otp[5]),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(28,28,"AUTH.not-receive")," ",e.\u0275\u0275pipeBind1(29,30,"LEAD_FORM.otp"),"?"),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",t.isExpired?"text-accent-green cursor-pointer":"text-gray-110 pe-none"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(33,32,"AUTH.resend")," ",e.\u0275\u0275pipeBind1(34,34,"LEAD_FORM.otp"),""),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!t.isExpired),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(39,36,"BUTTONS.submit"))}}function O(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div",46),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",null==t.resetPasswordForm||null==t.resetPasswordForm.errors?null:t.resetPasswordForm.errors.match_error,"")}}function U(i,p){1&i&&(e.\u0275\u0275elementStart(0,"div",47),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",e.\u0275\u0275pipeBind1(2,2,"AUTH.password")," ",e.\u0275\u0275pipeBind1(3,4,"AUTH.matches"),""))}function D(i,p){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",1)(2,"div")(3,"h4",14),e.\u0275\u0275text(4),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",35),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(9,"img",4),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"form",36)(12,"div",37),e.\u0275\u0275text(13),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"form-errors-wrapper",38),e.\u0275\u0275element(17,"input",39),e.\u0275\u0275pipe(18,"translate"),e.\u0275\u0275elementStart(19,"a",40),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.showNewPassword=!o.showNewPassword)}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(20,"div",37),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"form-errors-wrapper",41)(24,"input",42),e.\u0275\u0275listener("keyup.enter",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275reference(30);return e.\u0275\u0275resetView(o.click())}),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"a",40),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(o.showConfirmPassword=!o.showConfirmPassword)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(27,O,2,1,"div",43),e.\u0275\u0275template(28,U,4,6,"div",44),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(29,"h4",45,8),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const o=e.\u0275\u0275nextContext();return o.resetPassword(),e.\u0275\u0275resetView(null==o.trackingService?null:o.trackingService.trackFeature("Web.Login.Button.ChangePassword.Click"))}),e.\u0275\u0275text(31),e.\u0275\u0275pipe(32,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(5,18,"AUTH.change-password")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,20,"AUTH.enter-new-password")),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(10,22,"GLOBAL.otp")),e.\u0275\u0275advance(2),e.\u0275\u0275property("formGroup",t.resetPasswordForm),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(14,24,"GLOBAL.new")," ",e.\u0275\u0275pipeBind1(15,26,"AUTH.password"),""),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",null==t.resetPasswordForm?null:t.resetPasswordForm.controls.newPassword),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(18,28,"GLOBAL.enter-the-new-password")),e.\u0275\u0275property("type",t.showNewPassword?"text":"password"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",t.showNewPassword?"ic-eye-slash":"ic-eye-solid"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(22,30,"AUTH.confirm-password")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",null==t.resetPasswordForm?null:t.resetPasswordForm.controls.confirmPassword),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(25,32,"GLOBAL.reenter-the-new-password")),e.\u0275\u0275property("type",t.showConfirmPassword?"text":"password"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",t.showConfirmPassword?"ic-eye-slash":"ic-eye-solid"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","VALID"===(null==t.resetPasswordForm||null==t.resetPasswordForm.controls||null==t.resetPasswordForm.controls.confirmPassword?null:t.resetPasswordForm.controls.confirmPassword.status)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.resetPasswordForm?null:t.resetPasswordForm.valid),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(32,34,"USER_MANAGEMENT.change-password"),"")}}let N=(()=>{class i{constructor(t,n,o,r){var a,d,x,C,y;this.router=t,this._store=n,this.fb=o,this.trackingService=r,this.stopper=new e.EventEmitter,this.showOtp=!1,this.showAccount=!1,this.showForgetPassword=!0,this.otpVerified=!1,this.userName="",this.isShowPassword=!1,this.otp=[null,null,null,null,null,null],this.validateUserName=!1,this.showNewPassword=!1,this.shoConfirmPassword=!1,this.isExpired=!1,this.seconds=30,this.building={path:"assets/animations/building.json"},this.online={path:"assets/animations/circle-green.json"},this.frontLaptop={path:"assets/animations/front-laptop.json"},this.formattedSeconds="30";const k=this.router.getCurrentNavigation();this.userName=null===(a=null==k?void 0:k.extras.state)||void 0===a?void 0:a.userName,this.resetPasswordForm=this.fb.group({newPassword:["",[s.kI.required]],confirmPassword:["",s.kI.required]}),this.showForgetPassword?null===(d=this.trackingService)||void 0===d||d.trackFeature("Web.Login.Page.ForgotPassword.Visit"):this.showAccount&&this.userData?null===(x=this.trackingService)||void 0===x||x.trackFeature("Web.Login.Page.ConfirmDetails.Visit"):this.showOtp?null===(C=this.trackingService)||void 0===C||C.trackFeature("Web.Login.Page.EnterOTP.Visit"):this.otpVerified&&(null===(y=this.trackingService)||void 0===y||y.trackFeature("Web.Login.Page.ChangePassword.Visit")),this.resetPasswordForm.addValidators((0,u.tc)(this.resetPasswordForm.get("newPassword"),this.resetPasswordForm.get("confirmPassword")))}navigateToLogin(){this.router.navigate(["login"])}otpClickEvent(t,n,o){var r,a;"Backspace"===t.key?(document.getElementById(o).innerHTML=null,null===(r=document.getElementById(o))||void 0===r||r.focus()):(document.getElementById(n).innerHTML=null,null===(a=document.getElementById(n))||void 0===a||a.focus())}verifyUsername(){this._store.dispatch(new h.Ms(this.userName)),this._store.select(E.PR).pipe((0,_.R)(this.stopper)).subscribe(t=>{var n;this.userData=t,null!==(n=this.userData)&&void 0!==n&&n.id&&(this.showAccount=!0,this.showForgetPassword=!1)})}getOtp(){var t,n,o,r;let a={userId:null===(t=this.userData)||void 0===t?void 0:t.id,phoneNumber:null===(n=this.userData)||void 0===n?void 0:n.phoneNumber,email:null===(o=this.userData)||void 0===o?void 0:o.email};this._store.dispatch(new h.zW(a)),this.startTimer(),this.showOtp=!0,this.showAccount=!1,null===(r=document.getElementById("digit-1"))||void 0===r||r.focus()}startTimer(){const t=setInterval(()=>{0===this.seconds?(this.isExpired=!0,clearInterval(t)):(this.formattedSeconds=this.seconds.toString().padStart(2,"0"),this.seconds--)},1e3)}resendOtp(){var t,n,o;let r={userId:null===(t=this.userData)||void 0===t?void 0:t.id,phoneNumber:null===(n=this.userData)||void 0===n?void 0:n.phoneNumber,email:null===(o=this.userData)||void 0===o?void 0:o.email};this.startTimer(),this.isExpired=!1,this.seconds=30,this._store.dispatch(new h.zW(r))}verifyOtp(){var t,n;let o="";null===(t=this.otp)||void 0===t||t.forEach(a=>{o+=a});let r={userId:null===(n=this.userData)||void 0===n?void 0:n.id,otp:o};this._store.dispatch(new h.c_(r)),this._store.select(E.ve).pipe((0,_.R)(this.stopper)).subscribe(a=>{var d;this.otpVerified=a,a?this.showOtp=!1:null===(d=document.getElementById("digit-1"))||void 0===d||d.focus()})}resetPassword(){var t,n;if(!this.resetPasswordForm.valid)return void(0,u._5)(this.resetPasswordForm);let o="";null===(t=this.otp)||void 0===t||t.forEach(a=>{o+=a});let r={userId:null===(n=this.userData)||void 0===n?void 0:n.id,password:this.resetPasswordForm.value.newPassword,otp:o};this._store.dispatch(new h.tq(r))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(g.F0),e.\u0275\u0275directiveInject(f.yh),e.\u0275\u0275directiveInject(s.qu),e.\u0275\u0275directiveInject(S.e))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["login-forget-password"]],decls:4,vars:4,consts:[[4,"ngIf"],[1,"flex-between","mt-30"],[1,"fw-600"],[1,"mt-4"],["src","../../../../assets/images/side-laptop.svg",3,"alt"],[1,"field-label"],["type","text","id","inpLoginUsername","data-automate-id","inpLoginUsername",3,"placeholder","ngModel","keyup.enter","ngModelChange"],[1,"btn-accent-green-xl","mt-20",3,"ngClass","click"],["focusableLink",""],[1,"justify-end"],[1,"flex-end","mt-10","cursor-pointer",3,"click"],[1,"icon","ic-sm","ic-arrow-left","ic-dark"],[1,"fw-semi-bold","ml-4"],[1,"flex-between","my-30"],[1,"fw-600","w-150"],["src","../../../../assets/images/laptop-hand-wave.svg","width","75","height","70",3,"alt"],[1,"fw-600","text-center"],[1,"bg-light-pearl","p-16","mt-20","br-6","bg-mail","fw-700"],[1,"fw-semi-bold","mb-16","text-sm"],[1,"bg-light-pearl","p-16","mt-20","br-6","bg-phone","fw-700"],[1,"btn-accent-green-xl","mt-40",3,"click"],[1,"w-170"],[1,"fw-700","d-inline"],["src","../../../../assets/images/message-otp.svg",3,"alt"],[1,"gap-3","align-center"],["type","text","id","digit-1","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-2","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-3","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-4","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-5","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-6","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange","keyup.enter"],[1,"d-flex","mt-30","gap-2"],[1,"fw-semi-bold"],[1,"fw-semi-bold",3,"ngClass","click"],[1,"btn-accent-green-xl","mt-50",3,"click"],[1,"w-160","mt-160"],[3,"formGroup"],[1,"field-label-req"],["label","New Password",1,"text-large",3,"control"],["formControlName","newPassword","minlength","5",3,"type","placeholder"],[1,"icon","ic-gray","cursor-pointer","position-absolute","top-10","right-10",3,"ngClass","click"],["label","Confirm Password","text-large","",3,"control"],["minlength","5","formControlName","confirmPassword",3,"type","placeholder","keyup.enter"],["class","error-message mt-5",4,"ngIf"],["class","error-message text-accent-green mt-5",4,"ngIf"],[1,"mt-40","btn-accent-green-xl",3,"click"],[1,"error-message","mt-5"],[1,"error-message","text-accent-green","mt-5"]],template:function(t,n){1&t&&(e.\u0275\u0275template(0,b,27,25,"ng-container",0),e.\u0275\u0275template(1,M,44,63,"ng-container",0),e.\u0275\u0275template(2,V,40,38,"ng-container",0),e.\u0275\u0275template(3,D,33,36,"ng-container",0)),2&t&&(e.\u0275\u0275property("ngIf",n.showForgetPassword),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.showAccount&&n.userData),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.showOtp),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.otpVerified))},dependencies:[c.mk,c.O5,P.z,s._Y,s.Fj,s.JJ,s.JL,s.wO,s.nD,s.sg,s.u,s.On,c.OU,m.X$],encapsulation:2}),i})();var H=l(2976),v=l(66841),w=l(30242),j=l(22313);function G(i,p){1&i&&(e.\u0275\u0275elementStart(0,"span"),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"AUTH.login")))}function R(i,p){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275element(1,"div",24),e.\u0275\u0275elementContainerEnd())}const W=function(){return[1,2,3]};function J(i,p){1&i&&(e.\u0275\u0275elementStart(0,"div",22),e.\u0275\u0275template(1,R,2,0,"ng-container",23),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",e.\u0275\u0275pureFunction0(1,W)))}let z=(()=>{class i{constructor(t,n,o,r,a){var d;this.fb=t,this.store=n,this.metaTitle=o,this.router=r,this.trackingService=a,this.supportNumber=H.SR4,this.subDomain=(0,u.u9)(),this.isShowLogin=!0,this.isShowSupport=!1,this.isShowPassword=!1,this.isLoginLoading=!1,this.currentYear=(new Date).getFullYear(),this.getEnvDetails=u.Yr,this.building={path:"assets/animations/building.json"},this.online={path:"assets/animations/circle-green.json"},this.frontLaptop={path:"assets/animations/front-laptop.json"},this.metaTitle.setTitle("CRM | Login"),this.loginForm=this.fb.group({username:["",s.kI.required],password:["",s.kI.required]}),null===(d=this.trackingService)||void 0===d||d.trackFeature("Web.Login.Page.Login.Visit")}login(){if(!this.loginForm.valid)return void(0,u._5)(this.loginForm);const t=Object.assign({},this.loginForm.value);this.isLoginLoading=!0,this.store.dispatch(new v.yp({})),this.store.dispatch(new v.m3(t)),this.store.select(w.Po).subscribe(n=>{n||(this.isLoginLoading=n)}),this.store.select(w.t2).subscribe(n=>{!(0,u.Qr)(n)&&(null==n||!n.ok)&&(this.isLoginLoading=!1)})}navigateToForgotPassword(){var t;this.router.navigate(["login/forgot-password"],{state:{userName:null===(t=this.loginForm.value)||void 0===t?void 0:t.username}})}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(s.qu),e.\u0275\u0275directiveInject(f.yh),e.\u0275\u0275directiveInject(j.Dx),e.\u0275\u0275directiveInject(g.F0),e.\u0275\u0275directiveInject(S.e))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["login-form"]],decls:42,vars:37,consts:[[1,"flex-between"],[1,"fw-600"],[1,"mt-4"],["width","110px",3,"options"],[1,"field-label"],[1,"border-gray","bg-light-slate","br-5","w-100","align-center","text-coal"],[1,"w-70","p-12","ph-w-60"],[1,"border-right","h-40"],[1,"w-30","p-12","ph-w-40"],[3,"formGroup"],[1,"field-label-req"],["label","Username",3,"control"],["type","text","id","inpLoginName","data-automate-id","inpLoginName","formControlName","username","required","",3,"placeholder","keyup.enter"],["label","Password",3,"control"],["id","inpLoginPassword","data-automate-id","inpLoginPassword","formControlName","password","required","",3,"type","placeholder","keyup.enter"],["id","passwordhide","data-automate-id","passwordhide",1,"icon","ic-gray","cursor-pointer","position-absolute","top-10","right-10",3,"ngClass","click"],[1,"btn-accent-green-xl","mt-20",3,"click"],["focusableLogin",""],[4,"ngIf","ngIfElse"],[1,"justify-end"],[1,"fw-semi-bold","mt-10","cursor-pointer",3,"click"],["buttonDots",""],[1,"container","flex-center","py-8"],[4,"ngFor","ngForOf"],[1,"dot-falling","dot-white"]],template:function(t,n){if(1&t){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",0)(1,"div")(2,"h4",1),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",2),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(8,"ng-lottie",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",4),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(13,"div",5)(14,"div",6),e.\u0275\u0275text(15),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(16,"div",7),e.\u0275\u0275elementStart(17,"div",8),e.\u0275\u0275text(18),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(19,"form",9)(20,"div",10),e.\u0275\u0275text(21),e.\u0275\u0275pipe(22,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(23,"form-errors-wrapper",11)(24,"input",12),e.\u0275\u0275listener("keyup.enter",function(){e.\u0275\u0275restoreView(o);const a=e.\u0275\u0275reference(34);return e.\u0275\u0275resetView(a.click())}),e.\u0275\u0275pipe(25,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(26,"div",10),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(29,"form-errors-wrapper",13)(30,"input",14),e.\u0275\u0275listener("keyup.enter",function(){e.\u0275\u0275restoreView(o);const a=e.\u0275\u0275reference(34);return e.\u0275\u0275resetView(a.click())}),e.\u0275\u0275pipe(31,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"a",15),e.\u0275\u0275listener("click",function(){return n.isShowPassword=!n.isShowPassword}),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(33,"h4",16,17),e.\u0275\u0275listener("click",function(){return n.isLoginLoading||n.login(),n.trackingService.trackFeature("Web.Login.Button.Login.Click")}),e.\u0275\u0275template(35,G,3,3,"span",18),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(36,"div",19)(37,"h5",20),e.\u0275\u0275listener("click",function(){return n.navigateToForgotPassword(),n.trackingService.trackFeature("Web.Login.Button.ForgotPassword.Click")}),e.\u0275\u0275text(38),e.\u0275\u0275pipe(39,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(40,J,2,2,"ng-template",null,21,e.\u0275\u0275templateRefExtractor)}if(2&t){const o=e.\u0275\u0275reference(41);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,19,"AUTH.login")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(7,21,"AUTH.username-password")),e.\u0275\u0275advance(2),e.\u0275\u0275property("options",n.frontLaptop),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(11,23,"AUTH.subdomain")," ",e.\u0275\u0275pipeBind1(12,25,"PROFILE.name"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(n.subDomain),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.getEnvDetails()),e.\u0275\u0275advance(1),e.\u0275\u0275property("formGroup",n.loginForm),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(22,27,"AUTH.user-name")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",n.loginForm.controls.username),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(25,29,"GLOBAL.enter-your-username")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(28,31,"AUTH.password")),e.\u0275\u0275advance(2),e.\u0275\u0275property("control",n.loginForm.controls.password),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(31,33,"GLOBAL.enter-your-password")),e.\u0275\u0275property("type",n.isShowPassword?"text":"password"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",n.isShowPassword?"ic-eye-slash":"ic-eye-solid"),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!n.isLoginLoading)("ngIfElse",o),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(39,35,"AUTH.forgot-password"),"?")}},dependencies:[c.mk,c.sg,c.O5,P.z,s._Y,s.Fj,s.JJ,s.JL,s.Q7,s.sg,s.u,T.e$,m.X$],encapsulation:2}),i})();function Y(i,p){if(1&i&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.getTime())}}const Q=[{path:"",component:l(7601).G,children:[{path:"",component:z},{path:"forgot-password",component:N},{path:"two-factor-authentication",component:(()=>{class i{constructor(t,n){this.store=t,this.router=n,this.stopper=new e.EventEmitter,this.otp=[null,null,null,null,null,null],this.isExpired=!1,this.timerSeconds=180,this.showTryAnotherWay=!1,this.tryAnotherWayCounter=5,this.tryAnotherWayCounterCompleted=!1,this.store.select(w.vW).pipe((0,_.R)(this.stopper)).subscribe(o=>{null===o?this.router.navigate(["login"]):this.username=o}),this.store.select(w.MQ).pipe((0,_.R)(this.stopper)).subscribe(o=>{null===o?this.router.navigate(["login"]):this.sessionId=o})}ngOnInit(){this.startTimer()}startTimer(){this.countdownTimer=setInterval(()=>{this.timerSeconds>0?this.timerSeconds--:(this.isExpired=!0,clearInterval(this.countdownTimer))},1e3)}resendOtp(){if(this.isExpired){this.isExpired=!1,this.timerSeconds=180,this.startTimer(),this.store.dispatch(new v.ey(this.username));for(let t=0;t<this.otp.length;t++)this.otp[t]=null}}submitOTP(){let t="";this.otp.forEach(n=>{t+=n}),this.store.dispatch(new v.Ar(this.username,this.sessionId,t));for(let n=0;n<this.otp.length;n++)this.otp[n]=null}getTime(){const t=Math.floor(this.timerSeconds/60),n=this.timerSeconds%60;return`${t}:${n<10?`0${n}`:n.toString()}`}otpClickEvent(t,n,o){var r,a;"Backspace"===t.key?(document.getElementById(o).innerHTML=null,null===(r=document.getElementById(o))||void 0===r||r.focus()):(document.getElementById(n).innerHTML=null,null===(a=document.getElementById(n))||void 0===a||a.focus())}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(f.yh),e.\u0275\u0275directiveInject(g.F0))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["two-factor-authentication"]],decls:42,vars:41,consts:[[1,"flex-between","my-30"],[1,"fw-600"],[1,"w-170"],[1,"fw-700","d-inline"],["src","../../../../assets/images/message-otp.svg",3,"alt"],[1,"gap-3","align-center"],["type","text","id","digit-1","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-2","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-3","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-4","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-5","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange"],["type","text","id","digit-6","maxlength","1","placeholder","\u25cf","autocomplete","off",1,"otp-block",3,"ngModel","keyup","ngModelChange","keyup.enter"],[1,"d-flex","mt-30","gap-2"],[1,"fw-semi-bold"],[1,"fw-semi-bold",3,"ngClass","click"],[4,"ngIf"],[1,"btn-accent-green-xl","mt-50",3,"click"],["focusableLink",""]],template:function(t,n){if(1&t){const o=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",0)(1,"div")(2,"h4",1),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementStart(5,"h4",1),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"h6",2),e.\u0275\u0275text(9),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementStart(11,"div",3),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(16,"img",4),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",5)(19,"input",6),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"digit-2")})("ngModelChange",function(a){return n.otp[0]=a}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(20,"input",7),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"digit-3","digit-1")})("ngModelChange",function(a){return n.otp[1]=a}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(21,"input",8),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"digit-4","digit-2")})("ngModelChange",function(a){return n.otp[2]=a}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"div"),e.\u0275\u0275text(23,"-"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"input",9),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"digit-5","digit-3")})("ngModelChange",function(a){return n.otp[3]=a}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"input",10),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"digit-6","digit-4")})("ngModelChange",function(a){return n.otp[4]=a}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"input",11),e.\u0275\u0275listener("keyup",function(a){return n.otpClickEvent(a,"","digit-5")})("ngModelChange",function(a){return n.otp[5]=a})("keyup.enter",function(){e.\u0275\u0275restoreView(o);const a=e.\u0275\u0275reference(39);return e.\u0275\u0275resetView(a.click())}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(27,"div",12)(28,"div",13),e.\u0275\u0275text(29),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275pipe(31,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"h5",14),e.\u0275\u0275listener("click",function(){return n.resendOtp()}),e.\u0275\u0275elementStart(33,"u"),e.\u0275\u0275text(34),e.\u0275\u0275pipe(35,"translate"),e.\u0275\u0275pipe(36,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(37,Y,2,1,"div",15),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(38,"h4",16,17),e.\u0275\u0275listener("click",function(){return n.submitOTP()}),e.\u0275\u0275text(40),e.\u0275\u0275pipe(41,"translate"),e.\u0275\u0275elementEnd()}2&t&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,19,"GLOBAL.authenticate-your")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(7,21,"GLOBAL.account")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(10,23,"AUTH.we-sent-an")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(13,25,"LEAD_FORM.otp"),""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(15,27,"GLOBAL.to-your-admin-manager")),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(17,29,"GLOBAL.otp")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",n.otp[0]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",n.otp[1]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",n.otp[2]),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",n.otp[3]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",n.otp[4]),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",n.otp[5]),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(30,31,"AUTH.not-receive")," ",e.\u0275\u0275pipeBind1(31,33,"LEAD_FORM.otp"),"?"),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",n.isExpired?"text-accent-green cursor-pointer":"text-gray-110 pe-none"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(35,35,"AUTH.resend")," ",e.\u0275\u0275pipeBind1(36,37,"LEAD_FORM.otp"),""),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!n.isExpired),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(41,39,"BUTTONS.submit")))},dependencies:[c.mk,c.O5,s.Fj,s.JJ,s.nD,s.On,m.X$],encapsulation:2}),i})()}]}];let K=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[g.Bz.forChild(Q),g.Bz]}),i})();var Z=l(90810),L=l(51190);let q=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({providers:[m.sK],imports:[c.ez,K,Z.m,s.UX,s.u5,T.CT.forRoot({player:L.xd}),m.aw.forChild({loader:{provide:m.Zw,useFactory:L.gS,deps:[B.eN]},extend:!0})]}),i})()}}]);