{"ast": null, "code": "import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function repeatWhen(notifier) {\n  return source => source.lift(new RepeatWhenOperator(notifier));\n}\n\nclass RepeatWhenOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n  }\n\n}\n\nclass RepeatWhenSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, notifier, source) {\n    super(destination);\n    this.notifier = notifier;\n    this.source = source;\n    this.sourceIsBeingSubscribedTo = true;\n  }\n\n  notifyNext() {\n    this.sourceIsBeingSubscribedTo = true;\n    this.source.subscribe(this);\n  }\n\n  notifyComplete() {\n    if (this.sourceIsBeingSubscribedTo === false) {\n      return super.complete();\n    }\n  }\n\n  complete() {\n    this.sourceIsBeingSubscribedTo = false;\n\n    if (!this.isStopped) {\n      if (!this.retries) {\n        this.subscribeToRetries();\n      }\n\n      if (!this.retriesSubscription || this.retriesSubscription.closed) {\n        return super.complete();\n      }\n\n      this._unsubscribeAndRecycle();\n\n      this.notifications.next(undefined);\n    }\n  }\n\n  _unsubscribe() {\n    const {\n      notifications,\n      retriesSubscription\n    } = this;\n\n    if (notifications) {\n      notifications.unsubscribe();\n      this.notifications = undefined;\n    }\n\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = undefined;\n    }\n\n    this.retries = undefined;\n  }\n\n  _unsubscribeAndRecycle() {\n    const {\n      _unsubscribe\n    } = this;\n    this._unsubscribe = null;\n\n    super._unsubscribeAndRecycle();\n\n    this._unsubscribe = _unsubscribe;\n    return this;\n  }\n\n  subscribeToRetries() {\n    this.notifications = new Subject();\n    let retries;\n\n    try {\n      const {\n        notifier\n      } = this;\n      retries = notifier(this.notifications);\n    } catch (e) {\n      return super.complete();\n    }\n\n    this.retries = retries;\n    this.retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));\n  }\n\n} //# sourceMappingURL=repeatWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}