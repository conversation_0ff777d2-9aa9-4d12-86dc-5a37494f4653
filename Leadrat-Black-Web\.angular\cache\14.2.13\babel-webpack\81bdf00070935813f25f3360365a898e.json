{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n\n  return source => map(plucker(properties, length))(source);\n}\n\nfunction plucker(props, length) {\n  const mapper = x => {\n    let currentProp = x;\n\n    for (let i = 0; i < length; i++) {\n      const p = currentProp != null ? currentProp[props[i]] : undefined;\n\n      if (p !== void 0) {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n\n    return currentProp;\n  };\n\n  return mapper;\n} //# sourceMappingURL=pluck.js.map", "map": null, "metadata": {}, "sourceType": "module"}