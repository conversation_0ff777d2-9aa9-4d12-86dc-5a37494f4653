#!/usr/bin/env pwsh

Write-Host "Multi-Language Translation Generator" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is available
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Node.js not found"
    }
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if the English source file exists
if (-not (Test-Path "src/assets/i18n/en.json")) {
    Write-Host "Error: English source file not found at src/assets/i18n/en.json" -ForegroundColor Red
    Write-Host "Please make sure you're running this from the correct directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Available options:" -ForegroundColor Yellow
Write-Host "1. Generate all languages (Tamil, Telugu, Bengali, Malayalam)"
Write-Host "2. Generate specific language"
Write-Host "3. Show help"
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "Generating all language translations..." -ForegroundColor Green
        Write-Host "This may take several minutes..." -ForegroundColor Yellow
        Write-Host ""
        
        $startTime = Get-Date
        node multi-language-translator.js src/assets/i18n/en.json
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-Host ""
        Write-Host "Translation completed in $($duration.TotalMinutes.ToString('F1')) minutes" -ForegroundColor Green
    }
    
    "2" {
        Write-Host ""
        Write-Host "Available languages:" -ForegroundColor Yellow
        Write-Host "ta - Tamil"
        Write-Host "te - Telugu"
        Write-Host "bn - Bengali"
        Write-Host "ml - Malayalam"
        Write-Host "kn - Kannada"
        Write-Host ""
        
        $lang = Read-Host "Enter language code"
        
        if ($lang -match "^(ta|te|bn|ml|kn)$") {
            Write-Host ""
            Write-Host "Generating $lang translation..." -ForegroundColor Green
            Write-Host ""
            
            $startTime = Get-Date
            node multi-language-translator.js src/assets/i18n/en.json $lang
            $endTime = Get-Date
            $duration = $endTime - $startTime
            
            Write-Host ""
            Write-Host "Translation completed in $($duration.TotalSeconds.ToString('F1')) seconds" -ForegroundColor Green
        } else {
            Write-Host "Invalid language code. Please use: ta, te, bn, ml, or kn" -ForegroundColor Red
        }
    }
    
    "3" {
        Write-Host ""
        node multi-language-translator.js
    }
    
    default {
        Write-Host "Invalid choice. Please run the script again." -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
