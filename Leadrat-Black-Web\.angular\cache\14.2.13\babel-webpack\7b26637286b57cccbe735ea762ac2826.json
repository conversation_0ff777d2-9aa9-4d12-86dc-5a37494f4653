{"ast": null, "code": "import { scan } from './scan';\nimport { takeLast } from './takeLast';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { pipe } from '../util/pipe';\nexport function reduce(accumulator, seed) {\n  if (arguments.length >= 2) {\n    return function reduceOperatorFunctionWithSeed(source) {\n      return pipe(scan(accumulator, seed), takeLast(1), defaultIfEmpty(seed))(source);\n    };\n  }\n\n  return function reduceOperatorFunction(source) {\n    return pipe(scan((acc, value, index) => accumulator(acc, value, index + 1)), takeLast(1))(source);\n  };\n} //# sourceMappingURL=reduce.js.map", "map": null, "metadata": {}, "sourceType": "module"}