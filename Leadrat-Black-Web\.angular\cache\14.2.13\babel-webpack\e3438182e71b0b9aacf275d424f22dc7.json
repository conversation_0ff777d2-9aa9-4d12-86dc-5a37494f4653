{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { timeoutWith } from './timeoutWith';\nimport { throwError } from '../observable/throwError';\nexport function timeout(due, scheduler = async) {\n  return timeoutWith(due, throwError(new TimeoutError()), scheduler);\n} //# sourceMappingURL=timeout.js.map", "map": null, "metadata": {}, "sourceType": "module"}