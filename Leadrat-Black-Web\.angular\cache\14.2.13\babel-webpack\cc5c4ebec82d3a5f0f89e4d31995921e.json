{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nexport let HeaderNames = /*#__PURE__*/(() => {\n  class HeaderNames {}\n\n  HeaderNames.Authorization = \"Authorization\";\n  HeaderNames.Cookie = \"Cookie\"; //# sourceMappingURL=HeaderNames.js.map\n\n  return HeaderNames;\n})();", "map": null, "metadata": {}, "sourceType": "module"}