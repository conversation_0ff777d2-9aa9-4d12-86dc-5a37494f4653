"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[149],{44149:(r,_,o)=>{o.r(_),o.d(_,{InvoiceModule:()=>s});var t=o(69808),d=o(71511),l=o(18995),e=o(13582),c=o(88923),a=o(5e3);const E=[{path:"",component:c.U},{path:"edit-invoice/:id",component:e.a}];let s=(()=>{class n{}return n.\u0275fac=function(u){return new(u||n)},n.\u0275mod=a.\u0275\u0275defineNgModule({type:n}),n.\u0275inj=a.\u0275\u0275defineInjector({providers:[l.X$],imports:[t.ez,d.Bz.forChild(E)]}),n})()}}]);