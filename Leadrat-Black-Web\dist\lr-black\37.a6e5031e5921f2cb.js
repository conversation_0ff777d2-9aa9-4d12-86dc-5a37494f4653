"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[37],{25037:(ye,Z,s)=>{s.r(Z),s.d(Z,{attendanceModule:()=>tt});var l=s(69808),I=s(40520),x=s(93075),w=s(18995),Y=s(63172),M=s(23713),K=s(51190),se=s(90810),H=s(71511),e=s(5e3),ce=s(77579),v=s(82722),g=s(2976),f=s(61021),V=s(33315),de=s(15439),E=s(32049),N=s(92340),Q=s(38827),X=s(65620),ee=s(35174);function pe(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",16),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1("Working Hours - ",t.workingHours.slice(0,5),"")}}function me(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",5),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t),e.\u0275\u0275nextContext();const a=e.\u0275\u0275reference(5),r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.openLocationModal(a))}),e.\u0275\u0275elementStart(2,"div",6)(3,"div")(4,"span",7),e.\u0275\u0275element(5,"span",8),e.\u0275\u0275elementStart(6,"span",9),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(8,"span",10),e.\u0275\u0275element(9,"span",11),e.\u0275\u0275elementStart(10,"span",12),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(12,"div",13),e.\u0275\u0275element(13,"span",14),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(14,pe,2,1,"div",15),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(7),e.\u0275\u0275textInterpolate(null!=t.clockData&&t.clockData[0].clockInTime?t.getTimeZoneDate(null==t.clockData?null:t.clockData[0].clockInTime,null==t.userData||null==t.userData.timeZoneInfo?null:t.userData.timeZoneInfo.baseUTcOffset,"timeWithMeridiem"):"--"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(null!=t.clockData&&t.clockData[t.clockData.length-1].clockOutTime?t.getTimeZoneDate(null==t.clockData?null:t.clockData[t.clockData.length-1].clockOutTime,null==t.userData||null==t.userData.timeZoneInfo?null:t.userData.timeZoneInfo.baseUTcOffset,"timeWithMeridiem"):"--"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",(null==t.clockData?null:t.clockData.length)>1?"ic-multi-location ic-sm":"ic-location-dot ic-xs"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","00:00:00"!=t.workingHours)}}function ue(i,o){1&i&&(e.\u0275\u0275elementStart(0,"div",17),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.no-entries")))}function b(i,o){1&i&&e.\u0275\u0275element(0,"div",40)}function U(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,b,1,0,"div",28),e.\u0275\u0275elementStart(2,"div",29),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",30)(5,"div",31),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t),r=a.$implicit,d=a.index,u=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(u.showLocation(null==r?null:r.clockInLatitude,null==r?null:r.clockInLongitude,null==r?null:r.clockInLocation,d+1))}),e.\u0275\u0275elementStart(6,"div",32)(7,"div",33)(8,"h5",34),e.\u0275\u0275text(9),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(11,"div",35)(12,"span",36),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(14,"div",37),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(15,"div",38),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,d=e.\u0275\u0275nextContext(3),u=e.\u0275\u0275reference(4);return e.\u0275\u0275resetView(d.openImage(r.clockInImageUrl,u,"Clock in",null==r?null:r.clockInTime))}),e.\u0275\u0275element(16,"img",39),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(17,"div",30)(18,"div",31),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t),r=a.$implicit,d=a.index,u=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(u.showLocation(null==r?null:r.clockOutLatitude,null==r?null:r.clockOutLongitude,null==r?null:r.clockOutLocation,d+1))}),e.\u0275\u0275elementStart(19,"div",32)(20,"div",33)(21,"h5",34),e.\u0275\u0275text(22),e.\u0275\u0275pipe(23,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"div",35)(25,"span",36),e.\u0275\u0275text(26),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(27,"div",37),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(28,"div",38),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,d=e.\u0275\u0275nextContext(3),u=e.\u0275\u0275reference(4);return e.\u0275\u0275resetView(d.openImage(r.clockOutImageUrl,u,"Clock out",null==r?null:r.clockOutTime))}),e.\u0275\u0275element(29,"img",39),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=o.$implicit,n=o.index,a=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",0!=n),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Clock ",n+1,""),e.\u0275\u0275advance(2),e.\u0275\u0275property("title",null!=t&&t.clockInLocation?null==t?null:t.clockInLocation:"Location not available"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(10,14,"GLOBAL.clock-in")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(null!=t&&t.clockInTime?a.getTimeZoneDate(t.clockInTime,null==a.userData||null==a.userData.timeZoneInfo?null:a.userData.timeZoneInfo.baseUTcOffset):""),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",t.clockInImageUrl?"cursor-pointer":"pe-none"),e.\u0275\u0275advance(1),e.\u0275\u0275property("appImage",t.clockInImageUrl?a.s3BucketUrl+t.clockInImageUrl:"")("type","defaultAvatar"),e.\u0275\u0275advance(2),e.\u0275\u0275property("title",null!=t&&t.clockOutLocation?null==t?null:t.clockOutLocation:"Location not available"),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(23,16,"GLOBAL.clock-out")),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(null!=t&&t.clockOutTime?a.getTimeZoneDate(t.clockOutTime,null==a.userData||null==a.userData.timeZoneInfo?null:a.userData.timeZoneInfo.baseUTcOffset):""),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",t.clockOutImageUrl?"cursor-pointer":"pe-none"),e.\u0275\u0275advance(1),e.\u0275\u0275property("appImage",t.clockOutImageUrl?a.s3BucketUrl+t.clockOutImageUrl:"")("type","defaultAvatar")}}const h=function(i,o){return{lat:i,lng:o}};function _(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"map-marker",41,42),e.\u0275\u0275listener("mapClick",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275reference(1),r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.openInfoWindow(a))}),e.\u0275\u0275elementEnd()}if(2&i){const t=o.$implicit;e.\u0275\u0275property("position",e.\u0275\u0275pureFunction2(2,h,null==t?null:t.latitude,null==t?null:t.longitude))("label",null==t?null:t.label)}}function S(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",18)(1,"div",19)(2,"h3",20),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",21),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.modalService.hide())}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"div",22)(7,"div",23),e.\u0275\u0275template(8,U,30,18,"ng-container",24),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",25)(10,"google-map",26),e.\u0275\u0275template(11,_,2,5,"map-marker",27),e.\u0275\u0275elementStart(12,"map-info-window"),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd()()()()()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,5,"GLOBAL.clock-inout-location")),e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",t.clockData),e.\u0275\u0275advance(2),e.\u0275\u0275property("center",e.\u0275\u0275pureFunction2(7,h,null==t.center?null:t.center.lat,null==t.center?null:t.center.lng)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.markers),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.selectedAddress)}}function j(i,o){if(1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,me,15,4,"ng-container",0),e.\u0275\u0275template(2,ue,3,3,"ng-template",null,3,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(4,S,14,10,"ng-template",null,4,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementContainerEnd()),2&i){const t=e.\u0275\u0275reference(3),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==n.clockData?null:n.clockData.length)("ngIfElse",t)}}function z(i,o){1&i&&(e.\u0275\u0275elementStart(0,"div",43),e.\u0275\u0275text(1,"--"),e.\u0275\u0275elementEnd())}function G(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",44),e.\u0275\u0275element(1,"img",45),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementStart(3,"div",46),e.\u0275\u0275element(4,"img",47),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275styleProp("transform","rotate("+t.rotationAngle+"deg)"),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(2,12,"GLOBAL.image")),e.\u0275\u0275property("appImage",t.currentImage?t.s3BucketUrl+t.currentImage:"")("type","defaultAvatar"),e.\u0275\u0275advance(3),e.\u0275\u0275styleProp("transform","rotate("+t.rotationAngle+"deg)")("scale",t.currentZoom),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(5,14,"GLOBAL.image")),e.\u0275\u0275property("appImage",t.currentImage?t.s3BucketUrl+t.currentImage:"")("type","defaultAvatar")}}let R=(()=>{class i{constructor(t,n,a){this.modalService=t,this.modalRef=n,this._store=a,this.stopper=new e.EventEmitter,this.moment=de,this.markers=[],this.center={lat:12.9106262,lng:77.6405173},this.latitude=12.8898194,this.longitude=77.64237,this.label=1,this.s3BucketUrl=N.N.s3ImageBucketURL,this.rotationAngle=0,this.currentZoom=1,this.getTimeZoneDate=f.h5}ngOnInit(){var t;const n=null===(t=this.clockData)||void 0===t?void 0:t[0];this.showLocation(null==n?void 0:n.clockInLatitude,null==n?void 0:n.clockInLongitude,null==n?void 0:n.clockInLocation,1)}agInit(t){this._store.select(E.Xf).pipe((0,v.R)(this.stopper)).subscribe(n=>{var a,r,d,u,A,O,p,m,y;this.userData=n,this.params=t,this.clockData=null===(u=null===(d=null===(r=null===(a=this.params.value[0])||void 0===a?void 0:a.data)||void 0===r?void 0:r.logByDayDtos.filter(T=>{var k,D,L;return(0,f.h5)(T.day,null===(D=null===(k=this.userData)||void 0===k?void 0:k.timeZoneInfo)||void 0===D?void 0:D.baseUTcOffset,"ISO")===(null===(L=this.params.value[0])||void 0===L?void 0:L.date)}))||void 0===d?void 0:d[0])||void 0===u?void 0:u.logDtos,this.clockData&&(this.clockData=null===(A=[...this.clockData])||void 0===A?void 0:A.reverse()),this.workingHours=null===(y=null===(m=null===(p=null===(O=this.params.value[0])||void 0===O?void 0:O.data)||void 0===p?void 0:p.logByDayDtos.filter(T=>{var k,D,L;return(0,f.h5)(T.day,null===(D=null===(k=this.userData)||void 0===k?void 0:k.timeZoneInfo)||void 0===D?void 0:D.baseUTcOffset,"ISO")===(null===(L=this.params.value[0])||void 0===L?void 0:L.date)}))||void 0===m?void 0:m[0])||void 0===y?void 0:y.workingHours})}openLocationModal(t){this.modalRef=this.modalService.show(t,{class:"right-modal modal-650 ip-modal-unset"})}openInfoWindow(t){this.infoWindow.open(t)}showLocation(t,n,a,r){var d;this.markers[r-1]={latitude:Number(t),longitude:Number(n),label:String(r),address:a},this.selectedAddress=null===(d=this.markers[r-1])||void 0===d?void 0:d.address,this.center={lat:Number(t),lng:Number(n)}}openImage(t,n,a,r){this.currentImage=t,this.title=a,this.viewTime=r,this.modalRef=this.modalService.show(n,{class:"modal-350 modal-dialog-centered ip-modal-unset",keyboard:!1})}rotateImage(t){this.rotationAngle+=t}zoomImage(t){this.currentZoom*=t}downloadImage(){let t=this.s3BucketUrl+this.currentImage;const n=document.createElement("a");n.href=t,n.download="image",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(Q.tT),e.\u0275\u0275directiveInject(Q.UZ),e.\u0275\u0275directiveInject(X.yh))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["clock-in-out"]],viewQuery:function(t,n){if(1&t&&e.\u0275\u0275viewQuery(V.ch,5),2&t){let a;e.\u0275\u0275queryRefresh(a=e.\u0275\u0275loadQuery())&&(n.infoWindow=a.first)}},decls:5,vars:2,consts:[[4,"ngIf","ngIfElse"],["futureDate",""],["imageView",""],["noEntry",""],["location",""],[1,"bg-light-pearl","w-110","cursor-pointer","py-6",3,"click"],[1,"flex-between","pl-10"],[1,"align-center"],[1,"icon","ic-clock-nine","ic-accent-green","cursor-pointer","ic-xxs"],[1,"text-accent-green","fw-600","ml-4","text-xs"],[1,"align-center","mt-4"],[1,"icon","ic-clock-eight","ic-red-350","cursor-pointer","ic-xxs"],[1,"text-red-350","fw-600","ml-4","text-xs"],[1,"dot","dot-lg","bg-gray-dark","ml-10"],[1,"icon","ic-dark",3,"ngClass"],["class","text-xs text-nowrap mt-4 pl-4",4,"ngIf"],[1,"text-xs","text-nowrap","mt-4","pl-4"],[1,"px-36","py-24","text-black-200","bg-pink-700","text-xs"],[1,"bg-white","h-100vh","tb-w-100-40"],[1,"flex-between","bg-coal","w-100","px-20","py-12","text-white"],[1,"fw-semi-bold","fv-sm-caps"],[1,"icon","ic-close-secondary","ic-large","cursor-pointer",3,"click"],[1,"d-flex"],[1,"flex-column","h-100-80","scrollbar"],[4,"ngFor","ngForOf"],[1,"w-80pr","responsive-map"],[3,"center"],[3,"position","label","mapClick",4,"ngFor","ngForOf"],["class","border-bottom mx-12 mt-12 bg-light-pearl",4,"ngIf"],[1,"field-label","fw-600","text-mud","px-12"],[1,"flex-between","mt-12"],[1,"mx-12","p-8","cursor-pointer","w-180","bg-light-pearl","border-right","border-0","br-6",3,"title","click"],[1,"flex-between"],[1,"flex-column"],[1,"fw-600","text-mud"],[1,"text-dark-gray","text-xxs"],[1,"fw-700"],[1,"icon","ic-location-solid","ic-accent-green","ic-sm","mr-4"],[3,"ngClass","click"],["alt","","width","30","height","30",1,"br-50","obj-cover","mr-12",3,"appImage","type"],[1,"border-bottom","mx-12","mt-12","bg-light-pearl"],[3,"position","label","mapClick"],["mapMarker","mapMarker"],[1,"bg-blue-900","pt-9","pb-10","px-53"],[1,"flex-center","position-relative"],[1,"w-300","position-relative","h-300",3,"appImage","type","alt"],[1,"w-100","bg-blur","position-absolute","flex-center"],[1,"max-w-300","h-300",3,"appImage","type","alt"]],template:function(t,n){if(1&t&&(e.\u0275\u0275template(0,j,6,2,"ng-container",0),e.\u0275\u0275template(1,z,2,0,"ng-template",null,1,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(3,G,6,16,"ng-template",null,2,e.\u0275\u0275templateRefExtractor)),2&t){const a=e.\u0275\u0275reference(2);e.\u0275\u0275property("ngIf",null==n.params.value[0]?null:n.params.value[0].pastDate)("ngIfElse",a)}},dependencies:[l.mk,l.sg,l.O5,ee.S,V.b6,V.ch,V.O_,w.X$],encapsulation:2}),i})();var P=s(77225);const W=function(i){return{"pe-none":i}};let $=(()=>{class i{constructor(t,n){this.router=t,this.store=n,this.s3BucketUrl=N.N.s3ImageBucketURL}ngOnInit(){var t,n;this.store.select(P.HF).subscribe(r=>{null!=r&&r.includes("Users")&&(this.canViewUser=!0)}),(null===(t=JSON.parse(localStorage.getItem("userDetails")))||void 0===t?void 0:t.sub)==(null===(n=this.params.value[0])||void 0===n?void 0:n.userId)&&(this.isLoggedInUser=!0)}agInit(t){this.params=t}refresh(){return!1}navigateToUserDetails(){var t;this.router.navigate(["teams/user-details",null===(t=this.params.value[0])||void 0===t?void 0:t.userId])}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(H.F0),e.\u0275\u0275directiveInject(X.yh))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["user-details"]],decls:10,vars:9,consts:[[1,"py-10","px-20","align-center","cursor-pointer",3,"ngClass","click"],["width","30","height","30",1,"br-50","obj-cover",3,"appImage","type"],[1,"ml-10"],[1,"fw-600","text-large","text-coal","cursor-pointer","text-truncate-1","break-all",3,"title"],[1,"text-sm","fw-semi-bold","text-dark-gray","text-truncate-1","break-all"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275listener("click",function(){return n.navigateToUserDetails()}),e.\u0275\u0275elementStart(1,"div"),e.\u0275\u0275element(2,"img",1),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",2)(4,"div",3),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"div",4),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"div",4),e.\u0275\u0275text(9),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(7,W,!n.canViewUser&&!n.isLoggedInUser)),e.\u0275\u0275advance(2),e.\u0275\u0275property("appImage",null!=n.params.value[0]&&n.params.value[0].imageUrl?n.s3BucketUrl+(null==n.params.value[0]?null:n.params.value[0].imageUrl):"")("type","defaultAvatar"),e.\u0275\u0275advance(2),e.\u0275\u0275property("title",n.params.value[0].userName),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",n.params.value[0].userName,""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.params.value[0].designation),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.params.value[0].department))},dependencies:[l.mk,ee.S],encapsulation:2}),i})();var C=s(74775),F=s(38245),te=s(84766),B=s(30166),ne=s(54244),J=s(48315),q=s(11970),c=s(47511);function ie(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",8)(2,"ag-grid-angular",9,10),e.\u0275\u0275listener("gridReady",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onGridReady(a))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"div",11)(5,"pagination",12),e.\u0275\u0275listener("pageChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onPageChange(a))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275property("pagination",!0)("paginationPageSize",t.pageSize)("gridOptions",t.gridOptions)("rowData",t.rowData)("suppressPaginationPanel",!0),e.\u0275\u0275advance(3),e.\u0275\u0275property("offset",t.currOffset)("limit",1)("range",1)("size",t.getPages(t.totalReqCount,t.pageSize))}}function ae(i,o){1&i&&(e.\u0275\u0275elementStart(0,"div",13),e.\u0275\u0275element(1,"application-loader"),e.\u0275\u0275elementEnd())}let he=(()=>{class i{constructor(t,n,a){this.gridOptionsService=t,this.modalService=n,this._store=a,this.stopper=new e.EventEmitter,this.pageSize=g.IV1,this.currOffset=0,this.rowData=[],this.s3BucketUrl=N.N.s3ImageBucketURL,this.getPages=f.UQ,this.filtersPayload={pageNumber:1,pageSize:this.pageSize,path:"attendance"},this.gridOptions=this.gridOptionsService.getGridSettings(this),this.defaultColDef=this.gridOptions.defaultColDef,this.gridOptions.rowData=this.rowData,this.initializeGridSettings(),this._store.select(F.Nl).pipe((0,ne.n)(()=>this.isExportAttendanceStatusLoading),(0,v.R)(this.stopper)).subscribe(r=>{this.rowData=null==r?void 0:r.items,this.totalReqCount=null==r?void 0:r.totalCount}),this._store.select(F.nf).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.isExportAttendanceStatusLoading=r}),this._store.select(E.Xf).pipe((0,v.R)(this.stopper)).subscribe(r=>{this.userData=r}),this._store.select(P.Zu).pipe((0,v.R)(this.stopper)).subscribe(r=>{null==r||!r.length||(null!=r&&r.includes("Permissions.Attendance.ExportAllUsers")&&(this.canAttendanceExportAllUsers=!0),null!=r&&r.includes("Permissions.Attendance.ExportReportees")&&(this.canAttendanceExportReportees=!0))})}initializeGridSettings(){this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.columnDefs=[{headerName:"Export By",field:"Export By",minWidth:180,filter:!1,valueGetter:t=>{var n,a;return[null===(a=null===(n=t.data)||void 0===n?void 0:n.exportedUser)||void 0===a?void 0:a.name]},cellRenderer:t=>`<p class="text-truncate-2">${t.value}</p>`},{headerName:"Requested on",field:"Requested on",minWidth:150,valueGetter:t=>{var n,a,r;return[(0,f.h5)(null===(n=t.data)||void 0===n?void 0:n.createdOn,null===(r=null===(a=this.userData)||void 0===a?void 0:a.timeZoneInfo)||void 0===r?void 0:r.baseUTcOffset,"fullDateTime")]},cellRenderer:t=>{var n,a,r,d,u;return`<p class="text-nowrap">${t.value}</p>\n             <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(a=null===(n=this.userData)||void 0===n?void 0:n.timeZoneInfo)||void 0===a?void 0:a.timeZoneName)&&(null===(r=this.userData)||void 0===r?void 0:r.shouldShowTimeZone)&&t.value?"("+(null===(u=null===(d=this.userData)||void 0===d?void 0:d.timeZoneInfo)||void 0===u?void 0:u.timeZoneName)+")":""}</p>`}},{headerName:"Last Modified on",field:"Last Modified on",minWidth:150,valueGetter:t=>{var n,a,r;return[(0,f.h5)(null===(n=t.data)||void 0===n?void 0:n.lastModifiedOn,null===(r=null===(a=this.userData)||void 0===a?void 0:a.timeZoneInfo)||void 0===r?void 0:r.baseUTcOffset,"fullDateTime")]},cellRenderer:t=>{var n,a,r,d,u;return`<p class="text-nowrap">${t.value}</p>\n            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(a=null===(n=this.userData)||void 0===n?void 0:n.timeZoneInfo)||void 0===a?void 0:a.timeZoneName)&&(null===(r=this.userData)||void 0===r?void 0:r.shouldShowTimeZone)&&t.value?"("+(null===(u=null===(d=this.userData)||void 0===d?void 0:d.timeZoneInfo)||void 0===u?void 0:u.timeZoneName)+")":""}</p>`}},{headerName:"Total Count",field:"Total Count",maxWidth:100,filter:!1,valueGetter:t=>{var n;return[null===(n=t.data)||void 0===n?void 0:n.count]},cellRenderer:t=>`<p>${t.value}</p>`},{headerName:"File Name",field:"File Name",minWidth:100,filter:!1,valueGetter:t=>{var n;return[null===(n=t.data)||void 0===n?void 0:n.fileName]},cellRenderer:t=>`<p class="text-truncate-1 break-all">${t.value}</p>`},{headerName:"Excel File",field:"Status",maxWidth:180,minWidth:180,filter:!1,valueGetter:t=>{var n,a;return[null!==(n=t.data)&&void 0!==n&&n.s3BucketKey?null===(a=t.data)||void 0===a?void 0:a.s3BucketKey:""]},cellRenderer:t=>t.value[0]?`<a href="${t.value}" class="btn btn-xxs btn-linear-green text-nowrap flex-center w-150">\n            <span class="icon ic-xxs ic-download"></span>\n            <span class="text-white ml-8">Ready to Download</span></a>`:""}],this.gridOptions.context={componentParent:this}}onGridReady(t){this.gridApi=t.api,t.api.sizeColumnsToFit(),this.gridColumnApi=t.columnApi}onPageChange(t){this.currOffset=t,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:t+1,pageSize:this.pageSize}),this.updateTrackerList()}updateTrackerList(){var t;this._store.dispatch(new C.Tc({trackerPermission:this.canAttendanceExportAllUsers?0:1},null===(t=this.filtersPayload)||void 0===t?void 0:t.pageNumber,this.filtersPayload.pageSize))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(J.t),e.\u0275\u0275directiveInject(Q.tT),e.\u0275\u0275directiveInject(X.yh))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["export-attendance-tracker"]],decls:14,vars:8,consts:[[1,"bg-coal","w-100","px-20","py-12","text-white","brtl-10","brtr-10","flex-between"],[1,"fw-semi-bold"],[1,"btn","btn-sm","btn-linear-green","align-center",3,"click"],[1,"ic-refresh","icon","ic-xxs","mr-8","ph-mr-0"],[1,"text-white","text-normal","ph-d-none"],[1,"ic-close-secondary","ic-close-modal","tb-ic-close-secondary",3,"click"],[4,"ngIf","ngIfElse"],["loader",""],[1,"max-h-100-176","scrollbar"],[1,"ag-theme-alpine",3,"pagination","paginationPageSize","gridOptions","rowData","suppressPaginationPanel","gridReady"],["agGrid",""],[1,"flex-end","m-20"],[3,"offset","limit","range","size","pageChange"],[1,"flex-center","h-100","mt-60"]],template:function(t,n){if(1&t&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",0)(2,"h3",1),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"button",2),e.\u0275\u0275listener("click",function(){return n.updateTrackerList()}),e.\u0275\u0275element(6,"span",3),e.\u0275\u0275elementStart(7,"span",4),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"a",5),e.\u0275\u0275listener("click",function(){return n.modalService.hide()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(11,ie,6,9,"ng-container",6),e.\u0275\u0275elementContainerEnd(),e.\u0275\u0275template(12,ae,2,0,"ng-template",null,7,e.\u0275\u0275templateRefExtractor)),2&t){const a=e.\u0275\u0275reference(13);e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.attendance-export-tracker")),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,6,"BULK_LEAD.refresh-data")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!n.isExportAttendanceStatusLoading)("ngIfElse",a)}},dependencies:[l.O5,Y.N8,q.Q,c.t,w.X$],encapsulation:2}),i})();var be=s(35309),ke=s(63253),Se=s(22313),Ie=s(15634),Ee=s(1880),re=s(24376),_e=s(84617),Ae=s(17447);const Oe=["dt1"];function Te(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",35),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,d=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(d.currentVisibility(r.userStatus))}),e.\u0275\u0275elementStart(2,"div",36)(3,"a"),e.\u0275\u0275element(4,"img",37),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(6,"span",38),e.\u0275\u0275text(7),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=o.$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("title",null!=n.usersData&&n.usersData[null==t?null:t.visibility]?null==n.usersData?null:n.usersData[null==t?null:t.visibility]:""),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("active",n.appliedFilter.userStatus==t.userStatus),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(5,9,"GLOBAL.muso")),e.\u0275\u0275property("type","leadrat")("appImage",n.s3BucketUrl+t.image),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("active",n.appliedFilter.userStatus==t.userStatus),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1("",t.name," ")}}function De(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",39),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.openAttendanceTracker())}),e.\u0275\u0275element(1,"span",40),e.\u0275\u0275elementStart(2,"span",41),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()()}2&i&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,1,"GLOBAL.export-tracker")))}function Le(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",42),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.exportAttendanceReport())}),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()}2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(2,1,"REPORTS.export")," "))}function Me(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"ng-option",43),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=o.$implicit;e.\u0275\u0275property("value",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t,"")}}function Re(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",50),e.\u0275\u0275text(1),e.\u0275\u0275elementStart(2,"span",51),e.\u0275\u0275listener("click",function(){const r=e.\u0275\u0275restoreView(t).$implicit,d=e.\u0275\u0275nextContext().$implicit,u=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(u.onRemoveFilter(d.key,r))}),e.\u0275\u0275elementEnd()()}if(2&i){const t=o.$implicit,n=e.\u0275\u0275nextContext().$implicit,a=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",a.attendanceFiltersKeyLabel[n.key]||n.key,": ","reportsTo"===n.key||"users"===n.key?a.getReportsName(t):"department"===n.key?a.getDepartmentName(t):"designation"===n.key?a.getDesignationName(t):t," ")}}function Pe(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",48),e.\u0275\u0275template(1,Re,3,2,"div",49),e.\u0275\u0275elementEnd()),2&i){const t=o.$implicit,n=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",n.getArrayOfFilters(t.key,t.value))}}function Fe(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",44)(2,"drag-scroll",45),e.\u0275\u0275template(3,Pe,2,1,"div",46),e.\u0275\u0275pipe(4,"keyvalue"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",47),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return a.reset(!0),e.\u0275\u0275resetView(a.filterAttendanceList())}),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",e.\u0275\u0275pipeBind1(4,3,t.appliedFilter)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(7,5,"BUTTONS.clear")," ",e.\u0275\u0275pipeBind1(8,7,"GLOBAL.all")," ")}}function Be(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",62),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate7(" ",e.\u0275\u0275pipeBind1(2,7,"GLOBAL.showing")," ",t.currOffset*t.pageSize+1," ",e.\u0275\u0275pipeBind1(3,9,"GLOBAL.to-small")," ",t.currOffset*t.pageSize+(null==t.rowData?null:t.rowData.length)," ",e.\u0275\u0275pipeBind1(4,11,"GLOBAL.of-small")," ",t.totalCount," ",e.\u0275\u0275pipeBind1(5,13,"GLOBAL.entries-small")," ")}}function Ue(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",52)(1,"ag-grid-angular",53,54),e.\u0275\u0275listener("gridReady",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.onGridReady(a))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(3,"div",55)(4,"div",36),e.\u0275\u0275element(5,"span",56),e.\u0275\u0275elementStart(6,"span",57),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"span",58),e.\u0275\u0275elementStart(10,"span",57),e.\u0275\u0275text(11),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(13,"div",59),e.\u0275\u0275template(14,Be,6,15,"div",60),e.\u0275\u0275elementStart(15,"pagination",61),e.\u0275\u0275listener("pageChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.onPageChange(a))}),e.\u0275\u0275elementEnd()()()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("pagination",!0)("paginationPageSize",t.pageSize+1)("gridOptions",t.gridOptions)("rowData",t.rowData)("frameworkComponents",t.frameworkComponents)("suppressPaginationPanel",!0)("columnDefs",t.gridOptions.columnDefs)("alwaysShowHorizontalScroll",!0)("alwaysShowVerticalScroll",!0),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(8,16,"USER.unmarked-attendance"),""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(12,18,"USER.future-day")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",t.totalCount),e.\u0275\u0275advance(1),e.\u0275\u0275property("offset",t.currOffset)("limit",1)("range",1)("size",t.getPages(t.totalCount,t.pageSize))}}function Ve(i,o){1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",63),e.\u0275\u0275element(2,"img",64),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementStart(4,"div",65),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementContainerEnd()),2&i&&(e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(3,2,"GLOBAL.no-data-found")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(6,4,"PROFILE.no-data-found")))}function Ne(i,o){if(1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,Ve,7,6,"ng-container",32),e.\u0275\u0275elementContainerEnd()),2&i){e.\u0275\u0275nextContext();const t=e.\u0275\u0275reference(46),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!(null!=n.rowData&&n.rowData.length))("ngIfElse",t)}}function je(i,o){1&i&&e.\u0275\u0275element(0,"application-loader",66)}function ze(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"span",87),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t);return e.\u0275\u0275resetView((0,a.clear)(a.item))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(1,"span",88),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()}if(2&i){const t=o.item;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.firstName+" "+t.lastName,"")}}function Ge(i,o){1&i&&(e.\u0275\u0275elementStart(0,"span",94),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.-disabled-")))}function We(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",89)(1,"div",90),e.\u0275\u0275element(2,"input",91)(3,"span",75),e.\u0275\u0275elementStart(4,"span",92),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(6,Ge,3,3,"span",93),e.\u0275\u0275elementEnd()),2&i){const t=o.item,n=o.item$,a=o.index;e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("id","item-",a,"")("automate-id","item-",a,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName,""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isActive)}}function $e(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"span",87),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t);return e.\u0275\u0275resetView((0,a.clear)(a.item))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(1,"span",88),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()}if(2&i){const t=o.item;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.firstName+" "+t.lastName,"")}}function Ze(i,o){1&i&&(e.\u0275\u0275elementStart(0,"span",94),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&i&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.-disabled-")))}function Ye(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",89)(1,"div",90),e.\u0275\u0275element(2,"input",91)(3,"span",75),e.\u0275\u0275elementStart(4,"span",92),e.\u0275\u0275text(5),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(6,Ze,3,3,"span",93),e.\u0275\u0275elementEnd()),2&i){const t=o.item,n=o.item$,a=o.index;e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("id","item-",a,"")("automate-id","item-",a,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName,""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isActive)}}function Ke(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",90),e.\u0275\u0275element(1,"input",91)(2,"span",75),e.\u0275\u0275elementStart(3,"span",92),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()),2&i){const t=o.item,n=o.item$,a=o.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",a,"")("automate-id","item-",a,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.name)}}function He(i,o){if(1&i&&(e.\u0275\u0275elementStart(0,"div",90),e.\u0275\u0275element(1,"input",91)(2,"span",75),e.\u0275\u0275elementStart(3,"span",92),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()),2&i){const t=o.item,n=o.item$,a=o.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",a,"")("automate-id","item-",a,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.name)}}const le=function(i){return{"pe-none blinking":i}};function Qe(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",67)(1,"div",68)(2,"div",69)(3,"div",70)(4,"div",71)(5,"div",72),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"label",73)(9,"input",74),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.appliedFilter.withTeam=a)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(10,"span",75),e.\u0275\u0275text(11),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(13,"div",76)(14,"ng-select",77),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.appliedFilter.users=a)}),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275template(16,ze,3,1,"ng-template",78),e.\u0275\u0275template(17,We,7,6,"ng-template",79),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(18,"div",70)(19,"div",72),e.\u0275\u0275text(20),e.\u0275\u0275pipe(21,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"div",76)(23,"ng-select",80),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.appliedFilter.reportsTo=a)}),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275template(25,$e,3,1,"ng-template",78),e.\u0275\u0275template(26,Ye,7,6,"ng-template",79),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(27,"div",70)(28,"div",72),e.\u0275\u0275text(29),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"div",76)(32,"ng-select",81),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.appliedFilter.designation=a)}),e.\u0275\u0275pipe(33,"translate"),e.\u0275\u0275template(34,Ke,5,4,"ng-template",79),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(35,"div",70)(36,"div",72),e.\u0275\u0275text(37),e.\u0275\u0275pipe(38,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(39,"div",76)(40,"ng-select",82),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.appliedFilter.department=a)}),e.\u0275\u0275pipe(41,"translate"),e.\u0275\u0275template(42,He,5,4,"ng-template",79),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(43,"div",83)(44,"u",84),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.modalService.hide())}),e.\u0275\u0275text(45),e.\u0275\u0275pipe(46,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(47,"button",85),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.reset())}),e.\u0275\u0275text(48),e.\u0275\u0275pipe(49,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(50,"button",86),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.applyFilter())}),e.\u0275\u0275text(51),e.\u0275\u0275pipe(52,"translate"),e.\u0275\u0275elementEnd()()()()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(7,37,"USER.user")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",t.appliedFilter.withTeam),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(12,39,"DASHBOARD.with-team")," "),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(15,41,"GLOBAL.ex-manasa-pampana")),e.\u0275\u0275property("virtualScroll",!0)("items",t.canViewAllUsers?t.allUsers:t.onlyReportees)("multiple",!0)("closeOnSelect",!1)("ngModel",t.appliedFilter.users)("ngClass",e.\u0275\u0275pureFunction1(61,le,t.canViewAllUsers?t.allUserListIsLoading:t.isReporteesWithInactiveLoading)),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(21,43,"USER_MANAGEMENT.reporting-to")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(24,45,"GLOBAL.ex-manasa-pampana")),e.\u0275\u0275property("virtualScroll",!0)("items",t.usersList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.appliedFilter.reportsTo)("ngClass",e.\u0275\u0275pureFunction1(63,le,t.allUserListIsLoading)),e.\u0275\u0275advance(6),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(30,47,"USER_MANAGEMENT.designation")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(33,49,"GLOBAL.select-designation")),e.\u0275\u0275property("virtualScroll",!0)("items",t.designationList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.appliedFilter.designation)("ngClass",e.\u0275\u0275pureFunction1(65,le,t.isDesignationLoading)),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(38,51,"USER_MANAGEMENT.department")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(41,53,"GLOBAL.select-department")),e.\u0275\u0275property("virtualScroll",!0)("items",t.departmentList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.appliedFilter.department)("ngClass",e.\u0275\u0275pureFunction1(67,le,t.isDepartmentLoading)),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(46,55,"BUTTONS.cancel")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(49,57,"GLOBAL.reset")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(52,59,"GLOBAL.search"))}}function Xe(i,o){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div")(1,"div",1)(2,"ul",2),e.\u0275\u0275template(3,Te,8,11,"ng-container",3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,De,5,3,"div",4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",5)(6,"div",6)(7,"div",7),e.\u0275\u0275elementContainerStart(8),e.\u0275\u0275elementStart(9,"div",8),e.\u0275\u0275element(10,"span",9),e.\u0275\u0275elementStart(11,"input",10),e.\u0275\u0275listener("keydown",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.onSearch(a))})("input",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.isEmptyInput(a))})("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.searchTerm=a)}),e.\u0275\u0275pipe(12,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(13,"small",11),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd(),e.\u0275\u0275template(16,Le,3,3,"div",12),e.\u0275\u0275elementStart(17,"div",13),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275reference(51),r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.openAdvFiltersModal(a))}),e.\u0275\u0275element(18,"div",14),e.\u0275\u0275elementStart(19,"span",15),e.\u0275\u0275text(20),e.\u0275\u0275pipe(21,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(22,"div",16)(23,"div",17)(24,"span",18),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.previousMonth())}),e.\u0275\u0275element(25,"span",19),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(26,"span",20),e.\u0275\u0275text(27),e.\u0275\u0275pipe(28,"date"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(29,"input",21),e.\u0275\u0275elementStart(30,"owl-date-time",22,23),e.\u0275\u0275listener("afterPickerOpen",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.onPickerOpened(a.currentDate,"month"))})("monthSelected",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.monthChanged(a))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"span",18),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.nextMonth())}),e.\u0275\u0275element(33,"span",24),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(34,"div",25)(35,"span",26)(36,"span",27),e.\u0275\u0275text(37),e.\u0275\u0275pipe(38,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275text(39),e.\u0275\u0275pipe(40,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(41,"ng-select",28),e.\u0275\u0275listener("ngModelChange",function(a){e.\u0275\u0275restoreView(t);const r=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(r.selectedPageSize=a)})("change",function(){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.assignCount())}),e.\u0275\u0275template(42,Me,2,2,"ng-option",29),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(43,"div",30),e.\u0275\u0275template(44,Fe,9,9,"ng-container",0),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(45,Ue,16,20,"ng-template",null,31,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(47,Ne,2,2,"ng-container",32),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(48,je,1,0,"ng-template",null,33,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(50,Qe,53,69,"ng-template",null,34,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275reference(31),n=e.\u0275\u0275reference(49),a=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",a.visibilityList),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.canAttendanceExportAllUsers||a.canAttendanceExportReportees),e.\u0275\u0275advance(7),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(12,29,"GLOBAL.search-by-user")),e.\u0275\u0275property("ngModel",a.searchTerm),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" (",e.\u0275\u0275pipeBind1(15,31,"LEADS.lead-search-prompt"),")"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",a.canExportAllUsers||a.canExportReportees),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(21,33,"PROPERTY.advanced-filters")),e.\u0275\u0275advance(6),e.\u0275\u0275property("owlDateTimeTrigger",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",e.\u0275\u0275pipeBind2(28,35,a.currentMonth,"MMM"),", ",a.selectedYear,""),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngModel",a.selectedMonthAndYear)("owlDateTimeTrigger",t)("max",a.maxDate)("owlDateTime",t),e.\u0275\u0275advance(1),e.\u0275\u0275property("yearOnly",!0)("pickerType","calendar"),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("disabled",a.isPresentMonth),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(38,38,"GLOBAL.show")),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(40,40,"GLOBAL.entries"),""),e.\u0275\u0275advance(2),e.\u0275\u0275property("virtualScroll",!0)("placeholder",a.pageSize)("ngModel",a.selectedPageSize)("searchable",!1),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",a.showEntriesSize),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",a.showLeftNav?"w-100-190":"w-100-90"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",a.showFilters),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!a.isAttendanceLoading)("ngIfElse",n)}}const qe=[{path:"",component:(()=>{class i extends te.y{constructor(t,n,a,r,d,u,A,O){super(),this.headerTitle=t,this.modalService=n,this._store=a,this.gridOptionsService=r,this.metaTitle=d,this.shareDataService=u,this.cdr=A,this.trackingService=O,this.stopper=new e.EventEmitter,this.searchTermSubject=new ce.x,this.columnDefs=[],this.rowData=[],this.showEntriesSize=g.gv9,this.pageSize=g.IV1,this.currOffset=0,this.frameworkComponents={dateCellRenderer:R,usernameCellRenderer:$},this.getPages=f.UQ,this.currentMonth=new Date,this.selectedMonth=(new Date).getMonth()+1,this.selectedYear=(new Date).getFullYear(),this.canViewAll=!1,this.canViewAllUsers=!1,this.canViewReportees=!1,this.canExportReportees=!1,this.canExportAllUsers=!1,this.visibilityList=g.tHm.slice(0,3),this.allUsers=[],this.usersList=[],this.reportees=[],this.departmentList=[],this.designationList=[],this.newDesignation="",this.newDepartment="",this.showLeftNav=!0,this.users=[],this.onlyReportees=[],this.currentDate=new Date,this.onPickerOpened=f.tK,this.showFilters=!1,this.attendanceFiltersKeyLabel=g.F9U,this.s3BucketUrl=N.N.s3ImageBucketURL,this.metaTitle.setTitle("CRM | Attendance"),this.headerTitle.setLangTitle("SIDEBAR.attendance"),this._store.dispatch(new B.oO),this._store.dispatch(new B.Nq),this._store.dispatch(new B.MI),this.maxDate=new Date,this.maxDate.setDate(this.maxDate.getDate()),this.gridOptions=this.gridOptionsService.getGridSettings(this),this._store.select(E.HU).pipe((0,v.R)(this.stopper)).subscribe(p=>this.isUserListLoading=p),this._store.select(E.Xf).pipe((0,v.R)(this.stopper)).subscribe(p=>{var m,y;this.userData=p,this.currentDate=(0,f.Xp)(null===(y=null===(m=this.userData)||void 0===m?void 0:m.timeZoneInfo)||void 0===y?void 0:y.baseUTcOffset)}),this._store.select(E.YR).pipe((0,v.R)(this.stopper)).subscribe(p=>this.isDesignationLoading=p),this._store.select(E.Fg).pipe((0,v.R)(this.stopper)).subscribe(p=>this.isDepartmentLoading=p),this._store.select(E.To).pipe((0,v.R)(this.stopper)).subscribe(p=>this.isReporteesWithInactiveLoading=p),this._store.select(E.Sh).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.users=p,this.usersList=null==p?void 0:p.map(m=>Object.assign(Object.assign({},m),{fullName:m.firstName+" "+m.lastName})),this.usersList=(0,f.vF)(this.usersList,""),this.allUsers=null==p?void 0:p.map(m=>Object.assign(Object.assign({},m),{fullName:m.firstName+" "+m.lastName})),this.allUsers=(0,f.vF)(this.allUsers,"")}),this._store.select(E.W6).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.reportees=p,this.onlyReportees=null==p?void 0:p.map(m=>Object.assign(Object.assign({},m),{fullName:m.firstName+" "+m.lastName})),this.onlyReportees=(0,f.vF)(this.onlyReportees,"")}),this._store.select(F.wb).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.rowData=p.items||[],this.totalCount=p.totalCount}),this._store.select(E.OA).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.designationList=p.slice().sort((m,y)=>m.name.localeCompare(y.name))}),this._store.select(E.By).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.departmentList=p.slice().sort((m,y)=>m.name.localeCompare(y.name))}),this._store.select(F.b$).pipe((0,v.R)(this.stopper)).subscribe(p=>{var m,y,T,k,D,L,oe,ge,ve,fe,xe,Ce;this.filtersPayload=p,this.pageSize=null===(m=this.filtersPayload)||void 0===m?void 0:m.pageSize,this.appliedFilter=Object.assign(Object.assign({},this.appliedFilter),{pageNumber:null===(y=this.filtersPayload)||void 0===y?void 0:y.pageNumber,pageSize:null===(T=this.filtersPayload)||void 0===T?void 0:T.pageSize,month:null===(k=this.filtersPayload)||void 0===k?void 0:k.Month,year:null===(D=this.filtersPayload)||void 0===D?void 0:D.Year,userName:null===(L=this.filtersPayload)||void 0===L?void 0:L.UserName,withTeam:null===(oe=this.filtersPayload)||void 0===oe?void 0:oe.IsWithTeam,users:null===(ge=this.filtersPayload)||void 0===ge?void 0:ge.UserIds,userStatus:null===(ve=this.filtersPayload)||void 0===ve?void 0:ve.UserStatus,department:null===(fe=this.filtersPayload)||void 0===fe?void 0:fe.DepartmentIds,designation:null===(xe=this.filtersPayload)||void 0===xe?void 0:xe.DesignationIds,reportsTo:null===(Ce=this.filtersPayload)||void 0===Ce?void 0:Ce.ReportsTo})}),this.searchTermSubject.subscribe(()=>{this.appliedFilter.pageNumber=1,this.filterAttendanceList()}),this.shareDataService.showLeftNav$.subscribe(p=>{this.showLeftNav=p}),this._store.select(F.Bv).pipe((0,v.R)(this.stopper)).subscribe(p=>{this.isAttendanceLoading=p}),this._store.select(P.Zu).pipe((0,v.R)(this.stopper)).subscribe(p=>{if(null==p||!p.length)return;const m=new Set(p);this.canExportAllUsers=m.has("Permissions.Attendance.ExportAllUsers"),this.canViewAllUsers=m.has("Permissions.Attendance.ViewAllUsers"),this.canExportReportees=m.has("Permissions.Attendance.ExportReportees"),this.canViewReportees=m.has("Permissions.Attendance.ViewReportees"),this.canAttendanceExportAllUsers=m.has("Permissions.Attendance.ExportAllUsers"),this.canAttendanceExportReportees=m.has("Permissions.Attendance.ExportReportees"),this.canViewAllUsers&&this._store.dispatch(new B.MI),this.canViewReportees&&this._store.dispatch(new B.m9),this.filterAttendanceList()}),this.generateCalendarHeaders()}ngOnInit(){this.selectedPageSize=10,this.cdr.detectChanges(),this.trackingService.trackFeature("Web.Attendance.Page.Attendance.Visit")}generateCalendarHeaders(){this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.rowHeight=60;const t=new Date(this.currentDate);this.checkCurrentMonth();const n=this.currentMonth.getMonth(),a=this.selectedYear,r=new Date(a,n+1,0).getDate();this.gridOptions.columnDefs=[{headerName:"User Name",field:"User Name",pinned:window.innerWidth>480?"left":null,lockPinned:!0,cellClass:"lock-pinned",minWidth:240,valueGetter:d=>[d.data],cellRenderer:$},{headerName:"No.of Days",field:"No.of Days",filter:!1,minWidth:95,maxWidth:95,resizable:!1,valueGetter:d=>{var u;return[null===(u=d.data)||void 0===u?void 0:u.totalLoggedInDays]},cellRenderer:d=>`<p class="pl-40">${d.value}</p>`}];for(let d=1;d<=r;d++){const u=new Date(a,n,d);let A=!0;u>t&&(A=!1);const O=`${u.toLocaleString("default",{month:"short"})}, ${d}`;this.gridOptions.columnDefs.push({headerName:O,field:`day_${d}`,cellRenderer:R,minWidth:120,maxWidth:120,resizable:!1,valueGetter:m=>[m.data?{date:(0,f.Zf)(u),data:m.data,pastDate:A}:""]})}}previousMonth(){const t=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth()-1,1),n=new Date(this.currentDate);this.checkCurrentMonth(),this.trackingService.trackFeature("Web.Attendance.PreviousMonthFilter.Click"),t<n&&(this.currentMonth=t,this.selectedMonth=this.currentMonth.getMonth()+1,this.selectedYear=this.currentMonth.getFullYear(),this.generateCalendarHeaders()),this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{Month:this.selectedMonth,Year:this.selectedYear}),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC),this.generateCalendarHeaders()}nextMonth(){const t=new Date(this.currentMonth.getFullYear(),this.currentMonth.getMonth()+1,1),n=new Date(this.currentDate);this.checkCurrentMonth(),this.trackingService.trackFeature("Web.Attendance.NextMonthFilter.Click"),t<=n&&(this.currentMonth=t,this.selectedMonth=this.currentMonth.getMonth()+1,this.selectedYear=this.currentMonth.getFullYear(),this.generateCalendarHeaders()),this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{Month:this.selectedMonth,Year:this.selectedYear}),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC),this.generateCalendarHeaders()}openAttendanceTracker(){let t={trackerPermission:this.canAttendanceExportAllUsers?0:1};this.trackingService.trackFeature("Web.Attendance.Button.ExportTracker.Click"),this._store.dispatch(new C.Tc(t,1,10)),this.modalService.show(he,{class:"modal-1000 modal-dialog-centered h-100 tb-modal-unset"})}monthChanged(t){new Date(t)>new Date||(this.selectedMonthAndYear=t,this.onDateSelection()),this.dt1.close()}onDateSelection(){this.currentMonth=this.selectedMonthAndYear,this.selectedMonth=g.ggi.indexOf(this.selectedMonthAndYear.toString().slice(4,7))+1,this.selectedYear=this.selectedMonthAndYear.toString().slice(11,15),this.checkCurrentMonth();const t=new Date(this.currentDate);this.selectedMonthAndYear<=t&&(this.currentMonth=this.selectedMonthAndYear,this.selectedMonth=g.ggi.indexOf(this.selectedMonthAndYear.toString().slice(4,7))+1,this.selectedYear=this.selectedMonthAndYear.toString().slice(11,15),this.generateCalendarHeaders()),this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{Month:this.selectedMonth,Year:this.selectedYear}),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC),this.generateCalendarHeaders()}checkCurrentMonth(){const t=new Date(this.currentDate),n=t.getFullYear(),a=t.getMonth();this.isPresentMonth=+this.selectedYear==+n&&+this.currentMonth.getMonth()==+a,this.selectedMonthAndYear=new Date(`${this.selectedYear}-${this.currentMonth.getMonth()+1}-01`)}openAdvFiltersModal(t){this.modalService.show(t,{class:"ip-modal-unset  top-full-modal"}),this.trackingService.trackFeature("Web.Attendance.Button.AdvancedFilter.Click")}filterAttendanceList(){var t,n,a,r,d,u,A,O,p,m,y,T,k;this.appliedFilter.pageNumber=1,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:null===(t=this.appliedFilter)||void 0===t?void 0:t.pageNumber,pageSize:this.pageSize,Month:this.selectedMonth,Year:this.selectedYear,UserName:this.searchTerm,IsWithTeam:this.appliedFilter.withTeam,UserIds:this.appliedFilter.users,SearchText:this.searchTerm,DepartmentIds:this.appliedFilter.department,DesignationIds:this.appliedFilter.designation,UserStatus:this.appliedFilter.userStatus,ReportsTo:this.appliedFilter.reportsTo,AttendancePermission:this.canViewAllUsers?0:1,ExportPermission:this.canExportAllUsers?0:1,timeZoneId:(null===(a=null===(n=this.userData)||void 0===n?void 0:n.timeZoneInfo)||void 0===a?void 0:a.timeZoneId)||(0,f.p5)(),baseUTcOffset:(null===(d=null===(r=this.userData)||void 0===r?void 0:r.timeZoneInfo)||void 0===d?void 0:d.baseUTcOffset)||(0,f.Bj)()}),this.showFilters=!!((null===(A=null===(u=this.appliedFilter)||void 0===u?void 0:u.users)||void 0===A?void 0:A.length)||(null===(p=null===(O=this.appliedFilter)||void 0===O?void 0:O.department)||void 0===p?void 0:p.length)||(null===(y=null===(m=this.appliedFilter)||void 0===m?void 0:m.designation)||void 0===y?void 0:y.length)||(null===(k=null===(T=this.appliedFilter)||void 0===T?void 0:T.reportsTo)||void 0===k?void 0:k.length)),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC)}getArrayOfFilters(t,n){var a;return["year","withTeam","userStatus","pageNumber","pageSize","month"].includes(t)||0===(null==n?void 0:n.length)?[]:null===(a=null==n?void 0:n.toString())||void 0===a?void 0:a.split(",")}onRemoveFilter(t,n){var a;this.appliedFilter[t]=null===(a=this.appliedFilter[t])||void 0===a?void 0:a.filter(r=>r!==n),this.filterAttendanceList()}getReportsName(t){var n;let a="";return null===(n=this.users)||void 0===n||n.forEach(r=>{r.id===t&&(a=r.firstName+" "+r.lastName)}),a}getDepartmentName(t){let n="";return this.departmentList.forEach(a=>{a.id===t&&(n=a.name)}),n}getDesignationName(t){let n="";return this.designationList.forEach(a=>{a.id===t&&(n=a.name)}),n}assignCount(){this.pageSize=this.selectedPageSize,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageSize:this.pageSize,pageNumber:1}),this.trackingService.trackFeature(`Web.Attendance.Option.${this.pageSize}.Click`),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC),this.currOffset=0}applyFilter(){this.filterAttendanceList(),this.modalService.hide()}reset(){this.appliedFilter={pageNumber:1,pageSize:this.pageSize},this.filterAttendanceList()}currentVisibility(t){var n,a,r,d;if(this.appliedFilter.userStatus=t,this.appliedFilter.pageNumber=1,this.filterAttendanceList(),this.canViewAllUsers){switch(t){case 1:this.trackingService.trackFeature("Web.Attendance.Menu.Active.Click"),this.allUsers=null===(n=this.users)||void 0===n?void 0:n.filter(u=>u.isActive);break;case 2:this.trackingService.trackFeature("Web.Attendance.Menu.Inactive.Click"),this.allUsers=null===(a=this.users)||void 0===a?void 0:a.filter(u=>!u.isActive);break;case null:this.trackingService.trackFeature("Web.Attendance.Menu.All.Click"),this.allUsers=this.users}this.allUsers=(0,f.vF)(this.allUsers,"")}else{switch(t){case 1:this.onlyReportees=null===(r=this.reportees)||void 0===r?void 0:r.filter(u=>u.isActive);break;case 2:this.onlyReportees=null===(d=this.reportees)||void 0===d?void 0:d.filter(u=>!u.isActive);break;case null:this.onlyReportees=this.reportees}this.onlyReportees=(0,f.vF)(this.onlyReportees,"")}}onPageChange(t){this.currOffset=t,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageSize:this.pageSize,pageNumber:t+1}),this._store.dispatch(new C.GZ(this.filtersPayload)),this._store.dispatch(new C.OC)}exportAttendanceReport(){this._store.dispatch(new C.Th([])),this.filterAttendanceList(),this.trackingService.trackFeature("Web.Attendance.Button.Export.Click"),this.modalService.show(be.F,Object.assign({},{class:"modal-400 modal-dialog-centered ph-modal-unset",initialState:{payload:this.filtersPayload,class:"modal-400 modal-dialog-centered ph-modal-unset"}}))}onGridReady(t){this.gridApi=t.api,this.gridColumnApi=t.columnApi}onSearch(t){if("Enter"===t.key){if(!this.searchTerm)return;this.trackingService.trackFeature("Web.Attendance.DataEntry.Search.DataEntry"),this.searchTermSubject.next(this.searchTerm)}}isEmptyInput(t){(""===this.searchTerm||null===this.searchTerm)&&this.searchTermSubject.next("")}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(ke.g),e.\u0275\u0275directiveInject(Q.tT),e.\u0275\u0275directiveInject(X.yh),e.\u0275\u0275directiveInject(J.t),e.\u0275\u0275directiveInject(Se.Dx),e.\u0275\u0275directiveInject(Ie.u),e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(Ee.e))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["attendance"]],viewQuery:function(t,n){if(1&t&&e.\u0275\u0275viewQuery(Oe,5),2&t){let a;e.\u0275\u0275queryRefresh(a=e.\u0275\u0275loadQuery())&&(n.dt1=a.first)}},features:[e.\u0275\u0275InheritDefinitionFeature],decls:1,vars:1,consts:[[4,"ngIf"],[1,"flex-between","bg-white","border-bottom","px-24","py-8","flex-grow-1","z-index-1021"],[1,"align-center","top-nav-bar","text-nowrap","ip-scrollbar","scroll-hide"],[4,"ngFor","ngForOf"],["class","btn-coal","id","btnAttendanceTracker","data-automate-id","btnAttendanceTracker",3,"click",4,"ngIf"],[1,"px-24"],[1,"d-flex","ip-flex-col","bg-white","w-100","border-gray","mt-20"],[1,"align-center","pl-10","border-end","ip-br-0","flex-grow-1","no-validation"],[1,"align-center","w-100","py-12"],[1,"search","icon","ic-search","ic-sm","ic-slate-90","mr-12","ph-mr-4"],["name","search","autocomplete","off",1,"border-0","outline-0","w-100",3,"placeholder","ngModel","keydown","input","ngModelChange"],[1,"text-muted","text-nowrap","ph-d-none","mr-10"],["class","bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top",3,"click",4,"ngIf"],[1,"p-8","flex-center","cursor-pointer","tb-flex-grow-1","ph-w-40px","ph-flex-grow-unset",3,"click"],[1,"icon","ic-filter-solid","ic-xxs","ic-black"],[1,"fw-600","ph-d-none","text-nowrap","ml-10"],[1,"d-flex","ip-br-top"],[1,"border-end","px-10","py-4","align-center","ip-flex-grow-1"],[1,"border","p-10","br-4","cursor-pointer",3,"click"],[1,"icon","ic-chevron-left","ic-xxs","ic-coal"],[1,"fw-700","text-large","text-black-200","px-12","cursor-pointer",3,"owlDateTimeTrigger"],[1,"w-0","p-0","border-0","border-remove",3,"ngModel","owlDateTimeTrigger","max","owlDateTime"],["startView","year",3,"yearOnly","pickerType","afterPickerOpen","monthSelected"],["dt1",""],[1,"icon","ic-chevron-right","ic-xxs","ic-coal"],[1,"show-dropdown-white","align-center","position-relative"],[1,"fw-600","position-absolute","left-5","z-index-2"],[1,"tb-d-none"],["bindValue","id","ResizableDropdown","",1,"w-150","tb-w-120px",3,"virtualScroll","placeholder","ngModel","searchable","ngModelChange","change"],["name","showEntriesSize",3,"value",4,"ngFor","ngForOf"],[1,"bg-white","px-4","py-8","tb-w-100-50",3,"ngClass"],["attendanceData",""],[4,"ngIf","ngIfElse"],["loader",""],["AdvancedFilters",""],[1,"cursor-pointer",3,"title","click"],[1,"align-center"],["width","22","height","22",3,"type","appImage","alt"],[1,"text-large","ml-8","mr-16"],["id","btnAttendanceTracker","data-automate-id","btnAttendanceTracker",1,"btn-coal",3,"click"],[1,"ic-tracker","icon","ic-xxs"],[1,"ml-8","ph-d-none"],[1,"bg-accent-green","text-white","px-20","py-12","h-100","align-center","cursor-pointer","border-start","w-70px","tb-br-top",3,"click"],["name","showEntriesSize",3,"value"],[1,"bg-secondary","flex-between"],[1,"br-4","scrollbar","d-flex","scroll-hide","w-100"],["class","d-flex",4,"ngFor","ngForOf"],[1,"px-8","py-4","bg-slate-120","m-4","br-4","text-mud","text-center","fw-semi-bold","text-nowrap","cursor-pointer",3,"click"],[1,"d-flex"],["class","px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap",4,"ngFor","ngForOf"],[1,"px-8","py-4","bg-slate-120","m-4","br-4","text-mud","text-center","fw-semi-bold","text-nowrap"],[1,"icon","ic-cancel","ic-dark","ic-x-xs","cursor-pointer","text-light-slate","ml-4",3,"click"],[1,"attendance-grid"],[1,"ag-theme-alpine",3,"pagination","paginationPageSize","gridOptions","rowData","frameworkComponents","suppressPaginationPanel","columnDefs","alwaysShowHorizontalScroll","alwaysShowVerticalScroll","gridReady"],["agGrid",""],[1,"flex-between","my-20","ip-flex-col","ip-flex-start"],[1,"dot","dot-xsm","bg-pink-700","mr-6","border"],[1,"fw-semi-bold","text-sm","text-black-200","mr-10"],[1,"dot","dot-xsm","bg-blue-900","mr-6","border"],[1,"align-center","ip-mt-20"],["class","mr-10",4,"ngIf"],[3,"offset","limit","range","size","pageChange"],[1,"mr-10"],[1,"flex-center-col","h-100-337"],["src","assets/images/layered-cards.svg",3,"alt"],[1,"header-3","fw-600","text-center"],[1,"flex-center","h-100-200"],[1,"lead-adv-filter","p-30","bg-white","brbl-15","brbr-15"],[1,"adv-filter"],[1,"d-flex","w-100","flex-wrap","ng-select-sm"],[1,"flex-column","w-25","tb-w-33","ip-w-50","ph-w-100"],[1,"justify-between","align-end","mr-20"],[1,"field-label"],[1,"checkbox-container","mb-4"],["type","checkbox",3,"ngModel","ngModelChange"],[1,"checkmark"],[1,"mr-20","ph-mr-0"],["name","user","ResizableDropdown","","bindLabel","fullName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngClass","ngModelChange"],["ng-label-tmp",""],["ng-option-tmp",""],["name","reportsTo","ResizableDropdown","","bindLabel","fullName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngClass","ngModelChange"],["name","designation","ResizableDropdown","","bindLabel","name","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngClass","ngModelChange"],["name","department","ResizableDropdown","","bindValue","id","bindLabel","name",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngClass","ngModelChange"],[1,"flex-end","mt-10","tb-mr-20","ph-mr-0"],[1,"mr-20","fw-semi-bold","text-mud","cursor-pointer",3,"click"],[1,"btn-gray",3,"click"],[1,"btn-coal","ml-20",3,"click"],[1,"ic-cancel","ic-dark","icon","ic-x-xs","mr-4",3,"click"],[1,"ng-value-label"],[1,"flex-between"],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],[1,"text-truncate-1","break-all"],["class","text-disabled",4,"ngIf"],[1,"text-disabled"]],template:function(t,n){1&t&&e.\u0275\u0275template(0,Xe,52,42,"div",0),2&t&&e.\u0275\u0275property("ngIf",n.canViewAllUsers||n.canViewReportees)},dependencies:[l.mk,l.sg,l.O5,re.w9,re.jq,re.ir,re.mR,_e.sZ,_e.BO,_e.hV,q.Q,ee.S,Ae.s,c.t,Y.N8,x.Fj,x.Wl,x.JJ,x.On,l.uU,l.Nd,w.X$],encapsulation:2}),i})(),pathMatch:"full"}];let et=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({imports:[H.Bz.forChild(qe),H.Bz]}),i})(),tt=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.\u0275\u0275defineNgModule({type:i}),i.\u0275inj=e.\u0275\u0275defineInjector({providers:[w.sK],imports:[l.ez,et,se.m,Y.sF,x.UX,x.u5,V.Y4,w.aw.forChild({loader:{provide:w.Zw,useFactory:K.gS,deps:[I.eN]},extend:!0}),M.CT.forRoot({player:K.xd})]}),i})()},84766:(ye,Z,s)=>{s.d(Z,{y:()=>l});class l{constructor(){this.hideFloatingFilter=!0}onGridReady(x){this.gridApi=x.api,x.api.sizeColumnsToFit(),this.gridColumnApi=x.columnApi}onFloatingFilterUpdated(x){this.hideFloatingFilter=x}stringComparator(x,w,Y,M,K){return Array.isArray(x)&&Array.isArray(w)?x[0].toLowerCase()>w[0].toLowerCase()?1:-1:"string"==typeof x&&"string"==typeof w?x.toLowerCase()>w.toLowerCase()?1:-1:K&&"string"==typeof w?-1:1}}},35309:(ye,Z,s)=>{s.d(Z,{F:()=>ue});var l=s(5e3),I=s(93075),x=s(82722),w=s(61021),Y=s(74775),M=s(42223),K=s(82667),se=s(40553),H=s(97674),e=s(39830),ce=s(8577),v=s(78990),g=s(73175),f=s(30166),V=s(32049),de=s(38827),E=s(65620),N=s(69808),Q=s(46302),X=s(18995);function ee(b,U){if(1&b&&(l.\u0275\u0275elementStart(0,"div",12)(1,"div",13),l.\u0275\u0275element(2,"input",14),l.\u0275\u0275elementStart(3,"label",15),l.\u0275\u0275text(4),l.\u0275\u0275elementEnd()()()),2&b){const h=U.$implicit,_=l.\u0275\u0275nextContext(2);l.\u0275\u0275advance(2),l.\u0275\u0275property("id",(null==h?null:h.value)+"notesTypeOptionRadio")("value",h.value)("formControl",_.notesType),l.\u0275\u0275advance(1),l.\u0275\u0275property("for",(null==h?null:h.value)+"notesTypeOptionRadio"),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate1(" ",h.label," ")}}function pe(b,U){if(1&b&&(l.\u0275\u0275elementStart(0,"div",10),l.\u0275\u0275template(1,ee,5,5,"div",11),l.\u0275\u0275elementEnd()),2&b){const h=l.\u0275\u0275nextContext();l.\u0275\u0275advance(1),l.\u0275\u0275property("ngForOf",h.notesTypeOptions)}}function me(b,U){if(1&b&&(l.\u0275\u0275elementStart(0,"div",16)(1,"div",17)(2,"div",18),l.\u0275\u0275text(3),l.\u0275\u0275pipe(4,"translate"),l.\u0275\u0275elementEnd(),l.\u0275\u0275element(5,"div",19),l.\u0275\u0275pipe(6,"translate"),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(7,"form-errors-wrapper",20),l.\u0275\u0275element(8,"input",21),l.\u0275\u0275pipe(9,"translate"),l.\u0275\u0275elementEnd()()),2&b){const h=l.\u0275\u0275nextContext();l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(4,5,"GLOBAL.notes-count")),l.\u0275\u0275advance(2),l.\u0275\u0275propertyInterpolate("title",l.\u0275\u0275pipeBind1(6,7,"GLOBAL.between-1-to-100-most-recently")),l.\u0275\u0275advance(2),l.\u0275\u0275property("control",h.notesCount),l.\u0275\u0275advance(1),l.\u0275\u0275propertyInterpolate("placeholder",l.\u0275\u0275pipeBind1(9,9,"GLOBAL.ex-3")),l.\u0275\u0275property("formControl",h.notesCount)}}let ue=(()=>{class b{constructor(h,_){this.modalRef=h,this.store=_,this.stopper=new l.EventEmitter,this.notesType=new I.NI(!1),this.notesCount=new I.NI(3,I.kI.required),this.notesTypeOptions=[{value:!1,label:"Exclude Notes"},{value:!0,label:"Include Notes"}],this.currentDate=new Date}ngOnInit(){this.store.select(V.Xf).pipe((0,x.R)(this.stopper)).subscribe(h=>{var _,S;this.userData=h,this.currentDate=(0,w.Xp)(null===(S=null===(_=this.userData)||void 0===_?void 0:_.timeZoneInfo)||void 0===S?void 0:S.baseUTcOffset)})}getExportLabel(h){return{"lead/new/all":"lead(s)",prospect:"data","prospect/custom-filters":"data","Property/new/all":"property(s)",project:"project(s)",attendance:"attendance","team/id":"member(s)",agency:"agency(s)","user/getallusers":"user(s)",channelpartner:"channel partner(s)",campaign:"campaign(s)",Listing:"listing property(s)"}[h]||"reports"}exportData(){var h,_,S,j,z,G,R,P,W,$,C,F,te,B,ne,J,q;let c=Object.assign(Object.assign({},this.payload),{FileName:this.formatDateTime()});switch(null!=c&&c.IsWithTeam&&(c.IsWithTeam=!0),delete c.pageNumber,delete c.pageSize,delete c.PageNumber,delete c.PageSize,Object.entries(c).forEach(([ie,ae])=>{if(Array.isArray(ae)&&void 0!==ae[0]){const he=[(0,w.sn)(ie,ae)];c[ie]=he.flat()}}),c.path){case"lead/new/all":c=Object.assign(Object.assign(Object.assign({},c),{AgencyNames:"string"==typeof(null===(h=this.payload)||void 0===h?void 0:h.AgencyNames)?[null===(_=this.payload)||void 0===_?void 0:_.AgencyNames]:null===(S=this.payload)||void 0===S?void 0:S.AgencyNames,StatusIds:"string"==typeof(null===(j=this.payload)||void 0===j?void 0:j.StatusIds)?[null===(z=this.payload)||void 0===z?void 0:z.StatusIds]:null===(G=this.payload)||void 0===G?void 0:G.StatusIds,assignTo:"string"==typeof(null===(R=this.payload)||void 0===R?void 0:R.assignTo)?[null===(P=this.payload)||void 0===P?void 0:P.assignTo]:null===(W=this.payload)||void 0===W?void 0:W.assignTo,Projects:"string"==typeof(null===($=this.payload)||void 0===$?void 0:$.Projects)?[null===(C=this.payload)||void 0===C?void 0:C.Projects]:null===(F=this.payload)||void 0===F?void 0:F.Projects,SubSources:"string"==typeof(null===(te=this.payload)||void 0===te?void 0:te.SubSources)?[null===(B=this.payload)||void 0===B?void 0:B.SubSources]:null===(ne=this.payload)||void 0===ne?void 0:ne.SubSources,IsWithNotes:this.notesType.value}),!0===this.notesType.value&&{notesCount:this.notesCount.value}),this.store.dispatch(new se.eFb(c));break;case"prospect/custom-filters":case"prospect":c=Object.assign(Object.assign({},c),{SecondLevelFilter:null!=c&&c.SecondLevelFilter?null==c?void 0:c.SecondLevelFilter:0}),this.store.dispatch(new K.tN(c));break;case"project":c=Object.assign(Object.assign({},c),{ProjectType:null===(J=["All","Residential","Commercial","Agricultural"])||void 0===J?void 0:J.indexOf(null===(q=this.payload)||void 0===q?void 0:q.ProjectType)}),this.store.dispatch(new ce.Mh(c));break;case"Property/new/all":this.store.dispatch(new v.xQ(c));break;case"attendance":this.store.dispatch(new Y.gh(c));break;case"report/user/new/status":this.store.dispatch(new g.Mh4(c));break;case"report/project/status/new":this.store.dispatch(new g.iOh(c));break;case"report/source/status/new":this.store.dispatch(new g.yI5(c));break;case"report/subsource/status/new":this.store.dispatch(new g.y9n(c));break;case"report/agency/status/new":this.store.dispatch(new g.m8k(c));break;case"report/user/meetingandvisit/level1":this.store.dispatch(new g.mB7(c));break;case"report/activity/level9":this.store.dispatch(new g.jHq(c));break;case"datareport/activity":case"datareport/activity/communication":this.store.dispatch(new M.H_(c));break;case"report/substatus/bysubsource/updated":this.store.dispatch(new g.AQ2(c));break;case"report/substatus":this.store.dispatch(new g.iL3(c));break;case"report/user/call-log/new":this.store.dispatch(new g.r_e(c));break;case"report/project/bysubstatus/updated":this.store.dispatch(new g.Nbs(c));break;case"report/datewisesource":this.store.dispatch(new g.Ijz(c));break;case"datareport/user/status":this.store.dispatch(new M.aX(c));break;case"datareport/project/status":this.store.dispatch(new M.NQ(c));break;case"datareport/source/status":this.store.dispatch(new M.w3(c));break;case"datareport/subsource/status":this.store.dispatch(new M.ed(c));break;case"datareport/user/data-call-log":this.store.dispatch(new M.AK(c));break;case"report/uservssource":this.store.dispatch(new g.wo0(c));break;case"report/uservssubsource":this.store.dispatch(new g.Zv6(c));break;case"user/getallusers":this.store.dispatch(new f.Ml(c));break;case"report/user/meetingandvisit/new/level":this.store.dispatch(new g.VV3(c));break;case"agency":this.store.dispatch(new e.Jo(c));break;case"channelpartner":this.store.dispatch(new e.ny(c));break;case"campaign":this.store.dispatch(new e.oK(c));break;case"team/id":this.store.dispatch(new f.h2(c));break;case"report/cityvslead":this.store.dispatch(new g.XIv(c));break;case"Listing":this.store.dispatch(new H.Fh(c))}this.modalRef.hide()}formatDateTime(){const h=new Date(this.currentDate),_=W=>W.toString().padStart(2,"0");return`${_(h.getDate())}-${_(h.getMonth()+1)}-${h.getFullYear().toString().slice(-2)}_${_(h.getHours())}:${_(h.getMinutes())}:${_(h.getSeconds())}`}closePopup(){this.modalRef.hide()}}return b.\u0275fac=function(h){return new(h||b)(l.\u0275\u0275directiveInject(de.UZ),l.\u0275\u0275directiveInject(E.yh))},b.\u0275cmp=l.\u0275\u0275defineComponent({type:b,selectors:[["export-mail"]],decls:24,vars:20,consts:[[1,"p-20"],[1,"ic-close-secondary","ic-close-modal","ip-ic-close-modal",3,"click"],[1,"mt-20","text-center","fw-600"],[1,"text-gray","fw-600","mt-20","text-center"],[1,"header-5","text-coal"],["class","d-flex flex-wrap",4,"ngIf"],["class","w-100",4,"ngIf"],[1,"flex-center","mt-30"],[1,"btn-gray",3,"click"],[1,"btn-coal","ml-10",3,"disabled","click"],[1,"d-flex","flex-wrap"],["class","form-check form-check-inline",4,"ngFor","ngForOf"],[1,"form-check","form-check-inline"],[1,"align-center","mt-8"],["type","radio","name","notesType",1,"radio-check-input","border-remove",3,"id","value","formControl"],[1,"fw-600","text-dark-800","cursor-pointer","text-sm","ml-8",3,"for"],[1,"w-100"],[1,"align-end"],[1,"field-label-req"],[1,"icon","ic-circle-exclamation","ic-slate-110","cursor-pointer","ic-sm","ml-20","mb-6",3,"title"],["label","Notes Count",3,"control"],["type","number","min","1","max","100",3,"placeholder","formControl"]],template:function(h,_){1&h&&(l.\u0275\u0275elementStart(0,"div",0)(1,"a",1),l.\u0275\u0275listener("click",function(){return _.closePopup()}),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(2,"h4",2),l.\u0275\u0275text(3),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(4,"div",3)(5,"div"),l.\u0275\u0275text(6),l.\u0275\u0275pipe(7,"translate"),l.\u0275\u0275elementStart(8,"span",4),l.\u0275\u0275text(9),l.\u0275\u0275pipe(10,"translate"),l.\u0275\u0275elementEnd(),l.\u0275\u0275text(11),l.\u0275\u0275elementStart(12,"span",4),l.\u0275\u0275text(13),l.\u0275\u0275pipe(14,"translate"),l.\u0275\u0275elementEnd()()(),l.\u0275\u0275template(15,pe,2,1,"div",5),l.\u0275\u0275template(16,me,10,11,"div",6),l.\u0275\u0275elementStart(17,"div",7)(18,"button",8),l.\u0275\u0275listener("click",function(){return _.closePopup()}),l.\u0275\u0275text(19),l.\u0275\u0275pipe(20,"translate"),l.\u0275\u0275elementEnd(),l.\u0275\u0275elementStart(21,"button",9),l.\u0275\u0275listener("click",function(){return _.exportData()}),l.\u0275\u0275text(22),l.\u0275\u0275pipe(23,"translate"),l.\u0275\u0275elementEnd()()()),2&h&&(l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate1("Are you sure you want to export these ",_.getExportLabel(null==_.payload?null:_.payload.path),"?"),l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(7,10,"GLOBAL.clicking-on")),l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(10,12,"GLOBAL.confirm")),l.\u0275\u0275advance(2),l.\u0275\u0275textInterpolate1(" will enable the process of exporting the ",_.getExportLabel(null==_.payload?null:_.payload.path)," in an excel format. You can track the status of the export in "),l.\u0275\u0275advance(2),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(14,14,"GLOBAL.export-tracker")),l.\u0275\u0275advance(2),l.\u0275\u0275property("ngIf","lead/new/all"==_.payload.path),l.\u0275\u0275advance(1),l.\u0275\u0275property("ngIf","lead/new/all"==_.payload.path&&!0===_.notesType.value),l.\u0275\u0275advance(3),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(20,16,"BUTTONS.cancel")),l.\u0275\u0275advance(2),l.\u0275\u0275property("disabled",_.notesCount.invalid&&!0===_.notesType.value&&"lead/new/all"==_.payload.path),l.\u0275\u0275advance(1),l.\u0275\u0275textInterpolate(l.\u0275\u0275pipeBind1(23,18,"BUTTONS.confirm")))},dependencies:[I.Fj,I.wV,I._,I.JJ,I.qQ,I.Fd,N.sg,N.O5,Q.z,I.oH,X.X$],encapsulation:2}),b})()}}]);