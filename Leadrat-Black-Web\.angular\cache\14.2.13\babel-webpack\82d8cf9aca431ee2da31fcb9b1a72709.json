{"ast": null, "code": "import eq from './eq.js';\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\n\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n\n  return -1;\n}\n\nexport default assocIndexOf;", "map": null, "metadata": {}, "sourceType": "module"}