{"ast": null, "code": "import { isArray } from '../util/isArray';\nimport { race as raceStatic } from '../observable/race';\nexport function race(...observables) {\n  return function raceOperatorFunction(source) {\n    if (observables.length === 1 && isArray(observables[0])) {\n      observables = observables[0];\n    }\n\n    return source.lift.call(raceStatic(source, ...observables));\n  };\n} //# sourceMappingURL=race.js.map", "map": null, "metadata": {}, "sourceType": "module"}