{"ast": null, "code": "import createCtor from './_createCtor.js';\nimport root from './_root.js';\n/** Used to compose bitmasks for function metadata. */\n\nvar WRAP_BIND_FLAG = 1;\n/**\n * Creates a function that wraps `func` to invoke it with the optional `this`\n * binding of `thisArg`.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\n\nfunction createBind(func, bitmask, thisArg) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n      Ctor = createCtor(func);\n\n  function wrapper() {\n    var fn = this && this !== root && this instanceof wrapper ? Ctor : func;\n    return fn.apply(isBind ? thisArg : this, arguments);\n  }\n\n  return wrapper;\n}\n\nexport default createBind;", "map": null, "metadata": {}, "sourceType": "module"}