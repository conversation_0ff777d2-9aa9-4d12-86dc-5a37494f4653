@echo off
echo Multi-Language Translation Generator
echo ====================================
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if the English source file exists
if not exist "src\assets\i18n\en.json" (
    echo Error: English source file not found at src\assets\i18n\en.json
    echo Please make sure you're running this from the correct directory
    pause
    exit /b 1
)

echo Available options:
echo 1. Generate all languages (Tamil, Telugu, Bengali, Malayalam)
echo 2. Generate specific language
echo 3. Show help
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Generating all language translations...
    echo This may take several minutes...
    echo.
    node multi-language-translator.js src\assets\i18n\en.json
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo Available languages:
    echo ta - Tamil
    echo te - Telugu  
    echo bn - Bengali
    echo ml - Malayalam
    echo kn - Kannada
    echo.
    set /p lang="Enter language code: "
    echo.
    echo Generating %lang% translation...
    echo.
    node multi-language-translator.js src\assets\i18n\en.json %lang%
    goto :end
)

if "%choice%"=="3" (
    echo.
    node multi-language-translator.js
    goto :end
)

echo Invalid choice. Please run the script again.

:end
echo.
pause
