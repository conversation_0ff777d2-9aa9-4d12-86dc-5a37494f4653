{"ast": null, "code": "import assignValue from './_assignValue.js';\nimport baseAssignValue from './_baseAssignValue.js';\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\n\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n    var newValue = customizer ? customizer(object[key], source[key], key, object, source) : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n\n  return object;\n}\n\nexport default copyObject;", "map": null, "metadata": {}, "sourceType": "module"}