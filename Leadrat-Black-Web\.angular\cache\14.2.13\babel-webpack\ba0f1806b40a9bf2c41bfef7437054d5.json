{"ast": null, "code": "import getMapData from './_getMapData.js';\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\n\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nexport default mapCacheSet;", "map": null, "metadata": {}, "sourceType": "module"}