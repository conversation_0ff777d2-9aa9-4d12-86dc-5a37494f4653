{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function sampleTime(period, scheduler = async) {\n  return source => source.lift(new SampleTimeOperator(period, scheduler));\n}\n\nclass SampleTimeOperator {\n  constructor(period, scheduler) {\n    this.period = period;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));\n  }\n\n}\n\nclass SampleTimeSubscriber extends Subscriber {\n  constructor(destination, period, scheduler) {\n    super(destination);\n    this.period = period;\n    this.scheduler = scheduler;\n    this.hasValue = false;\n    this.add(scheduler.schedule(dispatchNotification, period, {\n      subscriber: this,\n      period\n    }));\n  }\n\n  _next(value) {\n    this.lastValue = value;\n    this.hasValue = true;\n  }\n\n  notifyNext() {\n    if (this.hasValue) {\n      this.hasValue = false;\n      this.destination.next(this.lastValue);\n    }\n  }\n\n}\n\nfunction dispatchNotification(state) {\n  let {\n    subscriber,\n    period\n  } = state;\n  subscriber.notifyNext();\n  this.schedule(state, period);\n} //# sourceMappingURL=sampleTime.js.map", "map": null, "metadata": {}, "sourceType": "module"}