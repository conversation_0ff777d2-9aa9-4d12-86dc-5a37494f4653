{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n/** `Object#toString` result references. */\n\nvar regexpTag = '[object RegExp]';\n/**\n * The base implementation of `_.isRegExp` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a regexp, else `false`.\n */\n\nfunction baseIsRegExp(value) {\n  return isObjectLike(value) && baseGetTag(value) == regexpTag;\n}\n\nexport default baseIsRegExp;", "map": null, "metadata": {}, "sourceType": "module"}