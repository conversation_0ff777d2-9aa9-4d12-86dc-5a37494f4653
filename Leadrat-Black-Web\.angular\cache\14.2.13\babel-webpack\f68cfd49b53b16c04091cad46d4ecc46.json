{"ast": null, "code": "import baseEach from './_baseEach.js';\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\n\nfunction baseSome(collection, predicate) {\n  var result;\n  baseEach(collection, function (value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\n\nexport default baseSome;", "map": null, "metadata": {}, "sourceType": "module"}