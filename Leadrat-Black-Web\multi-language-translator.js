const fs = require('fs');
const path = require('path');
const translate = require('translate-google');

class MultiLanguageTranslator {
    constructor() {
        this.cache = new Map();
        this.translatedCount = 0;
        this.totalCount = 0;
        this.supportedLanguages = {
            'ta': 'Tamil',
            'te': 'Telugu',
            'bn': 'Bengali',
            'ml': 'Malayalam',
            'kn': 'Kannada'
        };
    }

    /**
     * Get predefined translations for each language
     */
    getPredefinedTranslations(targetLang) {
        const translations = {
            'ta': this.getTamilTranslations(),
            'te': this.getTeluguTranslations(),
            'bn': this.getBengaliTranslations(),
            'ml': this.getMalayalamTranslations(),
            'kn': this.getKannadaTranslations()
        };

        return translations[targetLang] || {};
    }

    /**
     * Tamil translations
     */
    getTamilTranslations() {
        return {
            // Basic terms
            'hello world': 'வணக்கம் உலகம்',
            'the title of my app': 'என் பயன்பாட்டின் தலைப்பு',
            'settings': 'அமைப்புகள்',
            'today': 'இன்று',
            'all': 'அனைத்தும்',
            'new': 'புதிய',
            'old': 'பழைய',
            'overdue': 'தாமதமான',
            'upcoming': 'வரவிருக்கும்',
            'completed': 'முடிந்தது',
            'not interested': 'ஆர்வம் இல்லை',
            'select': 'தேர்ந்தெடுக்கவும்',
            'status': 'நிலை',
            'assigned to': 'ஒதுக்கப்பட்டது',
            'for': 'க்காக',
            'reset': 'மீட்டமை',
            'go': 'செல்',
            'no': 'இல்லை',
            'yes': 'ஆம்',
            'in': 'இல்',
            'lead': 'முன்னணி',
            'leads': 'முன்னணிகள்',
            'name': 'பெயர்',
            'show': 'காட்டு',
            'entries': 'உள்ளீடுகள்',
            'update': 'புதுப்பிக்கவும்',
            'details': 'விவரங்கள்',
            'personal': 'தனிப்பட்ட',
            'work': 'வேலை',
            'home': 'வீடு',
            'mobile': 'மொபைல்',
            'email': 'மின்னஞ்சல்',
            'phone': 'தொலைபேசி',
            'address': 'முகவரி',
            'city': 'நகரம்',
            'state': 'மாநிலம்',
            'country': 'நாடு',
            'pincode': 'அஞ்சல் குறியீடு',
            'save': 'சேமி',
            'cancel': 'ரத்து செய்',
            'delete': 'நீக்கு',
            'edit': 'திருத்து',
            'add': 'சேர்',
            'remove': 'அகற்று',
            'search': 'தேடு',
            'filter': 'வடிகட்டி',
            'sort': 'வரிசைப்படுத்து',
            'export': 'ஏற்றுமதி',
            'import': 'இறக்குமதி',
            'print': 'அச்சிடு',
            'download': 'பதிவிறக்கம்',
            'upload': 'பதிவேற்றம்',
            'submit': 'சமர்ப்பிக்கவும்',
            'close': 'மூடு',
            'open': 'திற',
            'view': 'பார்வை',
            'preview': 'முன்னோட்டம்',
            'next': 'அடுத்து',
            'previous': 'முந்தைய',
            'first': 'முதல்',
            'last': 'கடைசி',
            'page': 'பக்கம்',
            'of': 'இன்',
            'total': 'மொத்தம்',
            'loading': 'ஏற்றுகிறது',
            'please wait': 'தயவுசெய்து காத்திருக்கவும்',
            'error': 'பிழை',
            'success': 'வெற்றி',
            'warning': 'எச்சரிக்கை',
            'info': 'தகவல்',
            'confirm': 'உறுதிப்படுத்து',
            'are you sure': 'நீங்கள் உறுதியாக இருக்கிறீர்களா',
            'login': 'உள்நுழைவு',
            'logout': 'வெளியேறு',
            'register': 'பதிவு செய்',
            'forgot password': 'கடவுச்சொல்லை மறந்துவிட்டீர்களா',
            'username': 'பயனர்பெயர்',
            'password': 'கடவுச்சொல்',
            'remember me': 'என்னை நினைவில் வைத்துக்கொள்ளுங்கள்',
            'dashboard': 'டாஷ்போர்டு',
            'profile': 'சுயவிவரம்',
            'account': 'கணக்கு',
            'notifications': 'அறிவிப்புகள்',
            'messages': 'செய்திகள்',
            'help': 'உதவி',
            'support': 'ஆதரவு',
            'contact': 'தொடர்பு',
            'about': 'பற்றி',
            'privacy': 'தனியுரிமை',
            'terms': 'விதிமுறைகள்',
            'language': 'மொழி',
            'theme': 'தீம்',
            'dark': 'இருண்ட',
            'light': 'ஒளி',
            'auto': 'தானியங்கி'
        };
    }

    /**
     * Telugu translations
     */
    getTeluguTranslations() {
        return {
            // Basic terms
            'hello world': 'హలో వరల్డ్',
            'the title of my app': 'నా అప్లికేషన్ టైటిల్',
            'settings': 'సెట్టింగ్‌లు',
            'today': 'ఈరోజు',
            'all': 'అన్నీ',
            'new': 'కొత్త',
            'old': 'పాత',
            'overdue': 'గడువు దాటిన',
            'upcoming': 'రాబోయే',
            'completed': 'పూర్తయింది',
            'not interested': 'ఆసక్తి లేదు',
            'select': 'ఎంచుకోండి',
            'status': 'స్థితి',
            'assigned to': 'కు కేటాయించబడింది',
            'for': 'కోసం',
            'reset': 'రీసెట్',
            'go': 'వెళ్ళు',
            'no': 'లేదు',
            'yes': 'అవును',
            'in': 'లో',
            'lead': 'లీడ్',
            'leads': 'లీడ్‌లు',
            'name': 'పేరు',
            'show': 'చూపించు',
            'entries': 'ఎంట్రీలు',
            'update': 'అప్‌డేట్',
            'details': 'వివరాలు',
            'personal': 'వ్యక్తిగత',
            'work': 'పని',
            'home': 'ఇల్లు',
            'mobile': 'మొబైల్',
            'email': 'ఇమెయిల్',
            'phone': 'ఫోన్',
            'address': 'చిరునామా',
            'city': 'నగరం',
            'state': 'రాష్ట్రం',
            'country': 'దేశం',
            'pincode': 'పిన్‌కోడ్',
            'save': 'సేవ్',
            'cancel': 'రద్దు',
            'delete': 'తొలగించు',
            'edit': 'సవరించు',
            'add': 'జోడించు',
            'remove': 'తొలగించు',
            'search': 'వెతకండి',
            'filter': 'ఫిల్టర్',
            'sort': 'క్రమబద్ధీకరించు',
            'export': 'ఎగుమతి',
            'import': 'దిగుమతి',
            'print': 'ప్రింట్',
            'download': 'డౌన్‌లోడ్',
            'upload': 'అప్‌లోడ్',
            'submit': 'సమర్పించు',
            'close': 'మూసివేయి',
            'open': 'తెరువు',
            'view': 'వీక్షణ',
            'preview': 'ప్రివ్యూ',
            'next': 'తదుపరి',
            'previous': 'మునుపటి',
            'first': 'మొదటి',
            'last': 'చివరి',
            'page': 'పేజీ',
            'of': 'యొక్క',
            'total': 'మొత్తం',
            'loading': 'లోడవుతోంది',
            'please wait': 'దయచేసి వేచి ఉండండి',
            'error': 'లోపం',
            'success': 'విజయం',
            'warning': 'హెచ్చరిక',
            'info': 'సమాచారం',
            'confirm': 'నిర్ధారించు',
            'are you sure': 'మీరు ఖచ్చితంగా ఉన్నారా',
            'login': 'లాగిన్',
            'logout': 'లాగౌట్',
            'register': 'రిజిస్టర్',
            'forgot password': 'పాస్‌వర్డ్ మర్చిపోయారా',
            'username': 'యూజర్‌నేమ్',
            'password': 'పాస్‌వర్డ్',
            'remember me': 'నన్ను గుర్తుంచుకో',
            'dashboard': 'డాష్‌బోర్డ్',
            'profile': 'ప్రొఫైల్',
            'account': 'ఖాతా',
            'notifications': 'నోటిఫికేషన్‌లు',
            'messages': 'సందేశాలు',
            'help': 'సహాయం',
            'support': 'మద్దతు',
            'contact': 'సంప్రదించండి',
            'about': 'గురించి',
            'privacy': 'గోప్యత',
            'terms': 'నిబంధనలు',
            'language': 'భాష',
            'theme': 'థీమ్',
            'dark': 'చీకటి',
            'light': 'వెలుగు',
            'auto': 'ఆటో'
        };
    }

    /**
     * Bengali translations
     */
    getBengaliTranslations() {
        return {
            // Basic terms
            'hello world': 'হ্যালো ওয়ার্ল্ড',
            'the title of my app': 'আমার অ্যাপের শিরোনাম',
            'settings': 'সেটিংস',
            'today': 'আজ',
            'all': 'সব',
            'new': 'নতুন',
            'old': 'পুরানো',
            'overdue': 'মেয়াদোত্তীর্ণ',
            'upcoming': 'আসন্ন',
            'completed': 'সম্পন্ন',
            'not interested': 'আগ্রহী নয়',
            'select': 'নির্বাচন করুন',
            'status': 'অবস্থা',
            'assigned to': 'বরাদ্দ করা হয়েছে',
            'for': 'জন্য',
            'reset': 'রিসেট',
            'go': 'যান',
            'no': 'না',
            'yes': 'হ্যাঁ',
            'in': 'এ',
            'lead': 'লিড',
            'leads': 'লিডস',
            'name': 'নাম',
            'show': 'দেখান',
            'entries': 'এন্ট্রি',
            'update': 'আপডেট',
            'details': 'বিস্তারিত',
            'personal': 'ব্যক্তিগত',
            'work': 'কাজ',
            'home': 'বাড়ি',
            'mobile': 'মোবাইল',
            'email': 'ইমেইল',
            'phone': 'ফোন',
            'address': 'ঠিকানা',
            'city': 'শহর',
            'state': 'রাজ্য',
            'country': 'দেশ',
            'pincode': 'পিনকোড',
            'save': 'সংরক্ষণ',
            'cancel': 'বাতিল',
            'delete': 'মুছুন',
            'edit': 'সম্পাদনা',
            'add': 'যোগ করুন',
            'remove': 'সরান',
            'search': 'অনুসন্ধান',
            'filter': 'ফিল্টার',
            'sort': 'সাজান',
            'export': 'রপ্তানি',
            'import': 'আমদানি',
            'print': 'প্রিন্ট',
            'download': 'ডাউনলোড',
            'upload': 'আপলোড',
            'submit': 'জমা দিন',
            'close': 'বন্ধ',
            'open': 'খুলুন',
            'view': 'দেখুন',
            'preview': 'প্রিভিউ',
            'next': 'পরবর্তী',
            'previous': 'পূর্ববর্তী',
            'first': 'প্রথম',
            'last': 'শেষ',
            'page': 'পৃষ্ঠা',
            'of': 'এর',
            'total': 'মোট',
            'loading': 'লোড হচ্ছে',
            'please wait': 'অনুগ্রহ করে অপেক্ষা করুন',
            'error': 'ত্রুটি',
            'success': 'সফলতা',
            'warning': 'সতর্কতা',
            'info': 'তথ্য',
            'confirm': 'নিশ্চিত করুন',
            'are you sure': 'আপনি কি নিশ্চিত',
            'login': 'লগইন',
            'logout': 'লগআউট',
            'register': 'নিবন্ধন',
            'forgot password': 'পাসওয়ার্ড ভুলে গেছেন',
            'username': 'ব্যবহারকারীর নাম',
            'password': 'পাসওয়ার্ড',
            'remember me': 'আমাকে মনে রাখুন',
            'dashboard': 'ড্যাশবোর্ড',
            'profile': 'প্রোফাইল',
            'account': 'অ্যাকাউন্ট',
            'notifications': 'বিজ্ঞপ্তি',
            'messages': 'বার্তা',
            'help': 'সাহায্য',
            'support': 'সহায়তা',
            'contact': 'যোগাযোগ',
            'about': 'সম্পর্কে',
            'privacy': 'গোপনীয়তা',
            'terms': 'শর্তাবলী',
            'language': 'ভাষা',
            'theme': 'থিম',
            'dark': 'অন্ধকার',
            'light': 'আলো',
            'auto': 'অটো'
        };
    }

    /**
     * Malayalam translations
     */
    getMalayalamTranslations() {
        return {
            // Basic terms
            'hello world': 'ഹലോ വേൾഡ്',
            'the title of my app': 'എന്റെ ആപ്പിന്റെ ശീർഷകം',
            'settings': 'ക്രമീകരണങ്ങൾ',
            'today': 'ഇന്ന്',
            'all': 'എല്ലാം',
            'new': 'പുതിയ',
            'old': 'പഴയ',
            'overdue': 'കാലാവധി കഴിഞ്ഞ',
            'upcoming': 'വരാനിരിക്കുന്ന',
            'completed': 'പൂർത്തിയായി',
            'not interested': 'താൽപ്പര്യമില്ല',
            'select': 'തിരഞ്ഞെടുക്കുക',
            'status': 'നില',
            'assigned to': 'നിയോഗിച്ചത്',
            'for': 'വേണ്ടി',
            'reset': 'പുനഃസജ്ജമാക്കുക',
            'go': 'പോകുക',
            'no': 'ഇല്ല',
            'yes': 'അതെ',
            'in': 'ൽ',
            'lead': 'ലീഡ്',
            'leads': 'ലീഡുകൾ',
            'name': 'പേര്',
            'show': 'കാണിക്കുക',
            'entries': 'എൻട്രികൾ',
            'update': 'അപ്ഡേറ്റ്',
            'details': 'വിശദാംശങ്ങൾ',
            'personal': 'വ്യക്തിഗത',
            'work': 'ജോലി',
            'home': 'വീട്',
            'mobile': 'മൊബൈൽ',
            'email': 'ഇമെയിൽ',
            'phone': 'ഫോൺ',
            'address': 'വിലാസം',
            'city': 'നഗരം',
            'state': 'സംസ്ഥാനം',
            'country': 'രാജ്യം',
            'pincode': 'പിൻകോഡ്',
            'save': 'സേവ്',
            'cancel': 'റദ്ദാക്കുക',
            'delete': 'ഇല്ലാതാക്കുക',
            'edit': 'എഡിറ്റ്',
            'add': 'ചേർക്കുക',
            'remove': 'നീക്കം ചെയ്യുക',
            'search': 'തിരയുക',
            'filter': 'ഫിൽട്ടർ',
            'sort': 'ക്രമീകരിക്കുക',
            'export': 'എക്സ്പോർട്ട്',
            'import': 'ഇമ്പോർട്ട്',
            'print': 'പ്രിന്റ്',
            'download': 'ഡൗൺലോഡ്',
            'upload': 'അപ്ലോഡ്',
            'submit': 'സമർപ്പിക്കുക',
            'close': 'അടയ്ക്കുക',
            'open': 'തുറക്കുക',
            'view': 'കാണുക',
            'preview': 'പ്രിവ്യൂ',
            'next': 'അടുത്തത്',
            'previous': 'മുമ്പത്തെ',
            'first': 'ആദ്യത്തെ',
            'last': 'അവസാനത്തെ',
            'page': 'പേജ്',
            'of': 'ന്റെ',
            'total': 'ആകെ',
            'loading': 'ലോഡ് ചെയ്യുന്നു',
            'please wait': 'ദയവായി കാത്തിരിക്കുക',
            'error': 'പിശക്',
            'success': 'വിജയം',
            'warning': 'മുന്നറിയിപ്പ്',
            'info': 'വിവരം',
            'confirm': 'സ്ഥിരീകരിക്കുക',
            'are you sure': 'നിങ്ങൾക്ക് ഉറപ്പാണോ',
            'login': 'ലോഗിൻ',
            'logout': 'ലോഗൗട്ട്',
            'register': 'രജിസ്റ്റർ',
            'forgot password': 'പാസ്വേഡ് മറന്നോ',
            'username': 'ഉപയോക്തൃനാമം',
            'password': 'പാസ്വേഡ്',
            'remember me': 'എന്നെ ഓർക്കുക',
            'dashboard': 'ഡാഷ്ബോർഡ്',
            'profile': 'പ്രൊഫൈൽ',
            'account': 'അക്കൗണ്ട്',
            'notifications': 'അറിയിപ്പുകൾ',
            'messages': 'സന്ദേശങ്ങൾ',
            'help': 'സഹായം',
            'support': 'പിന്തുണ',
            'contact': 'ബന്ധപ്പെടുക',
            'about': 'കുറിച്ച്',
            'privacy': 'സ്വകാര്യത',
            'terms': 'നിബന്ധനകൾ',
            'language': 'ഭാഷ',
            'theme': 'തീം',
            'dark': 'ഇരുണ്ട',
            'light': 'വെളിച്ചം',
            'auto': 'ഓട്ടോ'
        };
    }

    /**
     * Kannada translations (existing)
     */
    getKannadaTranslations() {
        return {
            // Basic terms
            'hello world': 'ಹಲೋ ವರ್ಲ್ಡ್',
            'the title of my app': 'ನನ್ನ ಅಪ್ಲಿಕೇಶನ್‌ನ ಶೀರ್ಷಿಕೆ',
            'settings': 'ಸೆಟ್ಟಿಂಗ್‌ಗಳು',
            'today': 'ಇಂದು',
            'all': 'ಎಲ್ಲಾ',
            'new': 'ಹೊಸ',
            'old': 'ಹಳೆಯ',
            'overdue': 'ಅವಧಿ ಮೀರಿದ',
            'upcoming': 'ಮುಂಬರುವ',
            'completed': 'ಪೂರ್ಣಗೊಂಡ',
            'not interested': 'ಆಸಕ್ತಿ ಇಲ್ಲ',
            'select': 'ಆಯ್ಕೆ ಮಾಡಿ',
            'status': 'ಸ್ಥಿತಿ',
            'assigned to': 'ಗೆ ನಿಯೋಜಿಸಲಾಗಿದೆ',
            'for': 'ಗಾಗಿ',
            'reset': 'ಮರುಹೊಂದಿಸಿ',
            'go': 'ಹೋಗಿ',
            'no': 'ಇಲ್ಲ',
            'yes': 'ಹೌದು',
            'in': 'ನಲ್ಲಿ',
            'lead': 'ಲೀಡ್',
            'leads': 'ಲೀಡ್‌ಗಳು',
            'name': 'ಹೆಸರು',
            'show': 'ತೋರಿಸಿ',
            'entries': 'ನಮೂದುಗಳು',
            'update': 'ನವೀಕರಿಸಿ',
            'details': 'ವಿವರಗಳು',
            'personal': 'ವೈಯಕ್ತಿಕ',
            'work': 'ಕೆಲಸ',
            'home': 'ಮನೆ',
            'mobile': 'ಮೊಬೈಲ್',
            'email': 'ಇಮೇಲ್',
            'phone': 'ಫೋನ್',
            'address': 'ವಿಳಾಸ',
            'city': 'ನಗರ',
            'state': 'ರಾಜ್ಯ',
            'country': 'ದೇಶ',
            'pincode': 'ಪಿನ್‌ಕೋಡ್',
            'save': 'ಉಳಿಸಿ',
            'cancel': 'ರದ್ದುಮಾಡಿ',
            'delete': 'ಅಳಿಸಿ',
            'edit': 'ಸಂಪಾದಿಸಿ',
            'add': 'ಸೇರಿಸಿ',
            'remove': 'ತೆಗೆದುಹಾಕಿ',
            'search': 'ಹುಡುಕಿ',
            'filter': 'ಫಿಲ್ಟರ್',
            'sort': 'ವಿಂಗಡಿಸಿ',
            'export': 'ರಫ್ತು',
            'import': 'ಆಮದು',
            'print': 'ಮುದ್ರಿಸಿ',
            'download': 'ಡೌನ್‌ಲೋಡ್',
            'upload': 'ಅಪ್‌ಲೋಡ್',
            'submit': 'ಸಲ್ಲಿಸಿ',
            'close': 'ಮುಚ್ಚಿ',
            'open': 'ತೆರೆಯಿರಿ',
            'view': 'ವೀಕ್ಷಿಸಿ',
            'preview': 'ಪೂರ್ವವೀಕ್ಷಣೆ',
            'next': 'ಮುಂದಿನ',
            'previous': 'ಹಿಂದಿನ',
            'first': 'ಮೊದಲ',
            'last': 'ಕೊನೆಯ',
            'page': 'ಪುಟ',
            'of': 'ರ',
            'total': 'ಒಟ್ಟು',
            'loading': 'ಲೋಡ್ ಆಗುತ್ತಿದೆ',
            'please wait': 'ದಯವಿಟ್ಟು ಕಾಯಿರಿ',
            'error': 'ದೋಷ',
            'success': 'ಯಶಸ್ಸು',
            'warning': 'ಎಚ್ಚರಿಕೆ',
            'info': 'ಮಾಹಿತಿ',
            'confirm': 'ದೃಢೀಕರಿಸಿ',
            'are you sure': 'ನೀವು ಖಚಿತವಾಗಿದ್ದೀರಾ',
            'login': 'ಲಾಗಿನ್',
            'logout': 'ಲಾಗೌಟ್',
            'register': 'ನೋಂದಾಯಿಸಿ',
            'forgot password': 'ಪಾಸ್‌ವರ್ಡ್ ಮರೆತಿದ್ದೀರಾ',
            'username': 'ಬಳಕೆದಾರ ಹೆಸರು',
            'password': 'ಪಾಸ್‌ವರ್ಡ್',
            'remember me': 'ನನ್ನನ್ನು ನೆನಪಿಡಿ',
            'dashboard': 'ಡ್ಯಾಶ್‌ಬೋರ್ಡ್',
            'profile': 'ಪ್ರೊಫೈಲ್',
            'account': 'ಖಾತೆ',
            'notifications': 'ಅಧಿಸೂಚನೆಗಳು',
            'messages': 'ಸಂದೇಶಗಳು',
            'help': 'ಸಹಾಯ',
            'support': 'ಬೆಂಬಲ',
            'contact': 'ಸಂಪರ್ಕಿಸಿ',
            'about': 'ಬಗ್ಗೆ',
            'privacy': 'ಗೌಪ್ಯತೆ',
            'terms': 'ನಿಯಮಗಳು',
            'language': 'ಭಾಷೆ',
            'theme': 'ಥೀಮ್',
            'dark': 'ಗಾಢ',
            'light': 'ಬೆಳಕು',
            'auto': 'ಸ್ವಯಂ'
        };
    }

    /**
     * Translate text using predefined translations or API fallback
     */
    async translateText(text, targetLang) {
        if (!text || !text.trim()) {
            return text;
        }

        // Check cache first
        const cacheKey = `${targetLang}-${text}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // Skip translation for certain patterns
        const skipPatterns = [
            /^ex\./i, /^e\.g\./i, /^ID$/i, /^URL$/i, /^API$/i, /^JSON$/i,
            /^HTML$/i, /^CSS$/i, /^JS$/i, /^HTTP/i, /^FTP$/i, /^SQL$/i,
            /^XML$/i, /^PDF$/i, /^JPG$/i, /^PNG$/i, /^MP4$/i, /^GIF$/i,
            /^CSV$/i, /^XLS/i, /^DOC/i, /^\d+$/
        ];

        if (skipPatterns.some(pattern => pattern.test(text.trim()))) {
            this.cache.set(cacheKey, text);
            return text;
        }

        try {
            // Use predefined translations first
            const predefinedTranslations = this.getPredefinedTranslations(targetLang);
            const lowerText = text.toLowerCase().trim();

            if (predefinedTranslations[lowerText]) {
                const translated = predefinedTranslations[lowerText];
                this.cache.set(cacheKey, translated);
                this.translatedCount++;

                if (this.translatedCount % 50 === 0) {
                    console.log(`Translated ${this.translatedCount}/${this.totalCount} items...`);
                }

                return translated;
            }

            // For complex sentences, try to translate using API
            const translated = await this.callTranslationService(text, targetLang);
            this.cache.set(cacheKey, translated);
            this.translatedCount++;

            if (this.translatedCount % 50 === 0) {
                console.log(`Translated ${this.translatedCount}/${this.totalCount} items...`);
            }

            return translated;

        } catch (error) {
            console.warn(`Translation failed for: "${text}" - ${error.message}`);
            return text; // Fallback to original text
        }
    }

    /**
     * Call translation service using translate-google
     */
    async callTranslationService(text, targetLang) {
        try {
            const result = await translate(text, { from: 'en', to: targetLang });
            return result || text;
        } catch (error) {
            console.warn(`Translation API failed for: "${text}" - ${error.message}`);
            return text; // Fallback to original text
        }
    }

    /**
     * Count translatable items in JSON
     */
    countTranslatableItems(obj) {
        let count = 0;

        if (typeof obj === 'string') {
            return 1;
        } else if (Array.isArray(obj)) {
            for (const item of obj) {
                count += this.countTranslatableItems(item);
            }
        } else if (typeof obj === 'object' && obj !== null) {
            for (const value of Object.values(obj)) {
                count += this.countTranslatableItems(value);
            }
        }

        return count;
    }

    /**
     * Recursively translate JSON object
     */
    async translateJsonRecursive(obj, targetLang) {
        if (typeof obj === 'string') {
            return await this.translateText(obj, targetLang);
        } else if (Array.isArray(obj)) {
            const translatedArray = [];
            for (const item of obj) {
                translatedArray.push(await this.translateJsonRecursive(item, targetLang));
            }
            return translatedArray;
        } else if (typeof obj === 'object' && obj !== null) {
            const translatedObj = {};
            for (const [key, value] of Object.entries(obj)) {
                translatedObj[key] = await this.translateJsonRecursive(value, targetLang);
            }
            return translatedObj;
        }

        return obj;
    }

    /**
     * Sleep utility
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Translate entire JSON file
     */
    async translateFile(inputFile, outputFile, targetLang) {
        try {
            console.log(`Loading JSON file: ${inputFile}`);
            const data = JSON.parse(fs.readFileSync(inputFile, 'utf8'));

            console.log('Counting translatable items...');
            this.totalCount = this.countTranslatableItems(data);
            console.log(`Found ${this.totalCount} items to translate to ${this.supportedLanguages[targetLang] || targetLang}`);

            console.log('Starting translation...');
            const translatedData = await this.translateJsonRecursive(data, targetLang);

            console.log(`Saving translated file: ${outputFile}`);
            fs.writeFileSync(outputFile, JSON.stringify(translatedData, null, 2), 'utf8');

            console.log(`Translation completed! Translated ${this.translatedCount} items.`);
            return true;

        } catch (error) {
            console.error(`Error during translation: ${error.message}`);
            return false;
        }
    }

    /**
     * Generate all language files
     */
    async generateAllLanguages(inputFile) {
        const languages = ['ta', 'te', 'bn', 'ml'];
        const results = [];

        for (const lang of languages) {
            console.log(`\n=== Generating ${this.supportedLanguages[lang]} (${lang}) ===`);

            // Reset counters for each language
            this.translatedCount = 0;
            this.cache.clear();

            const outputFile = inputFile.replace('en.json', `${lang}.json`);
            const success = await this.translateFile(inputFile, outputFile, lang);

            results.push({
                language: this.supportedLanguages[lang],
                code: lang,
                success: success,
                outputFile: outputFile
            });

            // Small delay between languages
            await this.sleep(1000);
        }

        return results;
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Multi-Language Translator for JSON files');
        console.log('');
        console.log('Usage:');
        console.log('  Generate all languages: node multi-language-translator.js <input-file>');
        console.log('  Generate specific language: node multi-language-translator.js <input-file> <target-lang>');
        console.log('');
        console.log('Supported languages:');
        console.log('  ta - Tamil');
        console.log('  te - Telugu');
        console.log('  bn - Bengali');
        console.log('  ml - Malayalam');
        console.log('  kn - Kannada');
        console.log('');
        console.log('Examples:');
        console.log('  node multi-language-translator.js src/assets/i18n/en.json');
        console.log('  node multi-language-translator.js src/assets/i18n/en.json ta');
        process.exit(1);
    }

    const [inputFile, targetLang] = args;
    const translator = new MultiLanguageTranslator();

    if (targetLang) {
        // Generate specific language
        if (!translator.supportedLanguages[targetLang]) {
            console.error(`Unsupported language: ${targetLang}`);
            console.log('Supported languages: ta, te, bn, ml, kn');
            process.exit(1);
        }

        console.log(`Generating ${translator.supportedLanguages[targetLang]} translation...`);
        const outputFile = inputFile.replace('en.json', `${targetLang}.json`);
        const success = await translator.translateFile(inputFile, outputFile, targetLang);

        if (success) {
            console.log(`✅ ${translator.supportedLanguages[targetLang]} translation completed successfully!`);
            console.log(`Output file: ${outputFile}`);
        } else {
            console.log(`❌ ${translator.supportedLanguages[targetLang]} translation failed!`);
        }

        process.exit(success ? 0 : 1);
    } else {
        // Generate all languages
        console.log('Generating all language translations...');
        const results = await translator.generateAllLanguages(inputFile);

        console.log('\n=== Translation Summary ===');
        let allSuccessful = true;

        for (const result of results) {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.language} (${result.code}): ${result.outputFile}`);
            if (!result.success) allSuccessful = false;
        }

        if (allSuccessful) {
            console.log('\n🎉 All translations completed successfully!');
        } else {
            console.log('\n⚠️  Some translations failed. Check the logs above.');
        }

        process.exit(allSuccessful ? 0 : 1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MultiLanguageTranslator;