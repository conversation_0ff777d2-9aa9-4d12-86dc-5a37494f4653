{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function retry(count = -1) {\n  return source => source.lift(new RetryOperator(count, source));\n}\n\nclass RetryOperator {\n  constructor(count, source) {\n    this.count = count;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RetrySubscriber(subscriber, this.count, this.source));\n  }\n\n}\n\nclass RetrySubscriber extends Subscriber {\n  constructor(destination, count, source) {\n    super(destination);\n    this.count = count;\n    this.source = source;\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      const {\n        source,\n        count\n      } = this;\n\n      if (count === 0) {\n        return super.error(err);\n      } else if (count > -1) {\n        this.count = count - 1;\n      }\n\n      source.subscribe(this._unsubscribeAndRecycle());\n    }\n  }\n\n} //# sourceMappingURL=retry.js.map", "map": null, "metadata": {}, "sourceType": "module"}