"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[687],{68687:(X,T,r)=>{r.r(T),r.d(T,{whatsAppModule:()=>st});var v=r(69808),O=r(40520),m=r(93075),I=r(18995),S=r(63172),C=r(23713),L=r(51190),Q=r(90810),H=r(33315),R=r(71511),e=r(5e3),q=r(77579),p=r(82722),ee=r(54244),te=r(95698),y=r(2976),g=r(51420),u=r(61021),w=r(40553),_=r(66844),N=r(77225),$=r(69845),U=r(30166),D=r(32049),E=r(79618),f=r(41967),G=r(65620),V=r(38827),A=r(24376),z=r(17447);function ne(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"span",23),e.\u0275\u0275listener("click",function(){const n=e.\u0275\u0275restoreView(t);return e.\u0275\u0275resetView((0,n.clear)(n.item))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(1,"span",24),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()}if(2&a){const t=s.item;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.firstName+" "+t.lastName,"")}}function ie(a,s){1&a&&(e.\u0275\u0275elementStart(0,"span",28),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&a&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.-disabled-")))}function ae(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275template(4,ie,3,3,"span",27),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isActive)}}function le(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",21),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.advanceFilters.UserIds=n)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,ne,3,1,"ng-template",22),e.\u0275\u0275template(3,ae,5,6,"ng-template",12),e.\u0275\u0275elementEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,6,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.reportees)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.UserIds)}}function se(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"span",23),e.\u0275\u0275listener("click",function(){const n=e.\u0275\u0275restoreView(t);return e.\u0275\u0275resetView((0,n.clear)(n.item))}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(1,"span",24),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()}if(2&a){const t=s.item;e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1(" ",t.firstName+" "+t.lastName,"")}}function oe(a,s){1&a&&(e.\u0275\u0275elementStart(0,"span",28),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&a&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.-disabled-")))}function re(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275template(4,oe,3,3,"span",27),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.isActive)}}function pe(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",21),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.advanceFilters.TextByIds=n)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,se,3,1,"ng-template",22),e.\u0275\u0275template(3,re,5,6,"ng-template",12),e.\u0275\u0275elementEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,6,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.reportees)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.TextByIds)}}function de(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t.displayName," ")}}function ce(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t.displayName," ")}}function me(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function he(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",2)(1,"div",10),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",8)(5,"ng-select",29),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.advanceFilters.Sources=n)})("change",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.updateSubSource())}),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275template(7,me,4,4,"ng-template",12),e.\u0275\u0275elementEnd()()()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,7,"LEADS.source")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(6,9,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.leadSources)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.Sources)}}function ue(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",32),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275propertyInterpolate("title",t),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function ve(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",31),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.advanceFilters.SubSources=n)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,ue,4,5,"ng-template",12),e.\u0275\u0275elementEnd()}if(2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,6,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.subSourceList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.SubSources)}}function _e(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",2)(1,"div",10),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",8),e.\u0275\u0275template(5,ve,3,8,"ng-select",30),e.\u0275\u0275elementEnd()()),2&a){const t=e.\u0275\u0275nextContext(),i=e.\u0275\u0275reference(61);e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,3,"LEADS.sub-source")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!t.subSourceListIsLoading)("ngIfElse",i)}}function ge(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function fe(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",33),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.advanceFilters.Projects=n)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,ge,4,4,"ng-template",12),e.\u0275\u0275elementEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,6,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.projectList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.Projects)}}function xe(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275element(1,"input",26)(2,"span",7),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()),2&a){const t=s.item,i=s.item$,n=s.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",i.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t," ")}}function be(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",33),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.advanceFilters.Properties=n)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,xe,4,4,"ng-template",12),e.\u0275\u0275elementEnd()}if(2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,6,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",t.propertyList)("multiple",!0)("closeOnSelect",!1)("ngModel",t.advanceFilters.Properties)}}function Ie(a,s){1&a&&e.\u0275\u0275element(0,"ng-select",34),2&a&&e.\u0275\u0275property("virtualScroll",!0)}let Z=(()=>{class a{constructor(t,i){this.store=t,this.modalService=i,this.stopper=new e.EventEmitter,this.projectList=[],this.propertyList=[],this.isCustomStatusEnabled=!1,this.masterLeadStatus=JSON.parse(localStorage.getItem("masterleadstatus")||"[]"),this.leadSources=(0,u.OT)(),this.dateTypeList=y.DCI,this.store.dispatch(new w.I6P),this.store.dispatch(new w.vGE),this.store.dispatch(new U.bt),this.store.dispatch(new w.nj0)}ngOnInit(){var t,i;this.store.select(f.b$).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.filtersPayload=n,this.advanceFilters=Object.assign({},n),this.dateType=g.ff[this.filtersPayload.DateType],this.filterDate=[null==n?void 0:n.FromDate,null==n?void 0:n.ToDate]}),this.store.select(N.Zu).pipe((0,p.R)(this.stopper)).subscribe(n=>{if(null==n||!n.length)return;const l=new Set(n);this.canViewLeadSource=l.has("Permissions.Leads.ViewLeadSource")}),this.allSubStatusList=null===(t=this.masterLeadStatus)||void 0===t?void 0:t.map(n=>n.childTypes),this.allSubStatusList&&(this.allSubStatusList=Object.values(this.allSubStatusList).flat()),this.masterLeadStatus=null===(i=this.masterLeadStatus)||void 0===i?void 0:i.slice().sort((n,l)=>null==n?void 0:n.displayName.localeCompare(null==l?void 0:l.displayName)),this.store.select(D.Hr).pipe((0,p.R)(this.stopper)).subscribe(n=>{var l;this.reportees=n,this.reportees=null===(l=this.reportees)||void 0===l?void 0:l.map(o=>Object.assign(Object.assign({},o),{fullName:o.firstName+" "+o.lastName})),this.reportees=(0,u.vF)(this.reportees,"")}),this.store.select(D.Wn).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.reporteesIsLoading=n}),this.store.select(_.V9).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.propertyList=n.map(l=>String(l)).slice().sort((l,o)=>l.localeCompare(o))}),this.store.select(_._Y).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.propertyListIsLoading=n}),this.store.select(_.VO).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.allSubSourceList=n,this.subSourceList=Object.values(n).flat().filter(l=>l).slice().sort((l,o)=>l.localeCompare(o))}),this.store.select(_.BV).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.subSourceListIsLoading=n}),this.store.select(_.ku).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.projectList=n.slice().sort((l,o)=>l.localeCompare(o))}),this.store.select(_.Y1).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.projectListIsLoading=n}),this.updateSubStatus(),this.maxDate=new Date}updateSubStatus(){var t,i;null!==(i=null===(t=this.advanceFilters)||void 0===t?void 0:t.StatusesIds)&&void 0!==i&&i.length?this.subStatusList=this.allSubStatusList.filter(n=>this.advanceFilters.StatusesIds.includes(n.baseId)).slice().sort((n,l)=>n.displayName.localeCompare(l.displayName)):(this.subStatusList=[],this.subStatusList=Object.values(this.allSubStatusList).flat().slice().sort((n,l)=>n.displayName.localeCompare(l.displayName)))}updateSubSource(){var t,i,n;null!==(i=null===(t=this.advanceFilters)||void 0===t?void 0:t.Sources)&&void 0!==i&&i.length?(this.subSourceList=[],null===(n=this.advanceFilters)||void 0===n||n.Sources.forEach(l=>{this.subSourceList.push.apply(this.subSourceList,this.allSubSourceList[g.bP[g.yF[l]]]||[])})):this.subSourceList=Object.values(this.allSubSourceList).flat()}fetchCustomStatuses(){this.store.select($.j2).pipe((0,p.R)(this.stopper)).subscribe(t=>{this.customStatusList=t}),this.store.select($.b8).pipe((0,p.R)(this.stopper)).subscribe(t=>{this.isCustomStatusListLoading=t})}applyAdvancedFilter(){this.filtersPayload=Object.assign(Object.assign(Object.assign({},this.filtersPayload),this.advanceFilters),{PageNumber:1}),this.store.dispatch(new E.GZ(this.filtersPayload)),this.clearDataAndFilter(),this.modalService.hide()}onClearAllFilters(){this.advanceFilters=Object.assign({},f.E3.filtersPayload)}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(G.yh),e.\u0275\u0275directiveInject(V.tT))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["whatsApp-inbox-adv-filters"]],decls:62,vars:57,consts:[[1,"lead-adv-filter","pl-30","bg-white","brbl-15","brbr-15"],[1,"d-flex","flex-wrap","ng-select-sm"],[1,"w-25","tb-w-33","ip-w-50","ph-w-100"],[1,"justify-between","align-end","mr-20"],[1,"field-label","mt-0"],[1,"checkbox-container","mb-4"],["type","checkbox",3,"ngModel","ngModelChange"],[1,"checkmark"],[1,"mr-20"],["ResizableDropdown","","bindLabel","fullName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange",4,"ngIf","ngIfElse"],[1,"field-label"],["ResizableDropdown","","bindLabel","displayName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange","change"],["ng-option-tmp",""],["ResizableDropdown","","bindLabel","displayName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange"],["class","w-25 tb-w-33 ip-w-50 ph-w-100",4,"ngIf"],["ResizableDropdown","","bindLabel","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange",4,"ngIf","ngIfElse"],[1,"flex-end","mt-10"],[1,"mr-20","fw-semi-bold","text-mud","cursor-pointer",3,"click"],[1,"btn-gray","mr-20",3,"click"],[1,"btn-coal",3,"click"],["fieldLoader",""],["ResizableDropdown","","bindLabel","fullName","bindValue","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange"],["ng-label-tmp",""],[1,"ic-cancel","ic-dark","icon","ic-x-xs","mr-4",3,"click"],[1,"ng-value-label"],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],["class","error-message-custom",4,"ngIf"],[1,"error-message-custom"],["ResizableDropdown","",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange","change"],["ResizableDropdown","",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange",4,"ngIf","ngIfElse"],["ResizableDropdown","",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange"],[1,"checkbox-container",3,"title"],["ResizableDropdown","","bindLabel","id",3,"virtualScroll","items","multiple","closeOnSelect","placeholder","ngModel","ngModelChange"],[1,"pe-none","blinking",3,"virtualScroll"]],template:function(t,i){if(1&t&&(e.\u0275\u0275elementStart(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(7,"label",5)(8,"input",6),e.\u0275\u0275listener("ngModelChange",function(l){return i.advanceFilters.IsWithTeam=l}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(9,"span",7),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(12,"div",8),e.\u0275\u0275template(13,le,4,8,"ng-select",9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"div",2)(15,"div",10),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",8),e.\u0275\u0275template(19,pe,4,8,"ng-select",9),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(20,"div",2)(21,"div",10),e.\u0275\u0275text(22),e.\u0275\u0275pipe(23,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(24,"div",8)(25,"ng-select",11),e.\u0275\u0275listener("ngModelChange",function(l){return i.advanceFilters.StatusesIds=l})("change",function(){return i.updateSubStatus()}),e.\u0275\u0275pipe(26,"translate"),e.\u0275\u0275template(27,de,4,4,"ng-template",12),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(28,"div",2)(29,"div",10),e.\u0275\u0275text(30),e.\u0275\u0275pipe(31,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(32,"div",8)(33,"ng-select",13),e.\u0275\u0275listener("ngModelChange",function(l){return i.advanceFilters.SubStatusesIds=l}),e.\u0275\u0275pipe(34,"translate"),e.\u0275\u0275template(35,ce,4,4,"ng-template",12),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275template(36,he,8,11,"div",14),e.\u0275\u0275template(37,_e,6,5,"div",14),e.\u0275\u0275elementStart(38,"div",2)(39,"div",10),e.\u0275\u0275text(40),e.\u0275\u0275pipe(41,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(42,"div",8),e.\u0275\u0275template(43,fe,3,8,"ng-select",15),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(44,"div",2)(45,"div",10),e.\u0275\u0275text(46),e.\u0275\u0275pipe(47,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(48,"div",8),e.\u0275\u0275template(49,be,3,8,"ng-select",15),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(50,"div",16)(51,"u",17),e.\u0275\u0275listener("click",function(){return i.modalService.hide()}),e.\u0275\u0275text(52),e.\u0275\u0275pipe(53,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(54,"button",18),e.\u0275\u0275listener("click",function(){return i.onClearAllFilters()}),e.\u0275\u0275text(55),e.\u0275\u0275pipe(56,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(57,"button",19),e.\u0275\u0275listener("click",function(){return i.applyAdvancedFilter()}),e.\u0275\u0275text(58),e.\u0275\u0275pipe(59,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(60,Ie,1,1,"ng-template",null,20,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd()),2&t){const n=e.\u0275\u0275reference(61);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(6,33,"GLOBAL.assign-to")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",i.advanceFilters.IsWithTeam),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(11,35,"DASHBOARD.with-team")," "),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!i.reporteesIsLoading)("ngIfElse",n),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(17,37,"GLOBAL.text-by")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!i.reporteesIsLoading)("ngIfElse",n),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(23,39,"GLOBAL.status")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(26,41,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",i.isCustomStatusEnabled?i.customStatusList:i.masterLeadStatus)("multiple",!0)("closeOnSelect",!1)("ngModel",i.advanceFilters.StatusesIds),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(31,43,"LEADS.sub-status")),e.\u0275\u0275advance(3),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(34,45,"GLOBAL.select")),e.\u0275\u0275property("virtualScroll",!0)("items",i.subStatusList)("multiple",!0)("closeOnSelect",!1)("ngModel",i.advanceFilters.SubStatusesIds),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",i.canViewLeadSource),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.canViewLeadSource),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(41,47,"SIDEBAR.project")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!i.projectListIsLoading)("ngIfElse",n),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(47,49,"LABEL.property")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",!i.propertyListIsLoading)("ngIfElse",n),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(53,51,"BUTTONS.cancel")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(56,53,"GLOBAL.reset")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(59,55,"GLOBAL.search"))}},dependencies:[v.O5,A.w9,A.ir,A.mR,z.s,m.Wl,m.JJ,m.On,I.X$],encapsulation:2}),a})();var Se=r(92340),J=r(1563),Ce=r(63253),ye=r(15634),we=r(22313),P=r(84617),Ae=r(35174),Te=r(69048),Le=r(96616),De=r(47511);let Ee=(()=>{class a{transform(t){const i=new Date(t),l=(new Date).getTime()-i.getTime(),o=Math.floor(l/1e3);if(o<60)return`${o} sec ago`;const d=Math.floor(o/60);if(d<60)return`${d} min ago`;const c=Math.floor(d/60);if(c<24)return`${c} hr ago`;const h=Math.floor(c/24);if(h<7)return`${h} day ago`;const x=Math.floor(h/7);if(x<4)return`${x} wk ago`;const b=Math.floor(h/30);return b<12?`${b} mo ago`:`${Math.floor(h/365)} yr ago`}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275pipe=e.\u0275\u0275definePipe({name:"relativeTime",type:a,pure:!0}),a})();function Fe(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",32),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(t).$implicit,o=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(o.currentVisibility(l.name))}),e.\u0275\u0275elementStart(2,"div",33)(3,"a"),e.\u0275\u0275element(4,"img",34),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"span",35),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=s.$implicit,i=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",i.visibility),e.\u0275\u0275advance(2),e.\u0275\u0275classProp("active",i.LeadVisibility[i.filtersPayload.Visibility]==t.name),e.\u0275\u0275advance(1),e.\u0275\u0275property("type","leadrat")("appImage",i.s3BucketUrl+t.image),e.\u0275\u0275advance(1),e.\u0275\u0275classProp("active",i.LeadVisibility[i.filtersPayload.Visibility]==t.name),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.name)}}function Oe(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"ng-option",36),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&a){const t=s.$implicit;e.\u0275\u0275property("value",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t)}}function Ve(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",37),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.onResetDateFilter())}),e.\u0275\u0275elementStart(1,"span",38),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(4,"span",39),e.\u0275\u0275elementEnd()}2&a&&(e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,"GLOBAL.reset")))}function Pe(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",47),e.\u0275\u0275text(1),e.\u0275\u0275elementStart(2,"span",48),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(t).$implicit,o=e.\u0275\u0275nextContext().$implicit,d=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(d.onRemoveFilter(o.key,l))}),e.\u0275\u0275elementEnd()()}if(2&a){const t=s.$implicit,i=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",n.filtersKeyLabel[i.key]||n.filtersKeyLabel[null==i.key?null:i.key.toLowerCase()]||i.key,": ","UserIds"===i.key||"TextByIds"===i.key?n.getUserName(t):"StatusesIds"===i.key?n.getStatusName(t):"SubStatusesIds"===i.key?n.getSubStatusName(t):"SourceIds"===i.key?null==n.sourceIdMap?null:n.sourceIdMap[t]:t," ")}}function We(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",45),e.\u0275\u0275template(1,Pe,3,2,"div",46),e.\u0275\u0275elementEnd()),2&a){const t=s.$implicit,i=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",i.getArrayOfFilters(t.key,t.value))}}function Me(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",40)(1,"div",41)(2,"drag-scroll",42),e.\u0275\u0275template(3,We,2,1,"div",43),e.\u0275\u0275pipe(4,"keyvalue"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",44),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(n.onClearAllFilters())}),e.\u0275\u0275text(6),e.\u0275\u0275pipe(7,"translate"),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()()()}if(2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",e.\u0275\u0275pipeBind1(4,3,t.filtersPayload)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(7,5,"BUTTONS.clear")," ",e.\u0275\u0275pipeBind1(8,7,"GLOBAL.all"),"")}}function Be(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",49)(2,"div",50),e.\u0275\u0275element(3,"ng-lottie",51),e.\u0275\u0275elementStart(4,"h3",52),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275property("options",t.noDataFound),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(6,2,"GLOBAL.no-conversation-found"))}}function ke(a,s){1&a&&e.\u0275\u0275elementContainer(0)}function je(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",55),e.\u0275\u0275template(1,ke,1,0,"ng-container",56),e.\u0275\u0275elementEnd()),2&a){e.\u0275\u0275nextContext(3);const t=e.\u0275\u0275reference(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngTemplateOutlet",t)}}function Re(a,s){if(1&a&&e.\u0275\u0275element(0,"img",75),2&a){const t=e.\u0275\u0275nextContext().$implicit,i=e.\u0275\u0275nextContext(4);e.\u0275\u0275property("type","leadrat")("appImage",i.getImageUrl(t.leadSource))("title",i.LeadSource[t.leadSource])}}function Ne(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",76),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&a){const t=e.\u0275\u0275nextContext().$implicit,i=e.\u0275\u0275nextContext(4);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(i.getAssignedToDetails(t.assignTo,i.allUserList,!0)||"Unassigned lead")}}function $e(a,s){if(1&a&&e.\u0275\u0275element(0,"img",75),2&a){const t=e.\u0275\u0275nextContext().$implicit,i=e.\u0275\u0275nextContext(6);e.\u0275\u0275property("type","leadrat")("appImage",i.getImageUrl(t.leadSource))("title",i.LeadSource[t.leadSource])}}function Ue(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",76),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&a){const t=e.\u0275\u0275nextContext().$implicit,i=e.\u0275\u0275nextContext(6);let n;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t.assignTo!=i.EMPTY_GUID?((null==(n=i.getAssignedToDetails(t.assignTo,i.allUserList))?null:n.firstName)||"")+" "+((null==(n=i.getAssignedToDetails(t.assignTo,i.allUserList))?null:n.lastName[0])||""):"Unassigned lead","")}}const Y=function(a){return{"border-bottom":a}};function Ge(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",78)(1,"div",79)(2,"div",80),e.\u0275\u0275element(3,"div",81)(4,"div",82),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",83)(6,"div",62)(7,"div",63)(8,"h4",64),e.\u0275\u0275text(9),e.\u0275\u0275template(10,$e,1,3,"img",65),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(11,"div",66)(12,"h4",84),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"div",67),e.\u0275\u0275template(15,Ue,2,1,"div",68),e.\u0275\u0275elementEnd()()()()()),2&a){const t=s.$implicit,i=s.first,n=s.last,l=e.\u0275\u0275nextContext(6);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(9,Y,n)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",n?"h-20px ntop-15":"h-50px"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",i?"icon ic-xxs ic-dark-400 ic-circle position-absolute nright-6":""),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",n?"ic-turn-right":"ic-arrow-right"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(11,Y,!n)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",null==t?null:t.name[0]," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",l.canViewLeadSource),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",t.name," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.assignTo)}}function ze(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,Ge,16,13,"div",77),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(5);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.whatsAppChildData)}}function Ze(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div")(1,"div",61),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(t).$implicit,o=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(null!=l&&l.childLeadsCount?o.toggleChildData(l):o.fetchConversation(l))}),e.\u0275\u0275elementStart(2,"div",62)(3,"div",63)(4,"h4",64),e.\u0275\u0275text(5),e.\u0275\u0275template(6,Re,1,3,"img",65),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div",66)(8,"div",67),e.\u0275\u0275template(9,Ne,2,1,"div",68),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(10,"h4",69),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",70),e.\u0275\u0275text(13),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(14,"div",71)(15,"div",72),e.\u0275\u0275text(16),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(17,"div",73),e.\u0275\u0275text(18),e.\u0275\u0275pipe(19,"relativeTime"),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275template(20,ze,2,1,"ng-container",74),e.\u0275\u0275elementEnd()}if(2&a){const t=s.$implicit,i=e.\u0275\u0275nextContext(4);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",null==t?null:t.name[0]," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.canViewLeadSource),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",t.assignTo),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.name),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",t.contactNo," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("",t.waMessage," "),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(19,8,t.waLastModifiedOn)," "),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",i.expandedItemId===t.id)}}function Je(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",85),e.\u0275\u0275listener("inView",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(l.onInView(n))}),e.\u0275\u0275elementEnd()}}function Ye(a,s){if(1&a&&e.\u0275\u0275element(0,"whatsapp-chat",89),2&a){const t=e.\u0275\u0275nextContext(5);e.\u0275\u0275property("clickedData",t.clickedData)("whatsAppComp",!0)}}function Ke(a,s){if(1&a&&e.\u0275\u0275element(0,"lead-preview",89),2&a){const t=e.\u0275\u0275nextContext(5);e.\u0275\u0275property("clickedData",t.clickedData)("whatsAppComp",!0)}}function Xe(a,s){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",86),e.\u0275\u0275template(2,Ye,1,2,"whatsapp-chat",87),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",88),e.\u0275\u0275template(4,Ke,1,2,"lead-preview",87),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(4),i=e.\u0275\u0275reference(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",(null==t.clickedData?null:t.clickedData.id)===t.currentId)("ngIfElse",i),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",(null==t.clickedData?null:t.clickedData.id)===t.currentId)("ngIfElse",i)}}function Qe(a,s){1&a&&(e.\u0275\u0275elementStart(0,"h3",90),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&a&&(e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(2,1,"GLOBAL.select-an-item-to-read-and-pre")))}function He(a,s){if(1&a&&(e.\u0275\u0275elementStart(0,"div",57)(1,"div",58),e.\u0275\u0275template(2,Ze,21,10,"div",6),e.\u0275\u0275template(3,Je,1,0,"div",59),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,Xe,5,4,"ng-container",30),e.\u0275\u0275template(5,Qe,3,3,"ng-template",null,60,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd()),2&a){const t=e.\u0275\u0275reference(6),i=e.\u0275\u0275nextContext(3),n=e.\u0275\u0275reference(4);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",i.whatsAppDataItems),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!i.whatsAppListIsLoading)("ngIfElse",n),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",i.canShowChatScreen&&i.clickedData)("ngIfElse",t)}}function qe(a,s){if(1&a&&(e.\u0275\u0275template(0,je,2,1,"div",53),e.\u0275\u0275template(1,He,7,5,"div",54)),2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("ngIf",t.whatsAppListIsLoading&&!(null!=t.whatsAppDataItems&&t.whatsAppDataItems.length)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.whatsAppListIsLoading||(null==t.whatsAppDataItems?null:t.whatsAppDataItems.length))}}const et=function(a){return{"mb-30":a}};function tt(a,s){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",3)(1,"div",4)(2,"ul",5),e.\u0275\u0275template(3,Fe,7,8,"ng-container",6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"div",7)(5,"div",8)(6,"div",9)(7,"div",10),e.\u0275\u0275element(8,"span",11),e.\u0275\u0275elementStart(9,"input",12),e.\u0275\u0275listener("keydown",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.onSearch(n))})("input",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.isEmptyInput(n))})("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.searchTerm=n)}),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(11,"small",13),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(14,"div",14)(15,"div",15)(16,"div",16)(17,"div",17)(18,"ng-select",18),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(l.dateType=n)})("change",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.dateChange())}),e.\u0275\u0275pipe(19,"translate"),e.\u0275\u0275template(20,Oe,2,2,"ng-option",19),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(21,"div",20),e.\u0275\u0275element(22,"span",21),e.\u0275\u0275elementStart(23,"input",22),e.\u0275\u0275listener("ngModelChange",function(n){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext();return l.filterDate=n,e.\u0275\u0275resetView(l.dateChange())}),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(25,"owl-date-time",23,24),e.\u0275\u0275listener("afterPickerOpen",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.onPickerOpened(n.currentDate))}),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(27,Ve,5,3,"div",25),e.\u0275\u0275elementStart(28,"div",26),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const n=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(n.openAdvFiltersModal())}),e.\u0275\u0275element(29,"div",27),e.\u0275\u0275elementStart(30,"span",28),e.\u0275\u0275text(31),e.\u0275\u0275pipe(32,"translate"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(33,Me,9,9,"div",29),e.\u0275\u0275template(34,Be,7,4,"ng-container",30),e.\u0275\u0275template(35,qe,2,2,"ng-template",null,31,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd()()}if(2&a){const t=e.\u0275\u0275reference(26),i=e.\u0275\u0275reference(36),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",n.showLeftNav?"left-300":"left-200"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",n.visibilityList),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(33,et,!n.showFilters)),e.\u0275\u0275advance(4),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(10,23,"GLOBAL.type-to-search")),e.\u0275\u0275property("ngModel",n.searchTerm),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1("(",e.\u0275\u0275pipeBind1(13,25,"LEADS.lead-search-prompt"),")"),e.\u0275\u0275advance(6),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(19,27,"GLOBAL.all")),e.\u0275\u0275property("virtualScroll",!0)("ngModel",n.dateType)("searchable",!1),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",n.dateTypeList),e.\u0275\u0275advance(2),e.\u0275\u0275property("owlDateTimeTrigger",t),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(24,29,"GLOBAL.ex-19062025-29062025")),e.\u0275\u0275property("owlDateTimeTrigger",t)("owlDateTime",t)("selectMode","range")("ngModel",n.filterDate),e.\u0275\u0275advance(2),e.\u0275\u0275property("pickerType","calendar"),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==n.filtersPayload?null:n.filtersPayload.FromDate),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(32,31,"PROPERTY.advanced-filters")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",n.showFilters),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!(null!=n.whatsAppDataItems&&n.whatsAppDataItems.length||n.whatsAppListIsLoading))("ngIfElse",i)}}function nt(a,s){1&a&&(e.\u0275\u0275elementStart(0,"div",91),e.\u0275\u0275element(1,"application-loader"),e.\u0275\u0275elementEnd())}function it(a,s){1&a&&(e.\u0275\u0275elementStart(0,"div",92),e.\u0275\u0275element(1,"img",93),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&a&&(e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(2,1,"GLOBAL.loader")))}const at=[{path:"",redirectTo:"whatsApp-inbox",pathMatch:"full"},{path:"whatsApp-inbox",component:(()=>{class a{constructor(t,i,n,l,o,d){this.headerTitle=t,this.store=i,this.shareDataService=n,this.metaTitle=l,this.modalRef=o,this.modalService=d,this.stopper=new e.EventEmitter,this.visibilityList=y.ZR1.slice(0,3),this.showLeftNav=!0,this.noDataFound={path:"assets/animations/empty-notes.json"},this.noConversation={path:"assets/animations/whatsApp-chat.json"},this.searchTermSubject=new q.x,this.getAssignedToDetails=u.sW,this.EMPTY_GUID=y.sI9,this.allUserList=[],this.canShowChatScreen=!1,this.isEmptyObject=u.Qr,this.LeadSource=g.yF,this.filtersPayload={path:"wa/leads-with-message",PageNumber:1,PageSize:10},this.dateTypeList=y.DCI,this.LeadVisibility=g.ye,this.whatsAppDataItems=[],this.subscriptions=[],this.currentDate=new Date,this.filtersKeyLabel=y.iZX,this.masterLeadStatus=JSON.parse(localStorage.getItem("masterleadstatus")||"[]"),this.s3BucketUrl=Se.N.s3ImageBucketURL,this.onPickerOpened=u.tK,this.headerTitle.setTitle("WhatsApp Inbox"),this.metaTitle.setTitle("CRM | WhatsApp"),this.store.dispatch(new U.MI),this.store.select(N.Zu).pipe((0,p.R)(this.stopper)).subscribe(c=>{if(null==c||!c.length)return;const h=new Set(c);this.canUserView=h.has("Permissions.Leads.View"),this.canViewLeadSource=h.has("Permissions.Leads.ViewLeadSource")}),this.store.select(f.$r).pipe((0,p.R)(this.stopper)).subscribe(c=>{this.whatsAppChildData=c})}get showFilters(){var t,i,n,l,o,d,c,h,x,b,F,W,M,B,k,j;return!!((null===(i=null===(t=this.filtersPayload)||void 0===t?void 0:t.Sources)||void 0===i?void 0:i.length)||(null===(l=null===(n=this.filtersPayload)||void 0===n?void 0:n.SubSources)||void 0===l?void 0:l.length)||(null===(d=null===(o=this.filtersPayload)||void 0===o?void 0:o.Projects)||void 0===d?void 0:d.length)||(null===(h=null===(c=this.filtersPayload)||void 0===c?void 0:c.Properties)||void 0===h?void 0:h.length)||(null===(b=null===(x=this.filtersPayload)||void 0===x?void 0:x.UserIds)||void 0===b?void 0:b.length)||(null===(W=null===(F=this.filtersPayload)||void 0===F?void 0:F.TextByIds)||void 0===W?void 0:W.length)||(null===(B=null===(M=this.filtersPayload)||void 0===M?void 0:M.StatusesIds)||void 0===B?void 0:B.length)||(null===(j=null===(k=this.filtersPayload)||void 0===k?void 0:k.SubStatusesIds)||void 0===j?void 0:j.length))}ngOnInit(){this.shareDataService.showLeftNav$.subscribe(t=>{this.showLeftNav=t}),this.store.select(D.Xf).pipe((0,p.R)(this.stopper)).subscribe(t=>{var i,n;this.userBasicDetails=t,this.currentDate=(0,u.Xp)(null===(n=null===(i=this.userBasicDetails)||void 0===i?void 0:i.timeZoneInfo)||void 0===n?void 0:n.baseUTcOffset)}),this.store.select(f.b$).pipe((0,p.R)(this.stopper)).subscribe(t=>{var i,n,l,o;this.filtersPayload=Object.assign(Object.assign(Object.assign({},this.filtersPayload),t),{PageNumber:null==t?void 0:t.PageNumber,PageSize:null==t?void 0:t.PageSize,SearchText:null!=t&&t.SearchText?null==t?void 0:t.SearchText:null,DateType:(null==t?void 0:t.DateType)||0,FromDate:null==t?void 0:t.FromDate,ToDate:null==t?void 0:t.ToDate}),this.searchTerm=null!=t&&t.SearchText?null==t?void 0:t.SearchText:null,this.dateType=g.ff[this.filtersPayload.DateType],this.filterDate=[(0,u.qJ)(null==t?void 0:t.FromDate,null===(n=null===(i=this.userBasicDetails)||void 0===i?void 0:i.timeZoneInfo)||void 0===n?void 0:n.baseUTcOffset),(0,u.qJ)(null==t?void 0:t.ToDate,null===(o=null===(l=this.userBasicDetails)||void 0===l?void 0:l.timeZoneInfo)||void 0===o?void 0:o.baseUTcOffset)]}),this.store.select(f.vB).pipe((0,p.R)(this.stopper)).subscribe(t=>{this.whatsAppListIsLoading=t}),this.store.select(f.cI).pipe((0,ee.n)(()=>this.whatsAppListIsLoading),(0,p.R)(this.stopper)).subscribe(t=>{this.whatsAppData=t,this.whatsAppDataItems=[...this.whatsAppDataItems,...(null==t?void 0:t.items)||[]]}),this.searchTermSubject.subscribe(()=>{this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{PageNumber:1,SearchText:this.searchTerm?this.searchTerm:null}),this.filterFunction()}),this.store.select(D.Sh).pipe((0,p.R)(this.stopper)).subscribe(t=>{var i;this.allUserList=t,this.allUserList=null===(i=this.allUserList)||void 0===i?void 0:i.map(n=>Object.assign(Object.assign({},n),{fullName:n.firstName+" "+n.lastName}))}),this.store.select(f.vB).pipe((0,p.R)(this.stopper)).subscribe(t=>{this.whatsAppListIsLoading=t}),this.filterFunction()}currentVisibility(t){var i;g.ye[t]!==(null===(i=this.filtersPayload)||void 0===i?void 0:i.Visibility)&&(this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{Visibility:g.ye[t],PageNumber:1}),this.filterFunction())}onSearch(t){var i;if("Enter"===t.key){if(""===(null===(i=this.searchTerm)||void 0===i?void 0:i.trim())||null===this.searchTerm)return;this.searchTermSubject.next(this.searchTerm)}}isEmptyInput(t){(""===this.searchTerm||null===this.searchTerm)&&this.searchTermSubject.next("")}dateChange(){var t,i,n,l,o,d,c;this.dateType&&(null===(t=this.filterDate)||void 0===t?void 0:t[0])&&(this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{DateType:g.ff[this.dateType],FromDate:(0,u.D7)(null===(i=this.filterDate)||void 0===i?void 0:i[0],null===(l=null===(n=this.userBasicDetails)||void 0===n?void 0:n.timeZoneInfo)||void 0===l?void 0:l.baseUTcOffset),ToDate:(0,u.D7)(null===(o=this.filterDate)||void 0===o?void 0:o[1],null===(c=null===(d=this.userBasicDetails)||void 0===d?void 0:d.timeZoneInfo)||void 0===c?void 0:c.baseUTcOffset)}),this.filterFunction())}onResetDateFilter(){this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{DateType:0,FromDate:null,ToDate:null}),this.filterDate=[null,null],this.filterFunction()}fetchConversation(t){window.innerWidth>768?this.fetchConversation1(t):this.fetchConversation2(t)}fetchConversation1(t){this.clickedData=null,this.canShowChatScreen=window.innerWidth>480,this.currentId=null==t?void 0:t.id,this.store.dispatch(new w.bJB(null==t?void 0:t.id)),this.store.select(_.BI).pipe((0,p.R)(this.stopper)).subscribe(n=>{(0,u.Qr)(n)||(this.clickedData=n)}),this.store.select(_.GO).pipe((0,p.R)(this.stopper)).subscribe(n=>{this.clickedDataIsLoading=n})}fetchConversation2(t){var i;this.clickedData=null,this.currentId=null==t?void 0:t.id,this.store.dispatch(new w.bJB(null==t?void 0:t.id)),null===(i=this.subscriptions)||void 0===i||i.forEach(o=>o.unsubscribe()),this.subscriptions=[];const n=this.store.select(_.BI).pipe((0,p.R)(this.stopper)).subscribe(o=>{if(!(0,u.Qr)(o)&&(this.clickedData=o,this.clickedData)){let d={data:this.clickedData};this.modalRef&&this.modalRef.hide(),this.modalRef=this.modalService.show(J.c,Object.assign({},{class:"modal-550 right-modal ip-modal-unset",initialState:d}))}}),l=this.store.select(_.GO).pipe((0,p.R)(this.stopper)).subscribe(o=>{this.clickedDataIsLoading=o});this.subscriptions.push(n,l)}fetchChildData(t){this.store.dispatch(new E.sK(null==t?void 0:t.id)),this.fetchConversation(t)}clickedItem(t){null!=t&&t.childLeadsCount||(this.canShowChatScreen=!0,this.currentId=null==t?void 0:t.id,this.store.dispatch(new w.bJB(null==t?void 0:t.id)),this.store.select(_.BI).pipe((0,te.q)(1)).subscribe(i=>{(0,u.Qr)(i)||(this.clickedData=i)}),this.store.select(_.GO).pipe((0,p.R)(this.stopper)).subscribe(i=>{this.clickedDataIsLoading=i}))}toggleChildData(t){this.expandedItemId===t.id?this.expandedItemId=null:(this.expandedItemId=t.id,this.fetchChildData(t))}onInView(t){var i,n;t&&!this.whatsAppListIsLoading&&(null===(i=this.whatsAppDataItems)||void 0===i?void 0:i.length)<(null===(n=this.whatsAppData)||void 0===n?void 0:n.totalCount)&&this.loadMore()}loadMore(){this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{PageNumber:this.filtersPayload.PageNumber+1,PageSize:10}),this.filterAndUpdate()}filterAndUpdate(){this.clickedData=null,this.store.dispatch(new E.GZ(this.filtersPayload)),this.store.dispatch(new E.Sp(this.filtersPayload))}getImageUrl(t){const i=y.sQB.find(n=>n.leadSource===t);return i?this.s3BucketUrl+i.imageURL:"https://dleadrat-black.s3.ap-south-1.amazonaws.com/logos/IVR.svg"}openAdvFiltersModal(){this.modalService.show(Z,{class:"ip-modal-unset  top-full-modal",initialState:{clearDataAndFilter:()=>{this.filterFunction()}}})}filterFunction(){this.whatsAppDataItems=[],this.filterAndUpdate()}getUserName(t){var i;let n="";return null===(i=this.allUserList)||void 0===i||i.forEach(l=>{t===l.id&&(n=`${l.firstName} ${l.lastName}`)}),n}getStatusName(t){var i;let n="";return null===(i=this.masterLeadStatus)||void 0===i||i.forEach(l=>{l.id===t&&(n=l.displayName)}),n}getSubStatusName(t){var i;let n="",l=null===(i=this.masterLeadStatus)||void 0===i?void 0:i.map(o=>o.childTypes);return l&&(l=Object.values(l).flat()),null==l||l.forEach(o=>{o.id===t&&(n=o.displayName)}),n}getArrayOfFilters(t,i){var n;return"DateType"===t||"Visibility"===t||"PageSize"===t||"PageNumber"===t||"FromDate"===t||"ToDate"===t||"SearchText"===t||"path"===t||"IsWithTeam"===t||0===(null==i?void 0:i.length)?[]:null===(n=null==i?void 0:i.toString())||void 0===n?void 0:n.split(",")}onRemoveFilter(t,i){if(["Sources","SubSources","Projects","Properties","UserIds","TextByIds","StatusesIds","SubStatusesIds"].includes(t)){if(this.filtersPayload.hasOwnProperty(t)){const n=this.filtersPayload[t].filter(l=>l!==i);this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{[t]:n})}}else this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{[t]:null});this.filterFunction()}onClearAllFilters(){var t,i,n,l,o;const d=null===(t=this.filtersPayload)||void 0===t?void 0:t.Visibility,c=(null===(i=this.filtersPayload)||void 0===i?void 0:i.SearchText)||null,h=(null===(n=this.filtersPayload)||void 0===n?void 0:n.DateType)||0,x=null===(l=this.filtersPayload)||void 0===l?void 0:l.FromDate,b=null===(o=this.filtersPayload)||void 0===o?void 0:o.ToDate;this.filtersPayload=f.E3.filtersPayload,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{SearchText:c,Visibility:d,DateType:h,FromDate:x,ToDate:b,PageNumber:1,PageSize:10}),this.filterFunction()}ngOnDestroy(){var t;this.stopper.next(),this.stopper.complete(),null===(t=this.subscriptions)||void 0===t||t.forEach(i=>i.unsubscribe())}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(Ce.g),e.\u0275\u0275directiveInject(G.yh),e.\u0275\u0275directiveInject(ye.u),e.\u0275\u0275directiveInject(we.Dx),e.\u0275\u0275directiveInject(V.UZ),e.\u0275\u0275directiveInject(V.tT))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["whatsApp-inbox"]],decls:5,vars:1,consts:[["class","py-12 px-20",4,"ngIf"],["gridLoader",""],["ratLoader",""],[1,"py-12","px-20"],[1,"position-absolute","top-13","z-index-1021","tb-left-175",3,"ngClass"],[1,"align-center","top-nav-bar","text-nowrap"],[4,"ngFor","ngForOf"],[1,"bg-white"],[1,"align-center","w-100","border-gray","ph-flex-col","ph-flex-between-unset",3,"ngClass"],[1,"align-center","border-end","flex-grow-1","no-validation","ph-border-bottom"],[1,"align-center","w-100","px-10","py-12"],[1,"search","icon","ic-search","ic-sm","ic-slate-90","mr-12","ip-mr-4"],["autocomplete","off","name","search",1,"border-0","outline-0","w-100",3,"ngModel","placeholder","keydown","input","ngModelChange"],[1,"text-muted","text-nowrap","ip-d-none","pr-8"],[1,"align-center"],[1,"filters-grid","clear-padding","border-end","h-100"],[1,"align-center","h-100","ml-16"],[1,"manage-select","datefilter-scroll"],["ResizableDropdown","",1,"lead-date","ip-max-w-80px","min-w-60",3,"virtualScroll","placeholder","ngModel","searchable","ngModelChange","change"],["name","dateType","ngDefaultControl","",3,"value",4,"ngFor","ngForOf"],[1,"date-picker","border-start-0","align-center"],[1,"ic-appointment","icon","ic-xxs","ic-black",3,"owlDateTimeTrigger"],["type","text","readonly","",1,"pl-20","text-large",3,"placeholder","owlDateTimeTrigger","owlDateTime","selectMode","ngModel","ngModelChange"],[3,"pickerType","afterPickerOpen"],["dt1",""],["class","bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 align-center cursor-pointer",3,"click",4,"ngIf"],[1,"px-10","align-center","cursor-pointer",3,"click"],[1,"icon","ic-filter-solid","ic-xxs","ic-black","mr-10"],[1,"fw-600","ip-d-none"],["class","p-4 tb-w-100-40 w-100-180 ph-w-100-50",4,"ngIf"],[4,"ngIf","ngIfElse"],["whatsAppScreen",""],[1,"cursor-pointer",3,"ngClass","click"],[1,"align-center","ph-mb-4"],["alt","",3,"type","appImage"],[1,"text-large","fw-semi-bold","mx-8","d-flex"],["name","dateType","ngDefaultControl","",3,"value"],[1,"bg-coal","text-white","px-10","py-12","w-50px","ip-w-30px","h-100","align-center","cursor-pointer",3,"click"],[1,"ip-d-none"],[1,"ic-refresh","d-none","ip-d-block"],[1,"p-4","tb-w-100-40","w-100-180","ph-w-100-50"],[1,"bg-secondary","flex-between"],[1,"br-4","overflow-auto","d-flex","scroll-hide","w-100"],["class","d-flex",4,"ngFor","ngForOf"],[1,"px-8","py-4","bg-slate-120","m-4","br-4","text-mud","text-center","fw-semi-bold","text-nowrap","cursor-pointer",3,"click"],[1,"d-flex"],["class","px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap",4,"ngFor","ngForOf"],[1,"px-8","py-4","bg-slate-120","m-4","br-4","text-mud","text-center","fw-semi-bold","text-nowrap"],[1,"icon","ic-cancel","ic-dark","ic-x-xs","cursor-pointer","text-light-slate","ml-4",3,"click"],[1,"flex-center","h-100-160"],[1,"flex-column"],[3,"options"],[1,"header-5","fw-600","text-center"],["class","pt-40",4,"ngIf"],["class","d-flex h-100-155 w-100 ph-h-100-220",4,"ngIf"],[1,"pt-40"],[4,"ngTemplateOutlet"],[1,"d-flex","h-100-155","w-100","ph-h-100-220"],[1,"w-25","border","scrollbar","min-w-300","tb-w-50","ip-w-100"],["inView","",3,"inView",4,"ngIf","ngIfElse"],["selectItem",""],[1,"h-70px","border-bottom","justify-between","px-10","cursor-pointer","position-relative",3,"click"],[1,"d-flex","w-100"],[1,"flex-center-col"],[1,"dot","dot-xxl","bg-green-50","text-accent-green","fw-600","text-uppercase","position-relative","border-2"],["alt","","class","position-absolute nleft-3 nbottom-4",3,"type","appImage","title",4,"ngIf"],[1,"justify-center-col","ml-10","w-100"],[1,"justify-end"],["class","text-light-gray text-xs text-truncate-1 break-all",4,"ngIf"],[1,"fw-600","text-dark","text-truncate-1","break-all"],[1,"text-sm","text-dark","text-truncate-1","break-all","mt-2"],[1,"flex-between","text-xs","mt-2"],[1,"text-dark-gray","text-truncate-1","break-all","mr-2"],[1,"text-nowrap","fw-semi-bold"],[4,"ngIf"],["alt","",1,"position-absolute","nleft-3","nbottom-4",3,"type","appImage","title"],[1,"text-light-gray","text-xs","text-truncate-1","break-all"],["class","align-center",3,"ngClass",4,"ngFor","ngForOf"],[1,"align-center",3,"ngClass"],[1,"w-24","text-dark-gray"],[1,"border-right","position-relative",3,"ngClass"],[3,"ngClass"],[1,"icon","ic-xxs","ic-dark-400","position-absolute","nright-12","top-18",3,"ngClass"],[1,"h-50px","justify-between","px-12","cursor-pointer","position-relative","w-100",3,"ngClass"],[1,"fw-600","text-dark","text-truncate-1","break-all","mt-10"],["inView","",3,"inView"],[1,"w-35","border","position-relative","tb-w-50","ip-d-none"],[3,"clickedData","whatsAppComp",4,"ngIf","ngIfElse"],[1,"w-40pr","border","tb-d-none"],[3,"clickedData","whatsAppComp"],[1,"w-75","border","flex-center","tb-w-50","ip-d-none"],[1,"flex-center","h-100-182"],[1,"flex-center"],["src","assets/images/loader-rat.svg",1,"rat-loader","h-40px","w-30px",3,"alt"]],template:function(t,i){1&t&&(e.\u0275\u0275template(0,tt,37,35,"div",0),e.\u0275\u0275template(1,nt,2,0,"ng-template",null,1,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275template(3,it,3,3,"ng-template",null,2,e.\u0275\u0275templateRefExtractor)),2&t&&e.\u0275\u0275property("ngIf",i.canUserView)},dependencies:[v.mk,v.sg,v.O5,v.tP,A.w9,A.jq,P.sZ,P.BO,P.hV,Ae.S,Te.z,z.s,J.c,Le.c,De.t,m.Fj,m.JJ,m.On,C.e$,v.Nd,Ee,I.X$],encapsulation:2}),a})()}];let lt=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[R.Bz.forChild(at),R.Bz]}),a})(),st=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({providers:[I.sK],imports:[v.ez,lt,Q.m,S.sF,m.UX,m.u5,H.Y4,I.aw.forChild({loader:{provide:I.Zw,useFactory:L.gS,deps:[O.eN]},extend:!0}),C.CT.forRoot({player:L.xd})]}),a})()},69048:(X,T,r)=>{r.d(T,{z:()=>O});var v=r(5e3);let O=(()=>{class m{constructor(S){this.elementRef=S,this.inView=new v.EventEmitter,this.observer=new IntersectionObserver(C=>{this.handleIntersection(C)}),this.observer.observe(this.elementRef.nativeElement)}handleIntersection(S){const C=S.some(L=>L.isIntersecting);this.inView.emit(C)}ngOnDestroy(){this.observer.disconnect()}}return m.\u0275fac=function(S){return new(S||m)(v.\u0275\u0275directiveInject(v.ElementRef))},m.\u0275dir=v.\u0275\u0275defineDirective({type:m,selectors:[["","inView",""]],outputs:{inView:"inView"}}),m})()}}]);