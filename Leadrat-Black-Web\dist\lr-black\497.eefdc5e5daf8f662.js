"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[497],{79497:(Te,D,s)=>{s.r(D),s.d(D,{TaskModule:()=>fe});var c=s(69808),x=s(71511),E=s(63172),F=s(40520),u=s(18995),O=s(23713),C=s(93075),A=s(90810),z=s(2407),e=s(5e3),B=s(63253),S=s(65620);let j=(()=>{class a{constructor(t,n,i){this.translate=t,this.headerTitle=n,this.store=i,this.currentLang=localStorage.getItem("locale")?localStorage.getItem("locale"):"en"}ngOnInit(){this.headerTitle.setLangTitle("SIDEBAR.tasks"),this.translate.setDefaultLang("en"),this.translate.use(this.currentLang)}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(u.sK),e.\u0275\u0275directiveInject(B.g),e.\u0275\u0275directiveInject(S.yh))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["task"]],decls:1,vars:0,template:function(t,n){1&t&&e.\u0275\u0275element(0,"router-outlet")},dependencies:[x.lC],encapsulation:2}),a})();var v=s(82722),_=s(2976),k=s(51420),g=s(61021),I=s(4853),y=s(77225),m=s(46112),Z=s(52230),h=s(38827),M=s(1880);function R(a,o){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",6),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275elementStart(2,"span",7),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.initEditTodo(i.params.data))}),e.\u0275\u0275elementEnd()()}2&a&&e.\u0275\u0275propertyInterpolate("title",e.\u0275\u0275pipeBind1(1,1,"GLOBAL.edit"))}function N(a,o){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",8)(1,"span",9),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(i.openDeleteToDoModal(i.params.data))}),e.\u0275\u0275elementEnd()()}}let U=(()=>{class a{constructor(t,n,i,l){this.modalService=t,this.modalRef=n,this._store=i,this.trackingService=l,this.stopper=new e.EventEmitter,this.canEditTask=!1,this.canDeleteTask=!1,this._store.select(y.Bu).pipe((0,v.R)(this.stopper)).subscribe(r=>{null!=r&&r.includes("Todos")&&(this.canEditTask=!0)}),this._store.select(y.i0).pipe((0,v.R)(this.stopper)).subscribe(r=>{null!=r&&r.includes("Todos")&&(this.canDeleteTask=!0)})}agInit(t){this.params=t}initEditTodo(t){this.trackingService.trackFeature("Web.Task.Button.Edit.Click"),this.modalRef=this.modalService.show(I.K,{class:"right-modal modal-350",initialState:{selectedTodo:t}})}openDeleteToDoModal(t){var n;this.modalRef=this.modalService.show(Z._,Object.assign({},{class:"modal-400 top-modal ph-modal-unset",initialState:{message:"GLOBAL.user-confirmation",confirmType:"delete",title:null==t?void 0:t.title,fieldType:"task"}})),null!==(n=this.modalRef)&&void 0!==n&&n.onHide&&this.modalRef.onHide.subscribe(l=>{"confirmed"==l&&(this.trackingService.trackFeature("Web.Task.Button.Delete.Click"),this._store.dispatch(new m.$d(null==t?void 0:t.id)))})}markAsDone(t){var n,i;if(t.isMarkedDone)return;const l=Object.assign(Object.assign({},t),{assignedFrom:null===(n=t.assignedFrom)||void 0===n?void 0:n.id,assignedToUserId:null===(i=t.assignedToUser)||void 0===i?void 0:i.id,isMarkedDone:!0,priority:k.NR[t.priority]});this.trackingService.trackFeature("Web.Task.Button.MerkDone.Click"),this._store.dispatch(new m.EB(l.id,l))}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(h.tT),e.\u0275\u0275directiveInject(h.UZ),e.\u0275\u0275directiveInject(S.yh),e.\u0275\u0275directiveInject(M.e))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["todo-actions"]],decls:6,vars:4,consts:[[1,"align-center"],["class","bg-accent-green icon-badge",3,"title",4,"ngIf"],["title","Delete","class","bg-light-red icon-badge",4,"ngIf"],[3,"title"],[1,"icon-badge",3,"ngClass","click"],["id","clkSaveTodo","data-automate-id","clkSaveTodo",1,"icon","ic-tick","m-auto","ic-xxs","ic-pale","icon-hover-white"],[1,"bg-accent-green","icon-badge",3,"title"],["id","clkEditTodo","data-automate-id","clkEditTodo",1,"icon","ic-pen","m-auto","ic-xxs",3,"click"],["title","Delete",1,"bg-light-red","icon-badge"],["id","clkDeleteTodo","data-automate-id","clkDeleteTodo",1,"icon","ic-delete","m-auto","ic-xxs",3,"click"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div",0),e.\u0275\u0275template(1,R,3,3,"div",1),e.\u0275\u0275template(2,N,2,0,"div",2),e.\u0275\u0275elementStart(3,"div",3)(4,"div",4),e.\u0275\u0275listener("click",function(){return n.markAsDone(n.params.data)}),e.\u0275\u0275element(5,"span",5),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.canEditTask),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.canDeleteTask),e.\u0275\u0275advance(1),e.\u0275\u0275property("title",null!=n.params.data&&n.params.data.isMarkedDone?"Completed":"Mark as complete"),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",null!=n.params.data&&n.params.data.isMarkedDone?"bg-linear-gradient-green pe-none":"bg-pearl bg-hover-green"))},dependencies:[c.mk,c.O5,u.X$],encapsulation:2}),a})();var G=s(15439),V=s(30166),b=s(32049);function W(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",19),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.TodoFilterType[t.data.status])}}function $(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",20),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.TodoFilterType[t.data.status])}}function K(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",21),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.TodoFilterType[t.data.status])}}function H(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",22),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.TodoFilterType[t.data.status])}}let Q=(()=>{class a{constructor(t,n,i){this.store=t,this.modalService=n,this.modalRef=i,this.stopper=new e.EventEmitter,this.moment=G,this.getAssignedToDetails=g.sW,this.TodoFilterType=k.yH,this.getTimeZoneDate=g.h5,this.store.dispatch(new V.MI),this.store.select(b.Sh).pipe((0,v.R)(this.stopper)).subscribe(l=>{this.users=l}),this.store.select(b.Xf).pipe((0,v.R)(this.stopper)).subscribe(l=>{this.userData=l})}markAsDone(t){var n,i;if(t.isMarkedDone)return;const l=Object.assign(Object.assign({},t),{assignedFrom:null===(n=t.assignedFrom)||void 0===n?void 0:n.id,assignedToUserId:null===(i=t.assignedToUser)||void 0===i?void 0:i.id,isMarkedDone:!0,priority:k.NR[t.priority]});this.store.dispatch(new m.EB(l.id,l)),this.modalRef.hide()}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(S.yh),e.\u0275\u0275directiveInject(h.tT),e.\u0275\u0275directiveInject(h.UZ))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["task-preview"]],decls:88,vars:60,consts:[[1,"flex-between","w-100","bg-coal","px-24","py-12","text-white"],[1,"fw-600"],[1,"icon","ic-close-secondary","ic-large","cursor-pointer",3,"click"],[1,"px-12","py-24","bg-white"],[1,"fw-semi-bold"],[1,"text-dark-gray","text-sm","my-12"],[1,"align-center"],[1,"flex-center-col"],[1,"dot","dot-x-xxl","bg-green-50","text-accent-green","fw-600","text-uppercase","mb-4"],[1,"icon","ic-down-arrow-secondary","ic-xxxl","ic-gray","rotate-270","mx-12","mb-16"],[1,"align-center","mt-12"],[1,"text-dark-gray","text-sm","mr-12"],[4,"ngIf"],[1,"btn","btn-xxs","br-20","flex-center","px-12","fw-semi-bold","pe-none",3,"ngClass"],[1,"text-dark-gray","text-sm","mr-8"],[1,"mx-24","text-dark-gray"],[1,"w-50"],[1,"border-bottom-slate","m-12"],[1,"text-white","fw-semi-bold","py-12","br-4","text-center","mt-16",3,"ngClass","click"],[1,"text-purple"],[1,"text-green-900"],[1,"text-orange"],[1,"text-red"]],template:function(t,n){1&t&&(e.\u0275\u0275elementStart(0,"div")(1,"div",0)(2,"h3",1),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",2),e.\u0275\u0275listener("click",function(){return n.modalRef.hide()}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(6,"div",3)(7,"h4",4),e.\u0275\u0275text(8),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(9,"div",5),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",6)(13,"div",7)(14,"h4",8),e.\u0275\u0275text(15),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"div"),e.\u0275\u0275text(17),e.\u0275\u0275elementEnd()(),e.\u0275\u0275element(18,"span",9),e.\u0275\u0275elementStart(19,"div",7)(20,"h4",8),e.\u0275\u0275text(21),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(22,"div"),e.\u0275\u0275text(23),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(24,"div",10)(25,"div",11),e.\u0275\u0275text(26),e.\u0275\u0275pipe(27,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(28,"div",4),e.\u0275\u0275template(29,W,3,1,"ng-container",12),e.\u0275\u0275template(30,$,3,1,"ng-container",12),e.\u0275\u0275template(31,K,3,1,"ng-container",12),e.\u0275\u0275template(32,H,3,1,"ng-container",12),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(33,"div",10)(34,"div",11),e.\u0275\u0275text(35),e.\u0275\u0275pipe(36,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(37,"div",13),e.\u0275\u0275pipe(38,"lowercase"),e.\u0275\u0275text(39),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(40,"div",10)(41,"div",14),e.\u0275\u0275text(42),e.\u0275\u0275pipe(43,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(44,"div",1),e.\u0275\u0275text(45),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(46,"div",15),e.\u0275\u0275text(47),e.\u0275\u0275pipe(48,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(49,"div",14),e.\u0275\u0275text(50),e.\u0275\u0275pipe(51,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(52,"div",1),e.\u0275\u0275text(53),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(54,"div",10)(55,"div",16)(56,"div",14),e.\u0275\u0275text(57),e.\u0275\u0275pipe(58,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(59,"div",1),e.\u0275\u0275text(60),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(61,"div",16)(62,"div",14),e.\u0275\u0275text(63),e.\u0275\u0275pipe(64,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(65,"div",1),e.\u0275\u0275text(66),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(67,"div",10)(68,"div",16)(69,"div",14),e.\u0275\u0275text(70),e.\u0275\u0275pipe(71,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(72,"div",1),e.\u0275\u0275text(73),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(74,"div",16)(75,"div",14),e.\u0275\u0275text(76),e.\u0275\u0275pipe(77,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(78,"div",1),e.\u0275\u0275text(79),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275element(80,"div",17),e.\u0275\u0275elementStart(81,"div",14),e.\u0275\u0275text(82),e.\u0275\u0275pipe(83,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(84,"div",1),e.\u0275\u0275text(85),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(86,"h5",18),e.\u0275\u0275listener("click",function(){return n.markAsDone(n.data)}),e.\u0275\u0275text(87),e.\u0275\u0275elementEnd()()()),2&t&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(4,34,"TASK.view-task"),""),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(n.data.title),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,36,"GLOBAL.assignee")),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1(" ",(null==n.data.assignedFrom?null:n.data.assignedFrom.firstName[0])+(null==n.data.assignedFrom?null:n.data.assignedFrom.lastName[0]),""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate((null==n.data.assignedFrom?null:n.data.assignedFrom.firstName)+" "+(null==n.data.assignedFrom?null:n.data.assignedFrom.lastName)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate1(" ",(null==n.data.assignedUsers||null==n.data.assignedUsers[0]?null:n.data.assignedUsers[0].firstName[0])+(null==n.data.assignedUsers||null==n.data.assignedUsers[0]?null:n.data.assignedUsers[0].lastName[0]),""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate((null==n.data.assignedUsers||null==n.data.assignedUsers[0]?null:n.data.assignedUsers[0].firstName)+" "+(null==n.data.assignedUsers||null==n.data.assignedUsers[0]?null:n.data.assignedUsers[0].lastName)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(27,38,"GLOBAL.status")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngIf",1==n.data.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",2==n.data.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",3==n.data.status),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",4==n.data.status),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(36,40,"GLOBAL.priority")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass","btn-"+e.\u0275\u0275pipeBind1(38,42,n.data.priority)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.data.priority),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(43,44,"GLOBAL.date")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",n.getTimeZoneDate(n.data.scheduledDateTime,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"dayMonthYear"),""),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(48,46,"GLOBAL.")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(51,48,"GLOBAL.time")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" ",n.getTimeZoneDate(n.data.scheduledDateTime,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"timeWithMeridiem")," "),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(58,50,"GLOBAL.created-by")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.getAssignedToDetails(null==n.data?null:n.data.createdBy,n.users,!0)||"--"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(64,52,"GLOBAL.created-date")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(" ",n.getTimeZoneDate(null==n.data?null:n.data.createdOn,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"dayMonthYear")," at ",n.getTimeZoneDate(null==n.data?null:n.data.createdOn,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"timeWithMeridiem"),""),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(71,54,"GLOBAL.modified-by")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(n.getAssignedToDetails(null==n.data?null:n.data.lastModifiedBy,n.users,!0)||"--"),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(77,56,"GLOBAL.modified-date")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(" ",n.getTimeZoneDate(null==n.data?null:n.data.lastModifiedOn,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"dayMonthYear")," at ",n.getTimeZoneDate(null==n.data?null:n.data.lastModifiedOn,null==n.userData||null==n.userData.timeZoneInfo?null:n.userData.timeZoneInfo.baseUTcOffset,"timeWithMeridiem")," "),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(83,58,"GLOBAL.description")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(null==n.data?null:n.data.notes),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngClass",null!=n.data&&n.data.isMarkedDone?"bg-linear-gradient-green":"bg-accent-green cursor-pointer border-green-30"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",null!=n.data&&n.data.isMarkedDone?"completed":"Mark as complete",""))},dependencies:[c.mk,c.O5,c.i8,u.X$],encapsulation:2}),a})();function X(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",1),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,t.params.data.priority))}}function Y(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",2),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,t.params.data.priority))}}function J(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",3),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,t.params.data.priority))}}function q(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"p",4),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,t.params.data.priority))}}let ee=(()=>{class a{constructor(){}agInit(t){this.params=t}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["task-priority"]],decls:4,vars:4,consts:[[4,"ngIf"],[1,"text-green"],[1,"text-yellow"],[1,"text-light-orange"],[1,"text-red"]],template:function(t,n){1&t&&(e.\u0275\u0275template(0,X,4,3,"ng-container",0),e.\u0275\u0275template(1,Y,4,3,"ng-container",0),e.\u0275\u0275template(2,J,4,3,"ng-container",0),e.\u0275\u0275template(3,q,4,3,"ng-container",0)),2&t&&(e.\u0275\u0275property("ngIf","Low"===n.params.data.priority),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Medium"===n.params.data.priority),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","High"===n.params.data.priority),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Critical"===n.params.data.priority))},dependencies:[c.O5,c.rS],encapsulation:2}),a})();var w=s(66623),te=s(84766),ne=s(68201),ae=s(48315),ie=s(22313),L=s(24376),se=s(11970),oe=s(17447),le=s(47511);function re(a,o){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",8)(2,"div",9)(3,"div",10),e.\u0275\u0275listener("click",function(){const l=e.\u0275\u0275restoreView(t).$implicit,r=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(r.filterTodoList(l.name))}),e.\u0275\u0275elementStart(4,"div",11),e.\u0275\u0275element(5,"span",12),e.\u0275\u0275elementStart(6,"span",13),e.\u0275\u0275text(7),e.\u0275\u0275pipe(8,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"div",14)(10,"h3"),e.\u0275\u0275text(11),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementContainerEnd()}if(2&a){const t=o.$implicit,n=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("id","clk",t.name,"Todos")("automate-id","clk",t.name,"Todos"),e.\u0275\u0275property("ngClass",t.bg)("ngClass",n.selectedTodoState==t.name?t.bg+"-active":t.bg),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngClass",t.icon),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(8,7,"GLOBAL."+t.name.toLowerCase())),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(n.todoFiltersCount[t.name.toLowerCase()])}}function de(a,o){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"button",23),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(i.addToDoModal())}),e.\u0275\u0275element(1,"span",24),e.\u0275\u0275elementStart(2,"span",25),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()()}2&a&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,1,"TASK.add-task")))}function pe(a,o){if(1&a&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",15)(2,"div",16)(3,"div",17)(4,"div",18),e.\u0275\u0275element(5,"ng-lottie",19),e.\u0275\u0275elementStart(6,"div",20)(7,"h4",21),e.\u0275\u0275text(8),e.\u0275\u0275pipe(9,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(10,de,5,3,"button",22),e.\u0275\u0275elementEnd()()()()(),e.\u0275\u0275elementContainerEnd()),2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(5),e.\u0275\u0275property("options",t.musoTask),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(9,3,"TASK.empty-message")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",t.canAdd)}}function ce(a,o){if(1&a&&(e.\u0275\u0275elementStart(0,"ng-option",42),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&a){const t=o.$implicit;e.\u0275\u0275property("value",t),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t,"")}}function me(a,o){if(1&a){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",15)(1,"div",26)(2,"div",27),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.addTaskModal())}),e.\u0275\u0275element(3,"span",28),e.\u0275\u0275elementStart(4,"span",29),e.\u0275\u0275text(5),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(7,"div",30)(8,"div",31)(9,"h3",32),e.\u0275\u0275text(10),e.\u0275\u0275pipe(11,"titlecase"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"div",33)(13,"span"),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(16,"ng-select",34),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const i=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(i.assignCount())})("ngModelChange",function(i){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.selectedPageSize=i)}),e.\u0275\u0275template(17,ce,2,2,"ng-option",35),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"span"),e.\u0275\u0275text(19),e.\u0275\u0275pipe(20,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(21,"div",36)(22,"ag-grid-angular",37,38),e.\u0275\u0275listener("gridReady",function(i){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.onGridReady(i))})("cellClicked",function(i){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.onCellClicked(i))}),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(24,"div",39)(25,"div",40),e.\u0275\u0275text(26),e.\u0275\u0275pipe(27,"translate"),e.\u0275\u0275pipe(28,"translate"),e.\u0275\u0275pipe(29,"translate"),e.\u0275\u0275pipe(30,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(31,"pagination",41),e.\u0275\u0275listener("pageChange",function(i){e.\u0275\u0275restoreView(t);const l=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(l.onPageChange(i))}),e.\u0275\u0275elementEnd()()()()}if(2&a){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(6,29,"TASK.add-task")),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(11,31,t.selectedTodoState)),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(15,33,"GLOBAL.show")),e.\u0275\u0275advance(2),e.\u0275\u0275property("virtualScroll",!0)("placeholder",t.pageSize)("searchable",!1)("ngModel",t.selectedPageSize),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.showEntriesSize),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(20,35,"GLOBAL.entries")),e.\u0275\u0275advance(3),e.\u0275\u0275property("gridOptions",t.gridOptions)("pagination",!0)("paginationPageSize",t.pageSize)("defaultColDef",t.gridOptions.defaultColDef)("rowData",t.filteredTodoList)("alwaysShowHorizontalScroll",!0)("alwaysShowVerticalScroll",!0)("icons",t.gridOptions.icons)("suppressPaginationPanel",!0),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate7("",e.\u0275\u0275pipeBind1(27,37,"GLOBAL.showing")," ",t.currOffset*t.pageSize+1," ",e.\u0275\u0275pipeBind1(28,39,"GLOBAL.to-small")," ",t.currOffset*t.pageSize+(null==t.rowData?null:t.rowData.length)," ",e.\u0275\u0275pipeBind1(29,41,"GLOBAL.of-small")," ",t.todoTotalCount," ",e.\u0275\u0275pipeBind1(30,43,"GLOBAL.entries-small"),""),e.\u0275\u0275advance(5),e.\u0275\u0275property("offset",t.currOffset)("limit",1)("range",1)("size",t.getPages(t.todoTotalCount,t.pageSize))}}function ge(a,o){if(1&a&&(e.\u0275\u0275elementStart(0,"div",2)(1,"div",3)(2,"div",4),e.\u0275\u0275template(3,re,12,9,"ng-container",5),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(4,pe,11,5,"ng-container",6),e.\u0275\u0275template(5,me,32,45,"ng-template",null,7,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementEnd()),2&a){const t=e.\u0275\u0275reference(6),n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",n.taskFilterTypes),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!(null!=n.filteredTodoList&&n.filteredTodoList.length))("ngIfElse",t)}}function ue(a,o){1&a&&(e.\u0275\u0275elementStart(0,"div",43),e.\u0275\u0275element(1,"application-loader"),e.\u0275\u0275elementEnd())}const ve=[{path:"",component:j,children:[{path:"",redirectTo:"manage-task",pathMatch:"full"},{path:"manage-task",component:(()=>{class a extends te.y{constructor(t,n,i,l,r,p,f,T){super(),this.modalService=t,this.todoService=n,this._store=i,this.headerTitle=l,this.gridOptionsService=r,this.metaTitle=p,this.cdr=f,this.trackingService=T,this.stopper=new e.EventEmitter,this.pageSize=_.IV1,this.filteredTodoList=[],this.selectedTodoItem="",this.assignedTo={id:"",displayName:"All"},this.selectedTodoState="All",this.appliedFilter={todoFilterType:this.selectedTodoState,pageNumber:1,pageSize:this.pageSize},this.filtersPayload={todoFilterType:0,pageNumber:1,pageSize:this.pageSize,path:"todo"},this.taskFilterTypes=_.VLw,this.todoFiltersCount={today:0,upcoming:0,overdue:0,completed:0,all:0},this.rowData=[],this.columnDropDown=[],this.showEntriesSize=_.gv9,this.currOffset=0,this.musoTask={path:"assets/animations/no-task.json"},this.canView=!1,this.canAdd=!1,this.getPages=g.UQ,this.gridOptions=this.gridOptionsService.getGridSettings(this),this.defaultColDef=this.gridOptions.defaultColDef,this.gridOptions.rowData=this.rowData,this.initializeGridSettings(),this._store.select(y.HF).subscribe(d=>{null!=d&&d.includes("Todos")&&(this.canView=!0)}),this._store.select(y.it).subscribe(d=>{null!=d&&d.includes("Todos")&&(this.canAdd=!0)}),this._store.select(w.dC).subscribe(d=>{this.todoInfo=d||{},this.filteredTodoList=this.todoInfo.todos||[],this.rowData=this.filteredTodoList}),this._store.select(b.Xf).pipe((0,v.R)(this.stopper)).subscribe(d=>{this.userBasicDetails=d})}ngOnInit(){var t,n,i,l,r,p,f,T;this.selectedPageSize=10,this.cdr.detectChanges(),this.appliedFilter=Object.assign(Object.assign({},this.appliedFilter),{timeZoneId:(null===(n=null===(t=this.userBasicDetails)||void 0===t?void 0:t.timeZoneInfo)||void 0===n?void 0:n.timeZoneId)||(0,g.p5)(),baseUTcOffset:(null===(l=null===(i=this.userBasicDetails)||void 0===i?void 0:i.timeZoneInfo)||void 0===l?void 0:l.baseUTcOffset)||(0,g.Bj)()}),this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{timeZoneId:(null===(p=null===(r=this.userBasicDetails)||void 0===r?void 0:r.timeZoneInfo)||void 0===p?void 0:p.timeZoneId)||(0,g.p5)(),baseUTcOffset:(null===(T=null===(f=this.userBasicDetails)||void 0===f?void 0:f.timeZoneInfo)||void 0===T?void 0:T.baseUTcOffset)||(0,g.Bj)()}),this._store.dispatch(new m.ey(this.filtersPayload)),this._store.dispatch(new m.MQ(this.filtersPayload)),this._store.select(w.v).pipe((0,v.R)(this.stopper)).subscribe(d=>this.isTaskLoading=d),this._store.select(w.ul).subscribe(d=>{this.todoItemCount=d.itemsCount,this.todoFiltersCount={today:d.today,upcoming:d.upcoming,overdue:d.overdue,completed:d.completed,all:d.all},this.todoTotalCount=this.todoFiltersCount[this.selectedTodoState.toLocaleLowerCase()]}),this.headerTitle.setLangTitle("TASK.manage-tasks"),this.metaTitle.setTitle("CRM | Task"),this.initializeGridSettings(),this.trackingService.trackFeature("Web.Task.Page.Task.Visit")}initializeGridSettings(){this.gridOptions=this.gridOptionsService.getGridSettings(this),this.gridOptions.columnDefs=[{headerName:"Priority",field:"Priority",minWidth:85,cellClass:"cursor-pointer",valueGetter:t=>[t.data.priority],cellRenderer:ee},{headerName:"Task Name",field:"Task Name",minWidth:120,cellClass:"cursor-pointer",valueGetter:t=>[t.data.title],cellRenderer:t=>`<p class="text-truncate-1 break-all">${t.value}</p>`},{headerName:"Assigned To",field:"Assigned To",minWidth:100,cellClass:"cursor-pointer",valueGetter:t=>[t.data.assignedUsers.map(n=>n.firstName+" "+n.lastName)],cellRenderer:t=>`<p class="text-truncate-1 break-all">\n          ${t.value[0]?t.value[0]:""}</p>`},{headerName:"Scheduled Date",field:"Scheduled Date",minWidth:163,cellClass:"cursor-pointer",valueGetter:t=>{var n,i;return[t.data.scheduledDateTime?(0,g.h5)(t.data.scheduledDateTime,null===(i=null===(n=this.userBasicDetails)||void 0===n?void 0:n.timeZoneInfo)||void 0===i?void 0:i.baseUTcOffset):""]},cellRenderer:t=>{var n,i,l,r,p;return`<p>${t.value[0]}</p> \n            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${(null===(i=null===(n=this.userBasicDetails)||void 0===n?void 0:n.timeZoneInfo)||void 0===i?void 0:i.timeZoneName)&&(null===(l=this.userBasicDetails)||void 0===l?void 0:l.shouldShowTimeZone)&&t.value[0]?"("+(null===(p=null===(r=this.userBasicDetails)||void 0===r?void 0:r.timeZoneInfo)||void 0===p?void 0:p.timeZoneName)+")":""}</p>`}},{headerName:"Task Description",field:"Task Description",minWidth:100,cellClass:"cursor-pointer",valueGetter:t=>[t.data.notes],cellRenderer:t=>`<p class="text-truncate-1 text-break">${t.value}</p>`},{headerName:"Actions",minWidth:115,maxWidth:115,filter:!1,cellRenderer:U}],this.gridOptions.columnDefs.forEach((t,n)=>{n!=this.gridOptions.columnDefs.length-1&&this.columnDropDown.push({field:t.field,hide:t.hide})}),this.gridOptions.context={componentParent:this}}onCellClicked(t){"Actions"!==t.colDef.headerName&&this.modalService.show(Q,{class:"right-modal modal-350 ph-modal-unset",initialState:{data:t.data}})}addTaskModal(){this.trackingService.trackFeature("Web.Task.Button.AddTask.Click"),this.modalService.show(I.K,{class:"right-modal modal-350"})}filterTodoList(t){var n,i,l,r,p;this.selectedTodoState=t,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{todoFilterType:k.yH[t],pageNumber:null===(n=this.appliedFilter)||void 0===n?void 0:n.pageNumber,pageSize:this.pageSize,timeZoneId:(null===(l=null===(i=this.userBasicDetails)||void 0===i?void 0:i.timeZoneInfo)||void 0===l?void 0:l.timeZoneId)||(0,g.p5)(),baseUTcOffset:(null===(p=null===(r=this.userBasicDetails)||void 0===r?void 0:r.timeZoneInfo)||void 0===p?void 0:p.baseUTcOffset)||(0,g.Bj)()}),this.currOffset=0,this.trackingService.trackFeature(`Web.Task.Button.Filter${t}.Click`),this._store.dispatch(new m.ey(this.filtersPayload)),this._store.dispatch(new m.MQ(this.filtersPayload))}assignCount(){var t;this.pageSize=this.selectedPageSize,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageSize:this.pageSize,pageNumber:1}),this.trackingService.trackFeature(`Web.Task.Option.${this.pageSize}.Click`),this._store.dispatch(new m.ey(this.filtersPayload)),this._store.dispatch(new m.MQ(this.filtersPayload)),this.gridOptions.paginationPageSize=this.pageSize,null===(t=this.gridOptions.api)||void 0===t||t.paginationSetPageSize(this.selectedPageSize),this.gridApi.setRowData([]),this.gridApi.applyTransaction({add:this.rowData}),this.currOffset=0}onPageChange(t){this.currOffset=t,this.filtersPayload=Object.assign(Object.assign({},this.filtersPayload),{pageNumber:t+1,pageSize:this.pageSize}),this._store.dispatch(new m.ey(this.filtersPayload)),this._store.dispatch(new m.MQ(this.filtersPayload))}addToDoModal(){this.modalService.show(I.K,{class:"right-modal modal-350"})}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return a.\u0275fac=function(t){return new(t||a)(e.\u0275\u0275directiveInject(h.tT),e.\u0275\u0275directiveInject(ne.w),e.\u0275\u0275directiveInject(S.yh),e.\u0275\u0275directiveInject(B.g),e.\u0275\u0275directiveInject(ae.t),e.\u0275\u0275directiveInject(ie.Dx),e.\u0275\u0275directiveInject(e.ChangeDetectorRef),e.\u0275\u0275directiveInject(M.e))},a.\u0275cmp=e.\u0275\u0275defineComponent({type:a,selectors:[["manage-task"]],features:[e.\u0275\u0275InheritDefinitionFeature],decls:3,vars:2,consts:[["class","card-content border-top coldt-layout h-100 ip-flex-col tb-w-100",4,"ngIf","ngIfElse"],["loader",""],[1,"card-content","border-top","coldt-layout","h-100","ip-flex-col","tb-w-100"],[1,"h-100","pb-50","p-20","left-container","xl","ip-w-100","tb-w-25"],[1,"d-flex","flex-wrap"],[4,"ngFor","ngForOf"],[4,"ngIf","ngIfElse"],["taskData",""],[1,"p-10","w-50","tb-w-100","ip-w-50","ph-w-40"],[1,"card","clear-margin","br-5","cursor-pointer",3,"id","automate-id","ngClass"],[1,"card-body","py-12","px-24","cursor-pointer","text-white",3,"click"],[1,"align-center","mb-12"],[1,"icon","icon-xs",3,"ngClass"],[1,"mt-4","ml-8","text-white"],[1,"justify-end","text-white"],[1,"flex-grow-1","right-container","bg-secondary","todo-card-right"],[1,"p-30","tb-w-75","ip-p-0","ip-w-100"],[1,"flex-center-col","h-100-176","min-h-250","ip-h-auto"],[1,"d-flex","ip-flex-col"],["height","550px",3,"options"],[1,"flex-center-col","ml-30","ip-ml-0"],[1,"text-gray","fw-600","text-center","mb-30","w-300","ip-mb-20"],["class","btn-coal btn-large w-120","id","btnToDo","data-automate-id","btToDo",3,"click",4,"ngIf"],["id","btnToDo","data-automate-id","btToDo",1,"btn-coal","btn-large","w-120",3,"click"],[1,"ic-add","icon","icon-xxs","mr-8"],[1,"text-xl","fw-700"],[1,"flex-end","mr-16","mt-10"],[1,"btn-coal","mr-20",3,"click"],[1,"ic-add","icon","ic-xxs"],[1,"ml-8","ph-d-none"],[1,"p-30"],[1,"flex-between","mb-24"],[1,"fw-600","text-gray"],[1,"align-center","show-dropdown"],["bindValue","id","ResizableDropdown","",1,"w-70px","mx-10",3,"virtualScroll","placeholder","searchable","ngModel","change","ngModelChange"],["name","showEntriesSize",3,"value",4,"ngFor","ngForOf"],[1,"manage-user"],[1,"ag-theme-alpine",3,"gridOptions","pagination","paginationPageSize","defaultColDef","rowData","alwaysShowHorizontalScroll","alwaysShowVerticalScroll","icons","suppressPaginationPanel","gridReady","cellClicked"],["agGrid",""],[1,"flex-end","mt-20"],[1,"mr-10"],[3,"offset","limit","range","size","pageChange"],["name","showEntriesSize",3,"value"],[1,"flex-center","h-100"]],template:function(t,n){if(1&t&&(e.\u0275\u0275template(0,ge,7,3,"div",0),e.\u0275\u0275template(1,ue,2,0,"ng-template",null,1,e.\u0275\u0275templateRefExtractor)),2&t){const i=e.\u0275\u0275reference(2);e.\u0275\u0275property("ngIf",n.canView&&!n.isTaskLoading)("ngIfElse",i)}},dependencies:[c.mk,c.sg,c.O5,L.w9,L.jq,se.Q,oe.s,le.t,E.N8,C.JJ,C.On,O.e$,c.rS,u.X$],encapsulation:2}),a})()}]}];let he=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({imports:[x.Bz.forChild(ve),x.Bz]}),a})();var P=s(51190);let fe=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.\u0275\u0275defineNgModule({type:a}),a.\u0275inj=e.\u0275\u0275defineInjector({providers:[u.sK],imports:[c.ez,x.Bz.forChild(z._),he,A.m,E.sF,C.UX,C.u5,O.CT.forRoot({player:P.xd}),u.aw.forChild({loader:{provide:u.Zw,useFactory:P.gS,deps:[F.eN]}})]}),a})()}}]);