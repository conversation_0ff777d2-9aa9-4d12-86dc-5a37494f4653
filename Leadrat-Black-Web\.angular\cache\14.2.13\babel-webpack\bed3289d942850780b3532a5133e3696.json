{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function scan(accumulator, seed) {\n  let hasSeed = false;\n\n  if (arguments.length >= 2) {\n    hasSeed = true;\n  }\n\n  return function scanOperatorFunction(source) {\n    return source.lift(new ScanOperator(accumulator, seed, hasSeed));\n  };\n}\n\nclass ScanOperator {\n  constructor(accumulator, seed, hasSeed = false) {\n    this.accumulator = accumulator;\n    this.seed = seed;\n    this.hasSeed = hasSeed;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ScanSubscriber(subscriber, this.accumulator, this.seed, this.hasSeed));\n  }\n\n}\n\nclass ScanSubscriber extends Subscriber {\n  constructor(destination, accumulator, _seed, hasSeed) {\n    super(destination);\n    this.accumulator = accumulator;\n    this._seed = _seed;\n    this.hasSeed = hasSeed;\n    this.index = 0;\n  }\n\n  get seed() {\n    return this._seed;\n  }\n\n  set seed(value) {\n    this.hasSeed = true;\n    this._seed = value;\n  }\n\n  _next(value) {\n    if (!this.hasSeed) {\n      this.seed = value;\n      this.destination.next(value);\n    } else {\n      return this._tryNext(value);\n    }\n  }\n\n  _tryNext(value) {\n    const index = this.index++;\n    let result;\n\n    try {\n      result = this.accumulator(this.seed, value, index);\n    } catch (err) {\n      this.destination.error(err);\n    }\n\n    this.seed = result;\n    this.destination.next(result);\n  }\n\n} //# sourceMappingURL=scan.js.map", "map": null, "metadata": {}, "sourceType": "module"}