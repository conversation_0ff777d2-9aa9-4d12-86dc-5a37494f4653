{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function skip(count) {\n  return source => source.lift(new SkipOperator(count));\n}\n\nclass SkipOperator {\n  constructor(total) {\n    this.total = total;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SkipSubscriber(subscriber, this.total));\n  }\n\n}\n\nclass SkipSubscriber extends Subscriber {\n  constructor(destination, total) {\n    super(destination);\n    this.total = total;\n    this.count = 0;\n  }\n\n  _next(x) {\n    if (++this.count > this.total) {\n      this.destination.next(x);\n    }\n  }\n\n} //# sourceMappingURL=skip.js.map", "map": null, "metadata": {}, "sourceType": "module"}