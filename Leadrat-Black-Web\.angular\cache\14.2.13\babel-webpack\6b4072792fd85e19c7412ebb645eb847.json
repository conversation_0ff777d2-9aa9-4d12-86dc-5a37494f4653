{"ast": null, "code": "import Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport extractNationalNumber from './extractNationalNumber.js';\nimport checkNumberLength from './checkNumberLength.js';\nimport getCountryCallingCode from '../getCountryCallingCode.js';\n/**\r\n * Sometimes some people incorrectly input international phone numbers\r\n * without the leading `+`. This function corrects such input.\r\n * @param  {string} number — Phone number digits.\r\n * @param  {string?} country\r\n * @param  {string?} callingCode\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCode: string?, number: string }`.\r\n */\n\nexport default function extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata) {\n  var countryCallingCode = country ? getCountryCallingCode(country, metadata) : callingCode;\n\n  if (number.indexOf(countryCallingCode) === 0) {\n    metadata = new Metadata(metadata);\n    metadata.selectNumberingPlan(country, callingCode);\n    var possibleShorterNumber = number.slice(countryCallingCode.length);\n\n    var _extractNationalNumbe = extractNationalNumber(possibleShorterNumber, metadata),\n        possibleShorterNationalNumber = _extractNationalNumbe.nationalNumber;\n\n    var _extractNationalNumbe2 = extractNationalNumber(number, metadata),\n        nationalNumber = _extractNationalNumbe2.nationalNumber; // If the number was not valid before but is valid now,\n    // or if it was too long before, we consider the number\n    // with the country calling code stripped to be a better result\n    // and keep that instead.\n    // For example, in Germany (+49), `49` is a valid area code,\n    // so if a number starts with `49`, it could be both a valid\n    // national German number or an international number without\n    // a leading `+`.\n\n\n    if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) && matchesEntirely(possibleShorterNationalNumber, metadata.nationalNumberPattern()) || checkNumberLength(nationalNumber, metadata) === 'TOO_LONG') {\n      return {\n        countryCallingCode: countryCallingCode,\n        number: possibleShorterNumber\n      };\n    }\n  }\n\n  return {\n    number: number\n  };\n} //# sourceMappingURL=extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js.map", "map": null, "metadata": {}, "sourceType": "module"}