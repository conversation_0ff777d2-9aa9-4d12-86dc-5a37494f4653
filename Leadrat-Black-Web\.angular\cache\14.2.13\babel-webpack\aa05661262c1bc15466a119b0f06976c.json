{"ast": null, "code": "import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n/** `Object#toString` result references. */\n\nvar setTag = '[object Set]';\n/**\n * The base implementation of `_.isSet` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n */\n\nfunction baseIsSet(value) {\n  return isObjectLike(value) && getTag(value) == setTag;\n}\n\nexport default baseIsSet;", "map": null, "metadata": {}, "sourceType": "module"}