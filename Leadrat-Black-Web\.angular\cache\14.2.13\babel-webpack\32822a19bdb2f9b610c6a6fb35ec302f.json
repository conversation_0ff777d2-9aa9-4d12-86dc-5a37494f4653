{"ast": null, "code": "import trimmedEndIndex from './_trimmedEndIndex.js';\n/** Used to match leading whitespace. */\n\nvar reTrimStart = /^\\s+/;\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\n\nfunction baseTrim(string) {\n  return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;\n}\n\nexport default baseTrim;", "map": null, "metadata": {}, "sourceType": "module"}