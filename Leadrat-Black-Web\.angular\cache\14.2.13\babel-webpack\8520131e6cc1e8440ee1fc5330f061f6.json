{"ast": null, "code": "import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function window(windowBoundaries) {\n  return function windowOperatorFunction(source) {\n    return source.lift(new WindowOperator(windowBoundaries));\n  };\n}\n\nclass WindowOperator {\n  constructor(windowBoundaries) {\n    this.windowBoundaries = windowBoundaries;\n  }\n\n  call(subscriber, source) {\n    const windowSubscriber = new WindowSubscriber(subscriber);\n    const sourceSubscription = source.subscribe(windowSubscriber);\n\n    if (!sourceSubscription.closed) {\n      windowSubscriber.add(innerSubscribe(this.windowBoundaries, new SimpleInnerSubscriber(windowSubscriber)));\n    }\n\n    return sourceSubscription;\n  }\n\n}\n\nclass WindowSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.window = new Subject();\n    destination.next(this.window);\n  }\n\n  notifyNext() {\n    this.openWindow();\n  }\n\n  notifyError(error) {\n    this._error(error);\n  }\n\n  notifyComplete() {\n    this._complete();\n  }\n\n  _next(value) {\n    this.window.next(value);\n  }\n\n  _error(err) {\n    this.window.error(err);\n    this.destination.error(err);\n  }\n\n  _complete() {\n    this.window.complete();\n    this.destination.complete();\n  }\n\n  _unsubscribe() {\n    this.window = null;\n  }\n\n  openWindow() {\n    const prevWindow = this.window;\n\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n\n    const destination = this.destination;\n    const newWindow = this.window = new Subject();\n    destination.next(newWindow);\n  }\n\n} //# sourceMappingURL=window.js.map", "map": null, "metadata": {}, "sourceType": "module"}