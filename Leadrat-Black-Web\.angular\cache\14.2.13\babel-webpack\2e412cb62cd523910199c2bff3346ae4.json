{"ast": null, "code": "/**\n * A specialized version of `matchesProperty` for source values suitable\n * for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction matchesStrictComparable(key, srcValue) {\n  return function (object) {\n    if (object == null) {\n      return false;\n    }\n\n    return object[key] === srcValue && (srcValue !== undefined || key in Object(object));\n  };\n}\n\nexport default matchesStrictComparable;", "map": null, "metadata": {}, "sourceType": "module"}