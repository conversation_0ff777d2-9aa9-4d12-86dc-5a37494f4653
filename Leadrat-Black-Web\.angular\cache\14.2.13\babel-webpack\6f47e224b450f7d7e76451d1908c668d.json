{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { EmptyError } from '../util/EmptyError';\nexport function single(predicate) {\n  return source => source.lift(new SingleOperator(predicate, source));\n}\n\nclass SingleOperator {\n  constructor(predicate, source) {\n    this.predicate = predicate;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SingleSubscriber(subscriber, this.predicate, this.source));\n  }\n\n}\n\nclass SingleSubscriber extends Subscriber {\n  constructor(destination, predicate, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.source = source;\n    this.seenValue = false;\n    this.index = 0;\n  }\n\n  applySingleValue(value) {\n    if (this.seenValue) {\n      this.destination.error('Sequence contains more than one element');\n    } else {\n      this.seenValue = true;\n      this.singleValue = value;\n    }\n  }\n\n  _next(value) {\n    const index = this.index++;\n\n    if (this.predicate) {\n      this.tryNext(value, index);\n    } else {\n      this.applySingleValue(value);\n    }\n  }\n\n  tryNext(value, index) {\n    try {\n      if (this.predicate(value, index, this.source)) {\n        this.applySingleValue(value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  _complete() {\n    const destination = this.destination;\n\n    if (this.index > 0) {\n      destination.next(this.seenValue ? this.singleValue : undefined);\n      destination.complete();\n    } else {\n      destination.error(new EmptyError());\n    }\n  }\n\n} //# sourceMappingURL=single.js.map", "map": null, "metadata": {}, "sourceType": "module"}