{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { isScheduler } from '../util/isScheduler';\nexport function startWith(...array) {\n  const scheduler = array[array.length - 1];\n\n  if (isScheduler(scheduler)) {\n    array.pop();\n    return source => concat(array, source, scheduler);\n  } else {\n    return source => concat(array, source);\n  }\n} //# sourceMappingURL=startWith.js.map", "map": null, "metadata": {}, "sourceType": "module"}