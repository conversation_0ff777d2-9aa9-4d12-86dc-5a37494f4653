{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function skipWhile(predicate) {\n  return source => source.lift(new Skip<PERSON>hileOperator(predicate));\n}\n\nclass Skip<PERSON>hileOperator {\n  constructor(predicate) {\n    this.predicate = predicate;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));\n  }\n\n}\n\nclass Skip<PERSON>hileSubscriber extends Subscriber {\n  constructor(destination, predicate) {\n    super(destination);\n    this.predicate = predicate;\n    this.skipping = true;\n    this.index = 0;\n  }\n\n  _next(value) {\n    const destination = this.destination;\n\n    if (this.skipping) {\n      this.tryCallPredicate(value);\n    }\n\n    if (!this.skipping) {\n      destination.next(value);\n    }\n  }\n\n  tryCallPredicate(value) {\n    try {\n      const result = this.predicate(value, this.index++);\n      this.skipping = Boolean(result);\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n} //# sourceMappingURL=skipWhile.js.map", "map": null, "metadata": {}, "sourceType": "module"}