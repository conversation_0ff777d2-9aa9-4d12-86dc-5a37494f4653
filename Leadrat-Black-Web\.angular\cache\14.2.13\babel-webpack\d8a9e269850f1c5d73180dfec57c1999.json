{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Subject } from '../Subject';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n  return function windowCountOperatorFunction(source) {\n    return source.lift(new WindowCountOperator(windowSize, startWindowEvery));\n  };\n}\n\nclass WindowCountOperator {\n  constructor(windowSize, startWindowEvery) {\n    this.windowSize = windowSize;\n    this.startWindowEvery = startWindowEvery;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowCountSubscriber(subscriber, this.windowSize, this.startWindowEvery));\n  }\n\n}\n\nclass WindowCountSubscriber extends Subscriber {\n  constructor(destination, windowSize, startWindowEvery) {\n    super(destination);\n    this.destination = destination;\n    this.windowSize = windowSize;\n    this.startWindowEvery = startWindowEvery;\n    this.windows = [new Subject()];\n    this.count = 0;\n    destination.next(this.windows[0]);\n  }\n\n  _next(value) {\n    const startWindowEvery = this.startWindowEvery > 0 ? this.startWindowEvery : this.windowSize;\n    const destination = this.destination;\n    const windowSize = this.windowSize;\n    const windows = this.windows;\n    const len = windows.length;\n\n    for (let i = 0; i < len && !this.closed; i++) {\n      windows[i].next(value);\n    }\n\n    const c = this.count - windowSize + 1;\n\n    if (c >= 0 && c % startWindowEvery === 0 && !this.closed) {\n      windows.shift().complete();\n    }\n\n    if (++this.count % startWindowEvery === 0 && !this.closed) {\n      const window = new Subject();\n      windows.push(window);\n      destination.next(window);\n    }\n  }\n\n  _error(err) {\n    const windows = this.windows;\n\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().error(err);\n      }\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    const windows = this.windows;\n\n    if (windows) {\n      while (windows.length > 0 && !this.closed) {\n        windows.shift().complete();\n      }\n    }\n\n    this.destination.complete();\n  }\n\n  _unsubscribe() {\n    this.count = 0;\n    this.windows = null;\n  }\n\n} //# sourceMappingURL=windowCount.js.map", "map": null, "metadata": {}, "sourceType": "module"}