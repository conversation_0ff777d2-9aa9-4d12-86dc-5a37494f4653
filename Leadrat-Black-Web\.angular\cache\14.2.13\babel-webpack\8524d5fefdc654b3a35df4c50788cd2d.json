{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/listing/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\n\nfunction openDB(name, version, {\n  blocked,\n  upgrade,\n  blocking,\n  terminated\n} = {}) {\n  const request = indexedDB.open(name, version);\n  const openPromise = wrap(request);\n\n  if (upgrade) {\n    request.addEventListener('upgradeneeded', event => {\n      upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n    });\n  }\n\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked( // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event.newVersion, event));\n  }\n\n  openPromise.then(db => {\n    if (terminated) db.addEventListener('close', () => terminated());\n\n    if (blocking) {\n      db.addEventListener('versionchange', event => blocking(event.oldVersion, event.newVersion, event));\n    }\n  }).catch(() => {});\n  return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\n\n\nfunction deleteDB(name, {\n  blocked\n} = {}) {\n  const request = indexedDB.deleteDatabase(name);\n\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked( // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event));\n  }\n\n  return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\n\nfunction getMethod(target, prop) {\n  if (!(target instanceof IDBDatabase && !(prop in target) && typeof prop === 'string')) {\n    return;\n  }\n\n  if (cachedMethods.get(prop)) return cachedMethods.get(prop);\n  const targetFuncName = prop.replace(/FromIndex$/, '');\n  const useIndex = prop !== targetFuncName;\n  const isWrite = writeMethods.includes(targetFuncName);\n\n  if ( // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n  !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) || !(isWrite || readMethods.includes(targetFuncName))) {\n    return;\n  }\n\n  const method = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (storeName, ...args) {\n      // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n      const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n      let target = tx.store;\n      if (useIndex) target = target.index(args.shift()); // Must reject if op rejects.\n      // If it's a write operation, must reject if tx.done rejects.\n      // Must reject with op rejection first.\n      // Must resolve with op value.\n      // Must handle both promises (no unhandled rejections)\n\n      return (yield Promise.all([target[targetFuncName](...args), isWrite && tx.done]))[0];\n    });\n\n    return function method(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n\n  cachedMethods.set(prop, method);\n  return method;\n}\n\nreplaceTraps(oldTraps => ({ ...oldTraps,\n  get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n  has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop)\n}));\nexport { deleteDB, openDB };", "map": null, "metadata": {}, "sourceType": "module"}