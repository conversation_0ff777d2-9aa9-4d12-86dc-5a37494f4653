{"ast": null, "code": "import ListCache from './_ListCache.js';\nimport Map from './_Map.js';\nimport MapCache from './_MapCache.js';\n/** Used as the size to enable large array optimizations. */\n\nvar LARGE_ARRAY_SIZE = 200;\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\n\nfunction stackSet(key, value) {\n  var data = this.__data__;\n\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n\n    if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n\n    data = this.__data__ = new MapCache(pairs);\n  }\n\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\nexport default stackSet;", "map": null, "metadata": {}, "sourceType": "module"}