{"ast": null, "code": "/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\nexport default function matchesEntirely(text, regular_expression) {\n  // If assigning the `''` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  text = text || '';\n  return new RegExp('^(?:' + regular_expression + ')$').test(text);\n} //# sourceMappingURL=matchesEntirely.js.map", "map": null, "metadata": {}, "sourceType": "module"}