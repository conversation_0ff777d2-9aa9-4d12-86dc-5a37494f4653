{"ast": null, "code": "import { config } from './config';\nimport { hostReportError } from './util/hostReportError';\nexport const empty = {\n  closed: true,\n\n  next(value) {},\n\n  error(err) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n      throw err;\n    } else {\n      hostReportError(err);\n    }\n  },\n\n  complete() {}\n\n}; //# sourceMappingURL=Observer.js.map", "map": null, "metadata": {}, "sourceType": "module"}