{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { rxSubscriber as rxSubscriberSymbol } from '../symbol/rxSubscriber';\nimport { empty as emptyObserver } from '../Observer';\nexport function toSubscriber(nextOrObserver, error, complete) {\n  if (nextOrObserver) {\n    if (nextOrObserver instanceof Subscriber) {\n      return nextOrObserver;\n    }\n\n    if (nextOrObserver[rxSubscriberSymbol]) {\n      return nextOrObserver[rxSubscriberSymbol]();\n    }\n  }\n\n  if (!nextOrObserver && !error && !complete) {\n    return new Subscriber(emptyObserver);\n  }\n\n  return new Subscriber(nextOrObserver, error, complete);\n} //# sourceMappingURL=toSubscriber.js.map", "map": null, "metadata": {}, "sourceType": "module"}