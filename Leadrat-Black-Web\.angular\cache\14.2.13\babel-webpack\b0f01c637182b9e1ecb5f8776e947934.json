{"ast": null, "code": "import { subscribeToArray } from './subscribeToArray';\nimport { subscribeToPromise } from './subscribeToPromise';\nimport { subscribeToIterable } from './subscribeToIterable';\nimport { subscribeToObservable } from './subscribeToObservable';\nimport { isArrayLike } from './isArrayLike';\nimport { isPromise } from './isPromise';\nimport { isObject } from './isObject';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeTo = result => {\n  if (!!result && typeof result[Symbol_observable] === 'function') {\n    return subscribeToObservable(result);\n  } else if (isArrayLike(result)) {\n    return subscribeToArray(result);\n  } else if (isPromise(result)) {\n    return subscribeToPromise(result);\n  } else if (!!result && typeof result[Symbol_iterator] === 'function') {\n    return subscribeToIterable(result);\n  } else {\n    const value = isObject(result) ? 'an invalid object' : `'${result}'`;\n    const msg = `You provided ${value} where a stream was expected.` + ' You can provide an Observable, Promise, Array, or Iterable.';\n    throw new TypeError(msg);\n  }\n}; //# sourceMappingURL=subscribeTo.js.map", "map": null, "metadata": {}, "sourceType": "module"}