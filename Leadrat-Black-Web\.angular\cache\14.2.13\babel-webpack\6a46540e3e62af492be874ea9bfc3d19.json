{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n/**\n * The base implementation of `_.sampleSize` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\n\nfunction baseSampleSize(collection, n) {\n  var array = values(collection);\n  return shuffleSelf(array, baseClamp(n, 0, array.length));\n}\n\nexport default baseSampleSize;", "map": null, "metadata": {}, "sourceType": "module"}