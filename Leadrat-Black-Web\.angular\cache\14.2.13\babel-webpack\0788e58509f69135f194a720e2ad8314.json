{"ast": null, "code": "import memoize from './memoize.js';\n/** Used as the maximum memoize cache size. */\n\nvar MAX_MEMOIZE_SIZE = 500;\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\n\nfunction memoizeCapped(func) {\n  var result = memoize(func, function (key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n\n    return key;\n  });\n  var cache = result.cache;\n  return result;\n}\n\nexport default memoizeCapped;", "map": null, "metadata": {}, "sourceType": "module"}