{"ast": null, "code": "/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function (value, index, collection) {\n    accumulator = initAccum ? (initAccum = false, value) : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nexport default baseReduce;", "map": null, "metadata": {}, "sourceType": "module"}