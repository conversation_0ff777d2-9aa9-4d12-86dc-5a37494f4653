{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n\n/**\n * @module watchdog/utils/areconnectedthroughproperties\n */\n\n/* globals console */\nimport getSubNodes from './getsubnodes.js';\n/**\n * Traverses both structures to find out whether there is a reference that is shared between both structures.\n */\n\nexport default function areConnectedThroughProperties(target1, target2, excludedNodes = new Set()) {\n  if (target1 === target2 && isObject(target1)) {\n    return true;\n  } // @if CK_DEBUG_WATCHDOG // return checkConnectionBetweenProps( target1, target2, excludedNodes );\n\n\n  const subNodes1 = getSubNodes(target1, excludedNodes);\n  const subNodes2 = getSubNodes(target2, excludedNodes);\n\n  for (const node of subNodes1) {\n    if (subNodes2.has(node)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n/* istanbul ignore next -- @preserve */\n// eslint-disable-next-line\n\nfunction checkConnectionBetweenProps(target1, target2, excludedNodes) {\n  const {\n    subNodes: subNodes1,\n    prevNodeMap: prevNodeMap1\n  } = getSubNodes(target1, excludedNodes.subNodes);\n  const {\n    subNodes: subNodes2,\n    prevNodeMap: prevNodeMap2\n  } = getSubNodes(target2, excludedNodes.subNodes);\n\n  for (const sharedNode of subNodes1) {\n    if (subNodes2.has(sharedNode)) {\n      const connection = [];\n      connection.push(sharedNode);\n      let node = prevNodeMap1.get(sharedNode);\n\n      while (node && node !== target1) {\n        connection.push(node);\n        node = prevNodeMap1.get(node);\n      }\n\n      node = prevNodeMap2.get(sharedNode);\n\n      while (node && node !== target2) {\n        connection.unshift(node);\n        node = prevNodeMap2.get(node);\n      }\n\n      console.log('--------');\n      console.log({\n        target1\n      });\n      console.log({\n        sharedNode\n      });\n      console.log({\n        target2\n      });\n      console.log({\n        connection\n      });\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction isObject(structure) {\n  return typeof structure === 'object' && structure !== null;\n}", "map": null, "metadata": {}, "sourceType": "module"}