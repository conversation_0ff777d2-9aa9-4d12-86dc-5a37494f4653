{"ast": null, "code": "import { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function skipUntil(notifier) {\n  return source => source.lift(new SkipUntilOperator(notifier));\n}\n\nclass SkipUntilOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(destination, source) {\n    return source.subscribe(new SkipUntilSubscriber(destination, this.notifier));\n  }\n\n}\n\nclass SkipUntilSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, notifier) {\n    super(destination);\n    this.hasValue = false;\n    const innerSubscriber = new SimpleInnerSubscriber(this);\n    this.add(innerSubscriber);\n    this.innerSubscription = innerSubscriber;\n    const innerSubscription = innerSubscribe(notifier, innerSubscriber);\n\n    if (innerSubscription !== innerSubscriber) {\n      this.add(innerSubscription);\n      this.innerSubscription = innerSubscription;\n    }\n  }\n\n  _next(value) {\n    if (this.hasValue) {\n      super._next(value);\n    }\n  }\n\n  notifyNext() {\n    this.hasValue = true;\n\n    if (this.innerSubscription) {\n      this.innerSubscription.unsubscribe();\n    }\n  }\n\n  notifyComplete() {}\n\n} //# sourceMappingURL=skipUntil.js.map", "map": null, "metadata": {}, "sourceType": "module"}