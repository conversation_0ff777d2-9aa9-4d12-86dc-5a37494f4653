{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowToggle(openings, closingSelector) {\n  return source => source.lift(new WindowToggleOperator(openings, closingSelector));\n}\n\nclass WindowToggleOperator {\n  constructor(openings, closingSelector) {\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));\n  }\n\n}\n\nclass WindowToggleSubscriber extends OuterSubscriber {\n  constructor(destination, openings, closingSelector) {\n    super(destination);\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n    this.contexts = [];\n    this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n  }\n\n  _next(value) {\n    const {\n      contexts\n    } = this;\n\n    if (contexts) {\n      const len = contexts.length;\n\n      for (let i = 0; i < len; i++) {\n        contexts[i].window.next(value);\n      }\n    }\n  }\n\n  _error(err) {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.error(err);\n        context.subscription.unsubscribe();\n      }\n    }\n\n    super._error(err);\n  }\n\n  _complete() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.complete();\n        context.subscription.unsubscribe();\n      }\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.unsubscribe();\n        context.subscription.unsubscribe();\n      }\n    }\n  }\n\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    if (outerValue === this.openings) {\n      let closingNotifier;\n\n      try {\n        const {\n          closingSelector\n        } = this;\n        closingNotifier = closingSelector(innerValue);\n      } catch (e) {\n        return this.error(e);\n      }\n\n      const window = new Subject();\n      const subscription = new Subscription();\n      const context = {\n        window,\n        subscription\n      };\n      this.contexts.push(context);\n      const innerSubscription = subscribeToResult(this, closingNotifier, context);\n\n      if (innerSubscription.closed) {\n        this.closeWindow(this.contexts.length - 1);\n      } else {\n        innerSubscription.context = context;\n        subscription.add(innerSubscription);\n      }\n\n      this.destination.next(window);\n    } else {\n      this.closeWindow(this.contexts.indexOf(outerValue));\n    }\n  }\n\n  notifyError(err) {\n    this.error(err);\n  }\n\n  notifyComplete(inner) {\n    if (inner !== this.openSubscription) {\n      this.closeWindow(this.contexts.indexOf(inner.context));\n    }\n  }\n\n  closeWindow(index) {\n    if (index === -1) {\n      return;\n    }\n\n    const {\n      contexts\n    } = this;\n    const context = contexts[index];\n    const {\n      window,\n      subscription\n    } = context;\n    contexts.splice(index, 1);\n    window.complete();\n    subscription.unsubscribe();\n  }\n\n} //# sourceMappingURL=windowToggle.js.map", "map": null, "metadata": {}, "sourceType": "module"}