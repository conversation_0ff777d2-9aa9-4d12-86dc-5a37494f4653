{"ast": null, "code": "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n  array.sort(comparer);\n\n  while (length--) {\n    array[length] = array[length].value;\n  }\n\n  return array;\n}\n\nexport default baseSortBy;", "map": null, "metadata": {}, "sourceType": "module"}