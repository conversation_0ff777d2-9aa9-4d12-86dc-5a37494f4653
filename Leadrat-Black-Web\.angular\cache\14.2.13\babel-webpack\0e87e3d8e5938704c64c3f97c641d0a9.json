{"ast": null, "code": "import parsePhoneNumber from '../parsePhoneNumber.js';\n/**\r\n * Matches a phone number object against a phone number string.\r\n * @param  {string} phoneNumberString\r\n * @param  {PhoneNumber} phoneNumber\r\n * @param  {object} metadata — Metadata JSON\r\n * @return {'INVALID_NUMBER'|'NO_MATCH'|'SHORT_NSN_MATCH'|'NSN_MATCH'|'EXACT_MATCH'}\r\n */\n\nexport default function matchPhoneNumberStringAgainstPhoneNumber(phoneNumberString, phoneNumber, metadata) {\n  // Parse `phoneNumberString`.\n  var phoneNumberStringContainsCallingCode = true;\n  var parsedPhoneNumber = parsePhoneNumber(phoneNumberString, metadata);\n\n  if (!parsedPhoneNumber) {\n    // If `phoneNumberString` didn't contain a country calling code\n    // then substitute it with the `phoneNumber`'s country calling code.\n    phoneNumberStringContainsCallingCode = false;\n    parsedPhoneNumber = parsePhoneNumber(phoneNumberString, {\n      defaultCallingCode: phoneNumber.countryCallingCode\n    }, metadata);\n  }\n\n  if (!parsedPhoneNumber) {\n    return 'INVALID_NUMBER';\n  } // Check that the extensions match.\n\n\n  if (phoneNumber.ext) {\n    if (parsedPhoneNumber.ext !== phoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } else {\n    if (parsedPhoneNumber.ext) {\n      return 'NO_MATCH';\n    }\n  } // Check that country calling codes match.\n\n\n  if (phoneNumberStringContainsCallingCode) {\n    if (phoneNumber.countryCallingCode !== parsedPhoneNumber.countryCallingCode) {\n      return 'NO_MATCH';\n    }\n  } // Check if the whole numbers match.\n\n\n  if (phoneNumber.number === parsedPhoneNumber.number) {\n    if (phoneNumberStringContainsCallingCode) {\n      return 'EXACT_MATCH';\n    } else {\n      return 'NSN_MATCH';\n    }\n  } // Check if one national number is a \"suffix\" of the other.\n\n\n  if (phoneNumber.nationalNumber.indexOf(parsedPhoneNumber.nationalNumber) === 0 || parsedPhoneNumber.nationalNumber.indexOf(phoneNumber.nationalNumber) === 0) {\n    // \"A SHORT_NSN_MATCH occurs if there is a difference because of the\n    //  presence or absence of an 'Italian leading zero', the presence or\n    //  absence of an extension, or one NSN being a shorter variant of the\n    //  other.\"\n    return 'SHORT_NSN_MATCH';\n  }\n\n  return 'NO_MATCH';\n} //# sourceMappingURL=matchPhoneNumberStringAgainstPhoneNumber.js.map", "map": null, "metadata": {}, "sourceType": "module"}