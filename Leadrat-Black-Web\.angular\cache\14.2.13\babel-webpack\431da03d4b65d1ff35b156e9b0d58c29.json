{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { empty } from '../observable/empty';\nexport function repeat(count = -1) {\n  return source => {\n    if (count === 0) {\n      return empty();\n    } else if (count < 0) {\n      return source.lift(new RepeatOperator(-1, source));\n    } else {\n      return source.lift(new RepeatOperator(count - 1, source));\n    }\n  };\n}\n\nclass RepeatOperator {\n  constructor(count, source) {\n    this.count = count;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));\n  }\n\n}\n\nclass RepeatSubscriber extends Subscriber {\n  constructor(destination, count, source) {\n    super(destination);\n    this.count = count;\n    this.source = source;\n  }\n\n  complete() {\n    if (!this.isStopped) {\n      const {\n        source,\n        count\n      } = this;\n\n      if (count === 0) {\n        return super.complete();\n      } else if (count > -1) {\n        this.count = count - 1;\n      }\n\n      source.subscribe(this._unsubscribeAndRecycle());\n    }\n  }\n\n} //# sourceMappingURL=repeat.js.map", "map": null, "metadata": {}, "sourceType": "module"}