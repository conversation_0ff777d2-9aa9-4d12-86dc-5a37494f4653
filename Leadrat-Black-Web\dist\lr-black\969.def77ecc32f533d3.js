"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[969],{54969:(nt,q,m)=>{m.d(q,{p:()=>tt});var e=m(5e3),u=m(93075),j=m(82722),R=m(2976),U=m(61021),x=m(77410),z=m(1023),J=m(65620),Y=m(32496),$=m(38827),Q=m(71511),I=m(69808),C=m(24376),X=m(46302),Z=m(35174),H=m(17447),ee=m(18995);function te(i,l){if(1&i&&(e.\u0275\u0275element(0,"img",13),e.\u0275\u0275pipe(1,"translate")),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275propertyInterpolate("alt",e.\u0275\u0275pipeBind1(1,3,"GLOBAL.img")),e.\u0275\u0275property("type","leadrat")("appImage",t.image)}}function ne(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",14)(1,"span",15),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext();return s.isBulkAssignModel=!0,e.\u0275\u0275resetView(s.hideAssignmentPopup())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(2,"h3",16),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275elementEnd()()}2&i&&(e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2("",e.\u0275\u0275pipeBind1(4,2,"GLOBAL.lead")," ",e.\u0275\u0275pipeBind1(5,4,"LEADS.assignment"),""))}function ie(i,l){1&i&&(e.\u0275\u0275elementStart(0,"div",22),e.\u0275\u0275text(1," Login Id/Login Email(s) "),e.\u0275\u0275elementEnd())}function se(i,l){1&i&&(e.\u0275\u0275elementStart(0,"div",22),e.\u0275\u0275text(1," Login Id/Login Email "),e.\u0275\u0275elementEnd())}function le(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"span",24),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=l.$implicit,n=l.last;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",t.accountName,"",n?" ":", "," ")}}function ae(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,le,2,2,"span",23),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.selectedIntegrations)}}function re(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.selectedAccountName)}}function oe(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",17)(1,"div",18),e.\u0275\u0275template(2,ie,2,0,"div",19),e.\u0275\u0275template(3,se,2,0,"div",19),e.\u0275\u0275template(4,ae,2,1,"div",20),e.\u0275\u0275template(5,re,2,1,"div",21),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",(null==t.selectedIntegrations?null:t.selectedIntegrations.length)>0&&!t.isBulkAssignModel),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isBulkAssignModel),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.selectedIntegrations?null:t.selectedIntegrations.length)>0&&!t.isBulkAssignModel),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isBulkAssignModel)}}function ce(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"span",24),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=l.$implicit,n=l.last;e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate2(" ",null==t?null:t.referenceId,"",n?" ":", "," ")}}function de(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div"),e.\u0275\u0275template(1,ce,2,2,"span",23),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("ngForOf",t.selectedReferences)}}function pe(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",25),e.\u0275\u0275text(1),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate(t.selectedAccountName)}}function _e(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",17)(1,"div",18)(2,"div",22),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(4,de,2,1,"div",20),e.\u0275\u0275template(5,pe,2,1,"div",21),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate1(" Reference Id",null!=t.selectedReferences&&t.selectedReferences.length?"'s":""," "),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.selectedReferences?null:t.selectedReferences.length)&&!t.isBulkAssignModel),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!(null!=t.selectedIntegrations&&t.selectedIntegrations.length))}}function ge(i,l){1&i&&(e.\u0275\u0275elementStart(0,"div")(1,"div",30),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd()()),2&i&&(e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,"INTEGRATION.ad-name")))}function me(i,l){1&i&&(e.\u0275\u0275elementStart(0,"div")(1,"div",30),e.\u0275\u0275text(2),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementEnd()()),2&i&&(e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(3,1,"INTEGRATION.lead-form")))}function ue(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",26)(1,"div",27)(2,"div",22),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",28),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(7,ge,4,3,"div",20),e.\u0275\u0275template(8,me,4,3,"div",20),e.\u0275\u0275elementStart(9,"div",28),e.\u0275\u0275text(10),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(11,"div"),e.\u0275\u0275element(12,"img",29),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,5,"GLOBAL.login-idlogin-email")),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(t.selectedAccountName),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isAdAccount),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.isFormAccount),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.selectedAdName)}}const ve=function(i){return{active:i}};function he(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",32),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext().$implicit,a=e.\u0275\u0275nextContext();return a.selectedSectionLeadAssignment=s,e.\u0275\u0275resetView(a.setListSelection())}),e.\u0275\u0275elementStart(1,"span"),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd()()}if(2&i){const t=e.\u0275\u0275nextContext().$implicit,n=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(2,ve,n.selectedSectionLeadAssignment==t)),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t)}}function Ae(i,l){if(1&i&&(e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275template(1,he,3,4,"div",31),e.\u0275\u0275elementContainerEnd()),2&i){const t=l.$implicit,n=e.\u0275\u0275nextContext();e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Configuration"==t||"Configuration"!=t&&(!n.canAllowSecondaryUsers&&((null==n.assignedUser?null:n.assignedUser.length)||(null==n.assignedDuplicateUser?null:n.assignedDuplicateUser.length))||n.canAllowSecondaryUsers&&n.canEnableAllowSecondaryUsers&&((null==n.assignedPrimaryUsers?null:n.assignedPrimaryUsers.length)||(null==n.assignedSecondaryUsers?null:n.assignedSecondaryUsers.length)||(null==n.assignedDuplicateUser?null:n.assignedDuplicateUser.length))))}}function fe(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",44),e.\u0275\u0275text(1),e.\u0275\u0275pipe(2,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275property("ngClass",t.canAllowDuplicates?"label-req":"label"),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",e.\u0275\u0275pipeBind1(2,2,"SETTINGS.select-user")," ")}}function xe(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",49),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=l.item$,n=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",t.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.select-all"))}}function Ue(i,l){if(1&i&&e.\u0275\u0275text(0),2&i){const t=l.item;e.\u0275\u0275textInterpolate1(" ",null!=t&&t.firstName||null!=t&&t.lastName?t.firstName+" "+t.lastName:"All"," ")}}function ye(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).item,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.lastClickedOption=a)}),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275elementStart(3,"span",52),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()}if(2&i){const t=l.item,n=l.item$,s=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",s,"")("automate-id","item-",s,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(" ",t.firstName," ",t.lastName,"")}}function Ce(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"ng-select",45),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return s.sameAsSelectedUsers=!1,e.\u0275\u0275resetView(s.handleSelectAll(!0))})("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.assignedUser=s)}),e.\u0275\u0275pipe(1,"translate"),e.\u0275\u0275template(2,xe,5,6,"ng-template",46),e.\u0275\u0275template(3,Ue,1,1,"ng-template",47),e.\u0275\u0275template(4,ye,5,5,"ng-template",48),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(1,7,"GLOBAL.ex-mounika-pampana")),e.\u0275\u0275property("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("selectableGroup",!0)("closeOnSelect",!1)("clearSearchOnAdd",!0)("ngModel",t.assignedUser)}}function Ie(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",49),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=l.item$,n=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",t.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.select-all"))}}function Se(i,l){if(1&i&&e.\u0275\u0275text(0),2&i){const t=l.item;e.\u0275\u0275textInterpolate1(" ",null!=t&&t.firstName||null!=t&&t.lastName?t.firstName+" "+t.lastName:"All"," ")}}function we(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).item,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.lastClickedOption=a)}),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275elementStart(3,"span",52),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()}if(2&i){const t=l.item,n=l.item$,s=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",s,"")("automate-id","item-",s,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(" ",t.firstName," ",t.lastName,"")}}const f=function(){return{standalone:!0}};function De(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",53),e.\u0275\u0275text(2," Select Lead Duplicate User(s) "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"form-errors-wrapper",54)(4,"ng-select",55),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return s.sameAsSelectedUsers=!1,e.\u0275\u0275resetView(s.handleSelectAll())})("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.assignedDuplicateUser=s)}),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275template(6,Ie,5,6,"ng-template",46),e.\u0275\u0275template(7,Se,1,1,"ng-template",47),e.\u0275\u0275template(8,we,5,5,"ng-template",48),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"div",56)(10,"label",57),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.sameAsSelectedUsersClicked())}),e.\u0275\u0275elementStart(11,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.sameAsSelectedUsers=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(12,"span",37),e.\u0275\u0275elementStart(13,"span",58),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementStart(16,"span",59),e.\u0275\u0275text(17,"Selected User(s)"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(18,"div",56)(19,"label",60)(20,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.canAssignSequentially=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(21,"span",37),e.\u0275\u0275elementStart(22,"span",58),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(3),e.\u0275\u0275property("control",t.integrationDuplicateForm.controls.assignedDuplicateUser),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(5,15,"GLOBAL.ex-mounika-pampana")),e.\u0275\u0275property("required",!0)("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("selectableGroup",!0)("closeOnSelect",!1)("clearSearchOnAdd",!0)("ngModel",t.assignedDuplicateUser),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",t.sameAsSelectedUsers)("ngModelOptions",e.\u0275\u0275pureFunction0(21,f)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(15,17,"GLOBAL.same-as")),e.\u0275\u0275advance(6),e.\u0275\u0275property("ngModel",t.canAssignSequentially)("ngModelOptions",e.\u0275\u0275pureFunction0(22,f)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(24,19,"GLOBAL.assign-duplicate-leads-sequent"))}}function be(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",49),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=l.item$,n=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",t.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.select-all"))}}function Te(i,l){if(1&i&&e.\u0275\u0275text(0),2&i){const t=l.item;e.\u0275\u0275textInterpolate1(" ",null!=t&&t.firstName||null!=t&&t.lastName?t.firstName+" "+t.lastName:"All"," ")}}function Ee(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).item,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.lastClickedOption=a)}),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275elementStart(3,"span",52),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()}if(2&i){const t=l.item,n=l.item$,s=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",s,"")("automate-id","item-",s,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate2(" ",t.firstName," ",t.lastName,"")}}function ke(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",49),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=l.item$,n=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",t.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.select-all"))}}function Oe(i,l){if(1&i&&e.\u0275\u0275text(0),2&i){const t=l.item;e.\u0275\u0275textInterpolate1(" ",null!=t&&t.firstName||null!=t&&t.lastName?t.firstName+" "+t.lastName:"All"," ")}}function Me(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).item,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.lastClickedOption=a)}),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()}if(2&i){const t=l.item,n=l.item$,s=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",s,"")("automate-id","item-",s,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName," ")}}function Le(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",49),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275pipe(4,"translate"),e.\u0275\u0275elementEnd()),2&i){const t=l.item$,n=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",n,"")("automate-id","item-",n,""),e.\u0275\u0275property("checked",t.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(4,4,"GLOBAL.select-all"))}}function Ve(i,l){if(1&i&&e.\u0275\u0275text(0),2&i){const t=l.item;e.\u0275\u0275textInterpolate1(" ",null!=t&&t.firstName||null!=t&&t.lastName?t.firstName+" "+t.lastName:"All"," ")}}function Pe(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",51),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).item,r=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(r.lastClickedOption=a)}),e.\u0275\u0275element(1,"input",50)(2,"span",37),e.\u0275\u0275text(3),e.\u0275\u0275elementEnd()}if(2&i){const t=l.item,n=l.item$,s=l.index;e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("id","item-",s,"")("automate-id","item-",s,""),e.\u0275\u0275property("checked",n.selected),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate2("",t.firstName," ",t.lastName," ")}}function Fe(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",69)(1,"div",70)(2,"input",71),e.\u0275\u0275listener("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(s.sameAsAbove=!1)})("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(4);return e.\u0275\u0275resetView(a.selectedUserType=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"label",72),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()()()}if(2&i){const t=l.$implicit,n=l.index,s=e.\u0275\u0275nextContext(4);e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate1("id","userType",n,""),e.\u0275\u0275property("checked",t===s.selectedUserType)("value",t)("ngModel",s.selectedUserType)("ngModelOptions",e.\u0275\u0275pureFunction0(7,f)),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate1("for","userType",n,""),e.\u0275\u0275advance(1),e.\u0275\u0275textInterpolate1(" ",t,"")}}const Be=function(){return["Primary User(s)","Secondary User(s)"]};function Ne(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",53),e.\u0275\u0275text(2," Select Lead Duplicate User(s) "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"form-errors-wrapper",54)(4,"ng-select",66),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.assignedDuplicateUser=s)})("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return s.sameAsAbove=!1,e.\u0275\u0275resetView(s.handleSelectAll())}),e.\u0275\u0275pipe(5,"translate"),e.\u0275\u0275template(6,Le,5,6,"ng-template",46),e.\u0275\u0275template(7,Ve,1,1,"ng-template",47),e.\u0275\u0275template(8,Pe,4,5,"ng-template",48),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(9,"div",56)(10,"label",65),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.sameAsPrimarySecondaryUsersClicked())}),e.\u0275\u0275elementStart(11,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.sameAsAbove=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(12,"span",37),e.\u0275\u0275elementStart(13,"span",58),e.\u0275\u0275text(14),e.\u0275\u0275pipe(15,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(16,"div",67),e.\u0275\u0275template(17,Fe,5,8,"div",68),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(18,"div",56)(19,"label",60)(20,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(a.canAssignSequentially=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(21,"span",37),e.\u0275\u0275elementStart(22,"span",58),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275advance(3),e.\u0275\u0275property("control",null==t.integrationDualOwnerForm||null==t.integrationDualOwnerForm.controls?null:t.integrationDualOwnerForm.controls.assignedDuplicateUser),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(5,16,"GLOBAL.ex-mounika-pampana")),e.\u0275\u0275property("required",!0)("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("selectableGroup",!0)("closeOnSelect",!1)("clearSearchOnAdd",!0)("ngModel",t.assignedDuplicateUser),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",t.sameAsAbove)("ngModelOptions",e.\u0275\u0275pureFunction0(22,f)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(15,18,"GLOBAL.same-as-above")),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngForOf",e.\u0275\u0275pureFunction0(23,Be)),e.\u0275\u0275advance(3),e.\u0275\u0275property("ngModel",t.canAssignSequentially)("ngModelOptions",e.\u0275\u0275pureFunction0(24,f)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(24,20,"GLOBAL.assign-duplicate-leads-sequent"))}}function Ge(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"form",40)(2,"div",53),e.\u0275\u0275text(3," Select Primary Owner(s) "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"form-errors-wrapper",61)(5,"ng-select",62),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.assignedPrimaryUsers=s)})("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return s.sameAsPrimaryUsers=!1,e.\u0275\u0275resetView(s.handleSelectAll())}),e.\u0275\u0275pipe(6,"translate"),e.\u0275\u0275template(7,be,5,6,"ng-template",46),e.\u0275\u0275template(8,Te,1,1,"ng-template",47),e.\u0275\u0275template(9,Ee,5,5,"ng-template",48),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(10,"div",53),e.\u0275\u0275text(11," Select Secondary Owner(s) "),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(12,"form-errors-wrapper",63)(13,"ng-select",64),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.assignedSecondaryUsers=s)})("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return s.sameAsPrimaryUsers=!1,e.\u0275\u0275resetView(s.handleSelectAll())}),e.\u0275\u0275pipe(14,"translate"),e.\u0275\u0275template(15,ke,5,6,"ng-template",46),e.\u0275\u0275template(16,Oe,1,1,"ng-template",47),e.\u0275\u0275template(17,Me,4,5,"ng-template",48),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(18,"div",56)(19,"label",65),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(s.sameAsSelectedUsersClicked(!0))}),e.\u0275\u0275elementStart(20,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext(2);return e.\u0275\u0275resetView(a.sameAsPrimaryUsers=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(21,"span",37),e.\u0275\u0275elementStart(22,"span",58),e.\u0275\u0275text(23),e.\u0275\u0275pipe(24,"translate"),e.\u0275\u0275elementStart(25,"span",59),e.\u0275\u0275text(26,"Primary User(s)"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275template(27,Ne,25,25,"ng-container",20),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(1),e.\u0275\u0275property("formGroup",t.integrationDualOwnerForm),e.\u0275\u0275advance(3),e.\u0275\u0275property("control",null==t.integrationDualOwnerForm||null==t.integrationDualOwnerForm.controls?null:t.integrationDualOwnerForm.controls.assignedPrimaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(6,23,"GLOBAL.ex-mounika-pampana")),e.\u0275\u0275property("required",!0)("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("selectableGroup",!0)("closeOnSelect",!1)("clearSearchOnAdd",!0)("ngModel",t.assignedPrimaryUsers),e.\u0275\u0275advance(7),e.\u0275\u0275property("control",null==t.integrationDualOwnerForm||null==t.integrationDualOwnerForm.controls?null:t.integrationDualOwnerForm.controls.assignedSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("placeholder",e.\u0275\u0275pipeBind1(14,25,"GLOBAL.ex-mounika-pampana")),e.\u0275\u0275property("required",!0)("items",t.canAssignToAny?t.allActiveUsers:t.activeUsers)("multiple",!0)("selectableGroup",!0)("closeOnSelect",!1)("clearSearchOnAdd",!0)("ngModel",t.assignedSecondaryUsers),e.\u0275\u0275advance(7),e.\u0275\u0275property("ngModel",t.sameAsPrimaryUsers)("ngModelOptions",e.\u0275\u0275pureFunction0(29,f)),e.\u0275\u0275advance(3),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(24,27,"GLOBAL.same-as")),e.\u0275\u0275advance(4),e.\u0275\u0275property("ngIf",t.canAllowDuplicates&&t.canEnableAllowDuplicates)}}const W=function(i){return{"pe-none":i}},K=function(i){return{"text-coal":i}};function Re(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementContainerStart(0),e.\u0275\u0275elementStart(1,"div",33)(2,"div",34),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(),a=e.\u0275\u0275reference(19);return s.canEnableAllowDuplicates?s.resetIntegrationForm():s.openConfirmModal(a,"allowDuplicateLeads"),e.\u0275\u0275resetView(s.toggleAssignedUserValidation())}),e.\u0275\u0275pipe(3,"translate"),e.\u0275\u0275elementStart(4,"label",35)(5,"input",36),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.canAllowDuplicates=s)})("change",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(s.toggleAssignedUserValidation())}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"span",37),e.\u0275\u0275elementStart(7,"span",38),e.\u0275\u0275text(8,"Assign leads as duplicate(s)"),e.\u0275\u0275elementEnd()()(),e.\u0275\u0275elementStart(9,"div",34),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(),a=e.\u0275\u0275reference(19);return s.canEnableAllowSecondaryUsers?s.resetIntegrationForm():s.openConfirmModal(a,"allowSecondaryUsers"),e.\u0275\u0275resetView(s.toggleAssignedUserValidation())}),e.\u0275\u0275pipe(10,"translate"),e.\u0275\u0275elementStart(11,"label",35)(12,"input",39),e.\u0275\u0275listener("ngModelChange",function(s){e.\u0275\u0275restoreView(t);const a=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(a.canAllowSecondaryUsers=s)}),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(13,"span",37),e.\u0275\u0275elementStart(14,"span",38),e.\u0275\u0275text(15,"Assign to secondary user"),e.\u0275\u0275elementEnd()()()(),e.\u0275\u0275elementStart(16,"form",40),e.\u0275\u0275template(17,fe,3,4,"div",41),e.\u0275\u0275elementStart(18,"form-errors-wrapper",42),e.\u0275\u0275pipe(19,"translate"),e.\u0275\u0275template(20,Ce,5,9,"ng-select",43),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(21,De,25,23,"ng-container",20),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(22,Ge,28,30,"ng-container",20),e.\u0275\u0275elementContainerEnd()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("title",e.\u0275\u0275pipeBind1(3,17,"GLOBAL.this-feature-will-assign-a-dup")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(23,W,!t.canEnableAllowDuplicates)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.canAllowDuplicates)("ngModelOptions",e.\u0275\u0275pureFunction0(25,f)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(26,K,t.canEnableAllowDuplicates)),e.\u0275\u0275advance(2),e.\u0275\u0275propertyInterpolate("title",e.\u0275\u0275pipeBind1(10,19,"GLOBAL.this-feature-will-assign-a-sec")),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(28,W,!t.canEnableAllowSecondaryUsers)),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngModel",t.canAllowSecondaryUsers)("ngModelOptions",e.\u0275\u0275pureFunction0(30,f)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(31,K,t.canEnableAllowSecondaryUsers)),e.\u0275\u0275advance(2),e.\u0275\u0275property("formGroup",t.integrationDuplicateForm),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!t.canAllowSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275propertyInterpolate("label",e.\u0275\u0275pipeBind1(19,21,"SETTINGS.select-user")),e.\u0275\u0275property("control",null==t.integrationDuplicateForm||null==t.integrationDuplicateForm.controls?null:t.integrationDuplicateForm.controls.assignedUser),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",!t.canAllowSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.canAllowDuplicates&&t.canEnableAllowDuplicates&&!t.canAllowSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.canAllowSecondaryUsers&&t.canEnableAllowSecondaryUsers)}}function $e(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",78)(1,"div",69)(2,"div",79)(3,"span",80),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",81),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div")(8,"span",82),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).$implicit,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.removeUserFromSelection(a))}),e.\u0275\u0275element(9,"span",83),e.\u0275\u0275elementEnd()()()}if(2&i){const t=l.$implicit,n=e.\u0275\u0275nextContext(3);let s;e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t?(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.firstName[0])+(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.lastName[0]):"--"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList,!0)||"--")}}function We(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div",74)(1,"div",56)(2,"div",75),e.\u0275\u0275text(3,"Selected User(s) "),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(4,"div",76),e.\u0275\u0275template(5,$e,10,2,"div",77),e.\u0275\u0275elementEnd()()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(5),e.\u0275\u0275property("ngForOf",t.assignedUser)}}const y=function(i){return{"text-accent-green field-label-left-underline-green":i}};function Ke(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",86),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.originalDuplicateListToggle("original"))}),e.\u0275\u0275text(1,"Original User(s) "),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(1,y,"original"==t.listSelection))}}function qe(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",87),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.originalDuplicateListToggle("duplicate"))}),e.\u0275\u0275text(1," Duplicate User(s) "),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(1,y,"duplicate"==t.listSelection))}}function je(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",78)(1,"div",69)(2,"div",79)(3,"span",80),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",81),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div")(8,"span",82),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).$implicit,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.removeUserFromSelection(a))}),e.\u0275\u0275element(9,"span",83),e.\u0275\u0275elementEnd()()()}if(2&i){const t=l.$implicit,n=e.\u0275\u0275nextContext(3);let s;e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t?(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.firstName[0])+(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.lastName[0]):"--"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList,!0)||"--")}}function ze(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div")(1,"div",56),e.\u0275\u0275template(2,Ke,2,3,"div",84),e.\u0275\u0275template(3,qe,2,3,"div",85),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(4,"div",76),e.\u0275\u0275template(5,je,10,2,"div",77),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(6,"div",56),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==t.assignedUser?null:t.assignedUser.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.assignedDuplicateUser?null:t.assignedDuplicateUser.length),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.filteredUsers)}}function Je(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",86),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.originalDuplicateListToggle("primary"))}),e.\u0275\u0275text(1,"Primary "),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(1,y,"primary"==t.listSelection))}}function Ye(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",86),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.originalDuplicateListToggle("secondary"))}),e.\u0275\u0275text(1," Secondary "),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(1,y,"secondary"==t.listSelection))}}function Qe(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",87),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(s.originalDuplicateListToggle("duplicate"))}),e.\u0275\u0275text(1," Duplicate User(s) "),e.\u0275\u0275elementEnd()}if(2&i){const t=e.\u0275\u0275nextContext(3);e.\u0275\u0275property("ngClass",e.\u0275\u0275pureFunction1(1,y,"duplicate"==t.listSelection))}}function Xe(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",78)(1,"div",69)(2,"div",79)(3,"span",80),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(5,"div",81),e.\u0275\u0275text(6),e.\u0275\u0275elementEnd()(),e.\u0275\u0275elementStart(7,"div")(8,"span",82),e.\u0275\u0275listener("click",function(){const a=e.\u0275\u0275restoreView(t).$implicit,r=e.\u0275\u0275nextContext(3);return e.\u0275\u0275resetView(r.removeUserFromSelection(a))}),e.\u0275\u0275element(9,"span",83),e.\u0275\u0275elementEnd()()()}if(2&i){const t=l.$implicit,n=e.\u0275\u0275nextContext(3);let s;e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(t?(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.firstName[0])+(null==(s=n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList))?null:s.lastName[0]):"--"),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(n.getAssignedToDetails(t,n.canAssignToAny?n.allUserList:n.userList,!0)||"--")}}function Ze(i,l){if(1&i&&(e.\u0275\u0275elementStart(0,"div")(1,"div",56),e.\u0275\u0275template(2,Je,2,3,"div",84),e.\u0275\u0275template(3,Ye,2,3,"div",84),e.\u0275\u0275template(4,Qe,2,3,"div",85),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",76),e.\u0275\u0275template(6,Xe,10,2,"div",77),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(7,"div",56),e.\u0275\u0275elementEnd()),2&i){const t=e.\u0275\u0275nextContext(2);e.\u0275\u0275advance(2),e.\u0275\u0275property("ngIf",null==t.assignedPrimaryUsers?null:t.assignedPrimaryUsers.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",null==t.assignedSecondaryUsers?null:t.assignedSecondaryUsers.length),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.canAllowDuplicates&&(null==t.assignedDuplicateUser?null:t.assignedDuplicateUser.length)),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",t.primarySeondaryUsers)}}function He(i,l){if(1&i&&(e.\u0275\u0275template(0,We,6,1,"div",73),e.\u0275\u0275template(1,ze,7,3,"div",20),e.\u0275\u0275template(2,Ze,8,4,"div",20)),2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275property("ngIf",(null==t.assignedUser?null:t.assignedUser.length)&&!t.canAllowDuplicates&&!t.canAllowSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",t.canAllowDuplicates&&!t.canAllowSecondaryUsers),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",(null==t.primarySeondaryUsers?null:t.primarySeondaryUsers.length)&&t.canAllowSecondaryUsers)}}function et(i,l){if(1&i){const t=e.\u0275\u0275getCurrentView();e.\u0275\u0275elementStart(0,"div",88)(1,"h3",89),e.\u0275\u0275text(2),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(3,"div",90),e.\u0275\u0275text(4),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(5,"div",91)(6,"button",92),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(s.closePopup())}),e.\u0275\u0275text(7," cancel"),e.\u0275\u0275elementEnd(),e.\u0275\u0275elementStart(8,"button",93),e.\u0275\u0275listener("click",function(){e.\u0275\u0275restoreView(t);const s=e.\u0275\u0275nextContext();return e.\u0275\u0275resetView(s.goToGlobalConfig())}),e.\u0275\u0275text(9," Go to Lead Settings "),e.\u0275\u0275elementEnd()()()}if(2&i){const t=e.\u0275\u0275nextContext();e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate(t.message),e.\u0275\u0275advance(2),e.\u0275\u0275textInterpolate1("Note: ",t.notes,"")}}let tt=(()=>{class i{constructor(t,n,s,a,r,d){this.store=t,this._notificationService=n,this.fb=s,this.modalService=a,this.modalRef=r,this.router=d,this.stopper=new e.EventEmitter,this.assignedSecondaryUsers=[],this.assignedDuplicateUser=[],this.assignedPrimaryUsers=[],this.assignedUser=[],this.isBulkFb=!1,this.isShowAssignModalChanged=new e.EventEmitter,this.selectedSectionLeadAssignment="Configuration",this.leadAssignmentOptions=["Configuration","Selected Users"],this.listSelection="original",this.selectedUserType="Primary User(s)",this.getAssignedToDetails=U.sW}get filteredUsers(){return"original"===this.listSelection?this.assignedUser:this.assignedDuplicateUser}get primarySeondaryUsers(){return"primary"===this.listSelection?this.assignedPrimaryUsers:"secondary"===this.listSelection?this.assignedSecondaryUsers:this.assignedDuplicateUser}ngOnInit(){if(this.integrationDuplicateForm=this.fb.group({assignedUser:[null,[u.kI.required]],assignedDuplicateUser:[null,[u.kI.required]]}),this.integrationDualOwnerForm=this.fb.group({assignedPrimaryUsers:[null,u.kI.required],assignedSecondaryUsers:[null,u.kI.required],assignedDuplicateUser:[null,u.kI.required],selectedUserType:["Primary User(s)",u.kI.required]}),this.isBulkAssignModel||this.isFbComponent)return this.selectedAccountId&&this.store.dispatch(new x.a6(this.selectedAccountId)),void this.store.select(z.od).pipe((0,j.R)(this.stopper)).subscribe(t=>{this.canAssignSequentially=!(null!=t&&t.shouldCreateMultipleDuplicates),this.canAllowSecondaryUsers=null==t?void 0:t.isDualAssignmentEnabled,this.canAllowDuplicates=null==t?void 0:t.isDuplicateAssignmentEnabled,this.assignedUser=null==t?void 0:t.userIds,this.assignedUserDetails=null==t?void 0:t.userIds,this.assignedPrimaryUsers=this.assignedUser,this.assignedSecondaryUsers=null==t?void 0:t.secondaryUserIds,this.assignedDuplicateUser=null==t?void 0:t.duplicateUserIds,this.toggleAssignedUserValidation()})}toggleAssignedUserValidation(){this.canAllowDuplicates?(0,U.fN)(R.$iz,this.integrationDuplicateForm,"assignedUser",[u.kI.required]):(0,U.fN)(R.qLM,this.integrationDuplicateForm,"assignedUser")}removeUserFromSelection(t){var n,s,a,r,d,p,v,c,h;return this.canAllowSecondaryUsers?(this.sameAsPrimaryUsers=!1,"secondary"==this.listSelection?(this.assignedSecondaryUsers=null===(n=this.assignedSecondaryUsers)||void 0===n?void 0:n.filter(_=>_!==t),void(null!==(s=this.assignedSecondaryUsers)&&void 0!==s&&s.length||(this.selectedSectionLeadAssignment="Configuration"))):"duplicate"==this.listSelection?(this.assignedDuplicateUser=null===(a=this.assignedDuplicateUser)||void 0===a?void 0:a.filter(_=>_!==t),void(null!==(r=this.assignedDuplicateUser)&&void 0!==r&&r.length||(this.selectedSectionLeadAssignment="Configuration"))):(this.assignedPrimaryUsers=null===(d=this.assignedPrimaryUsers)||void 0===d?void 0:d.filter(_=>_!==t),void(null!==(p=this.assignedPrimaryUsers)&&void 0!==p&&p.length||(this.selectedSectionLeadAssignment="Configuration")))):this.canAllowDuplicates&&!this.canAllowSecondaryUsers?(this.sameAsSelectedUsers=!1,"original"==this.listSelection?(this.assignedUser=this.assignedUser.filter(_=>_!==t),this.assignedUserDetails=this.assignedUser,void(null!==(v=this.assignedUser)&&void 0!==v&&v.length||(this.selectedSectionLeadAssignment="Configuration"))):(this.assignedDuplicateUser=this.assignedDuplicateUser.filter(_=>_!==t),void(null!==(c=this.assignedDuplicateUser)&&void 0!==c&&c.length||(this.selectedSectionLeadAssignment="Configuration")))):(this.assignedUser=this.assignedUser.filter(_=>_!==t),this.assignedUserDetails=this.assignedUser,void(null!==(h=this.assignedUser)&&void 0!==h&&h.length||(this.selectedSectionLeadAssignment="Configuration")))}assignFbAccount(){var t,n,s,a,r,d,p;if(!this.validateAssignmentForm())return;let v={entityId:this.selectedAccountId,userIds:this.canAllowSecondaryUsers?null!==(t=this.assignedPrimaryUsers)&&void 0!==t&&t.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(c=>null==c?void 0:c.id):this.assignedPrimaryUsers:(null!==(n=this.assignedUser)&&void 0!==n&&n.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(c=>null==c?void 0:c.id):this.assignedUser)||[],secondaryUserIds:null!==(s=this.assignedSecondaryUsers)&&void 0!==s&&s.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(c=>null==c?void 0:c.id):this.assignedSecondaryUsers||[],duplicateUserIds:null!==(a=this.assignedDuplicateUser)&&void 0!==a&&a.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(c=>null==c?void 0:c.id):this.assignedDuplicateUser||[],isDuplicateAssignmentEnabled:this.canAllowDuplicates||!1,isDualAssignmentEnabled:this.canAllowSecondaryUsers||!1,moduleId:this.moduleId,shouldCreateMultipleDuplicates:!this.canAssignSequentially};this.isBulkFb&&(v.entityIds=this.selectedAccountId,null==v||delete v.entityId),this.isBulkFb?null===(d=null===(r=this.store)||void 0===r?void 0:r.dispatch)||void 0===d||d.call(r,new x.kn(v,null,!0)):this.store.dispatch(new x.kn(v)),this.hideAssignmentPopup(),null===(p=this.gridApi)||void 0===p||p.deselectAll()}assignAccount(){var t,n,s,a,r,d,p,v,c,h,_;if(this.isFbComponent||this.isBulkFb)return void this.assignFbAccount();if(!this.validateAssignmentForm())return;let A;if(this.isReferenceId?A=null===(s=this.selectedReferences)||void 0===s?void 0:s.map(g=>null==g?void 0:g.id):(this.selectedIntegrations=null===(t=this.updatedIntegrationList)||void 0===t?void 0:t.filter(g=>g.isSelected),A=null===(n=this.selectedIntegrations)||void 0===n?void 0:n.map(g=>null==g?void 0:g.accountId)),(null==A?void 0:A.length)>0){let g={moduleId:this.moduleId,entityIds:A,userIds:this.canAllowSecondaryUsers?null!==(a=this.assignedPrimaryUsers)&&void 0!==a&&a.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedPrimaryUsers:null!==(r=this.assignedUser)&&void 0!==r&&r.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedUser,secondaryUserIds:null!==(d=this.assignedSecondaryUsers)&&void 0!==d&&d.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedSecondaryUsers,duplicateUserIds:null!==(p=this.assignedDuplicateUser)&&void 0!==p&&p.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedDuplicateUser,isDuplicateAssignmentEnabled:this.canAllowDuplicates,isDualAssignmentEnabled:this.canAllowSecondaryUsers,shouldCreateMultipleDuplicates:!this.canAssignSequentially};this.store.dispatch(new x.yS(g,this.isReferenceId?"Reference":"Project"))}else{let g={entityId:this.selectedAccountId,userIds:this.canAllowSecondaryUsers?null!==(v=this.assignedPrimaryUsers)&&void 0!==v&&v.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedPrimaryUsers:null!==(c=this.assignedUser)&&void 0!==c&&c.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedUser,secondaryUserIds:null!==(h=this.assignedSecondaryUsers)&&void 0!==h&&h.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedSecondaryUsers,duplicateUserIds:null!==(_=this.assignedDuplicateUser)&&void 0!==_&&_.includes("selectedAllGroup")?(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(o=>null==o?void 0:o.id):this.assignedDuplicateUser,isDuplicateAssignmentEnabled:this.canAllowDuplicates,isDualAssignmentEnabled:this.canAllowSecondaryUsers,moduleId:this.moduleId,shouldCreateMultipleDuplicates:!this.canAssignSequentially};this.store.dispatch(this.isReferenceId?new x.kn(g,"Reference"):new x.kn(g))}this.reset(),this.hideAssignmentPopup()}hideAssignmentPopup(){this.isShowAssignModalChanged.emit(!1),this.modalService.hide()}handleSelectAll(t=!1){var n,s,a,r;const d=(this.canAssignToAny?this.allActiveUsers:this.activeUsers).map(p=>null==p?void 0:p.id);!(null===(n=this.assignedUser)||void 0===n)&&n.includes("selectedAllGroup")&&(this.assignedUser=d),!(null===(s=this.assignedDuplicateUser)||void 0===s)&&s.includes("selectedAllGroup")&&(this.assignedDuplicateUser=d),!(null===(a=this.assignedPrimaryUsers)||void 0===a)&&a.includes("selectedAllGroup")&&(this.assignedPrimaryUsers=d),!(null===(r=this.assignedSecondaryUsers)||void 0===r)&&r.includes("selectedAllGroup")&&(this.assignedSecondaryUsers=d),t?this.assignedPrimaryUsers=this.assignedUser:this.assignedUser=this.assignedPrimaryUsers}sameAsSelectedUsersClicked(t=!1){if(!this.sameAsPrimaryUsers){if(t)return void(this.assignedSecondaryUsers=[...this.assignedPrimaryUsers]);this.assignedDuplicateUser=[...this.assignedUser]}}reset(){var t;null===(t=this.updatedIntegrationList)||void 0===t||t.forEach(n=>n.isSelected=!1),this.selectedCount=0,this.assignedUser=[]}resetIntegrationForm(){this.sameAsPrimaryUsers=!1,this.sameAsSelectedUsers=!1,this.sameAsAbove=!1,this.listSelection="original",this.toggleAssignedUserValidation(),this.revertValidation()}revertValidation(){const t=["assignedUser","assignedDuplicateUser"];if(null==t||t.forEach(n=>{this.integrationDuplicateForm.get(n).markAsPristine(),this.integrationDuplicateForm.get(n).markAsUntouched(),"assignedUser"!==n&&this.integrationDuplicateForm.get(n).setErrors(null)}),!this.canAllowSecondaryUsers){const n=["assignedPrimaryUsers","assignedSecondaryUsers","assignedDuplicateUser"];null==n||n.forEach(s=>{this.integrationDualOwnerForm.get(s).markAsPristine(),this.integrationDualOwnerForm.get(s).markAsUntouched(),this.integrationDualOwnerForm.get(s).setErrors(null)})}this.selectedUserType="Primary User(s)"}setListSelection(){var t,n,s;this.listSelection=this.canAllowDuplicates&&!this.canAllowSecondaryUsers?null!==(t=this.assignedUser)&&void 0!==t&&t.length?"original":"duplicate":null!==(n=this.assignedPrimaryUsers)&&void 0!==n&&n.length?"primary":null!==(s=this.assignedSecondaryUsers)&&void 0!==s&&s.length?"secondary":"duplicate"}originalDuplicateListToggle(t){this.listSelection=t}sameAsPrimarySecondaryUsersClicked(){this.sameAsAbove||(this.assignedDuplicateUser="Primary User(s)"==this.selectedUserType?[...this.assignedPrimaryUsers]:[...this.assignedSecondaryUsers])}validateAssignmentForm(){var t,n,s,a,r,d,p,v,c,h,_,A,g,o,S,w,D,b,T,E,k,O,M,L,V,P,F,B,N,G;if(this.canAllowDuplicates&&!this.canAllowSecondaryUsers){if(null===(t=this.integrationDuplicateForm)||void 0===t||!t.valid)return(0,U._5)(this.integrationDuplicateForm),!1;if(1==(null===(n=this.assignedUser)||void 0===n?void 0:n.length)&&1==(null===(s=this.assignedDuplicateUser)||void 0===s?void 0:s.length)&&(null===(a=this.assignedUser)||void 0===a?void 0:a[0])==(null===(r=this.assignedDuplicateUser)||void 0===r?void 0:r[0]))return this._notificationService.warn("Warning","Duplicate user assignment detected."),!1}else if(this.canAllowDuplicates&&this.canAllowSecondaryUsers){if(null===(d=this.integrationDualOwnerForm)||void 0===d||!d.valid)return(0,U._5)(this.integrationDualOwnerForm),!1;if((1==(null===(p=this.assignedPrimaryUsers)||void 0===p?void 0:p.length)&&1==(null===(v=this.assignedSecondaryUsers)||void 0===v?void 0:v.length)||1==(null===(c=this.assignedDuplicateUser)||void 0===c?void 0:c.length)&&1==(null===(h=this.assignedSecondaryUsers)||void 0===h?void 0:h.length)||1==(null===(_=this.assignedDuplicateUser)||void 0===_?void 0:_.length)&&1==(null===(A=this.assignedPrimaryUsers)||void 0===A?void 0:A.length))&&((null===(g=this.assignedPrimaryUsers)||void 0===g?void 0:g[0])==(null===(o=this.assignedDuplicateUser)||void 0===o?void 0:o[0])&&1==(null===(S=this.assignedPrimaryUsers)||void 0===S?void 0:S.length)&&1==(null===(w=this.assignedDuplicateUser)||void 0===w?void 0:w.length)||(null===(D=this.assignedPrimaryUsers)||void 0===D?void 0:D[0])==(null===(b=this.assignedSecondaryUsers)||void 0===b?void 0:b[0])&&1==(null===(T=this.assignedPrimaryUsers)||void 0===T?void 0:T.length)&&1==(null===(E=this.assignedSecondaryUsers)||void 0===E?void 0:E.length)||(null===(k=this.assignedSecondaryUsers)||void 0===k?void 0:k[0])==(null===(O=this.assignedDuplicateUser)||void 0===O?void 0:O[0])&&1==(null===(M=this.assignedSecondaryUsers)||void 0===M?void 0:M.length)&&1==(null===(L=this.assignedDuplicateUser)||void 0===L?void 0:L.length)))return this._notificationService.warn("Warning","Duplicate user assignment detected."),!1}else if(!this.canAllowDuplicates&&this.canAllowSecondaryUsers){if(!this.integrationDualOwnerForm.controls.assignedPrimaryUsers.valid||!this.integrationDualOwnerForm.controls.assignedSecondaryUsers.valid)return(0,U._5)(this.integrationDualOwnerForm),!1;if((null===(V=this.assignedPrimaryUsers)||void 0===V?void 0:V.length)&&(null===(P=this.assignedSecondaryUsers)||void 0===P?void 0:P.length))return 1!=(null===(F=this.assignedPrimaryUsers)||void 0===F?void 0:F.length)||1!=(null===(B=this.assignedSecondaryUsers)||void 0===B?void 0:B.length)||(null===(N=this.assignedPrimaryUsers)||void 0===N?void 0:N[0])!=(null===(G=this.assignedSecondaryUsers)||void 0===G?void 0:G[0])||(this._notificationService.warn("Warning","Duplicate user assignment detected."),!1)}return!0}openConfirmModal(t,n){switch(this.allowDuplicatesPopupRef=this.modalService.show(t,{class:"modal-600 top-modal ip-modal-unset",ignoreBackdropClick:!0,keyboard:!1}),n){case"allowDuplicateLeads":this.message="To use this feature \u201cAllow Lead Duplicates\u201d must be enabled.",this.notes="Please read the instructions clearly and proceed.";break;case"allowSecondaryUsers":this.message="To use this feature \u201cDual Lead Ownership\u201d must be enabled.",this.notes="Please read the instructions clearly and proceed."}}closePopup(){this.allowDuplicatesPopupRef.hide()}goToGlobalConfig(){this.hideAssignmentPopup(),this.closePopup(),this.closeModal(),this.router.navigate(["global-config","lead-settings"])}closeModal(){this.modalRef.hide(),this.reset()}ngOnDestroy(){this.stopper.next(),this.stopper.complete()}}return i.\u0275fac=function(t){return new(t||i)(e.\u0275\u0275directiveInject(J.yh),e.\u0275\u0275directiveInject(Y.TF),e.\u0275\u0275directiveInject(u.qu),e.\u0275\u0275directiveInject($.tT),e.\u0275\u0275directiveInject($.UZ),e.\u0275\u0275directiveInject(Q.F0))},i.\u0275cmp=e.\u0275\u0275defineComponent({type:i,selectors:[["integration-assignment"]],inputs:{image:"image",selectedAccountName:"selectedAccountName",selectedIntegrations:"selectedIntegrations",isBulkAssignModel:"isBulkAssignModel",canAllowSecondaryUsers:"canAllowSecondaryUsers",canAllowDuplicates:"canAllowDuplicates",canAssignToAny:"canAssignToAny",sameAsPrimaryUsers:"sameAsPrimaryUsers",sameAsSelectedUsers:"sameAsSelectedUsers",sameAsAbove:"sameAsAbove",assignedSecondaryUsers:"assignedSecondaryUsers",assignedDuplicateUser:"assignedDuplicateUser",assignedPrimaryUsers:"assignedPrimaryUsers",assignedUser:"assignedUser",assignedUserDetails:"assignedUserDetails",allActiveUsers:"allActiveUsers",allUserList:"allUserList",userList:"userList",activeUsers:"activeUsers",updatedIntegrationList:"updatedIntegrationList",moduleId:"moduleId",selectedAccountId:"selectedAccountId",selectedCount:"selectedCount",canEnableAllowDuplicates:"canEnableAllowDuplicates",canEnableAllowSecondaryUsers:"canEnableAllowSecondaryUsers",canAssignSequentially:"canAssignSequentially",isFbComponent:"isFbComponent",isAdAccount:"isAdAccount",isFormAccount:"isFormAccount",selectedAdName:"selectedAdName",isBulkFb:"isBulkFb",gridApi:"gridApi",isReferenceId:"isReferenceId",selectedReferences:"selectedReferences",displayName:"displayName"},outputs:{isShowAssignModalChanged:"isShowAssignModalChanged"},decls:20,vars:14,consts:[[3,"type","appImage","alt",4,"ngIf"],["class","align-center mt-30",4,"ngIf"],["class","bg-light-pearl mt-20 br-6 flex-between break-all bg-profile",4,"ngIf"],["class","bg-light-pearl mt-20 br-6 align-end justify-content-between",4,"ngIf"],[1,"border","br-20","bg-white","align-center","user","w-max-content","mt-10"],[4,"ngFor","ngForOf"],[4,"ngIf","ngIfElse"],["selectedUsers",""],[1,"flex-end","px-20","py-10","box-shadow-20","w-100","position-absolute","left-0","bottom-0"],[1,"fw-600","text-black-200","text-decoration-underline","cursor-pointer",3,"click"],[1,"border-right","mx-12","h-16"],[1,"br-4","bg-coal","py-10","px-20","text-white","cursor-pointer",3,"click"],["changePopup",""],[3,"type","appImage","alt"],[1,"align-center","mt-30"],[1,"icon","ic-chevron-left","ic-xxs","ic-black","cursor-pointer","mr-10",3,"click"],[1,"fw-700"],[1,"bg-light-pearl","mt-20","br-6","flex-between","break-all","bg-profile"],[1,"flex-column","pt-20","pl-10","pb-20"],["class","fw-semi-bold fv-sm-caps",4,"ngIf"],[4,"ngIf"],["class","fw-700 text-small text-truncate-1 break-all",4,"ngIf"],[1,"fw-semi-bold","fv-sm-caps"],["class","fw-700 text-small",4,"ngFor","ngForOf"],[1,"fw-700","text-small"],[1,"fw-700","text-small","text-truncate-1","break-all"],[1,"bg-light-pearl","mt-20","br-6","align-end","justify-content-between"],[1,"flex-column","pt-10","pl-10","pb-20"],[1,"fw-700","text-large"],["src","../../../../assets/images/profile.svg",1,"mt-8"],[1,"fw-semi-bold","fv-sm-caps","mt-10"],["class","activation",3,"ngClass","click",4,"ngIf"],[1,"activation",3,"ngClass","click"],[1,"mt-10","mb-4","d-flex","w-100","flex-wrap","cursor-pointer"],[1,"w-50",3,"title","click"],[1,"mb-4","checkbox-container","text-gray",3,"ngClass"],["type","checkbox",3,"ngModel","ngModelOptions","ngModelChange","change"],[1,"checkmark"],[1,"line-break",3,"ngClass"],["type","checkbox",3,"ngModel","ngModelOptions","ngModelChange"],["autocomplete","off",1,"prevent-text-select",3,"formGroup"],[3,"ngClass",4,"ngIf"],[3,"control","label"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedUser","class","bg-white","formControlName","assignedUser",3,"items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","change","ngModelChange",4,"ngIf"],[3,"ngClass"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedUser","formControlName","assignedUser",1,"bg-white",3,"items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","change","ngModelChange"],["ng-optgroup-tmp",""],["ng-label-tmp",""],["ng-option-tmp",""],[1,"checkbox-container"],["type","checkbox",3,"id","automate-id","checked"],[1,"checkbox-container",3,"click"],[1,"text-truncate-1","break-all"],[1,"label-req"],["label","Select Lead Duplicate User(s)",3,"control"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedDuplicateUser","formControlName","assignedDuplicateUser",1,"bg-white",3,"required","items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","change","ngModelChange"],[1,"d-flex"],[1,"checkbox-container","mt-10",3,"click"],[1,"line-break","text-sm","ip-pl-10"],[1,"fw-600","text-coal"],[1,"checkbox-container","mt-10"],["label","'Select Primary Owner(s)'",3,"control"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedPrimaryUsers","formControlName","assignedPrimaryUsers",1,"bg-white",3,"required","items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","ngModelChange","change"],["label","Select Secondary Owner(s)",3,"control"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedSecondaryUsers","formControlName","assignedSecondaryUsers",1,"bg-white",3,"required","items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","ngModelChange","change"],[1,"mt-10","checkbox-container",3,"click"],["ResizableDropdown","","bindLabel","fullName","bindValue","id","groupBy","selectedAllGroup","name","assignedDuplicateUser","formControlName","assignedDuplicateUser",1,"bg-white",3,"required","items","multiple","selectableGroup","closeOnSelect","clearSearchOnAdd","placeholder","ngModel","ngModelChange","change"],[1,"flex-between"],["class","align-center",4,"ngFor","ngForOf"],[1,"align-center"],[1,"form-check","form-check-inline","align-center","btn","pl-0","py-0","mr-0"],["type","radio","data-automate-id","userType","name","userType",1,"radio-check-input","mr-10",3,"id","checked","value","ngModel","ngModelOptions","change","ngModelChange"],[1,"fw-600","text-secondary","cursor-pointer","text-large",3,"for"],["class","mt-10",4,"ngIf"],[1,"mt-10"],[1,"cursor-pointer","mt-12","text-accent-green"],[1,"mt-12"],["class","flex-between mb-12",4,"ngFor","ngForOf"],[1,"flex-between","mb-12"],[1,"dot","dot-xl","bg-pearl-90","mr-6"],[1,"fw-semi-bold","text-normal","text-white","text-uppercase"],[1,"fw-semi-bold","text-large","text-coal"],[1,"bg-light-red","icon-badge",3,"click"],[1,"icon","ic-delete","m-auto","ic-xxs"],["class","cursor-pointer mr-20 mt-12",3,"ngClass","click",4,"ngIf"],["class","cursor-pointer mt-12",3,"ngClass","click",4,"ngIf"],[1,"cursor-pointer","mr-20","mt-12",3,"ngClass","click"],[1,"cursor-pointer","mt-12",3,"ngClass","click"],[1,"p-20"],[1,"text-black-100","fw-semi-bold","mb-20"],[1,"text-black-200","p-10","bg-light-pearl","text-large","br-4"],[1,"flex-end","mt-30"],["id","clkSettingsCancel","data-automate-id","clkSettingsCancel",1,"btn-gray","mr-20",3,"click"],["id","clkSettingsYes","data-automate-id","clkSettingsYes",1,"btn-green","px-12","text-nowrap","min-w-fit-content",3,"click"]],template:function(t,n){if(1&t&&(e.\u0275\u0275template(0,te,2,5,"img",0),e.\u0275\u0275template(1,ne,6,6,"div",1),e.\u0275\u0275template(2,oe,6,4,"div",2),e.\u0275\u0275template(3,_e,6,3,"div",2),e.\u0275\u0275template(4,ue,13,7,"div",3),e.\u0275\u0275elementStart(5,"div",4),e.\u0275\u0275template(6,Ae,2,1,"ng-container",5),e.\u0275\u0275elementEnd(),e.\u0275\u0275template(7,Re,23,33,"ng-container",6),e.\u0275\u0275template(8,He,3,3,"ng-template",null,7,e.\u0275\u0275templateRefExtractor),e.\u0275\u0275elementStart(10,"div",8)(11,"div",9),e.\u0275\u0275listener("click",function(){return n.hideAssignmentPopup(),n.isBulkAssignModel=!0}),e.\u0275\u0275text(12),e.\u0275\u0275pipe(13,"translate"),e.\u0275\u0275elementEnd(),e.\u0275\u0275element(14,"div",10),e.\u0275\u0275elementStart(15,"div",11),e.\u0275\u0275listener("click",function(){return n.assignAccount()}),e.\u0275\u0275text(16),e.\u0275\u0275pipe(17,"translate"),e.\u0275\u0275elementEnd()(),e.\u0275\u0275template(18,et,10,2,"ng-template",null,12,e.\u0275\u0275templateRefExtractor)),2&t){const s=e.\u0275\u0275reference(9);e.\u0275\u0275property("ngIf",!n.isBulkFb&&!n.isReferenceId),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.isBulkFb&&!n.isReferenceId),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",!n.isFbComponent&&!n.isBulkFb&&!n.isReferenceId),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.isReferenceId),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf",n.isFbComponent),e.\u0275\u0275advance(2),e.\u0275\u0275property("ngForOf",n.leadAssignmentOptions),e.\u0275\u0275advance(1),e.\u0275\u0275property("ngIf","Configuration"===n.selectedSectionLeadAssignment)("ngIfElse",s),e.\u0275\u0275advance(5),e.\u0275\u0275textInterpolate1("",e.\u0275\u0275pipeBind1(13,10,"BUTTONS.cancel")," "),e.\u0275\u0275advance(4),e.\u0275\u0275textInterpolate(e.\u0275\u0275pipeBind1(17,12,"BUTTONS.save"))}},dependencies:[u._Y,u.Fj,u.Wl,u._,u.JJ,u.JL,u.Q7,u.On,I.mk,I.sg,I.O5,C.w9,C.C5,C.ir,C.mR,X.z,u.sg,u.u,Z.S,H.s,ee.X$],encapsulation:2}),i})()}}]);