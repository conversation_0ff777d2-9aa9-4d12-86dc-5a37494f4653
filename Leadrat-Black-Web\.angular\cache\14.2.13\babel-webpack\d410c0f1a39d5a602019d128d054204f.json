{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function ignoreElements() {\n  return function ignoreElementsOperatorFunction(source) {\n    return source.lift(new IgnoreElementsOperator());\n  };\n}\n\nclass IgnoreElementsOperator {\n  call(subscriber, source) {\n    return source.subscribe(new IgnoreElementsSubscriber(subscriber));\n  }\n\n}\n\nclass IgnoreElementsSubscriber extends Subscriber {\n  _next(unused) {}\n\n} //# sourceMappingURL=ignoreElements.js.map", "map": null, "metadata": {}, "sourceType": "module"}