"use strict";(self.webpackChunklr_black=self.webpackChunklr_black||[]).push([[642],{12113:(O,v,t)=>{t.r(v),t.d(v,{EngageToModule:()=>A});var T=t(69808),E=t(40520),d=t(93075),C=t(33315),l=t(71511),p=t(18995),f=t(6162),x=t(23713),I=t(21718),h=t(51190),R=t(6252),j=t(90810),a=t(5e3),N=t(82722),r=t(61021),M=t(32049),U=t(8377),u=t(92340),J=t(65620),y=t(22313),z=t(63253);const D=["iframeElement"],S=[{path:"",component:(()=>{class s{constructor(e,o,n,g){this.store=e,this.sanitizer=o,this.metaTitle=n,this.headerTitle=g,this.stopper=new a.EventEmitter,this.getAppName=r.PO,this.rotate={path:"assets/animations/rotate.json"},this.circles=[{style:{width:"450px",height:"450px"},images:[{src:"../../../../assets/images/engageto/like.svg",style:{top:"0",left:"50%"}},{src:"../../../../assets/images/engageto/message-32.svg",style:{top:"35%",left:"100%"}},{src:"../../../../assets/images/engageto/like-count.svg",style:{top:"100%",left:"50%"}}]},{style:{width:"750px",height:"750px"},images:[{src:"../../../../assets/images/engageto/smile.svg",style:{top:"6%",left:"74%"}},{src:"../../../../assets/images/engageto/rupees.svg",style:{top:"50%",left:"100%"}},{src:"../../../../assets/images/engageto/light.svg",style:{top:"94%",left:"73%"}},{src:"../../../../assets/images/engageto/bag.svg",style:{top:"80%",left:"10%"}},{src:"../../../../assets/images/engageto/message-10.svg",style:{top:"55%",left:"1%"}},{src:"../../../../assets/images/engageto/pie-chart.svg",style:{top:"22%",left:"9%"}}]},{style:{width:"1000px",height:"1000px"},images:[{src:"../../../../assets/images/engageto/whatsapp.svg",style:{top:"20%",left:"90%"}},{src:"../../../../assets/images/engageto/check-circle.svg",style:{top:"44%",left:"100%"}},{src:"../../../../assets/images/engageto/like.svg",style:{top:"79%",left:"91%"}},{src:"../../../../assets/images/engageto/check-circle.svg",style:{top:"90%",left:"80%"}},{src:"../../../../assets/images/engageto/building.svg",style:{top:"87%",left:"16%"}},{src:"../../../../assets/images/engageto/heart.svg",style:{top:"57%",left:"0%"}},{src:"../../../../assets/images/engageto/star-count.svg",style:{top:"25%",left:"7%"}}]},{style:{width:"1250px",height:"1250px"},images:[{src:"../../../../assets/images/engageto/sparkle.svg",style:{top:"21%",left:"91%"}},{src:"../../../../assets/images/engageto/bag.svg",style:{top:"50%",left:"100%"}},{src:"../../../../assets/images/engageto/pie-chart.svg",style:{top:"69%",left:"96%"}},{src:"../../../../assets/images/engageto/rupees.svg",style:{top:"82%",left:"12%"}},{src:"../../../../assets/images/engageto/smile.svg",style:{top:"67%",left:"3%"}},{src:"../../../../assets/images/engageto/check-circle.svg",style:{top:"33%",left:"3%"}}]},{style:{width:"1500px",height:"1500px"},images:[{src:"../../../../assets/images/engageto/heart.svg",style:{top:"50%",left:"100%"}},{src:"../../../../assets/images/engageto/bag.svg",style:{top:"50%",left:"0%"}}]},{style:{width:"1750px",height:"1750px"},images:[{src:"../../../../assets/images/engageto/whatsapp-icon.svg",style:{top:"40%",left:"99%"}},{src:"../../../../assets/images/engageto/star-count.svg",style:{top:"70%",left:"96%"}},{src:"../../../../assets/images/engageto/sparkle.svg",style:{top:"67%",left:"3%"}},{src:"../../../../assets/images/engageto/heart.svg",style:{top:"30%",left:"4%"}}]}],this.engageToUrl=u.N.engageToURL}ngOnInit(){this.metaTitle.setTitle("CRM | EngageTo"),this.headerTitle.setLangTitle("EngageTo"),this.store.select(M.et).pipe((0,N.R)(this.stopper)).subscribe(e=>{this.userDetails=e}),this.safeUrl=this.sanitizer.bypassSecurityTrustResourceUrl(this.engageToUrl)}JoinEngageTo(){let e={Sender:"<EMAIL>",Subject:"New Enquiry for EngageTo",ContentBody:this.getContent(),ToRecipients:["<EMAIL>"]};this.store.dispatch(new U.oz(e))}getContent(){var e,o,n,g,m;const B=null===(g=null===(n=null===(o=null===(e=this.userDetails)||void 0===e?void 0:e.userRoles)||void 0===o?void 0:o.filter(c=>"Default"!=c.name))||void 0===n?void 0:n.map(c=>c.name))||void 0===g?void 0:g.join(", "),L=JSON.parse(localStorage.getItem("userDetails")||"{}"),{given_name:F,family_name:Q,preferred_username:w}=L;return`A Client is interested in "EngageTo" Business API  \n  Tenant Name: ${(0,r.u9)()},\n  Environment: ${u.N.envt} ,\n  User Name: ${F} ${Q},\n  User ID: ${w},\n  Phone Number: ${null===(m=this.userDetails)||void 0===m?void 0:m.phoneNumber},\n  Role(s): ${B},\n`}ngAfterViewInit(){this.initIframe()}onIframeLoad(){var e,o;null!==(e=this.iframeElement)&&void 0!==e&&e.nativeElement&&(null===(o=this.iframeElement.nativeElement.contentWindow)||void 0===o||o.postMessage({tenantId:(0,r.u9)(),emailContent:this.getContent()},"*"))}initIframe(){var e;const o=null===(e=this.iframeElement)||void 0===e?void 0:e.nativeElement;o&&(o.onload=()=>{setTimeout(()=>this.onIframeLoad(),1e3)},o.setAttribute("src",o.getAttribute("src")))}}return s.\u0275fac=function(e){return new(e||s)(a.\u0275\u0275directiveInject(J.yh),a.\u0275\u0275directiveInject(y.H7),a.\u0275\u0275directiveInject(y.Dx),a.\u0275\u0275directiveInject(z.g))},s.\u0275cmp=a.\u0275\u0275defineComponent({type:s,selectors:[["engage-to-join"]],viewQuery:function(e,o){if(1&e&&a.\u0275\u0275viewQuery(D,5),2&e){let n;a.\u0275\u0275queryRefresh(n=a.\u0275\u0275loadQuery())&&(o.iframeElement=n.first)}},decls:2,vars:1,consts:[[1,"w-100","h-100",3,"src","load"],["iframeElement",""]],template:function(e,o){1&e&&(a.\u0275\u0275elementStart(0,"iframe",0,1),a.\u0275\u0275listener("load",function(){return o.onIframeLoad()}),a.\u0275\u0275elementEnd()),2&e&&a.\u0275\u0275property("src",o.safeUrl,a.\u0275\u0275sanitizeResourceUrl)},encapsulation:2}),s})()}];let $=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=a.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=a.\u0275\u0275defineInjector({imports:[l.Bz.forChild(S),l.Bz]}),s})(),A=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=a.\u0275\u0275defineNgModule({type:s}),s.\u0275inj=a.\u0275\u0275defineInjector({imports:[T.ez,l.Bz.forChild(R._),$,j.m,C.Y4,I.d,p.aw.forChild({loader:{provide:p.Zw,useFactory:h.gS,deps:[E.eN]}}),x.CT.forRoot({player:h.xd}),d.u5,d.UX,f.l1,f.l1]}),s})()}}]);