{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nimport { defaultThrottleConfig } from './throttle';\nexport function throttleTime(duration, scheduler = async, config = defaultThrottleConfig) {\n  return source => source.lift(new ThrottleTimeOperator(duration, scheduler, config.leading, config.trailing));\n}\n\nclass ThrottleTimeOperator {\n  constructor(duration, scheduler, leading, trailing) {\n    this.duration = duration;\n    this.scheduler = scheduler;\n    this.leading = leading;\n    this.trailing = trailing;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ThrottleTimeSubscriber(subscriber, this.duration, this.scheduler, this.leading, this.trailing));\n  }\n\n}\n\nclass ThrottleTimeSubscriber extends Subscriber {\n  constructor(destination, duration, scheduler, leading, trailing) {\n    super(destination);\n    this.duration = duration;\n    this.scheduler = scheduler;\n    this.leading = leading;\n    this.trailing = trailing;\n    this._hasTrailingValue = false;\n    this._trailingValue = null;\n  }\n\n  _next(value) {\n    if (this.throttled) {\n      if (this.trailing) {\n        this._trailingValue = value;\n        this._hasTrailingValue = true;\n      }\n    } else {\n      this.add(this.throttled = this.scheduler.schedule(dispatchNext, this.duration, {\n        subscriber: this\n      }));\n\n      if (this.leading) {\n        this.destination.next(value);\n      } else if (this.trailing) {\n        this._trailingValue = value;\n        this._hasTrailingValue = true;\n      }\n    }\n  }\n\n  _complete() {\n    if (this._hasTrailingValue) {\n      this.destination.next(this._trailingValue);\n      this.destination.complete();\n    } else {\n      this.destination.complete();\n    }\n  }\n\n  clearThrottle() {\n    const throttled = this.throttled;\n\n    if (throttled) {\n      if (this.trailing && this._hasTrailingValue) {\n        this.destination.next(this._trailingValue);\n        this._trailingValue = null;\n        this._hasTrailingValue = false;\n      }\n\n      throttled.unsubscribe();\n      this.remove(throttled);\n      this.throttled = null;\n    }\n  }\n\n}\n\nfunction dispatchNext(arg) {\n  const {\n    subscriber\n  } = arg;\n  subscriber.clearThrottle();\n} //# sourceMappingURL=throttleTime.js.map", "map": null, "metadata": {}, "sourceType": "module"}