{"HELLO": "hello world", "TITLE": "The title of my app", "GLOBAL": {"settings": "ക്രമീകരണങ്ങൾ", "today": "ഇന്ന്", "all": "എല്ലാം", "new": "പുതിയ", "old": "പഴയ", "overdue": "കാലാവധി കഴിഞ്ഞ", "upcoming": "വരാനിരിക്കുന്ന", "completed": "പൂർത്തിയായി", "ivr": "IVR", "not-interested": "താൽപ്പര്യമില്ല", "un-served": "Un Served", "select": "തിരഞ്ഞെടുക്കുക", "status": "നില", "assigned-to": "നിയോഗിച്ചത്", "for": "വേണ്ടി", "reset": "പുനഃസജ്ജമാക്കുക", "go": "പോകുക", "no": "ഇല്ല", "yes": "അതെ", "in": "in", "lead": "ലീഡ്", "leads": "ലീഡുകൾ", "name": "പേര്", "show": "കാണിക്കുക", "entries": "എൻട്രികൾ", "update": "അപ്ഡേറ്റ്", "reassign": "Reassign", "details": "വിശദാംശങ്ങൾ", "personal": "വ്യക്തിഗത", "work": "ജോലി", "edit": "എഡിറ്റ്", "alternate": "Alternate", "user-confirmation": "Are you sure you want to", "section": "Section", "submitted": "Submitted", "successfully": "Successfully", "dateType": "Date Type", "history": "History", "current": "Current", "send-comment": "Send Comment as", "changed": "Changed", "great": "Great", "by": "By", "from": "From", "to": "വരെ", "enter": "Enter", "date": "തീയതി", "actions": "Actions", "browse": "Browse", "qualified": "Qualified", "mark-as": "<PERSON> as", "mark-as-read": "<PERSON>", "notifications": "അറിയിപ്പുകൾ", "page-not-found": "The page you are looking for can not be found.", "oops": "OOPS", "or": "OR", "or-small": "or", "showing": "കാണിക്കുന്നു", "of": "ന്റെ", "search": "തിരയുക", "featured": "Featured", "not-found": "Page Not Found", "gender": "Gender", "blood-group": "Blood Group", "save-changes": "Be sure to save the changes before proceeding to the next page.", "remove-document": "Are you sure you want to remove the uploaded document?", "done-by": "done by", "your": "Your", "character": "Character", "uppercase": "Uppercase", "lowercase": "Lowercase", "number": "Number", "symbol": "Symbol", "to-small": "വരെ", "of-small": "ന്റെ", "entries-small": "എൻട്രികൾ", "selected-file": "File selected successfully", "is-required-field": "is a required field", "go-to": "Go to", "s-no": "S.NO", "variable": "Variable", "under-construction": "Under construction", "under-construction-message": "Please check back soon just putting little touch up on some pretty updates.", "min": "<PERSON>.", "max": "<PERSON>.", "lower": "Lower", "upper": "Upper", "default": "<PERSON><PERSON><PERSON>", "match": "Match", "required": "Required", "added": "Added", "executive": "Sales Executive", "items": "Items", "deselect": "Deselect", "overview": "Overview", "document": "Document", "copy-link": "Copy link", "link-copied": "Link copied to clipboard", "select-your-preferences": "Select your preferences", "change-source": "Change Source", "change-project": "Change Project", "project-name": "Project Name", "create-duplicate": "Create duplicate", "change": "Change", "activity": "Activity", "assign-from": "Assign From", "refresh": "Refresh", "mandatoryUpdateAvailable": "There is a mandatory update available, please refresh the application.", "newVersionAvailable": "There is a new version available, please refresh to continue", "no-internet-connection": "No Internet Connection. Please check your network.", "delete": "ഇല്ലാതാക്കുക", "update-tag": "Update Tag", "tag-name": "Tag Name", "select-tag-icon": "Select Tag Icon", "crm": "CRM", "please-enable-javascript-to-co": "Please enable JavaScript to continue using this application.", "capture-photo": "Capture Photo", "s": "S(", "important-notes": "Important Notes", "attention": "Attention:", "cancel": "റദ്ദാക്കുക", "publish": "Publish", "proceed-with-adding": "Proceed With Adding", "app-logo": "App Logo", "share-details": "Share Details", "info": "വിവരം", "lead-name": "Lead Name:", "project-unit": "Project Unit:", "choose-which-number-you-want-t": "choose which number you want to", "choose-which-template-you-want": "choose which template you want to", "": "?", "ex-dear-nichola-ferrellas-per-": "ex. Dear <PERSON><PERSON><PERSON>,As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.\r\n  \r\n  Thankyo<PERSON>,\r\n  <PERSON><PERSON><PERSON>", "saved-filter": "Saved Filter", "no-saved-filters-found": "No saved filters found", "type-to-search": "Type to search", "select-form": "Select Form:", "ex-qr-form": "ex. QR form", "ok": "OK", "go-to-first-page": "Go to first page", "go-to-previous-page": "Go to previous page", "go-to-next-page": "Go to next page", "go-to-last-page": "Go to last page", "how-to-import": "How to import?", "step-1": "Step 1", "step-2": "Step 2", "step-3": "Step 3", "step-1-": "step 1 :", "step-2-": "step 2 :", "step-3-": "step 3 :", "field-mapping": "Field mapping", "select-the": "select the", "csv": "CSV", "excel": "EXCEL", "fields-that-match-our": "fields that match our", "fields": "fields.", "primary-number": "Primary Number", "confirm-mapping": "Confirm Mapping", "verified": "Verified", "review-finalize-your-import": "Review & finalize your Import", "file-uploaded": "File uploaded:", "select-sheet": "Select sheet", "select-a-field": "Select a field", "property-subtype": "Property Sub-type", "beds": "Beds", "baths": "Baths", "furnish-status": "Furnish Status", "preferred-floors": "Preferred Floors", "offering-type": "Offering Type", "builtup-area": "Built-up Area", "saleable-area": "Saleable Area", "property-area": "Property Area", "net-area": "Net Area", "unit-numbername": "Unit Number/Name", "cluster-name": "Cluster Name", "deleted-date": "Deleted Date", "possession-needed-by": "Possession Needed By", "referral-name": "Referral Name", "referral-number": "Referral Number", "referral-email": "Referral Email", "serial-number": "Serial Number", "facebook-info": "Facebook Info", "ad-id": "Ad ID", "ad-name": "Ad Name", "ad-set-id": "Ad Set ID", "ad-set-name": "Ad Set Name", "page-id": "Page ID", "campaign-id": "Campaign ID", "campaign-name": "Campaign Name", "ad-account-id": "Ad Account ID", "ad-account-name": "Ad Account Name", "facebook-id": "Facebook ID", "isautomated": "IsAutomated", "automation-id": "Automation ID", "is-subscribed": "Is Subscribed", "previous-lead": "Previous Lead", "next-lead": "Next Lead", "p": "P", "calling-": "Calling ....", "supported-formatsjpg-png-pdf": "Supported formats.Jpg, Png, Pdf", "users-export-tracker": "Users - Export Tracker", "teams-export-tracker": "Teams - Export Tracker", "reports-export-tracker": "Reports - Export Tracker", "property-export-tracker": "Property - Export Tracker", "project-export-tracker": "Project - Export Tracker", "clicking-on": "Clicking on", "confirm": "\"Confirm\"", "export-tracker": "\"Export Tracker\"", "notes-count": "Notes Count", "between-1-to-100-most-recently": "Between 1 to 100 most recently updated notes can only be exported", "ex-3": "ex 3", "facebook-bulk-fetch-tracker": "Facebook - Bulk Fetch Tracker", "attendance-export-tracker": "Attendance - Export Tracker", "importing-users-becomes-more-e": "Importing users becomes more easier", "users-upload-scheduled": "User(s) Upload Scheduled", "deleted-users-tracker": "Deleted Users - Tracker", "select-users": "Select User(s)", "bulk-operation-tracker": "Bulk Operation Tracker", "drag-and-drop-or": "Drag and drop or", "drop-your-files-here-or": "Drop your file(s) here or", "fetching-setting-up-your-data": "fetching & setting up your data...", "whatsapp": "WhatsApp", "invoice": "Invoice", "data": "Data", "project-vs-substatus": "Project vs Sub-Status", "received-date-vs-source": "Received Date vs Source", "substatus-vs-subsource": "Sub-Status vs Sub-Source", "refer-earn": "Refer & Earn", "engage-to": "Engage to", "app-text": "App Text", "clock-in-out": "“Clock IN / OUT”", "clock-in": "Clock IN", "since-last-login": "since last login", "current-date-time": "Current Date & Time", "new-lead-assigned": "New lead assigned", "2-min-ago": "2 min ago", "documents-successfully-uploade": "Documents successfully uploaded", "4-documents": "4 Documents", "have-been-uploaded-against-the": "have been uploaded against the", "site-visit-scheduled": "Site Visit Scheduled", "oh-great-a-sitevisit-has-been-": "Oh great!! A site-visit has been scheduled with", "sk-nowlak": "SK Nowlak", "5pm": "5PM", "on": "on", "15th-july-2025": "15th July, 2025", "check-it-out": "Check it out!", "meeting-scheduled": "Meeting Scheduled", "ravi": "<PERSON>", "12th-july-2025": "12th July, 2025", "profile-pic": "profile pic", "no-conversation-found": "No Conversation Found", "select-an-item-to-read-and-pre": "Select an item to read and preview", "ex-19062025-29062025": "ex. 19-06-2025 - 29-06-2025", "loader": "loader", "assign-to": "Assign To", "-disabled-": "( Disabled )", "text-by": "Text By", "workflows-": "Workflows /", "lead-assignment-flow": "Lead Assignment Flow", "select-action-type-and-trigger": "Select action type and trigger preference", "immediate": "immediate", "when-lead-is-added": "When lead is added", "when-lead-is-updated": "When lead is updated", "set-condition-to-filter-record": "Set condition to filter records", "all-leads": "All Leads", "based-on-conditions": "Based on conditions", "this-workflow-will-be-applied-": "This workflow will be applied to all the leads", "set-actions-to-be-performed": "Set actions to be performed", "hi-there-zoom-me": "Hi there Zoom Me!!!!!", "choose": "choose", "zoomin": "zoom-in", "zoomout": "zoom-out", "tracker": "Tracker", "bulk-role-update": "Bulk Role Update", "user": "ഉപയോക്താവ്", "designation": "Designation", "department": "വകുപ്പ്", "reporting-to": "Reporting To", "general-manager": "General Manager", "timezone": "Timezone", "available-licenses": "available licenses.", "muso": "muso", "reset-to-default-password": "Reset to Default Password", "ex-5032025-14032025": "ex. 5-03-2025 - 14-03-2025", "select-designation": "select designation", "select-department": "select department", "time-zone": "Time Zone:", "edit-user": "Edit User", "roles-permissions": "Roles & permissions", "upload-new-file": "upload new file", "identity-documents": "Identity Documents", "previous-experience": "Previous Experience", "signature": "Signature", "you-can-select-and-upload-your": "You can select and upload your file", "supported-formats-pdf": "Supported formats pdf", "upload": "അപ്ലോഡ്", "search-for-document-name": "Search for document name..", "ex-aadhaar-card": "ex. <PERSON><PERSON><PERSON><PERSON>", "ex-offer-letter": "ex. Offer Letter", "file": "file", "pdf": "Pdf", "doc": "Doc", "xls": "xls", "img": "img", "no-leads-found": "No leads found", "clock-out": "Clock OUT", "clock-inout-location": "Clock in/out location", "image": "image", "no-entries-found": "No entries found", "app-tour-": "App Tour :", "ex-manasapampanagmailcom": "ex. manasapa<PERSON><PERSON>@gmail.com", "ex-o": "ex. o+", "ex-india": "ex. India", "gender-type": "Gender Type", "ex-15851-level-2-20th-main-rd-": "ex. 1585/1 level, 2, 20th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102", "enter-the-old-password": "Enter the old password", "enter-the-new-password": "Enter the new password", "reenter-the-new-password": "Re-enter the new password", "integration": "Integration", "projects": "Projects", "city": "നഗരം", "zone": "Zone", "locality": "Locality", "this-user-is-associated-with-t": "This user is associated with these localities", "locality-title": "Locality Title", "action": "Action", "remove-assignment": "Remove Assignment", "no-data-found": "No Data Found", "user-delete-scheduled": "User Delete Scheduled", "permanent-delete": "Permanent Delete", "license-number": "License Number", "language": "ഭാഷ", "min-length-should-be-6-only-al": "Min length should be 6.\r\nOnly Alpha-numeric.\r\nWhite or empty space is not allowed.", "ex-mounikap": "ex. mounikap", "ex-1qaz2wsx": "ex. 1qaz2wsx....", "ex-mounika": "ex. <PERSON><PERSON>", "ex-pampana": "ex. Pampana", "ex-pmounimnkgmailcom": "ex. <EMAIL>", "9133xxxxxx": "9133XXXXXX", "ex-hsr-layout-bengaluru": "ex.  HSR Layout, Bengaluru", "ex-deepak": "ex. <PERSON>", "ex-go1033": "ex. GO-1033", "ex-manasa": "ex. <PERSON>", "selectcreate-department": "Select/Create Department", "selectcreate-designation": "Select/Create Designation", "select-time-zone": "select time zone", "select-language": "Select language", "show-time-zone": "Show Time Zone", "ex-98765467": "ex. 98765467", "ex-i-want-to-say": "ex.  I want to say.....", "search-for-role": "Search for role...", "add-team": "Add Team", "create-customize-and-delete-te": "Create, customize, and delete teams with ease", "members": "Members", "bulk-delete": "Bulk Delete", "team": "ടീം", "search-team": "Search team", "no-team-found": "No team found", "logo": "logo", "no-member-found": "No member found", "team-leader": "Team Leader", "no-users-left-for-assignment": "No users left for assignment.", "ex-leads-recovery-team": "ex. Leads Recovery Team", "select-a-leader": "Select a leader", "alert": "<PERSON><PERSON>", "save": "സേവ്", "ex-manager": "ex. Manager", "mounika-pampana": "Mounika Pampana", "software-engineer": "Software engineer", "0841-pm": "08:41 PM", "see-location": "See location", "1241-pm": "12:41 PM", "robert-williams": "<PERSON>", "assignee": "assignee", "priority": "priority:", "time": "time:", "created-by": "created by:", "created-date": "created date:", "modified-by": "modified by:", "modified-date": "modified date:", "description": "description:", "ex-need-to-call": "ex. Need to call", "ex-mounika-pampana": "ex. Mounika Pampana", "ex-19062025-1149-am": "ex. 19/06/2025, 11:49 am", "state": "സംസ്ഥാനം", "country": "രാജ്യം", "ex-manasa-pampana": "ex. Manasa Pampana", "search-by-user": "Search by User", "search-by-subsource": "Search by Sub-Source", "subsource-date-filter": "Sub-Source Date Filter", "columns": "Columns", "source-date-filter": "Source Date Filter", "search-by-source": "Search by Source", "received-date-vs": "Received Date vs", "lead-generating-from": "Lead Generating From", "leads-received-date": "Leads Received Date", "search-by-received-date": "Search by Received Date", "search-by-project": "Search by Project", "project-date-filter": "Project Date Filter", "lead-city-reports": "Lead City Reports", "call-date-filters": "Call Date Filters", "agency-date-filter": "Agency Date Filter", "search-by-agency-name": "Search by Agency Name", "add-reference-id": "Add Reference ID", "selected-reference-ids": "Selected reference id(s)", "reference-assignment": "Reference Assignment", "filter-by": "Filter by", "associated-property": "Associated Property", "portal-name": "Portal Name", "property-title": "Property Title", "reference-id": "Reference Id", "apply-filter": "Apply filter", "ex-rohit-house": "ex. <PERSON><PERSON><PERSON> house", "ex-bayut": "ex. Bayut", "ex-txn2023081512345": "ex. TXN-20230815-12345", "ex-p987654": "ex. P-987654", "bulk-restore": "Bulk Restore", "property-name": "Property name", "associated-leads": "Associated Leads", "associated-data": "Associated Data", "delete-permanently": "Delete Permanently", "delete-temporarily": "Delete Temporarily", "looking-to": "Looking to:", "price": "Price:", "select-agent": "Select Agent", "possession": "Possession", "owner-name": "Owner Name", "property-status": "സ്വത്ത് നില", "serial-no": "Serial No", "lead-count": "Lead Count", "data-count": "Data Count", "area": "Area:", "carpet-area": "Carpet Area", "budget": "Budget:", "location": "Location:", "attributes-amenities": "Attributes & Amenities:", "facing": "Facing", "no-of-floors": "No. of Floors", "no-of-bathrooms": "No. of Bathrooms", "no-of-living-rooms": "No. of Living rooms", "no-of-bedrooms": "No. of Bedrooms", "no-of-utilites": "No. of Utilites", "no-of-kitchens": "No. of Kitchens", "no-of-balconies": "No. of Balconies", "ex-532025": "ex. 5/3/2025", "enter-property-title": "enter property title", "enter-serial-no": "enter serial no", "ex-10": "ex. 10", "ex-1000": "ex. 1000", "ex-123": "ex. 123", "ex-sq-feet": "ex. sq. feet.", "ex-1000000": "ex. 1000000", "disabled": "(Disabled)", "disable": "(Disable)", "restore": "Rest<PERSON>", "clone": "<PERSON><PERSON>", "copy-microsite-url": "Copy Microsite URL", "select-user": "Select User", "select-date-range": "Select date range", "selected-propertys": "Selected Property(s)", "select-portal": "Select Portal:", "agent-name": "Agent Name", "portal": "Portal", "address": "വിലാസം", "modify-portal": "Modify Portal", "modify": "മാറ്റുക", "select-portals": "Select Portals", "listing-sync-tracker": "Listing - Sync Tracker", "listing-status": "Listing Status", "listing-by": "Listing By", "listing-on-behalf": "Listing On Behalf", "completion-status": "Completion Status", "listing-level": "Listing Level", "reference-no": "Reference No", "property-type": "Property Type:", "br": "BR", "br-type": "BR Type", "community": "Community", "subcommunity": "Sub-Community", "no-of-utilities": "No. of Utilities", "amenities": "Amenities", "enter-reference-no": "enter reference no", "properties-upload-scheduled": "Properties Upload Scheduled", "facilities": "Facilities", "hide-price-on-application": "Hide Price on Application", "eng": "(eng)", "co-working-operator": "Co Working Operator", "maintenance-cost": "Maintenance Cost", "depositsecurity-amount": "Deposit/Security Amount", "service-charges": "Service Charges", "security-deposit": "Security Deposit", "common-area-charges": "Common Area Charges", "lock-in-period": "Lock In Period", "notice-period": "Notice Period", "rent-amount": "Rent Amount", "per-month": "per month", "number-of-cheques": "Number of Cheques", "escalation": "Escalation", "refrence-no": "Refrence No:", "permit-number": "Permit Number", "dld": "DLD", "dtmc": "DTMC", "notes": "Notes", "sub-community": "Sub Community", "pin-code": "Pin Code", "pincode": "പിൻകോഡ്", "listing": "Listing", "remove-location": "Remove Location", "add-location": "Add Location", "tenant-poc-name": "Tenant POC Name", "tenant-poc-designation": "Tenant POC Designation", "tenant-poc-phone": "Tenant POC Phone", "co-working-operator-poc-name": "Co Working Operator POC Name", "co-working-operator-poc-phone": "Co Working Operator POC Phone", "contact-information": "Contact Information", "remove": "നീക്കം ചെയ്യുക", "ownerbuilder-name": "Owner/Builder Name", "phone-number": "Phone Number", "alternate-number": "Alternate Number", "email": "ഇമെയിൽ", "number-of-floor-occupied": "Number Of Floor Occupied", "total-floors": "Total Floors", "property-gallery": "Property gallery", "360-video-url": "360 video URL", "only-url-will-save": "Only .URL will save", "third-party-url": "Third Party URL", "photos": "Photos", "-add-photos": "+ Add Photos", "max-upload-limit-15-mb": "(max upload limit 15 MB)", "videos": "Videos", "only-mp4-file-will-be-accepted": "Only .mp4 file will be accepted.", "-add-videos": "+ Add Videos", "brochures": "Brochures", "only-pdf-file-will-be-accepted": "Only .pdf file will be accepted.", "-add-documents": "+ Add Documents", "arabic": "(arabic)", "uploadingnbsp": "Uploading&nbsp;", "transaction-type": "Transaction Type", "ex-utpal": "ex. <PERSON><PERSON><PERSON>", "ex-": "ex. أوتبال", "ex-20": "ex. 20", "ex-30": "ex. 30", "ex-1906": "ex. 1906", "ex-10122025": "ex. 10/12/2025", "ex-20000": "ex. 20000", "ex-300000": "ex. 300000", "select-security-deposit": "Select Security Deposit", "select-lock-in-period": "Select lock in period", "select-notice-period": "Select notice period", "ex-4000000": "ex. 4000000", "ex-yearly": "ex. Yearly", "ex-29": "ex. 29", "ex-789465682": "ex. 789465682", "select-location": "Select Location", "ex-hsr-layout": "ex. HSR Layout", "ex-bengaluru": "ex. Bengaluru", "ex-karnataka": "ex. Karnataka", "ex-560102": "ex. 560102", "ex-560010": "ex. 560010", "ex-ceo": "ex. CEO", "ex-utpalgmailcom": "ex. <EMAIL>", "2nd-floor-and-4th-floor": "2nd floor and 4th floor", "add-360-video-url": "Add 360 video URL", "select-field": "Select field", "video": "video", "fetching-amenities": "fetching amenities...", "property": "സ്വത്ത്", "pattern": "pattern", "property-image": "property image", "sold-out": "Sold Out", "check": "check", "gallery": "Gallery", "attr": "attr", "amenity": "amenity", "availability": "Availability", "builderdeveloper-name": "Builder/developer Name", "close": "അടയ്ക്കുക", "land-area": "Land Area", "select-possession": "Select possession", "select-month-and-year": "Select month and year", "ex-05032025": "ex. 05/03/2025", "add-project": "Add Project", "bulk-share": "Bulk Share", "select-all": "Select All", "same-as": "Same as", "assign-duplicate-leads-sequent": "Assign duplicate leads sequentially", "same-as-above": "Same as above", "this-feature-will-assign-a-dup": "This feature will assign a duplicate of the Incoming Lead to a block of  selected user(s) at once.", "this-feature-will-assign-a-sec": "This feature will assign a secondary Owner to a Incoming Lead to a block of  selected user(s) in Sequential Manner.", "importing-projects-becomes-mor": "Importing projects becomes more easier", "projects-upload-scheduled": "Projects Upload Scheduled", "add-new-unit": "Add new unit", "no-unit-available": "No unit available", "go-to-next": "Go To Next", "no-unit-found": "No unit found", "unit-area": "Unit Area", "super-builtup-area": "Super Built-up Area", "price-per-unit-area": "Price Per Unit Area", "total-price": "Total Price", "unit-type": "Unit Type", "unit-subtype": "Unit Sub-Type", "noof-bathrooms": "No.of Bathrooms", "noof-living-rooms": "No.of Living rooms", "noof-bedrooms": "No.of Bedrooms", "noof-utilites": "No.of Utilites", "no-of-maximum-occupants": "No. of Maximum Occupants", "ex-200000": "ex. 200000", "ex-inr": "ex. INR", "ex-residential": "ex. Residential", "ex-plot": "ex. Plot", "ex-1-bhk": "ex. 1 BHK", "importing-project-units-become": "Importing project units becomes more easier", "please-choose-the-sheet-and-ve": "Please choose the sheet and verify the mapped fields", "project-units-upload-scheduled": "Project Units Upload Scheduled", "unit-details": "Unit Details", "unit-name": "Unit Name", "furnishing-status": "Furnishing Status", "attributes": "Attributes", "uploads": "Uploads", "ex-east-luxury": "ex. East luxury", "ex-503": "ex. 503", "project-gallery": "Project gallery", "save-and-finish": "Save and Finish", "add-project-url": "Add Project URL", "no-block-available": "No block available", "select-blocks": "Select Blocks", "block": "block", "no-block-found": "No block found", "number-of-floors": "Number of Floors", "save-block": "Save Block", "ex-block-a": "ex. Block A", "ex-1400": "ex. 1400", "ex-sq-mt": "ex. Sq. Mt.", "ex-19062025": "ex. 19/06/2025", "ex-29062025": "ex. 29/06/2025", "select-date": "Select date", "project-details": "Project Details", "project-type": "Project Type", "project-subtype": "Project Sub-Type", "certificate": "Certificate", "total-blocks": "Total Blocks", "total-units": "Total Units", "project-description": "Project Description", "builder-details": "Builder details", "builder-contact-number": "Builder Contact Number", "point-of-contact": "Point of Contact", "rera-details": "RERA Details", "rera-registration-numbers": "RERA Registration numbers", "project-price": "Project price", "date-status": "Date Status", "associated-banks": "Associated Banks", "select-banks": "Select Bank(s)", "project-location-details": "Project location details", "save-go-to-next": "Save & Go To Next", "ex-upcoming": "ex. Upcoming", "ex-4758": "ex. 4758", "ex-govt-approved": "ex. Govt. Approved", "ex-utpal-by-aashish-group-has-": "ex. Utpal by Aashish Group has a pleasing appearance that would charm most buyers. The units of this property are Under Construction. The units offered are Flat, which have been developed to provide complete satisfaction.", "ex-prestige-group": "ex. Prestige Group", "ex-p02919062000": "ex. P02919062000", "add-rera-numbers": "Add rera numbers", "ex-3210000": "ex. 3210000", "ex-5210000": "ex. 5210000", "ex-sector-1-hsr-layout": "ex. sector 1 HSR Layout", "ex-bangalore": "ex. Bangalore", "ex-228118": "ex. 228118", "no-amenities-found": "No Amenities Found", "no-testimonial-found": "No testimonial found", "profile": "പ്രൊഫൈൽ", "ex-abc-company": "ex. ABC Company", "ex-the-standard-and-quality-is": "ex. The standard and quality is very good", "active-subscription-info": "Active Subscription Info", "date-of-subscription": "Date of Subscription", "total-payment-done": "Total Payment Done", "inc-gst": "(inc GST)", "price-per-user": "Price Per User", "x": "x", "inr": "INR", "total-due-amount": "Total Due Amount", "pay-now": "Pay now", "payment-due-date": "Payment Due Date", "plan-expiry-date": "Plan Expiry Date", "active-plan": "Active Plan", "plan-validity": "Plan Validity", "days-left": "Days Left", "days-completed": "Days Completed", "license-bought": "License Bought", "active-users": "Active Users", "inactive-users": "Inactive Users", "user-licenses": "User Licenses", "buy-licenses": "Buy Licenses", "transaction-details": "Transaction Details", "transaction-date": "Transaction Date", "plan-status": "Plan Status", "noof-users": "No.of Users", "plan-net-amount": "Plan Net Amount", "plan-gst": "Plan GST", "total-amount": "Total Amount", "paid-amount": "<PERSON><PERSON>", "amount-due": "Amount Due", "due-date": "Due Date", "payment-details": "Payment Details", "payment": "Payment", "amount-": "Amount :", "qr-code": "QR Code", "bank-transfer": "Bank Transfer", "card-payment": "Card Payment", "dhinwa-solutions-private-limit": "Dhinwa Solutions Private Limited", "leadrat-crm-software": "Leadrat CRM Software", "steps-to-pay": "Steps to pay", "enter-amount": "Enter Amount", "pay-using-neftrtgsimps": "Pay using NEFT/RTGS/IMPS", "to-complete-the-transaction-ma": "To complete the transaction, make NEFT / RTGS / IMPS transfer to :", "beneficiary-name": "Beneficiary Name:", "account": "Account:", "ifsc": "IFSC:", "icic0007295": "ICIC0007295", "copy-details": "Copy Details", "please-contact-our-customer-ca": "Please contact our customer care to proceed with card payment.", "our-customer-care-no": "Our customer care no.", "add-license": "Add License", "number-of-license": "Number of License", "net-amount-": "Net Amount :", "gst-igst-18-": "GST / IGST (18%) :", "total-amount-": "Total Amount :", "payment-methods": "Payment Methods", "upi": "UPI", "credit-debit-card": "Credit / Debit Card", "net-banking": "Net Banking", "sl-no": "SL NO", "net-amount": "Net Amount", "gst-18": "GST (18%)", "pending-amount": "Pending Amount", "payment-mode": "Payment Mode", "next-due-date": "Next Due Date", "please-contact-support-team-80": "Please contact support team. **********.", "banner": "Banner", "gst-number": "GST Number", "ex-smart-project-of-the-year": "ex. Smart project of the year", "noimg": "noimg", "posted-on": "Posted on", "highlight": "highlight", "more-properties": "More Properties", "negotiable": "Negotiable", "this-feature-is-available-in-h": "This feature is available in higher-tier subscriptions.", "enquire-now": "Enquire now", "i-agreed-to-receive-communicat": "I agreed to receive communication regarding lead enquiry.", "in-order-to-proceed-please-che": "In order to proceed, please check checkbox.", "enter-your-name": "Enter your name", "property-urls": "Property URL(s)", "read-more": "read more", "read-less": "read less", "regulatory-information": "Regulatory Information", "reference-": "Reference -", "broker-name-": "Broker Name -", "broker-licenseno-": "Broker LicenseNo -", "dld-permit-number-": "DLD Permit Number -", "dtmc-permit-number-": "DTMC Permit Number -", "additional-info": "Additional info", "rent-amount-per-month": "Rent amount per month", "operator-poc-name": "Operator POC name", "operator-poc-phone": "Operator POC phone", "no-of-floor-occupied": "No. of floor occupied", "contact-seller": "contact seller", "unit-range": "Unit Range", "floor-details": "Floor Details", "520-mins": "5:20 Mins", "project-image": "project image", "i-agree-with-the": "I agree with the", "ex-deepak-yadav": "ex. <PERSON><PERSON>", "ex-xyzgmailcom": "ex. <EMAIL>", "project-status": "Project Status", "about-project": "About Project", "project-urls": "Project URL(s)", "brochure": "Brochure", "download": "ഡൗൺലോഡ്", "enquiry-form": "Enquiry Form", "basic-info": "Basic Info", "more-information": "More Information", "customer-locality": "Customer Locality", "customer-subcommunity": "Customer Sub-Community", "customer-community": "Customer Community", "customer-tower-name": "Customer Tower Name", "customer-city": "Customer City", "customer-state": "Customer State", "customer-country": "Customer Country", "purpose": "Purpose", "agency-name": "Agency Name", "enquired-locality": "Enquired Locality", "enquired-subcommunity": "Enquired Sub-Community", "enquired-community": "Enquired Community", "enquired-tower-name": "Enquired Tower Name", "enquired-country": "Enquired Country", "nationality": "Nationality", "phone": "ഫോൺ", "submit": "സമർപ്പിക്കുക", "qr-code-is-inactive-please-con": "QR code is inactive. Please connect with admin.", "enter-referral-name": "enter referral name", "enter-referral-email": "enter referral email", "enter-locality": "enter locality", "enter-subcommunity": "enter sub-community", "enter-community": "enter community", "enter-tower-name": "enter tower name", "enter-city": "enter city", "enter-state": "enter state", "enter-country": "enter country", "ex-1901": "ex. 1901", "ex-506": "ex. 506", "ex-196": "ex. 196", "ex-916": "ex. 916", "enter-unit-numbername": "enter unit number/name", "enter-cluster-name": "enter cluster name", "select-offering-type": "Select Offering Type", "select-br": "select BR", "select-baths": "select baths", "select-beds": "select beds", "select-preferred-floor": "select preferred floor", "select-furnish-status": "select furnish status", "ex-abc": "ex. abc", "selectcreate-project": "select/create project", "enter-project": "enter project", "select-agency-name": "Select agency name", "enter-agency-name": "enter agency name", "select-campaign-name": "Select campaign name", "enter-campaign-name": "enter campaign name", "select-channel-partner-name": "Select channel partner name", "enter-channel-partner-name": "enter channel partner name", "enter-enquired-city": "enter enquired city", "select-profession": "select profession", "enter-company-name": "enter company name", "enter-designation": "enter designation", "select-nationality": "select nationality", "enter-executive-name": "enter executive name", "ex-i-want-to-say-": "ex. I want to say ....", "social-media": "social media", "awesome": "Awesome!", "thank-you-for-your-enquiry-our": "Thank you for your enquiry. Our team will get back to you shortly.", "app-image": "App Image", "chatbots": "chat-bots", "authenticate-your": "Authenticate Your", "to-your-admin-manager": "to your admin / manager", "having-trouble-loging-in": "Having trouble loging in?", "try-another-way": "Try Another Way", "trying-different-login-method": "Trying Different Login Method...", "otp": "otp", "laptop": "laptop", "enter-your-username": "Enter your username", "enter-your-password": "Enter your password", "aa": "AA", "show-parent-lead": "Show Parent Lead", "filter": "ഫിൽട്ടർ", "save-filter": "Save Filter", "edit-filter": "Edit Filter", "update-filter": "Update Filter", "the-associated-parent-lead-has": "The associated parent lead has been deleted!", "select-visibility": "Select Visibility", "icon": "icon", "refresh-data": "Refresh Data", "flag-image": "Flag Image", "no-lead-found": "No lead found", "enter-filter-name": "enter filter name", "whatsapp-chat": "<PERSON><PERSON><PERSON><PERSON>", "send-to": "Send To", "choose-whatsapp-template": "<PERSON><PERSON> WhatsApp Template", "content": "Content", "select-template-to-start-conve": "select template to start conversation....", "may-09-2025": "May 09, 2025", "no-conversation": "no conversation", "24-hours-window-surpassed": "24 Hours window surpassed", "the-customer-has-not-spoken-to": "The customer has not spoken to you in the last 24 Hours.", "you-can-only-send-preapproved-": "You can only send pre-approved templates to start conversation again.", "attachments": "Attachments", "select-a-template": "select a template", "no-template-found": "No Template Found", "back-to-templates": "back to templates", "edit-message": "Edit Message", "send-as-message": "Send as message", "choose-template": "<PERSON><PERSON>", "back": "Back", "clear": "Clear", "lead-details": "Lead Details", "media": "media", "photo": "Photo", "caption-optional": "Caption (optional)", "type-here-to-start-conversatio": "type here to start conversation...", "select-project": "Select Project", "select-property": "Select Property", "ex-dear-vikram-as-per-our-tele": "ex. Dear <PERSON><PERSON><PERSON>,As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.Thankyo<PERSON>, <PERSON><PERSON><PERSON>", "add-another-location": "Add another location", "booking-under-name": "Booking under name", "booked-date": "Booked Date", "agreement-value": "Agreement Value", "choose-property-project": "Choose Property/ Project", "choose-property": "Choose Property", "choose-project": "Choose Project", "choose-unit": "Choose Unit", "primary": "primary", "secondary": "secondary", "save-fill-booking-form": "Save & fill booking form", "there-is-no-unit-information-p": "There is no unit information provided for this project. Please check with the admin.", "bulk-update-status": "Bulk Update Status", "muso-walking": "<PERSON><PERSON>", "ex-400000": "ex. 400000", "ex-19062025-1200-pm": "ex. 19/06/2025, 12:00 pm", "ex-5032025-1200-pm": "ex. 5/03/2025, 12:00 pm", "ex-abc-property": "ex. ABC Property", "ex-xyz-project": "ex. XYZ Project", "sales-executive-name": "Sales Executive Name:", "sales-executive-number": "Sales Executive Number:", "uploaded-documents": "Uploaded Documents:", "select-mail-templates": "Select Mail Templates", "cc": "CC", "bcc": "BCC", "subject": "Subject:", "view": "കാണുക", "send-email": "Send Email", "upload-files": "Upload Files", "ex-mounikagmailcom": "ex. <EMAIL>", "type-here": "type here....", "ex-dear-nichola-ferrell-as-per": "ex. Dear <PERSON><PERSON><PERSON>, As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.Thankyo<PERSON>, <PERSON><PERSON><PERSON>", "jpgpngpdf": "JPG,PNG,PDF", "copy-document-link": "Copy Document Link", "view-document": "View Document", "delete-document": "Delete Document", "enter-lead-document-name": "Enter lead document name...", "bulk-whatsapp": "Bulk WhatsApp", "this-will-create-a-duplicate-o": "This will create a duplicate of the current lead(s)", "select-source": "Select Source", "select-subsource": "Select Sub-Source", "assign": "Assign:", "secondary-owner": "Secondary owner", "secondary-assigned-from": "Secondary Assigned From", "assignment-done-by": "Assignment Done By", "status-source": "Status & Source:", "project-property": "Project & Property:", "-deleted-": "( Deleted )", "preferred-floor": "Preferred Floor", "tower-name": "Tower Name", "postal-code": "Postal Code", "latitude": "Latitude", "longitude": "Longitude", "radius": "<PERSON><PERSON>", "in-kilometer": "(in kilometer)", "budget-area": "Budget & Area:", "referral": "Referral:", "booking-details": "Booking Details:", "book-under-name": "Book under name", "car-parking-charges": "Car Parking Charges", "addon-charges": "Add-on Charges", "total-sold-price": "Total Sold Price", "discount": "Discount", "balance-amount": "Balance Amount", "type-of-payment-selected": "Type of Payment Selected", "brokerage-charges": "Brokerage Charges", "net-brokerage-amount": "Net Brokerage Amount", "gst": "GST", "total-brokerage": "Total Brokerage", "brokerage-earned": "Brokerage Earned", "booked-by": "Booked By", "-disabled": "( Disabled)", "others": "Others:", "channel-partner-name": "Channel Partner Name", "excel-sheet": "Excel Sheet", "is-untouched": "Is Untouched", "data-converted": "Data Converted", "qualified-by": "Qualified By", "facebook-properties": "Facebook Properties", "confidential-notes": "Confidential Notes", "show-child-count": "Show Child Count", "show-unique-lead": "Show Unique Lead", "ex-129209914": "ex. 12.9209914", "ex-776428506": "ex. 77.6428506", "ex-5": "ex. 5", "enter-referral-phone-no": "enter referral phone no", "ex-abid": "ex. abid", "enter-notes": "enter notes", "enter-count": "enter count", "select-virtual-number": "Select virtual Number", "choose-which-type-you-want-to-": "choose which type you want to call?", "dialer-pad": "<PERSON><PERSON><PERSON>", "select-your-whatsapp-type": "Select your whatsapp type", "integrated": "Integrated", "ok-got-it": "Ok, Got it", "lead-history": "Lead History", "lead-document": "Lead Document", "navigate-to-link": "Navigate to Link", "call-recording": "Call Recording", "no-notes-found": "No Notes Found", "dc": "DC", "tags": "Tags", "data-converted-this-lead-was-c": "Data Converted: This lead was converted from a data.", "test-contact-no": "Test Contact No", "with-comma-separated": "(with comma separated)", "file-name": "File Name", "did-you-receive-message": "Did you Receive Message?", "ex-9133xxxxxx": "ex. 9133XXXXXX", "ex-text": "ex. Text", "ex-name-time-company-name": "ex. name, time, company name", "no-projetcs": "No Projetcs", "add-project-name": "add project name", "done": "Done", "not-done": "Not Done", "update-later": "Update later", "change-property": "change property", "go-to-current-location": "go to current location", "add-location-manually": "add location manually", "location-permission-turned-off": "Location Permission Turned Off", "add-documents": "Add documents", "save-close": "Save & Close", "ex-title": "ex. Title", "primary-is-a-required-field": "primary is a required field", "this-leads-has-already-been-as": "This lead(s) has already been assigned to this user.", "customer-pincode": "Customer <PERSON><PERSON>", "enquired-pincode": "Enquired <PERSON><PERSON><PERSON>", "ex-manasapgmailcom": "ex. <EMAIL>", "selectcreate": "Select/Create", "ex-karthik": "ex. <PERSON><PERSON><PERSON>", "search-location": "Search Location", "enter-pincode": "enter pincode", "ex-a1b2c": "ex. A1B2C", "select-purpose": "Select purpose", "select-property-type": "select property type", "select-property-subtype": "select property sub-type", "select-bhk": "select BHK", "select-bhk-type": "select BHK type", "selectcreate-agency-name": "Select/Create Agency Name", "ex-software-developer": "ex. Software Developer", "search-nationality": "Search nationality", "select-sourcing-manager": "Select Sourcing Manager", "select-closing-manager": "Select Closing Manager", "selectcreate-property": "Select/Create Property", "no-fields-selected-yet": "NO FIELDS SELECTED YET.....", "select-campaign": "Select Campaign:", "select-campaigncreate-campaign": "Select Campaign/Create Campaign", "ex-pampanagmailcom": "ex. <EMAIL>", "builder-name": "Builder name", "booking-date": "Booking Date", "team-head": "Team Head", "upload-documents": "Upload Documents", "upload-image": "Upload Image", "document-name-is-required-fiel": "document name is required field.", "filename-already-exists": "filename already exists.", "booking-done-by": "Booking done by", "token-amount-paid": "<PERSON><PERSON> Amount <PERSON>", "referral-commission": "Referral Commission", "are-you-sure-you-want-to-save": "Are you sure you want to save?", "add-document-file-name-here": "add document file name here...", "ex-arif": "ex. <PERSON><PERSON>", "ex-40000": "ex. 40000", "percentage": "percentage", "cashback": "cashback", "agg-value": "Agg. value", "major-outage": "major outage", "you-may-face-issues-with-leads": "you may face issues with leads not dropping into your CRM", "immediate-action-required": "Immediate Action Required:", "x2022": "&#x2022;", "see-less": "see less...", "off": "Off", "3rd-parties": "3rd Parties", "social-platforms": "Social Platforms", "close-alert": "close alert", "two-factor-authentication": "Two Factor Authentication", "enable-two-factor-authenticati": "Enable two factor authentication to protect your account.", "otp-configuration": "OTP Configuration", "otp-receiver": "OTP Receiver", "select-admins": "Select Admin(s)", "selected": "Selected", "time-frame-to-get": "Time frame to get", "to-the-user": "to the user", "apply-two-factor-authenticatio": "Apply two factor authentication to", "copypaste": "Copy/Paste", "enable-copy-paste-functionalit": "Enable Copy - Paste Functionality.", "disable-screenshot-functionali": "Disable Screenshot Functionality", "automation-settings": "Automation Settings", "add-automation": "Add Automation", "automated-reports": "Automated Reports", "configure-automation-to-get-yo": "Configure automation to get your reports automatically", "select-report-": "Select Report :", "report-tracker": "Report - Tracker", "report-name": "Report name", "select-report": "Select Report", "select-sub-report": "Select Sub Report", "select-report-frequency": "Select Report Frequency", "apply": "Apply", "receive-reports": "Receive Reports", "select-day": "Select Day", "select-time": "Select Time", "mode": "Mode", "save-details": "Save details", "ex-open-deals-report": "ex. Open Deals Report", "select-frequency": "Select frequency", "enter-days": "Enter Days", "ex-1000-am": "ex. 10:00 AM", "add-qr-template": "Add QR Template", "template-name": "Template Name", "created-by-date": "Created By & Date", "modified-by-date": "Modified By & Date", "no-templates-added-yet": "no templates added yet...", "no-templates-deleted-yet": "no templates deleted yet...", "add-new-template": "Add New Template", "selected-templates": "Selected Template(s)", "type-template-name": "type template name", "config": "Config", "qr-preview": "QR Preview", "preview": "പ്രിവ്യൂ", "form-templates": "Form Templates", "contents": "Contents", "header": "Header", "footer": "Footer", "form-fields": "Form Fields", "customize-your-form-fields-to-": "Customize your form fields to your requirement.", "header-template": "Head<PERSON>", "add-header-section": "Add Header Section", "remove-header-section": "Remove Header Section", "selected-template": "Selected Template", "change-template": "Change Template", "edit-header-details": "Edit Head<PERSON>", "company-logo": "Company Logo", "background-color": "Background Color", "text-colour": "Text Colour", "add-company-name": "Add Company Name", "footer-template": "<PERSON>er <PERSON>", "add-footer-section": "Add Footer Section", "remove-footer-section": "Remove Footer Section", "edit-footer-details": "<PERSON> <PERSON><PERSON>", "add-company-address": "Add Company Address", "add-phone-no": "Add Phone No", "add-email": "Add <PERSON>", "social-profiles": "Social Profiles", "template-name-already-exists": "Template Name already Exists", "edit-section": "Edit Section", "ex-easy-gharoffice-solutions-p": "ex. easy gharoffice solutions private limited", "untitled-form": "Untitled Form", "ex-7569xxxxx": "ex. 7569xxxxx", "ex-manasa99gmailcom": "ex. <EMAIL>", "ex-7036xxxxx": "ex. 7036xxxxx", "describe-your-requirement": "Describe your requirement", "configure": "Configure", "assign-user": "Assign User", "assign-project": "Assign Project", "project": "Project", "assign-agency": "Assign Agency", "track-campaign": "Track campaign", "channel-partner": "Channel Partner", "assign-source": "Assign Source", "assign-subsource": "Assign Sub-Source", "assign-tags": "Assign tags", "ex-abc-project": "ex. ABC project", "select-channel-partner": "Select channel partner", "select-tags": "Select tags", "amenities-and-attributes": "Amenities and Attributes", "you-can-add-or-modify-amenitie": "you can add or modify amenities and attributes", "click-to-manage": "<PERSON>lick to manage", "update-and-personalize-microsi": "update and personalize microsites", "add-new-agency": "Add New Agency", "add-new-campaign": "Add New Campaign", "add-new-channel-partner": "Add New Channel Partner", "no-channel-partner-found": "No Channel Partner Found!", "selected-channel-partners": "Selected Channel Partners", "ex-0-50": "ex. 0 - 50", "email-id": "Email ID", "rera-number": "RERA Number", "company-name": "Company Name", "ex-zee": "<PERSON><PERSON> <PERSON>", "ex-zee324gmailcom": "ex. <EMAIL>", "enter-rera-number": "Enter RERA Number", "selected-campaigns": "Selected Campaign(s)", "no-campaign-found": "No Campaign Found!", "no-agency-found": "No Agency Found!", "selected-agencies": "Selected Agencies", "turn-on-off-the-google-map-api": "Turn ON / OFF the Google Map API Service", "this-will-be-used-in-leads-pro": "this will be used in leads, properties and projects locations options.", "assign-to-users": "Assign to user(s)", "no-zone-found": "No Zone Found!", "ex-central": "ex. Central", "no-state-found": "No State Found!", "ex-maharashtra": "ex. Maharashtra", "no-locality-found": "No Locality Found!", "search-for-locationcreate-loca": "Search for Location/Create Location", "ex-560002": "ex. 560002", "no-country-found": "No Country Found!", "no-city-found": "No City Found!", "user-assignment": "User Assignment", "importing-localities-becomes-m": "Importing localities becomes more easier", "upload-your-localities": "Upload your localities", "download-template": "Download template", "listing-configuration": "Listing configuration", "listing-management": "Listing management", "add-list-and-delist-your-prope": "Add, list and delist your property", "configuration": "Configuration", "add-acount": "Add Acount", "account-name": "Account Name", "url": "URL", "enter-secret-key": "Enter Secret Key", "enter-api-key": "Enter API Key", "added-accounts": "Added Accounts", "eg-shanu": "e.g., <PERSON><PERSON>", "eg-propertyfindercom": "e.g., propertyfinder.com", "copy": "Copy", "dubai-lead-form": "Dubai Lead Form", "whatsapp-deep-integration": "Whatsapp Deep Integration", "you-can-enable-or-disable-the-": "you can enable or disable the call detection for your tenant", "mandatory-notes": "Mandatory Notes", "mandatory-projects": "Mandatory Projects", "mandatory-property": "Mandatory Property", "retention-configurations": "Retention configurations", "add": "ചേർക്കുക", "lead-rotation": "Lead rotation", "substatus": "Sub-Status", "substatus-": "Sub-Status :", "shift-timing": "Shift timing", "lead-rotation-time": "Lead rotation time", "number-of-rotation": "Number of rotation", "days": "Days", "group": "ഗ്രൂപ്പ്", "add-group": "Add Group", "team-leader-": "Team leader :", "manage-substatus-leads": "Manage Sub-Status Leads", "add-edit-and-delete-lead-subst": "add, edit and delete lead sub-status", "integration-leads-will-rotate-": "Integration Leads will rotate inside a Created group or a Team within a Configured time.", "ex-busy": "ex. Busy", "select-option": "Select option", "rotation": "Rotation", "enable-lead-rotation": "Enable Lead Rotation", "team-name": "Team Name", "select-team-leader": "Select Team Leader", "shift-time": "Shift time", "minutes": "Minutes", "everyday": "Everyday", "abc-team": "ABC Team", "select-status": "Select Status", "select-substatus": "Select Sub-Status", "ex-enter": "ex. enter...", "enquiry-info": "Enquiry Info", "marital-status": "Marital status", "nri": "NRI", "mark-as-mandatory": "Mark as mandatory", "marital-info": "Marital Info", "ex-samyuktha": "ex. <PERSON><PERSON><PERSON><PERSON>", "ex-select-gender": "ex. Select Gender", "ex-add-fathers-name": "ex. Add Father's name", "ex-add-mothers-name": "ex. Add Mother's name", "ex-select-religion": "ex. Select Religion", "ex-25": "ex. 25", "ex-122454255566": "ex. 122454255566", "ex-12wq566": "ex. 12wq566", "select-industry": "Select Industry", "select-annual-income": "Select Annual Income", "ex-non-veg": "ex. Non Veg", "ex-select": "ex. Select", "ex-2": "ex. 2", "ex-select-current-address-type": "ex. Select Current Address Type", "ex-select-spouse-profession": "ex. Select Spouse Profession", "ex-select-spouse-annual-income": "ex. Select Spouse Annual Income", "select-office-locality": "Select Office Locality", "ex-select-passport-type": "ex. Select Passport Type", "ex-select-visa-status": "ex. Select Visa Status", "call-notification": "Call Notification", "which-lead-calls-would-you-lik": "which lead calls would you like to track?", "assigned": "Assigned", "set-timezone-automatically": "Set Time-Zone Automatically", "you-can-enable-or-disable-auto": "You can enable or disable automatic time-zone", "select-the-default-time-zone": "Select the default time Zone", "default-unit": "Default Unit", "you-can-select-the-default-uni": "you can select the default unit", "default-currency": "De<PERSON>ult <PERSON>:", "add-more-currencies": "Add more currencies", "country-code": "Country Code", "you-can-select-the-country-cod": "you can select the country code", "watermark-settings": "Watermark Settings", "watermark-preview": "Watermark Preview", "set-watermark-image": "Set Watermark Image", "replace": "Replace", "set-watermark-positioning": "Set Watermark Positioning", "placement-of-your-watermark-ov": "placement of your watermark over the image", "image-opacity": "Image Opacity", "image-size": "Image Size", "save-settings": "Save Settings", "ex-sqfeet": "ex. sq.feet.", "manage-forms": "Manage Forms", "customise-your-crm-forms": "Customise your CRM Forms", "you-can-customise-forms-of-the": "You can customise forms of the CRM based on your requirement", "manage-lead-form": "Manage Lead Form", "you-can-customise-lead-form-ba": "You can customise lead form based on your requirement", "data-migration": "Data Migration", "lead-migration": "Lead Migration", "add-new-tag": "Add New Tag", "no-tags-found": "No Tags Found", "tag": "tag?", "are-you-sure-you-want-to-delet": "Are you sure you want to delete the", "save-tag": "Save Tag", "ex-warm": "ex. Warm", "ex-a-warm-tag-represents-a-lea": "ex. A Warm tag represents a lead that has.......", "week-off": "Week off", "ex-building": "ex. Building", "basic": "Basic", "residential": "Residential", "unit": "Unit", "engageto": "EngageTo", "body": "Body", "ex-property-details-template": "ex. Property Details Template", "ex-hey": "ex. Hey,", "search-for-variables": "search for variables", "ex-we-selected-property-that-m": "ex. We selected property that might be matching to your interest. Check it out now.", "ex-thanks-mounika": "ex. Thanks, <PERSON><PERSON><PERSON>.", "ex-project-unit-details-templa": "ex. Project Unit Details Template", "ex-project-details-template": "ex. Project Details Template", "ex-lead-details-template": "ex. Lead Details Template", "integrate-your-sms-service-pro": "integrate your SMS service provider", "enter-api-key-": "enter API key &", "other-details": "other details", "who-is-your-sms-gateway-provid": "Who is your SMS Gateway provider?", "textlocal": "Textlocal", "daksh-infosoft": "Daksh Infosoft", "sms-service-configuration": "SMS service configuration", "textlocal-api-key": "Textlocal API Key", "you-can-get-this-api-key-from": "You can get this API key from", "your-sms-gateway-provider": "your SMS Gateway provider", "sender-id": "Sender ID", "add-your-verified-sender-ids": "Add your verified Sender ID(s)", "1-sender-name": "1. Sender Name", "self": "self", "sms-templates": "SMS templates", "template-message": "Template Message", "finish": "Finish", "enter-the-api-key-here": "enter the API key here...", "sms-gateways": "SMS GateWays", "gateway-name": "GateWay Name", "api-key": "API Key", "added-date": "Added Date", "add-template": "Add Template", "dlt-template-id": "DLT Template ID", "select-sender-id": "Select Sender ID", "message-body": "Message Body", "tips": "Tips:", "verify-the-sms-template-to-add": "Verify the SMS template to add into the system", "template": "template", "dear": "Dear", "manasa": "manasa,", "at": "at", "ravi-j": "<PERSON>", "thanks": "Thanks", "to-number": "To number", "send-this-sample-test-message-": "Send this sample test message to", "send-test-sms": "Send test SMS", "sms-has-been-initiated-sent-to": "SMS has been initiated & sent to", "please-check-confirm-if-you-ha": "Please check & confirm if you have received the SMS or not.", "yes-received-the-sms": "Yes, received the SMS", "no-resend-the-sms": "No, resend the SMS", "yohooo-your-template-has-been-": "Yohooo! Your template has been verified and added to the system.", "ivr-settings": "IVR Settings:", "direction-of-data-flow": "Direction Of Data Flow:", "ivr-only": "IVR Only", "dialer-only": "Dialer Only", "service-provider": "Service Provider", "assignment": "Assignment", "add-additional-number": "add additional number", "this-setting-refers-to-the-dat": "This setting refers to the Data flow of IVR call \r\nwhere CRM Should create new record of the data which is not in the CRM. \r\n<PERSON>ose 'Lead' if data should be created in Leads Module and 'Data' for Data Management. \r\nChoose 'Both' if need a copy of the data in both Lead and Data.", "assignments": "Assignments", "ex-abc-location": "ex. ABC location", "payload-method-type": "Payload Method Type", "payload-content-type": "Payload Content Type", "payload-mapping": "Payload Mapping", "add-additional-payload": "add additional payload", "method-type": "Method Type", "content-type": "Content Type", "base-url": "Base URL", "resources": "Resources", "add-query-parameters": "add query parameters", "add-header-variables": "add header variables", "add-body-variables": "add body variables", "ex-servetel": "ex. <PERSON><PERSON><PERSON>", "ex-get": "ex. GET", "ex-applicationjson": "ex. application/json", "key": "key", "value": "value", "ex-httpswwwexamplecom": "ex. https://www.example.com", "query-key": "Query key", "query-value": "Query value", "header-key": "Header Key", "header-value": "Header Value", "body-key": "Body Key", "body-value": "Body Value", "email-smtp-servers": "Email SMTP Servers", "add-smtp-server": "Add SMTP Server", "sender-email": "Sender <PERSON><PERSON>", "smtp-server": "SMTP Server", "port": "Port", "username": "ഉപയോക്തൃനാമം", "enter-your-smtp-configuration": "Enter Your SMTP Configuration", "test-configuration": "Test Configuration", "assigned-users": "Assigned Users", "assign-users": "Assign Users", "ex-smtpgmailcom": "ex. <EMAIL>", "ex-8080": "ex. 8080", "the-app-password-is-provided-b": "The app password is provided by the email service provider and is not email password", "password": "പാസ്വേഡ്", "connect-now": "Connect Now", "api-email-integration": "API Email Integration", "login-idlogin-email": "Login Id/Login Email", "relationship-manager-email": "Relationship Manager <PERSON><PERSON>", "additional-email": "Additional Email", "bcc-email": "Bcc Email", "webhook-link": "Webhook Link", "forms": "Forms", "personal-whatsapp": "Personal Whatsapp", "integration-whatsapp": "Integration WhatsApp", "integration-status": "Integration Status", "relationship-manager-emails": "Relationship Manager Email(s)", "additional-emails": "Additional Email(s)", "bcc-emails": "Bcc Email(s)", "no-account-found": "No Account Found", "google": "google", "ex-abcgmailcom": "ex. <EMAIL>", "project-location": "Project & Location", "lead-assignment": "Lead Assignment", "additional-assignments": "Additional Assignments", "assign-sequentially": "Assign sequentially", "assignment-base": "Assignment Base", "sequentialnbsp": "Sequential&nbsp;", "based": "Based", "percentagenbsp": "Percentage&nbsp;", "assignment-type": "Assignment Type", "select-team": "Select Team", "must-equal-100": "(Must equal 100%)", "bulk-fetch-tracker": "Bulk Fetch Tracker", "custom": "Custom", "facebook": "Facebook", "bulk-project": "Bulk Project", "bulk-location": "Bulk Location", "bulk-country-code": "Bulk Country Code", "bulk-reassign": "Bulk Reassign", "bulk-agency": "Bulk Agency", "pixel-integration": "Pixel Integration", "selected-status": "Selected Status", "sync": "Sync", "pixel": "Pixel", "ex-************": "ex. ************", "choose-status": "Choose <PERSON>", "subscribe": "Subscribe", "unsubscribe": "Unsubscribe", "ex-19062025-1149-am-29062025-0": "ex. 19-06-2025, 11:49 am - 29-06-2025, 03:49 pm", "share-it-with-your": "Share it with your", "user-registration-details-for-": "User Registration Details For Pull:", "user-id": "User ID:", "user-name": "User Name:", "push": "PUSH", "secret-id": "Secret Id", "secret-key": "Secret Key", "request-type": "Request Type", "api-type": "API Type", "no-acount-found": "No Acount Found", "select-request-type": "Select Request Type", "copyurl": "CopyUrl", "engage": "Engage", "clients-smarter": "Clients <PERSON>", "bring-the-power-of-next-gen-wh": "Bring the power of next gen <PERSON>s<PERSON><PERSON>", "join-engageto-now": "Join <PERSON> now", "select-filter": "Select Filter", "source": "source", "enquiry": "Enquiry:", "previous-contact": "Previous Contact", "next-contact": "Next Contact", "convert-to-lead": "Convert to Lead", "about-this-contact": "About this Contact", "created-on": "created on", "modified-on": "modified on", "sub-source": "sub source:", "enquired-for": "enquired for:", "properties": "properties:", "min-budget": "min. budget", "max-budget": "max. budget", "profession": "profession", "sourcing-manager": "sourcing manager", "closing-manager": "closing manager", "location-info": "Location Info", "current-status-": "Current Status :", "scheduled-date-time-": "Scheduled date & time :", "last-modified-by-": "Last modified by :", "last-modified-on-": "Last modified on :", "update-status-to": "Update status to", "schedule-date": "Schedule date", "call": "Call", "sms": "sms", "ddmmyyyy": "DD/MM/YYYY", "remove-schedule-date-and-time": "remove schedule date and time", "0000-am": "00:00 AM", "remove-schedule-time": "remove schedule time", "add-a-note-": "Add a note ....", "ex-type-here-to-add-note-": "ex. Type here to add note .........", "contact-info": "Contact Info", "assign-to-": "Assign To :", "email-address": "Email Address:", "enquired-info": "Enquired Info", "property-sub-type": "Property Sub Type:", "bhk": "BHK:", "bhk-type": "BHK Type:", "convert-and-assign": "Convert And Assign", "assign-and-convert": "Assign And Convert", "data-cannot-be-converted-into-": "Data cannot be converted into lead as it is being assigned to the same user.", "share-property": "share property", "update-status": "Update Status", "selected-data": "Selected data", "convert-to-leads-": "Convert to Leads :", "proceed-": "Proceed :", "select-sub-source": "Select Sub Source", "data-history": "Data History", "ex-abid-ansari": "ex. abid ansari", "ex-manasapamapana99gmailcom": "ex. <EMAIL>", "filters": "Filters", "with-team": "With Team", "users": "ഉപയോക്താക്കൾ", "clear-all": "Clear All", "leads-from-source": "Leads From Source", "no-data-available": "No Data Available", "reset-date": "reset date", "reset-all": "reset all", "no-records-found": "No records found", "whatsapp-report": "WhatsApp Report", "type-agent-name": "type agent name", "leads-report": "Leads Report", "data-report": "Data Report", "calls-report": "Calls Report", "total-leads": "Total Leads", "active": "സജീവം", "unassigned": "Unassigned", "deleted": "ഇല്ലാതാക്കിയത്", "booked": "Booked", "booking-cancel": "Booking cancel", "dropped": "Dropped", "pending": "Pending", "callbacks": "Callbacks", "meetings-scheduled": "Meetings Scheduled", "meeting-done": "Meeting done", "leads-pipeline": "Leads pipeline", "lead-received": "Lead Received", "calls": "Calls", "dialed": "dialed", "incoming": "incoming", "connected": "കണക്റ്റ് ചെയ്തത്", "not-connected": "not connected", "missed": "missed", "activity-on-leads": "Activity On Leads", "meetings": "Meetings", "scheduled-meetings": "Scheduled Meetings", "meeting-not-done": "meeting not done", "meeting-overdue": "meeting overdue", "upcoming-meetings": "Upcoming Meetings", "site-visits": "Site Visits", "scheduled-site-visits": "Scheduled Site Visits", "site-visit-done": "site visit done", "site-visit-not-done": "site visit not done", "upcoming-site-visits": "Upcoming Site Visits", "expression-of-interest": "Expression Of Interest", "team-performanceoverview": "Team performance/overview", "assigned-leads": "Assigned Leads", "top-performanceoverview": "Top performance/overview", "booked-leads": "Booked Leads", "total-incentive-earned": "Total Incentive Earned", "scroll-to-bottom": "<PERSON><PERSON> To <PERSON>", "this-graph-will-give-you-the-s": "This Graph will give you the segregated count of Leads in the System Based on source you can also filter it out based on date.", "this-graph-will-give-you-a-seg": "This Graph will give you a segregated count of Active leads which are in play categorized as new for new leads, In engagement for callbacks and meetings and site visit scheduled, site visit done, booked are represented as name. you can also filter this out based on source and date.", "this-graph-compares-the-leads-": "This Graph Compares the leads received by integration based on source and user can Filter based on the date.", "this-graph-will-give-you-the-c": "This Graph will give you the comparison of activity of calls done by the users.", "this-graph-will-represent-the-": "This graph will represent the Communicational activity done on the leads. can be filtered based on source and lead dates.", "this-graph-represents-the-meet": "this graph represents the Meeting which are completed, not completed and scheduled. User can filter the date based on dates. Unique Leads count represents the distinct leads of the Meeting.", "this-graph-represents-the-site": "this graph represents the site visits which are completed and not completed and scheduled and user can filter the date based on dates. Unique Leads count represents the distinct leads of the site visits.", "this-grid-will-give-you-a-brie": "This grid will give you a brief understanding about your users and their leads current status, can be filtered based on source and dates of leads.", "scroll-to-top": "<PERSON><PERSON> To Top", "no-entries": "no entries"}, "LABEL": {"property": "സ്വത്ത്", "type": "Type", "sub-type": "Sub-Type", "budget": "Budget", "active": "സജീവം", "inactive": "നിഷ്ക്രിയം", "sold": "Sold"}, "BUTTONS": {"add-lead": "Add Lead", "edit-lead": "Edit Lead", "add-data": "Add Data", "add-property": "Add Property", "edit-property": "Edit Property", "add-testimonial": "Add Testimonial", "next": "അടുത്തത്", "back": "Back", "show-hide-columns": "Show / Hide Columns", "manage-columns": "Manage Columns", "submit": "സമർപ്പിക്കുക", "save": "സേവ്", "cancel": "റദ്ദാക്കുക", "close": "അടയ്ക്കുക", "clear": "Clear", "create": "സൃഷ്ടിക്കുക", "add-more": "Add more info", "apply-changes": "Apply Changes", "apply": "Apply", "manage-accounts": "Manage Accounts", "delete": "ഇല്ലാതാക്കുക", "delete-account": "Delete Account", "action": "Action", "update": "അപ്ഡേറ്റ്", "connect": "Connect", "discard": "Discard", "save-changes": "Save Changes", "skip": "<PERSON><PERSON>", "assign": "Assign", "assign-all-ads": "Assign All Ads", "restore": "Rest<PERSON>", "confirm": "സ്ഥിരീകരിക്കുക", "fetch": "<PERSON>tch", "remove": "നീക്കം ചെയ്യുക", "reset-to-default-values": "Reset to default values", "proceed": "Proceed", "replace": "Replace", "add-new-data": "Add New Data", "edit-data": "Edit Data", "save-and-close": "Save and Close", "save-and-next": "Save and Next", "save-and-update-status": "Save and Update Status", "update-lead-status": "Update lead status"}, "LOCATION": {"locate": "Locate me", "country": "രാജ്യം", "state": "സംസ്ഥാനം", "city": "നഗരം", "locality": "Locality", "location": "Location", "propertyType": "Enquired Property Type", "zone": "Zone", "pinCode": "Pin Code"}, "SHARE": {"share": "Share", "sms": "SMS", "email": "ഇമെയിൽ", "whatsapp": "WhatsApp"}, "SIDEBAR": {"dashboard": "ഡാഷ്ബോർഡ്", "add": "ചേർക്കുക", "tasks": "Tasks", "lead": "ലീഡ്", "leads": "ലീഡുകൾ", "my-leads": "My Leads", "my-properties": "My Properties", "reports": "Reports", "project": "Project", "manage-projects": "Manage Projects", "manage-properties": "സ്വത്തുകൾ കൈകാര്യം ചെയ്യുക", "manage-leads": "Manage Leads", "manage-data": "Manage Data", "marketing": "Marketing", "refer-and-earn": "Refer & Earn", "subscription": "Subscription", "support": "പിന്തുണ", "profile": "പ്രൊഫൈൽ", "account": "അക്കൗണ്ട്", "accounts": "Accounts", "manage": "Manage", "properties": "സ്വത്തുകൾ", "communication": "Communication", "attendance": "Attendance", "holidays": "Holidays", "app": "App", "others": "Others", "my-team": "My Team", "team": "ടീം", "manage-team": "Manage Team", "manage-users": "Manage Users", "user-roles": "User Roles", "organizational-chart": "Organizational Chart", "integration": "Integration", "home": "വീട്", "contacts": "Contacts", "wallet": "Wallet", "work-flow": "Workflow", "login-history": "Login History", "global-config": "Global Config", "module-settings": "<PERSON><PERSON><PERSON>", "user-details": "User Details", "general": "General", "settings": "ക്രമീകരണങ്ങൾ", "import-leads": "Import Leads", "import-properties": "Import Properties", "import-data": "Import Data", "data-management": "Data", "data-preview": "Data Preview", "form-customization": "Form customization", "module-attendance": "Attendance", "lead-customization": "Lead Customization"}, "AUTH": {"mobile-email": "Mobile Number or Email id", "glad-here": "Glad you are here!", "create-account": "Create your account for FREE, today!", "personal-details": "Please provide your personal details!", "verify": "Verify", "confirm-otp": "Confirm OTP", "account-exist": "Already have an account?", "login-now": "Login Now", "login": "ലോഗിൻ", "company-name": "Company Name", "company-address": "Company Address", "full-name": "Full Name", "first-name": "First Name", "last-name": "Last Name", "mobile": "Mobile Number", "agree-to": "I agree to", "agree-label": "Agree", "terms": "Terms & Conditions", "subscribe-text": "Subscribe for Newsletter", "read-agree": "I have read and agree to the", "cookie": "Cookie Preferences", "term": "നിബന്ധനകൾ", "privacy-policy": "Privacy Policy", "mobile/email": "Mobile/Email", "enter-otp": "Enter OTP", "request-otp": "Request OTP", "user-name": "ഉപയോക്തൃനാമം", "password": "പാസ്വേഡ്", "confirm-password": "Confirm Password", "forgot-password": "പാസ്വേഡ് മറന്നോ", "welcome": "Welcome", "login-to-account": "Please login to your account.", "terms-condition": "By login in, you are indicating that you have read and agree to the", "and": "and", "seconds-left": "Seconds left", "not-received": "Not received OTP?", "resend": "Resend", "link": "<PERSON> Account", "configure": "Configure Now", "multiple-account": "Multiple accounts found. Please enter as a,", "lets-go": "Let's Go", "log-out": "Log Out", "business-nature": "Please let us know your business nature", "broker": "Broker", "builder": "Builder", "signUp-validation": "In order to proceed, please check T&C checkbox", "username-password": "Please enter your username and password to login", "password-reset": "Please enter your email to get the password reset link", "go-back-login": "Go back to Login", "get-link": "Get Link", "contact-support": "Contact support for more help", "call-support": "Call support", "whatsapp-support": "Whatsapp support", "online": "ഓൺലൈൻ", "matches": "Matches", "recover-account": "Recovering Your Account", "enter-new-password": "Please enter new password for your account", "change-password": "Change your Password", "type-twice": "Just type it twice and try not to forget it", "can-contain": "can contain", "not-receive": "did not receive", "we-sent-an": "We have sent an", "to-email-and-phone": "to your email and phone number", "get": "Get", "recovery-options": "Below are the recovery options we found associated with your account", "otp-to": "We will send an OTP to", "subdomain": "Subdomain", "valid-username": "Enter a valid username"}, "HOME_PAGE": {"refer-friend": "Refer your Friend", "recent-properties": "Recent Properties", "shared-with": "Shared with", "view-all": "View all", "show-less": "Show Less", "residential": "Residential", "commercial": "Commercial", "agricultural": "Agricultural", "import": "ഇമ്പോർട്ട്"}, "DASHBOARD": {"received-date": "Received date", "status-date": "Status date", "current-week": "Current Week", "current-month": "Current Month", "custom": "Custom", "with-team": "With Team", "pending": "Pending", "meetings": "Meetings", "meetings-scheduled": "Meetings Scheduled", "site-visits": "Site Visits", "site-visits-scheduled": "Site Visits Scheduled", "lead-slab": "Lead Slab", "total": "ആകെ", "weekly": "Weekly", "monthly": "Monthly", "workable-leads": "Workable Leads", "booked": "Booked", "follow-ups": "Follow Ups", "feed": "Feed", "recent-actions": "Recent Actions", "team-dashboard": "Team Dashboard", "user-dashboard": "User Dashboard", "sales-funnel": "Sales Funnel", "lead-source": "Lead Sources", "task-schedule": "Task Schedule", "hi": "Hi", "good": "Good", "dropped": "dropped", "your-lead": "Your lead", "in-conversation-with": "in conversation with", "callbacks": "Callbacks", "last-Updated": "Last Updated", "leads-in-contact-with": "Leads in contact with", "lead-tracker": "Lead Tracker", "upcoming-events": "Upcoming events", "not-done": "Not Done", "lead-report": "Lead Report", "agent": "Agent", "organisation-dashboard": "Organisation Dashboard", "events": "Events", "no-events-found": "No events found", "no-records-found": "No records found", "call": "Call", "created-on": "Created On", "unassigned": "Unassigned", "deleted": "ഇല്ലാതാക്കിയത്", "workable": "Workable", "reminders": "Reminders", "with": "with"}, "TASK": {"task-title": "Title of the Task", "priority": "Priority", "mark-done": "<PERSON> as <PERSON>", "high": "High", "medium": "Medium", "low": "Low", "critical": "Critical", "manage-tasks": "Manage Tasks", "add-task": "Add Task", "edit-task": "Edit Task", "view-task": "View Task", "self": "Self", "title": "Title", "title-task": "Task Title", "date-and-time": "Due Date and Time", "due-date": "Due Date", "notes": "Notes", "task-description": "Task Description", "task-name": "Task Name", "max-char": "Maximum Characters", "empty-message": "Task organizer is at your service, be on top of all your due works"}, "LEADS": {"source": "Source", "sub-source": "Sub-Source", "action": "Action", "re-assign": "Re-Assign", "assign": "Assign", "bulk": "Bulk", "select-reason": "Please select a suitable reason", "assignment": "Assignment", "more": "More", "less": "Less", "contact-info": "Contact Info", "enquiry-info": "Enquiry Info", "escalate": "Escalate", "escalated": "Escalated", "de-escalate": "De-Escalate", "shared": "Shared", "highlighted": "Highlighted", "qualified": "Qualified", "hot": "Hot", "warm": "Warm", "cold": "Cold", "about-to-convert": "About to convert", "not-converted": "Not converted", "upload": "അപ്ലോഡ്", "property-details": "സ്വത്ത് വിവരങ്ങൾ", "personal-details": "Personal Details", "personal-info": "Personal Info", "lead-info": "Lead Info", "personal-information": "Personal Information", "current-status": "Current Status", "empty-message": "Sort your life by managing your client contacts and their journey with you.", "we-provide": "We provide", "integrations": "integrations", "share-portfolio": "Share Portfolio", "share-properties": "Share Properties", "chat": "Cha<PERSON>", "lead-added": "Lead got added", "assign-to": "Assign to", "assigned-to": "Assigned to", "reassign-to": "Reassigned To", "latest-status": "Latest status update", "flag-hot": "Flag as Hot", "deflag-hot": "Deflag Hot", "matching-properties": "Matching Properties", "matching-projects": "Matching Projects", "no-matching": "No Matching Properties Found!", "team": "ടീം", "individual": "Individual", "lead-preview": "Lead Data Preview", "lead-status-update": "Lead Status Update", "selected-lead": "Selected Lead(s)", "lead-unassigned": "Lead updates can be updated only after they are assigned to someone", "lead-unassigned-status": "Lead status can be updated only after they are assigned to someone", "agent-not-registered": "Agent not registered with IVR", "register-agent-ivr": "You can register yourself as an agent on IVR portal", "select-agent": "You can select the agent from below list", "lead-call-recordings": "Lead Call Recordings", "calling-lead": "Calling Lead", "created-date": "Created Date", "created-by": "Created By", "modified-date": "Modified Date", "modified-by": "Modified By", "last-modified-by": "Last Modified By", "qualified-by": "Qualified By", "restored-by": "Restored By", "converted-by": "Converted By", "document-title": "Document Title", "upload-new-document": "Upload New Document", "upload-another-document": "Upload Another Document", "lead-documents": "Lead Documents", "no-documents": "No documents found for this lead", "filter-by": "Filter <PERSON>", "tags": "Tags", "site-visit-status": "Site Visit Status", "contacted-through": "Contact initiated through", "lead-flagged": "Lead flagged as", "lead-unflagged": "Lead unflagged from", "updated-document-list": "Documents list updated", "added-document": "Document added", "deleted-document": "Document deleted", "flag-highlighted": "Flag as Highlighted", "deflag-highlighted": "Deflag Highlighted", "flag-warm": "Flag as Warm", "deflag-warm": "Deflag Warm", "flag-cold": "Flag as Cold", "deflag-cold": "Deflag Cold", "send-whatsapp-message": "Send Whatsapp message", "message": "Message", "send-message": "Send Message", "lead-search": "Enter lead name, phone no or email", "lead-search-prompt": "Press enter to search", "propertyType": "സ്വത്ത് തരം", "site-visit-location": "Site Visit Location", "location-punch-in": "Location Punch-In", "site-visit-done": "Site visit Done", "meeting-done": "Meeting Done", "budget-validation": "Max budget can't be less than min budget", "deleted": "Lead was deleted", "restored": "Lead was restored", "selected": "Selected", "download-report": "Download Report", "excel-upload-tracker": "Excel upload tracker", "dropped": "Dropped", "assigned": "Assigned", "mark-unassigned": "mark as unassigned", "unassign-lead": "Unassign lead", "filters": "Filters", "carpet-area": "Carpet Area", "sub-status": "Sub status", "picked-date": "Picked Date", "qr-generator": "QR Generator", "qr-code": "QR Code", "qr-content": "You can download this QR Code use to get lead", "print": "പ്രിന്റ്", "is-manual": "Manually Enter Location", "save-without-location": "Do you want to save without updating location", "booked-with-count-msg": "Wow! There have been total", "booked-without-count-msg": "Oops! Looks like no leads have been Booked yet.", "leads-booked": "leads booked.", "all-leads-msg-1": "You have got total", "all-leads-msg-2": "leads in your CRM platform.", "search-location": "Search location", "hide-location-search": "Hide Location Search", "preview": "പ്രിവ്യൂ", "last-activity": "Last Activity", "call-are-recorded": "Call Are Recorded", "ivr-call-recordings": "IVR Call Recordings", "change-status": "Change Status", "review-import": "Review & Import", "closing-manager": "Closing Manager", "sourcing-manager": "Sourcing Manager", "profession": "Profession", "customer-location": "Customer Location", "appointment": "Appointment"}, "DATA": {"no-history": "No history found for this data"}, "BULK_LEAD": {"easy-step": "Update data in 3 easy steps", "use-template": "Use template", "update-data": "Update data", "template": "Template", "templates": "Templates", "template-name": "Template Name", "download": "Download the", "upload-file": "Upload the File", "select-and-upload": "You can select and upload your file", "supported-format": "Supported formats xls, xlx, csv, xlsx ", "file-uploaded": "File Uploaded", "upload-correct-format": "Please upload the correct file format", "to-manage-your-leads": "to manage your leads", "selected-file": "You have selected the below file", "confirm-upload": "Confirm upload", "cancel-upload": "Cancel Upload", "lead-not-added": "records have not been added as they are duplicates.", "back-to-lead": "Ok, go back to leads", "out-of": "Out of", "uploaded-documents": "Uploaded Documents", "select-file": "Please select a file to upload", "assign-to": "As<PERSON> leads!", "confirm-assignment": "Confirm Assignment", "got-it": "Ok got it", "duplicate": "Duplicate", "invalid-lead": "Invalid Leads", "added-successfully": "Added Successfully", "distinct-leads": "Distinct Leads", "total-leads": "Total Leads", "lead-scheduled": "Lead(s) upload has been scheduled", "view-status": "to view status", "select-sheet": "Select Sheet", "refresh-data": "Click to Refresh Data", "importing-description": "Importing can be smooth if you follow the steps below", "download-sample-file": "Download sample file", "prepare-import-file": "Prepare Import file", "upload-your-file": "Upload your file", "download-description": "in this sample, you will find dummy data for your reference", "upload-description": "drag and drop your updated file into the system using below area", "upload-lead-file": "to upload your file", "file-type-acceptable": ".xlsx and .xls file types are acceptable", "media-type-acceptable": "Supported formate: JPG,PNG,PDF,MP4,GIF", "successfully-upload": "You have successfully uploaded the file"}, "LEAD_FORM": {"reason": "Reason", "date-time": "Date & Time", "select-a-suitable-reason": "Please select a suitable reason", "reassign-to": "Choose a person to reassign the lead to", "enquired-for": "Enquired For", "budget": "Budget", "schedule-date": "Schedule date", "scheduled-date": "Scheduled Date", "updated-budget": "What was the lead's Budget", "revert-date": "Has the lead mentioned a tentative date?", "whom": "Purchased From Whom?", "updated-location": "Which location is the lead looking for?", "sold-price": "Sold Price", "chosen": "<PERSON><PERSON>", "book-under": "Book under name", "meeting": "Meeting", "visit": "Visit", "site-visit": "Site Visit", "done": "Done", "otp": "OTP", "generate": "Generate", "customer-denied": "Customer Denied", "share-details": "Share details via", "share-portfolio-client": "Share portfolio with client", "is-meeting-done": "Is Meeting Done ?", "is-visit-done": "Is Site Visit Done ?", "meeting-done": "Done with the meeting", "meetings-done": "MD", "meetings-not-done": "MND", "meeting-not-done": "Meeting not done", "visit-done": "Done with the site visit", "visits-done": "SVD", "visit-not-done": "Site visit not done", "visits-not-done": "SVND", "rating": "Rating", "documents": "Documents", "view-on-map": "View on map", "referral-name": "Referral Name", "referral-phone-no": "Referral Phone No", "referral-details": "Referral Details", "channel-partner-name": "Channel Partner Name", "campaign-name": "Campaign Name", "executive-name": "Executive Name", "executive-phone-no": "Executive Phone No"}, "PROPERTY": {"property-submitted": "Property Details are Submitted Successfully.", "empty-message": "Don't get Hay-wire with too many properties in head or book; allow us to track them for you. All you need to do is, Click 'Add'", "matching-leads": "Matching Leads", "no-matching": "No Matching Leads Found!", "highlight-property": "Highlight this Property", "about-property": "About Property", "prop-type-required": "Property Type is a required field.", "bhk-type-required": "BHK Type is a required field.", "possession-availability": "Possession Availability", "select-and-upload": "You can select and upload your Brochure", "supported-format": "Supported formats Pdf", "select-permission": "Permissions are required.", "advanced-filters": "Advanced Filters", "select-all": "Select All", "selected-property": "selected-properties", "ready-to-move": "Ready To Move", "upload-images": "Upload images of your property", "property-brochures": "Property Brochures", "property-page-preview": "Property page preview", "property-finish-data": "your property will look like this after you finish adding the data", "add-property-images": "Add your property images", "property-quality-images": "At least 8 photos will increase property quality score by 15%", "quality-score": "Hall, Bedroom, Kitchen and Bathroom photos will get more points in quality score", "add-property-attributes": "Add Property Attributes", "attributes-attract": "attributes of your property will help to attract more customers", "add-property-amenities": "Add Property Amenities", "amenities-attract": "amenities of your property will help to attract more customers", "area-validation": "Max area can't be less than min area", "amenity": "Amenity", "STEPS": {"property-info": "Property Info", "owner-info": "Owner Info", "attributes": "Attributes", "attributes-info": "Attributes Info", "attributes-section": "Attributes Section", "amenities": "Amenities", "gallery": "Gallery", "add-document": "ADD DOCUMENTS", "name-of-document": "NAME OF DOCUMENT ", "add-previous-experience": "ADD PREVIOUS EXPERIENCE", "add-signature": "ADD SIGNATURE"}, "TYPES": {"Residential": "Residential", "Commercial": "Commercial", "Agricultural": "Agricultural"}, "BASIC_INFO": {"want-to": "I want to"}, "PROPERTY_DETAIL": {"total-floors": "Total Floors", "floor-number": "Floor Number", "furnish-status": "Furnish Status", "facing": "Facing", "title": "Property Title", "size": "Property Size", "area-size": "Area Size", "price": "Price", "total-price": "Total Price", "incl-charges": "(incl. of other charges)", "negotiable": "Negotiable", "brokerage": "Brokerage Amount", "brokerage-unit": "Brokerage Unit", "size-unit": "Size Unit", "dimension": "Dimension", "length": "Length", "breadth": "Bread<PERSON>", "lead-contact": "Lead Contact", "owner-name": "Owner Name", "owner-phone": "Owner Phone", "owner-builder-name": "Owner/Builder Name", "owner-builder-phone": "Owner/Builder Phone", "owner-builder-mail": "Owner/Builder Email", "property-description": "സ്വത്ത് വിവരണം", "list-on-go": "List this property on", "built-up-area": "Built-up Area", "saleable-area": "Saleable Area"}, "OWNER_INFO": {"phone": "ഫോൺ", "email": "ഇമെയിൽ"}, "ATTRIBUTE": {"bedroom": "No. of Bedrooms", "bathroom": "No. of Bathrooms", "utility": "No. of Utilities", "kitchen": "No. of Kitchens", "balcony": "No. of Balconies", "dl-room": "No. of Drawing/Living Rooms", "rating": "Quality Rating"}, "GALLERY": {"add-new": "Add New", "file-type": "Only .jpg, .jpeg, .png, .bmp file will be accepted."}, "sub-type": "Property Sub-Type", "bhk": "BHK", "resale": "resale", "sale-type": "Sale Type"}, "PROJECTS": {"title": "പേര്", "start-date": "Start Date", "end-date": "End Date", "possession-date": "Possession Date", "address": "വിലാസം", "permanent": "Permanent", "description": "Description", "facing": "Facing", "blocks": "No. of Blocks", "area": "Area", "area-unit": "Area Unit", "size-unit": "Size Unit", "carpet-size": "Carpet Si<PERSON>", "carpet-area-unit": "Carpet Area Unit", "hide-details": "Hide Details", "show-details": "Show Details", "projects": "Projects", "project": "Project", "assign_projects": "Assigned Projects"}, "PROJECTS_UNIT_INFO": {"add-new-unit": "Add New Unit", "edit-unit": "Edit Unit", "name": "Unit Name", "unit-type": "Unit Type", "bhk": "BHK", "unit-price": "Unit’s Price (Rs)", "upload-plan": "Upload Plan", "add-unit": "Add Unit", "unit-name": "Unit Name will appear here", "supported-formats": "Supported format: JPG, PNG, PDF", "no-of-units": "No. of Units", "unit-info": "Unit Info", "no-items": "No Unit created yet.", "unit-sale-status": "Unit Sale Status", "unit-definition": "Unit definition", "unit-status": "Unit Status"}, "PROJECTS_BLOCK_INFO": {"add-new-block": "Add New Block", "edit-block": "Edit Block", "bulk-upload": "Bulk Upload", "blockName": "Block Name", "no-floors": "No. of Floors", "floor": "Floor", "floors": "Floors", "drag-and-drop": "Drag & Drop file here", "upload-file": "Upload File", "upload-blueprint": "Upload Floor Blueprint", "please": "Please", "click-here": "Click Here", "download-ref-file": "to download reference file.", "add-floor": "Add Floor", "repeat-plan": "Repeat the plan in other floors?", "copy-inventory": "Copy Inventory to", "b-name": "Block name", "inventory": "Inventory", "copy-from": "Copied from", "copy-to": "Copy to", "copy-inventory-to": "Copy Inventory to", "copy-to-units": "Copy To Other Units", "details": "വിശദാംശങ്ങൾ", "no-items": "No blocks created yet.", "on-hold": "On Hold", "available": "ലഭ്യം", "unavailable": "ലഭ്യമല്ല", "sold-at-price": "Sold At Price", "supported-formats": "Supported format: JPG, PNG, PDF, PNG, DOX, DOCS", "map-the-fields": "Map the fields"}, "PROJECT_ROUTES": {"project-details": "Project Details", "units-info": "Units Info", "block-info": "Blocks Info"}, "PROJECTS_GALLERY": {"upload-image": "Upload Image", "change-image": "Change Image", "no-image-selected": "No Image selected to Upload", "unit": "Unit", "block": "Block", "upload-attachment": "Upload Attachments"}, "USER_MANAGEMENT": {"change-password": "Change Password", "update-role": "Update Role", "basic-info": "Basic Info", "basic-information": "Basic Information", "office-info": "Office Info", "office-information": "Office Information", "office": "ഓഫീസ്", "roles-permission": "Roles & Permissions", "role-permissions": "Roles and Permissions", "employee-number": "Employee Number", "home-location": "Home Location", "note": "Note", "reporting-to": "Reporting to", "department": "വകുപ്പ്", "designation": "Designation", "add-department": "Add New Department", "add-designation": "Add New Designation", "select-department": "Select a Department", "select-designation": "Select a Designation"}, "TEAM": {"all-teams": "All Teams", "save-team": "Save Team", "add-team": "Add Team", "edit-team": "Edit Team", "team-name": "Team Name", "team-description": "Team Description", "members": "Members", "add-member": "Add Member", "save-member": "Save Member", "total-members": "No. of Total Members", "members-selected": "Members Selected", "select-team-members": "Select Member(s)", "leads-assigned": "No. of Leads Assigned", "no-team-found": "No Team Found", "no-member-found": "No Member Found"}, "ROLE": {"manage-role": "Manage Role", "role-name": "Role Name", "select-permissions": "Select Permissions"}, "USER": {"phone-number": "Phone Number", "phone-no": "Phone No", "email": "ഇമെയിൽ", "email-id": "Email ID", "dob": "Date of Birth", "marriage-anniversary": "Marriage Anniversary", "add-new-role": "Add New Role", "add-role": "Add Role", "add-new-user": "Add New User", "add-user": "Add User", "edit-user": "Edit User", "all-users": "All Users", "edit-role": "Edit Role", "users": "ഉപയോക്താക്കൾ", "address": "വിലാസം", "blood-group-type": "Blood Group Type", "gender": "Gender", "description": "Description", "user": "ഉപയോക്താവ്", "please-select-role": "At least one role must be selected", "replace-image": "Replace Image", "remove-image": "Remove Image", "basic-details": "Basic Details", "modified-on": "Modified On", "location-info": "Location Info", "user-progress-message": "Streamline and visualize user's progress with a comprehensive", "party-platform": "Select the 3rd party platform", "currently-assigned": "where this user is currently assigned", "clock-in": "Clock In", "clock-out": "Clock Out", "working-hours": "Working Hours", "attendance-visuals": "Attendance Visuals", "user-project": "This user is associated with these projects", "no-attendance-found": "No attendance entries found", "no-document-found": "No documents found", "unmarked-attendance": "Unmarked Attendance", "future-day": "Future Day", "employee-tracking": "Employee Tracking", "total-employees": "Total Employees", "tracking": "Tracking", "live-tracking": "live tracking", "attendance-records": "Attendance Records"}, "BULK_USER": {"user-not-added": "User have not been added as they are duplicates.", "back-to-user": "Ok, go back to Users", "remove-duplicates": "Kindly remove invalid data from the Excel file and upload again."}, "SETTINGS": {"notify-on-update": "Notify on updates and activity", "notified-when-anyone": "You will be notified when anyone shares a report or invites you to a project.", "view": "കാണുക", "select-user": "Select User(s)", "lead-description": "Customized lead features", "data-description": "Customized data features", "general-desciption": "Common platform configuration", "international-number": "International number", "shift-timing": "Shift timing", "limit-user": "Limit user access", "mandatory-upload": "Mandatory selfie upload", "customization": "customization", "assign-description": "you can enable or disable the view assigned settings for your tenant", "export-description": "you can enable or disable the export settings for your tenant", "source-description": "you can enable or disable lead source edit settings for your tenant", "international-description": "you can enable or disable the option of using the CRM with international number support", "shift-description": "Manage your shift timings for your organization.", "limit-desciption": "This option will logout users from CRM after shift timings/completion of shift. Admins cannot be selected", "mandatory-upload-desciption": "This option will make selfie mandatory for attendance", "locality": "locality", "attendance": "attendance", "attendance-desciption": "Customize the shift timing", "locality-description": "Customized locality feature", "allow-duplicates": "Allow Duplicates", "allow-duplicates-description": "allow duplicate leads from integration sources into the application", "priority-list": "Set priority list for lead assignment", "priority-list-description": "default priority list set up for the flow of lead assignment will be provided", "water-mark-project-description": "property/project image watermark configuration"}, "SUPPORT": {"need-help": "Need Help?", "support-welcome-message": "We are here for you", "product-support": "For premium product support, we normally take up to 24-48 hours to reply except weekends.", "limited-support": "We provide limited support for all of our free products.", "free-product-support": "If you are requesting free product support, please remember that it can take up to 72 hours.", "form-title": "Send us a support message here", "query": "Query"}, "ATTENDANCE": {"not-clocked": "You have not clocked in yet", "hey": "Hey", "signed-in": "You are signed in as", "to-date": "To Date", "form-date": "Form Date", "attachments": "Attachments", "leave-type": "Leave Type", "manage-leaves": "Manage Leaves", "apply-leave": "Apply Leave", "half-day": "Half Day"}, "INTEGRATION": {"title": "We provide integrations to manage your leads", "third-party": "3rd Party", "download": "ഡൗൺലോഡ്", "download-excel": "Download Excel", "account-attached": "Account attached", "accounts-attached": "Accounts attached", "link-account": "Link Account", "link-new": "<PERSON>", "linked-date": "Linked Date", "leads-count": "Leads Count", "page-name": "Page Name", "lead-form": "Lead Form", "map": "Map", "set": "Set", "subscribe": "Subscribe", "unsubscribe": "Unsubscribe", "subscribe-all-ads": "Subscribe All Ads", "active-ad": "Active Ad", "ad-set": "Ad Set", "campaign": "Campaign", "ad-account": "Ad Account", "fb-login": "Log in With Facebook", "mapped": "Mapped", "unmapped": "Unmapped", "confirm-mapping": "Confirm mapping", "share-message1": "Share it with your", "share-message2": "relationship manager or representative. Alternately, you may provide your", "share-message3": "credentials to", "share-message4": "support and we shall help you integrate successfully", "connection-message": "The connection gets established, once API integration is done. Rejoice when you recieve your first lead, 'coz the connection is confirmed :)", "enquired-location": "Enquired Location", "enquired-city": "Enquired City", "enquired-state": "Enquired State", "enquired-country": "Enquired Country", "account-name": "Account Name", "ad-name": "Ad Name", "account-id": "Account ID", "display-message": "Gather leads from different platforms to manage at one place.", "click-google-btn": "Click Sign in with Google", "share-message-google1": "Select the Email Id you want to link, and agree to give the consent to", "share-message-google2": "to integrate with your Gmail account.", "sign-in-with-google": "Sign in with Google", "ivr-options": "Select IVR Option", "auth-token": "<PERSON><PERSON>", "primary": "Primary", "primary-no": "Primary No", "virtual-number": "Virtual Number", "confirm-mapping-assign": "Confirm mapping and assign", "assign-user": "Assign users", "ivr-option": "IVR option", "login-facebook": "Login With Facebook", "sign-in-with-facebook": "Click Sign in with Facebook", "agency-name": "Agency Name", "ads": "Ads", "form-id": "Form ID", "all-forms": "All Form(s)", "external-forms": "External Form(s)", "forms": "Forms", "associated-projects": "Associated Project(s)", "user-id": "User ID", "facebook-bulk-lead-fetch": "Bulk Fetch Facebook Leads", "city-default": "mark this city as default"}, "CONTACT": {"add-contact": "Add Contact", "my-data": "My Data", "data": "Data", "edit-contact": "Edit Contact", "no-contacts": "No Contacts Found", "same-phone": "Same as Phone", "import-contacts": "Import Contacts", "import-data": "Import Data", "contact": "ബന്ധപ്പെടുക", "contact-details": "New Contact Details", "map-fields": "Map form Fields", "confirm-upload": "Confirm Upload", "compare-fields": "Compare the fields to finish importing contacts", "convert-to-lead": "Are you sure, you want to convert the contact as lead?", "already-exist": "The phone number already exists.", "check": "Please check"}, "GENERATE_LEADS": {"lead-generation-heading": "Means of lead generation at your finger tips", "sharing-portfolio": "Sharing your Portfolio", "sharing-properties": "Sharing your Properties", "manage-contacts": "Manage Contacts", "lead-generation": "Lead Generation", "max-share-message": "You can only share 15 properties at a time."}, "WALLET": {"transaction-history": "Transaction History", "rewards": "Rewards"}, "PROFILE": {"name": "പേര്", "reraNo": "<PERSON><PERSON>", "phone": "Phone No", "email": "ഇമെയിൽ", "website": "Website", "address": "വിലാസം", "units-sold": "Total Units Sold", "properties-sold": "Total Properties Sold", "projects-developed": "Projects Developed", "properties-developed": "Properties Developed", "experience": "Years of experience", "link": "Link", "my-profile": "My Profile", "your-profile": "Your Profile is", "about-us": "About Us", "mission": "Mission/Vision", "what-we-do": "What We Do", "social-media": "Social Media", "work-details": "Work Details", "business-nature": "Business Nature", "facebook": "Facebook", "twitter": "Twitter", "instagram": "Instagram", "linkedin": "LinkedIn", "whatsapp": "WhatsApp", "another-account": "Another Account", "change-plan": "Change plan", "about-me": "About Me", "testimonial": "Testimonial", "my-testimonials": "My Testimonials", "add-testimonial": "Add New Testimonial", "edit-testimonial": "Edit Testimonial", "image-format": "The image should be in the format jpeg, jpg, png or svg.", "award-name": "Award Name", "project-properties-info": "Project/Properties Info", "company-brochure": "Company Brochure", "organization-brochure": "Organization Brochure", "supported-formats": "Supported formats", "uploaded-award-file": "Uploaded Award File", "uploaded": "Uploaded", "awards-certificate": "Awards & Certificate", "org-profile": "Org Profile", "organization-profile": "Organization Profile", "empty-message": "Hurry up! Add Your First Testimonial", "no-data-found": "No Data Found!", "no-lead-found": "No Lead Found", "no-property-found": "No Property Found", "no-project-found": "No Project Found"}, "REPORTS": {"select-user": "Select User", "records-of": "Records Of", "date-filters": "Date Filters", "visit-meeting": "Visit & Meeting", "export": "എക്സ്പോർട്ട്", "yesterday": "Yesterday", "last-7-days": "Last 7 days", "agency": "Agency", "activity-date-filter": "Meeting/Site-Visit Activity Date Filter"}, "COMMON": {"information": "Information", "search-by-information": "Search by Information", "no-information-found": "No information Found", "amenities": "Amenities", "attributes": "Attributes", "type-to-search": "type to search", "no-amenities-found": "No Amenities Found", "no-attributes-found": "No Attributes Found", "select-all": "Select All", "in-header": "in Header", "in-body": "in Body", "in-footer": "in Footer", "select-create-campaign": "Select/Create Campaign", "create-new-campaign": "Create New Campaign"}}