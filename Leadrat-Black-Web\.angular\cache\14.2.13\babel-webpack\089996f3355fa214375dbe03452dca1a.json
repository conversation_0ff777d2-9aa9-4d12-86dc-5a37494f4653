{"ast": null, "code": "import Metadata from './metadata.js';\nimport checkNumberLength from './helpers/checkNumberLength.js';\n/**\r\n * Checks if a phone number is \"possible\" (basically just checks its length).\r\n *\r\n * isPossible(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function isPossiblePhoneNumber(input, options, metadata) {\n  /* istanbul ignore if */\n  if (options === undefined) {\n    options = {};\n  }\n\n  metadata = new Metadata(metadata);\n\n  if (options.v2) {\n    if (!input.countryCallingCode) {\n      throw new Error('Invalid phone number object passed');\n    }\n\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else {\n    if (!input.phone) {\n      return false;\n    }\n\n    if (input.country) {\n      if (!metadata.hasCountry(input.country)) {\n        throw new Error(\"Unknown country: \".concat(input.country));\n      }\n\n      metadata.country(input.country);\n    } else {\n      if (!input.countryCallingCode) {\n        throw new Error('Invalid phone number object passed');\n      }\n\n      metadata.selectNumberingPlan(input.countryCallingCode);\n    }\n  } // Old metadata (< 1.0.18) had no \"possible length\" data.\n\n\n  if (metadata.possibleLengths()) {\n    return isPossibleNumber(input.phone || input.nationalNumber, metadata);\n  } else {\n    // There was a bug between `1.7.35` and `1.7.37` where \"possible_lengths\"\n    // were missing for \"non-geographical\" numbering plans.\n    // Just assume the number is possible in such cases:\n    // it's unlikely that anyone generated their custom metadata\n    // in that short period of time (one day).\n    // This code can be removed in some future major version update.\n    if (input.countryCallingCode && metadata.isNonGeographicCallingCode(input.countryCallingCode)) {\n      // \"Non-geographic entities\" did't have `possibleLengths`\n      // due to a bug in metadata generation process.\n      return true;\n    } else {\n      throw new Error('Missing \"possibleLengths\" in metadata. Perhaps the metadata has been generated before v1.0.18.');\n    }\n  }\n}\nexport function isPossibleNumber(nationalNumber, metadata) {\n  //, isInternational) {\n  switch (checkNumberLength(nationalNumber, metadata)) {\n    case 'IS_POSSIBLE':\n      return true;\n    // This library ignores \"local-only\" phone numbers (for simplicity).\n    // See the readme for more info on what are \"local-only\" phone numbers.\n    // case 'IS_POSSIBLE_LOCAL_ONLY':\n    // \treturn !isInternational\n\n    default:\n      return false;\n  }\n} //# sourceMappingURL=isPossible.js.map", "map": null, "metadata": {}, "sourceType": "module"}