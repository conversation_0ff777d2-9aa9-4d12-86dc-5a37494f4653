{"ast": null, "code": "import Metadata from '../metadata.js';\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\n\nvar SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/; // For regions that have multiple IDD prefixes\n// a preferred IDD prefix is returned.\n\nexport default function getIddPrefix(country, callingCode, metadata) {\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n\n  if (countryMetadata.defaultIDDPrefix()) {\n    return countryMetadata.defaultIDDPrefix();\n  }\n\n  if (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\n    return countryMetadata.IDDPrefix();\n  }\n} //# sourceMappingURL=getIddPrefix.js.map", "map": null, "metadata": {}, "sourceType": "module"}