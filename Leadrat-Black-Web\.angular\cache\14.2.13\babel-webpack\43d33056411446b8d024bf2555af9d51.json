{"ast": null, "code": "/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction nativeKeysIn(object) {\n  var result = [];\n\n  if (object != null) {\n    for (var key in Object(object)) {\n      result.push(key);\n    }\n  }\n\n  return result;\n}\n\nexport default nativeKeysIn;", "map": null, "metadata": {}, "sourceType": "module"}