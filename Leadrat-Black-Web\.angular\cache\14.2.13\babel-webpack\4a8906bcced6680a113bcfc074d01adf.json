{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function takeWhile(predicate, inclusive = false) {\n  return source => source.lift(new TakeWhileOperator(predicate, inclusive));\n}\n\nclass TakeWhileOperator {\n  constructor(predicate, inclusive) {\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));\n  }\n\n}\n\nclass TakeWhileSubscriber extends Subscriber {\n  constructor(destination, predicate, inclusive) {\n    super(destination);\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n    this.index = 0;\n  }\n\n  _next(value) {\n    const destination = this.destination;\n    let result;\n\n    try {\n      result = this.predicate(value, this.index++);\n    } catch (err) {\n      destination.error(err);\n      return;\n    }\n\n    this.nextOrComplete(value, result);\n  }\n\n  nextOrComplete(value, predicateResult) {\n    const destination = this.destination;\n\n    if (Boolean(predicateResult)) {\n      destination.next(value);\n    } else {\n      if (this.inclusive) {\n        destination.next(value);\n      }\n\n      destination.complete();\n    }\n  }\n\n} //# sourceMappingURL=takeWhile.js.map", "map": null, "metadata": {}, "sourceType": "module"}