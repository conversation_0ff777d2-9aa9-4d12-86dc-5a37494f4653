{"ast": null, "code": "import isValidNumber from '../isValid.js';\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\n\nexport default function isValidNumberForRegion(input, country, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  return input.country === country && isValidNumber(input, options, metadata);\n} //# sourceMappingURL=isValidNumberForRegion_.js.map", "map": null, "metadata": {}, "sourceType": "module"}