{"ast": null, "code": "import { Subscription } from './Subscription';\nexport class SubjectSubscription extends Subscription {\n  constructor(subject, subscriber) {\n    super();\n    this.subject = subject;\n    this.subscriber = subscriber;\n    this.closed = false;\n  }\n\n  unsubscribe() {\n    if (this.closed) {\n      return;\n    }\n\n    this.closed = true;\n    const subject = this.subject;\n    const observers = subject.observers;\n    this.subject = null;\n\n    if (!observers || observers.length === 0 || subject.isStopped || subject.closed) {\n      return;\n    }\n\n    const subscriberIndex = observers.indexOf(this.subscriber);\n\n    if (subscriberIndex !== -1) {\n      observers.splice(subscriberIndex, 1);\n    }\n  }\n\n} //# sourceMappingURL=SubjectSubscription.js.map", "map": null, "metadata": {}, "sourceType": "module"}