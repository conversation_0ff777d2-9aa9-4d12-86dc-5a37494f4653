{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/listing/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ErrorFactory, isBrowserExtension, isMobileCordova, isReactNative, FirebaseError, querystring, getModularInstance, base64Decode, getUA, isIE, createSubscribe, deepEqual, querystringDecode, extractQuerystring, isEmpty, getExperimentalSetting, getDefaultEmulatorHost } from '@firebase/util';\nimport { SDK_VERSION, _getProvider, _registerComponent, registerVersion, getApp } from '@firebase/app';\nimport { __rest } from 'tslib';\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { Component } from '@firebase/component';\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * An enum of factors that may be used for multifactor authentication.\r\n *\r\n * @public\r\n */\n\nconst FactorId = {\n  /** Phone as second factor */\n  PHONE: 'phone',\n  TOTP: 'totp'\n};\n/**\r\n * Enumeration of supported providers.\r\n *\r\n * @public\r\n */\n\nconst ProviderId = {\n  /** Facebook provider ID */\n  FACEBOOK: 'facebook.com',\n\n  /** GitHub provider ID */\n  GITHUB: 'github.com',\n\n  /** Google provider ID */\n  GOOGLE: 'google.com',\n\n  /** Password provider */\n  PASSWORD: 'password',\n\n  /** Phone provider */\n  PHONE: 'phone',\n\n  /** Twitter provider ID */\n  TWITTER: 'twitter.com'\n};\n/**\r\n * Enumeration of supported sign-in methods.\r\n *\r\n * @public\r\n */\n\nconst SignInMethod = {\n  /** Email link sign in method */\n  EMAIL_LINK: 'emailLink',\n\n  /** Email/password sign in method */\n  EMAIL_PASSWORD: 'password',\n\n  /** Facebook sign in method */\n  FACEBOOK: 'facebook.com',\n\n  /** GitHub sign in method */\n  GITHUB: 'github.com',\n\n  /** Google sign in method */\n  GOOGLE: 'google.com',\n\n  /** Phone sign in method */\n  PHONE: 'phone',\n\n  /** Twitter sign in method */\n  TWITTER: 'twitter.com'\n};\n/**\r\n * Enumeration of supported operation types.\r\n *\r\n * @public\r\n */\n\nconst OperationType = {\n  /** Operation involving linking an additional provider to an already signed-in user. */\n  LINK: 'link',\n\n  /** Operation involving using a provider to reauthenticate an already signed-in user. */\n  REAUTHENTICATE: 'reauthenticate',\n\n  /** Operation involving signing in a user. */\n  SIGN_IN: 'signIn'\n};\n/**\r\n * An enumeration of the possible email action types.\r\n *\r\n * @public\r\n */\n\nconst ActionCodeOperation = {\n  /** The email link sign-in action. */\n  EMAIL_SIGNIN: 'EMAIL_SIGNIN',\n\n  /** The password reset action. */\n  PASSWORD_RESET: 'PASSWORD_RESET',\n\n  /** The email revocation action. */\n  RECOVER_EMAIL: 'RECOVER_EMAIL',\n\n  /** The revert second factor addition email action. */\n  REVERT_SECOND_FACTOR_ADDITION: 'REVERT_SECOND_FACTOR_ADDITION',\n\n  /** The revert second factor addition email action. */\n  VERIFY_AND_CHANGE_EMAIL: 'VERIFY_AND_CHANGE_EMAIL',\n\n  /** The email verification action. */\n  VERIFY_EMAIL: 'VERIFY_EMAIL'\n};\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nfunction _debugErrorMap() {\n  return {\n    [\"admin-restricted-operation\"\n    /* AuthErrorCode.ADMIN_ONLY_OPERATION */\n    ]: 'This operation is restricted to administrators only.',\n    [\"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    ]: '',\n    [\"app-not-authorized\"\n    /* AuthErrorCode.APP_NOT_AUTHORIZED */\n    ]: \"This app, identified by the domain where it's hosted, is not \" + 'authorized to use Firebase Authentication with the provided API key. ' + 'Review your key configuration in the Google API console.',\n    [\"app-not-installed\"\n    /* AuthErrorCode.APP_NOT_INSTALLED */\n    ]: 'The requested mobile application corresponding to the identifier (' + 'Android package name or iOS bundle ID) provided is not installed on ' + 'this device.',\n    [\"captcha-check-failed\"\n    /* AuthErrorCode.CAPTCHA_CHECK_FAILED */\n    ]: 'The reCAPTCHA response token provided is either invalid, expired, ' + 'already used or the domain associated with it does not match the list ' + 'of whitelisted domains.',\n    [\"code-expired\"\n    /* AuthErrorCode.CODE_EXPIRED */\n    ]: 'The SMS code has expired. Please re-send the verification code to try ' + 'again.',\n    [\"cordova-not-ready\"\n    /* AuthErrorCode.CORDOVA_NOT_READY */\n    ]: 'Cordova framework is not ready.',\n    [\"cors-unsupported\"\n    /* AuthErrorCode.CORS_UNSUPPORTED */\n    ]: 'This browser is not supported.',\n    [\"credential-already-in-use\"\n    /* AuthErrorCode.CREDENTIAL_ALREADY_IN_USE */\n    ]: 'This credential is already associated with a different user account.',\n    [\"custom-token-mismatch\"\n    /* AuthErrorCode.CREDENTIAL_MISMATCH */\n    ]: 'The custom token corresponds to a different audience.',\n    [\"requires-recent-login\"\n    /* AuthErrorCode.CREDENTIAL_TOO_OLD_LOGIN_AGAIN */\n    ]: 'This operation is sensitive and requires recent authentication. Log in ' + 'again before retrying this request.',\n    [\"dependent-sdk-initialized-before-auth\"\n    /* AuthErrorCode.DEPENDENT_SDK_INIT_BEFORE_AUTH */\n    ]: 'Another Firebase SDK was initialized and is trying to use Auth before Auth is ' + 'initialized. Please be sure to call `initializeAuth` or `getAuth` before ' + 'starting any other Firebase SDK.',\n    [\"dynamic-link-not-activated\"\n    /* AuthErrorCode.DYNAMIC_LINK_NOT_ACTIVATED */\n    ]: 'Please activate Dynamic Links in the Firebase Console and agree to the terms and ' + 'conditions.',\n    [\"email-change-needs-verification\"\n    /* AuthErrorCode.EMAIL_CHANGE_NEEDS_VERIFICATION */\n    ]: 'Multi-factor users must always have a verified email.',\n    [\"email-already-in-use\"\n    /* AuthErrorCode.EMAIL_EXISTS */\n    ]: 'The email address is already in use by another account.',\n    [\"emulator-config-failed\"\n    /* AuthErrorCode.EMULATOR_CONFIG_FAILED */\n    ]: 'Auth instance has already been used to make a network call. Auth can ' + 'no longer be configured to use the emulator. Try calling ' + '\"connectAuthEmulator()\" sooner.',\n    [\"expired-action-code\"\n    /* AuthErrorCode.EXPIRED_OOB_CODE */\n    ]: 'The action code has expired.',\n    [\"cancelled-popup-request\"\n    /* AuthErrorCode.EXPIRED_POPUP_REQUEST */\n    ]: 'This operation has been cancelled due to another conflicting popup being opened.',\n    [\"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    ]: 'An internal AuthError has occurred.',\n    [\"invalid-app-credential\"\n    /* AuthErrorCode.INVALID_APP_CREDENTIAL */\n    ]: 'The phone verification request contains an invalid application verifier.' + ' The reCAPTCHA token response is either invalid or expired.',\n    [\"invalid-app-id\"\n    /* AuthErrorCode.INVALID_APP_ID */\n    ]: 'The mobile app identifier is not registed for the current project.',\n    [\"invalid-user-token\"\n    /* AuthErrorCode.INVALID_AUTH */\n    ]: \"This user's credential isn't valid for this project. This can happen \" + \"if the user's token has been tampered with, or if the user isn't for \" + 'the project associated with this API key.',\n    [\"invalid-auth-event\"\n    /* AuthErrorCode.INVALID_AUTH_EVENT */\n    ]: 'An internal AuthError has occurred.',\n    [\"invalid-verification-code\"\n    /* AuthErrorCode.INVALID_CODE */\n    ]: 'The SMS verification code used to create the phone auth credential is ' + 'invalid. Please resend the verification code sms and be sure to use the ' + 'verification code provided by the user.',\n    [\"invalid-continue-uri\"\n    /* AuthErrorCode.INVALID_CONTINUE_URI */\n    ]: 'The continue URL provided in the request is invalid.',\n    [\"invalid-cordova-configuration\"\n    /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */\n    ]: 'The following Cordova plugins must be installed to enable OAuth sign-in: ' + 'cordova-plugin-buildinfo, cordova-universal-links-plugin, ' + 'cordova-plugin-browsertab, cordova-plugin-inappbrowser and ' + 'cordova-plugin-customurlscheme.',\n    [\"invalid-custom-token\"\n    /* AuthErrorCode.INVALID_CUSTOM_TOKEN */\n    ]: 'The custom token format is incorrect. Please check the documentation.',\n    [\"invalid-dynamic-link-domain\"\n    /* AuthErrorCode.INVALID_DYNAMIC_LINK_DOMAIN */\n    ]: 'The provided dynamic link domain is not configured or authorized for the current project.',\n    [\"invalid-email\"\n    /* AuthErrorCode.INVALID_EMAIL */\n    ]: 'The email address is badly formatted.',\n    [\"invalid-emulator-scheme\"\n    /* AuthErrorCode.INVALID_EMULATOR_SCHEME */\n    ]: 'Emulator URL must start with a valid scheme (http:// or https://).',\n    [\"invalid-api-key\"\n    /* AuthErrorCode.INVALID_API_KEY */\n    ]: 'Your API key is invalid, please check you have copied it correctly.',\n    [\"invalid-cert-hash\"\n    /* AuthErrorCode.INVALID_CERT_HASH */\n    ]: 'The SHA-1 certificate hash provided is invalid.',\n    [\"invalid-credential\"\n    /* AuthErrorCode.INVALID_IDP_RESPONSE */\n    ]: 'The supplied auth credential is malformed or has expired.',\n    [\"invalid-message-payload\"\n    /* AuthErrorCode.INVALID_MESSAGE_PAYLOAD */\n    ]: 'The email template corresponding to this action contains invalid characters in its message. ' + 'Please fix by going to the Auth email templates section in the Firebase Console.',\n    [\"invalid-multi-factor-session\"\n    /* AuthErrorCode.INVALID_MFA_SESSION */\n    ]: 'The request does not contain a valid proof of first factor successful sign-in.',\n    [\"invalid-oauth-provider\"\n    /* AuthErrorCode.INVALID_OAUTH_PROVIDER */\n    ]: 'EmailAuthProvider is not supported for this operation. This operation ' + 'only supports OAuth providers.',\n    [\"invalid-oauth-client-id\"\n    /* AuthErrorCode.INVALID_OAUTH_CLIENT_ID */\n    ]: 'The OAuth client ID provided is either invalid or does not match the ' + 'specified API key.',\n    [\"unauthorized-domain\"\n    /* AuthErrorCode.INVALID_ORIGIN */\n    ]: 'This domain is not authorized for OAuth operations for your Firebase ' + 'project. Edit the list of authorized domains from the Firebase console.',\n    [\"invalid-action-code\"\n    /* AuthErrorCode.INVALID_OOB_CODE */\n    ]: 'The action code is invalid. This can happen if the code is malformed, ' + 'expired, or has already been used.',\n    [\"wrong-password\"\n    /* AuthErrorCode.INVALID_PASSWORD */\n    ]: 'The password is invalid or the user does not have a password.',\n    [\"invalid-persistence-type\"\n    /* AuthErrorCode.INVALID_PERSISTENCE */\n    ]: 'The specified persistence type is invalid. It can only be local, session or none.',\n    [\"invalid-phone-number\"\n    /* AuthErrorCode.INVALID_PHONE_NUMBER */\n    ]: 'The format of the phone number provided is incorrect. Please enter the ' + 'phone number in a format that can be parsed into E.164 format. E.164 ' + 'phone numbers are written in the format [+][country code][subscriber ' + 'number including area code].',\n    [\"invalid-provider-id\"\n    /* AuthErrorCode.INVALID_PROVIDER_ID */\n    ]: 'The specified provider ID is invalid.',\n    [\"invalid-recipient-email\"\n    /* AuthErrorCode.INVALID_RECIPIENT_EMAIL */\n    ]: 'The email corresponding to this action failed to send as the provided ' + 'recipient email address is invalid.',\n    [\"invalid-sender\"\n    /* AuthErrorCode.INVALID_SENDER */\n    ]: 'The email template corresponding to this action contains an invalid sender email or name. ' + 'Please fix by going to the Auth email templates section in the Firebase Console.',\n    [\"invalid-verification-id\"\n    /* AuthErrorCode.INVALID_SESSION_INFO */\n    ]: 'The verification ID used to create the phone auth credential is invalid.',\n    [\"invalid-tenant-id\"\n    /* AuthErrorCode.INVALID_TENANT_ID */\n    ]: \"The Auth instance's tenant ID is invalid.\",\n    [\"login-blocked\"\n    /* AuthErrorCode.LOGIN_BLOCKED */\n    ]: 'Login blocked by user-provided method: {$originalMessage}',\n    [\"missing-android-pkg-name\"\n    /* AuthErrorCode.MISSING_ANDROID_PACKAGE_NAME */\n    ]: 'An Android Package Name must be provided if the Android App is required to be installed.',\n    [\"auth-domain-config-required\"\n    /* AuthErrorCode.MISSING_AUTH_DOMAIN */\n    ]: 'Be sure to include authDomain when calling firebase.initializeApp(), ' + 'by following the instructions in the Firebase console.',\n    [\"missing-app-credential\"\n    /* AuthErrorCode.MISSING_APP_CREDENTIAL */\n    ]: 'The phone verification request is missing an application verifier ' + 'assertion. A reCAPTCHA response token needs to be provided.',\n    [\"missing-verification-code\"\n    /* AuthErrorCode.MISSING_CODE */\n    ]: 'The phone auth credential was created with an empty SMS verification code.',\n    [\"missing-continue-uri\"\n    /* AuthErrorCode.MISSING_CONTINUE_URI */\n    ]: 'A continue URL must be provided in the request.',\n    [\"missing-iframe-start\"\n    /* AuthErrorCode.MISSING_IFRAME_START */\n    ]: 'An internal AuthError has occurred.',\n    [\"missing-ios-bundle-id\"\n    /* AuthErrorCode.MISSING_IOS_BUNDLE_ID */\n    ]: 'An iOS Bundle ID must be provided if an App Store ID is provided.',\n    [\"missing-or-invalid-nonce\"\n    /* AuthErrorCode.MISSING_OR_INVALID_NONCE */\n    ]: 'The request does not contain a valid nonce. This can occur if the ' + 'SHA-256 hash of the provided raw nonce does not match the hashed nonce ' + 'in the ID token payload.',\n    [\"missing-password\"\n    /* AuthErrorCode.MISSING_PASSWORD */\n    ]: 'A non-empty password must be provided',\n    [\"missing-multi-factor-info\"\n    /* AuthErrorCode.MISSING_MFA_INFO */\n    ]: 'No second factor identifier is provided.',\n    [\"missing-multi-factor-session\"\n    /* AuthErrorCode.MISSING_MFA_SESSION */\n    ]: 'The request is missing proof of first factor successful sign-in.',\n    [\"missing-phone-number\"\n    /* AuthErrorCode.MISSING_PHONE_NUMBER */\n    ]: 'To send verification codes, provide a phone number for the recipient.',\n    [\"missing-verification-id\"\n    /* AuthErrorCode.MISSING_SESSION_INFO */\n    ]: 'The phone auth credential was created with an empty verification ID.',\n    [\"app-deleted\"\n    /* AuthErrorCode.MODULE_DESTROYED */\n    ]: 'This instance of FirebaseApp has been deleted.',\n    [\"multi-factor-info-not-found\"\n    /* AuthErrorCode.MFA_INFO_NOT_FOUND */\n    ]: 'The user does not have a second factor matching the identifier provided.',\n    [\"multi-factor-auth-required\"\n    /* AuthErrorCode.MFA_REQUIRED */\n    ]: 'Proof of ownership of a second factor is required to complete sign-in.',\n    [\"account-exists-with-different-credential\"\n    /* AuthErrorCode.NEED_CONFIRMATION */\n    ]: 'An account already exists with the same email address but different ' + 'sign-in credentials. Sign in using a provider associated with this ' + 'email address.',\n    [\"network-request-failed\"\n    /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n    ]: 'A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.',\n    [\"no-auth-event\"\n    /* AuthErrorCode.NO_AUTH_EVENT */\n    ]: 'An internal AuthError has occurred.',\n    [\"no-such-provider\"\n    /* AuthErrorCode.NO_SUCH_PROVIDER */\n    ]: 'User was not linked to an account with the given provider.',\n    [\"null-user\"\n    /* AuthErrorCode.NULL_USER */\n    ]: 'A null user object was provided as the argument for an operation which ' + 'requires a non-null user object.',\n    [\"operation-not-allowed\"\n    /* AuthErrorCode.OPERATION_NOT_ALLOWED */\n    ]: 'The given sign-in provider is disabled for this Firebase project. ' + 'Enable it in the Firebase console, under the sign-in method tab of the ' + 'Auth section.',\n    [\"operation-not-supported-in-this-environment\"\n    /* AuthErrorCode.OPERATION_NOT_SUPPORTED */\n    ]: 'This operation is not supported in the environment this application is ' + 'running on. \"location.protocol\" must be http, https or chrome-extension' + ' and web storage must be enabled.',\n    [\"popup-blocked\"\n    /* AuthErrorCode.POPUP_BLOCKED */\n    ]: 'Unable to establish a connection with the popup. It may have been blocked by the browser.',\n    [\"popup-closed-by-user\"\n    /* AuthErrorCode.POPUP_CLOSED_BY_USER */\n    ]: 'The popup has been closed by the user before finalizing the operation.',\n    [\"provider-already-linked\"\n    /* AuthErrorCode.PROVIDER_ALREADY_LINKED */\n    ]: 'User can only be linked to one identity for the given provider.',\n    [\"quota-exceeded\"\n    /* AuthErrorCode.QUOTA_EXCEEDED */\n    ]: \"The project's quota for this operation has been exceeded.\",\n    [\"redirect-cancelled-by-user\"\n    /* AuthErrorCode.REDIRECT_CANCELLED_BY_USER */\n    ]: 'The redirect operation has been cancelled by the user before finalizing.',\n    [\"redirect-operation-pending\"\n    /* AuthErrorCode.REDIRECT_OPERATION_PENDING */\n    ]: 'A redirect sign-in operation is already pending.',\n    [\"rejected-credential\"\n    /* AuthErrorCode.REJECTED_CREDENTIAL */\n    ]: 'The request contains malformed or mismatching credentials.',\n    [\"second-factor-already-in-use\"\n    /* AuthErrorCode.SECOND_FACTOR_ALREADY_ENROLLED */\n    ]: 'The second factor is already enrolled on this account.',\n    [\"maximum-second-factor-count-exceeded\"\n    /* AuthErrorCode.SECOND_FACTOR_LIMIT_EXCEEDED */\n    ]: 'The maximum allowed number of second factors on a user has been exceeded.',\n    [\"tenant-id-mismatch\"\n    /* AuthErrorCode.TENANT_ID_MISMATCH */\n    ]: \"The provided tenant ID does not match the Auth instance's tenant ID\",\n    [\"timeout\"\n    /* AuthErrorCode.TIMEOUT */\n    ]: 'The operation has timed out.',\n    [\"user-token-expired\"\n    /* AuthErrorCode.TOKEN_EXPIRED */\n    ]: \"The user's credential is no longer valid. The user must sign in again.\",\n    [\"too-many-requests\"\n    /* AuthErrorCode.TOO_MANY_ATTEMPTS_TRY_LATER */\n    ]: 'We have blocked all requests from this device due to unusual activity. ' + 'Try again later.',\n    [\"unauthorized-continue-uri\"\n    /* AuthErrorCode.UNAUTHORIZED_DOMAIN */\n    ]: 'The domain of the continue URL is not whitelisted.  Please whitelist ' + 'the domain in the Firebase console.',\n    [\"unsupported-first-factor\"\n    /* AuthErrorCode.UNSUPPORTED_FIRST_FACTOR */\n    ]: 'Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.',\n    [\"unsupported-persistence-type\"\n    /* AuthErrorCode.UNSUPPORTED_PERSISTENCE */\n    ]: 'The current environment does not support the specified persistence type.',\n    [\"unsupported-tenant-operation\"\n    /* AuthErrorCode.UNSUPPORTED_TENANT_OPERATION */\n    ]: 'This operation is not supported in a multi-tenant context.',\n    [\"unverified-email\"\n    /* AuthErrorCode.UNVERIFIED_EMAIL */\n    ]: 'The operation requires a verified email.',\n    [\"user-cancelled\"\n    /* AuthErrorCode.USER_CANCELLED */\n    ]: 'The user did not grant your application the permissions it requested.',\n    [\"user-not-found\"\n    /* AuthErrorCode.USER_DELETED */\n    ]: 'There is no user record corresponding to this identifier. The user may ' + 'have been deleted.',\n    [\"user-disabled\"\n    /* AuthErrorCode.USER_DISABLED */\n    ]: 'The user account has been disabled by an administrator.',\n    [\"user-mismatch\"\n    /* AuthErrorCode.USER_MISMATCH */\n    ]: 'The supplied credentials do not correspond to the previously signed in user.',\n    [\"user-signed-out\"\n    /* AuthErrorCode.USER_SIGNED_OUT */\n    ]: '',\n    [\"weak-password\"\n    /* AuthErrorCode.WEAK_PASSWORD */\n    ]: 'The password must be 6 characters long or more.',\n    [\"web-storage-unsupported\"\n    /* AuthErrorCode.WEB_STORAGE_UNSUPPORTED */\n    ]: 'This browser is not supported or 3rd party cookies and data may be disabled.',\n    [\"already-initialized\"\n    /* AuthErrorCode.ALREADY_INITIALIZED */\n    ]: 'initializeAuth() has already been called with ' + 'different options. To avoid this error, call initializeAuth() with the ' + 'same options as when it was originally called, or call getAuth() to return the' + ' already initialized instance.',\n    [\"missing-recaptcha-token\"\n    /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n    ]: 'The reCAPTCHA token is missing when sending request to the backend.',\n    [\"invalid-recaptcha-token\"\n    /* AuthErrorCode.INVALID_RECAPTCHA_TOKEN */\n    ]: 'The reCAPTCHA token is invalid when sending request to the backend.',\n    [\"invalid-recaptcha-action\"\n    /* AuthErrorCode.INVALID_RECAPTCHA_ACTION */\n    ]: 'The reCAPTCHA action is invalid when sending request to the backend.',\n    [\"recaptcha-not-enabled\"\n    /* AuthErrorCode.RECAPTCHA_NOT_ENABLED */\n    ]: 'reCAPTCHA Enterprise integration is not enabled for this project.',\n    [\"missing-client-type\"\n    /* AuthErrorCode.MISSING_CLIENT_TYPE */\n    ]: 'The reCAPTCHA client type is missing when sending request to the backend.',\n    [\"missing-recaptcha-version\"\n    /* AuthErrorCode.MISSING_RECAPTCHA_VERSION */\n    ]: 'The reCAPTCHA version is missing when sending request to the backend.',\n    [\"invalid-req-type\"\n    /* AuthErrorCode.INVALID_REQ_TYPE */\n    ]: 'Invalid request parameters.',\n    [\"invalid-recaptcha-version\"\n    /* AuthErrorCode.INVALID_RECAPTCHA_VERSION */\n    ]: 'The reCAPTCHA version is invalid when sending request to the backend.'\n  };\n}\n\nfunction _prodErrorMap() {\n  // We will include this one message in the prod error map since by the very\n  // nature of this error, developers will never be able to see the message\n  // using the debugErrorMap (which is installed during auth initialization).\n  return {\n    [\"dependent-sdk-initialized-before-auth\"\n    /* AuthErrorCode.DEPENDENT_SDK_INIT_BEFORE_AUTH */\n    ]: 'Another Firebase SDK was initialized and is trying to use Auth before Auth is ' + 'initialized. Please be sure to call `initializeAuth` or `getAuth` before ' + 'starting any other Firebase SDK.'\n  };\n}\n/**\r\n * A verbose error map with detailed descriptions for most error codes.\r\n *\r\n * See discussion at {@link AuthErrorMap}\r\n *\r\n * @public\r\n */\n\n\nconst debugErrorMap = _debugErrorMap;\n/**\r\n * A minimal error map with all verbose error messages stripped.\r\n *\r\n * See discussion at {@link AuthErrorMap}\r\n *\r\n * @public\r\n */\n\nconst prodErrorMap = _prodErrorMap;\n\nconst _DEFAULT_AUTH_ERROR_FACTORY = new ErrorFactory('auth', 'Firebase', _prodErrorMap());\n/**\r\n * A map of potential `Auth` error codes, for easier comparison with errors\r\n * thrown by the SDK.\r\n *\r\n * @remarks\r\n * Note that you can't tree-shake individual keys\r\n * in the map, so by using the map you might substantially increase your\r\n * bundle size.\r\n *\r\n * @public\r\n */\n\n\nconst AUTH_ERROR_CODES_MAP_DO_NOT_USE_INTERNALLY = {\n  ADMIN_ONLY_OPERATION: 'auth/admin-restricted-operation',\n  ARGUMENT_ERROR: 'auth/argument-error',\n  APP_NOT_AUTHORIZED: 'auth/app-not-authorized',\n  APP_NOT_INSTALLED: 'auth/app-not-installed',\n  CAPTCHA_CHECK_FAILED: 'auth/captcha-check-failed',\n  CODE_EXPIRED: 'auth/code-expired',\n  CORDOVA_NOT_READY: 'auth/cordova-not-ready',\n  CORS_UNSUPPORTED: 'auth/cors-unsupported',\n  CREDENTIAL_ALREADY_IN_USE: 'auth/credential-already-in-use',\n  CREDENTIAL_MISMATCH: 'auth/custom-token-mismatch',\n  CREDENTIAL_TOO_OLD_LOGIN_AGAIN: 'auth/requires-recent-login',\n  DEPENDENT_SDK_INIT_BEFORE_AUTH: 'auth/dependent-sdk-initialized-before-auth',\n  DYNAMIC_LINK_NOT_ACTIVATED: 'auth/dynamic-link-not-activated',\n  EMAIL_CHANGE_NEEDS_VERIFICATION: 'auth/email-change-needs-verification',\n  EMAIL_EXISTS: 'auth/email-already-in-use',\n  EMULATOR_CONFIG_FAILED: 'auth/emulator-config-failed',\n  EXPIRED_OOB_CODE: 'auth/expired-action-code',\n  EXPIRED_POPUP_REQUEST: 'auth/cancelled-popup-request',\n  INTERNAL_ERROR: 'auth/internal-error',\n  INVALID_API_KEY: 'auth/invalid-api-key',\n  INVALID_APP_CREDENTIAL: 'auth/invalid-app-credential',\n  INVALID_APP_ID: 'auth/invalid-app-id',\n  INVALID_AUTH: 'auth/invalid-user-token',\n  INVALID_AUTH_EVENT: 'auth/invalid-auth-event',\n  INVALID_CERT_HASH: 'auth/invalid-cert-hash',\n  INVALID_CODE: 'auth/invalid-verification-code',\n  INVALID_CONTINUE_URI: 'auth/invalid-continue-uri',\n  INVALID_CORDOVA_CONFIGURATION: 'auth/invalid-cordova-configuration',\n  INVALID_CUSTOM_TOKEN: 'auth/invalid-custom-token',\n  INVALID_DYNAMIC_LINK_DOMAIN: 'auth/invalid-dynamic-link-domain',\n  INVALID_EMAIL: 'auth/invalid-email',\n  INVALID_EMULATOR_SCHEME: 'auth/invalid-emulator-scheme',\n  INVALID_IDP_RESPONSE: 'auth/invalid-credential',\n  INVALID_MESSAGE_PAYLOAD: 'auth/invalid-message-payload',\n  INVALID_MFA_SESSION: 'auth/invalid-multi-factor-session',\n  INVALID_OAUTH_CLIENT_ID: 'auth/invalid-oauth-client-id',\n  INVALID_OAUTH_PROVIDER: 'auth/invalid-oauth-provider',\n  INVALID_OOB_CODE: 'auth/invalid-action-code',\n  INVALID_ORIGIN: 'auth/unauthorized-domain',\n  INVALID_PASSWORD: 'auth/wrong-password',\n  INVALID_PERSISTENCE: 'auth/invalid-persistence-type',\n  INVALID_PHONE_NUMBER: 'auth/invalid-phone-number',\n  INVALID_PROVIDER_ID: 'auth/invalid-provider-id',\n  INVALID_RECIPIENT_EMAIL: 'auth/invalid-recipient-email',\n  INVALID_SENDER: 'auth/invalid-sender',\n  INVALID_SESSION_INFO: 'auth/invalid-verification-id',\n  INVALID_TENANT_ID: 'auth/invalid-tenant-id',\n  MFA_INFO_NOT_FOUND: 'auth/multi-factor-info-not-found',\n  MFA_REQUIRED: 'auth/multi-factor-auth-required',\n  MISSING_ANDROID_PACKAGE_NAME: 'auth/missing-android-pkg-name',\n  MISSING_APP_CREDENTIAL: 'auth/missing-app-credential',\n  MISSING_AUTH_DOMAIN: 'auth/auth-domain-config-required',\n  MISSING_CODE: 'auth/missing-verification-code',\n  MISSING_CONTINUE_URI: 'auth/missing-continue-uri',\n  MISSING_IFRAME_START: 'auth/missing-iframe-start',\n  MISSING_IOS_BUNDLE_ID: 'auth/missing-ios-bundle-id',\n  MISSING_OR_INVALID_NONCE: 'auth/missing-or-invalid-nonce',\n  MISSING_MFA_INFO: 'auth/missing-multi-factor-info',\n  MISSING_MFA_SESSION: 'auth/missing-multi-factor-session',\n  MISSING_PHONE_NUMBER: 'auth/missing-phone-number',\n  MISSING_SESSION_INFO: 'auth/missing-verification-id',\n  MODULE_DESTROYED: 'auth/app-deleted',\n  NEED_CONFIRMATION: 'auth/account-exists-with-different-credential',\n  NETWORK_REQUEST_FAILED: 'auth/network-request-failed',\n  NULL_USER: 'auth/null-user',\n  NO_AUTH_EVENT: 'auth/no-auth-event',\n  NO_SUCH_PROVIDER: 'auth/no-such-provider',\n  OPERATION_NOT_ALLOWED: 'auth/operation-not-allowed',\n  OPERATION_NOT_SUPPORTED: 'auth/operation-not-supported-in-this-environment',\n  POPUP_BLOCKED: 'auth/popup-blocked',\n  POPUP_CLOSED_BY_USER: 'auth/popup-closed-by-user',\n  PROVIDER_ALREADY_LINKED: 'auth/provider-already-linked',\n  QUOTA_EXCEEDED: 'auth/quota-exceeded',\n  REDIRECT_CANCELLED_BY_USER: 'auth/redirect-cancelled-by-user',\n  REDIRECT_OPERATION_PENDING: 'auth/redirect-operation-pending',\n  REJECTED_CREDENTIAL: 'auth/rejected-credential',\n  SECOND_FACTOR_ALREADY_ENROLLED: 'auth/second-factor-already-in-use',\n  SECOND_FACTOR_LIMIT_EXCEEDED: 'auth/maximum-second-factor-count-exceeded',\n  TENANT_ID_MISMATCH: 'auth/tenant-id-mismatch',\n  TIMEOUT: 'auth/timeout',\n  TOKEN_EXPIRED: 'auth/user-token-expired',\n  TOO_MANY_ATTEMPTS_TRY_LATER: 'auth/too-many-requests',\n  UNAUTHORIZED_DOMAIN: 'auth/unauthorized-continue-uri',\n  UNSUPPORTED_FIRST_FACTOR: 'auth/unsupported-first-factor',\n  UNSUPPORTED_PERSISTENCE: 'auth/unsupported-persistence-type',\n  UNSUPPORTED_TENANT_OPERATION: 'auth/unsupported-tenant-operation',\n  UNVERIFIED_EMAIL: 'auth/unverified-email',\n  USER_CANCELLED: 'auth/user-cancelled',\n  USER_DELETED: 'auth/user-not-found',\n  USER_DISABLED: 'auth/user-disabled',\n  USER_MISMATCH: 'auth/user-mismatch',\n  USER_SIGNED_OUT: 'auth/user-signed-out',\n  WEAK_PASSWORD: 'auth/weak-password',\n  WEB_STORAGE_UNSUPPORTED: 'auth/web-storage-unsupported',\n  ALREADY_INITIALIZED: 'auth/already-initialized',\n  RECAPTCHA_NOT_ENABLED: 'auth/recaptcha-not-enabled',\n  MISSING_RECAPTCHA_TOKEN: 'auth/missing-recaptcha-token',\n  INVALID_RECAPTCHA_TOKEN: 'auth/invalid-recaptcha-token',\n  INVALID_RECAPTCHA_ACTION: 'auth/invalid-recaptcha-action',\n  MISSING_CLIENT_TYPE: 'auth/missing-client-type',\n  MISSING_RECAPTCHA_VERSION: 'auth/missing-recaptcha-version',\n  INVALID_RECAPTCHA_VERSION: 'auth/invalid-recaptcha-version',\n  INVALID_REQ_TYPE: 'auth/invalid-req-type'\n};\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nconst logClient = new Logger('@firebase/auth');\n\nfunction _logWarn(msg, ...args) {\n  if (logClient.logLevel <= LogLevel.WARN) {\n    logClient.warn(`Auth (${SDK_VERSION}): ${msg}`, ...args);\n  }\n}\n\nfunction _logError(msg, ...args) {\n  if (logClient.logLevel <= LogLevel.ERROR) {\n    logClient.error(`Auth (${SDK_VERSION}): ${msg}`, ...args);\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _fail(authOrCode, ...rest) {\n  throw createErrorInternal(authOrCode, ...rest);\n}\n\nfunction _createError(authOrCode, ...rest) {\n  return createErrorInternal(authOrCode, ...rest);\n}\n\nfunction _errorWithCustomMessage(auth, code, message) {\n  const errorMap = Object.assign(Object.assign({}, prodErrorMap()), {\n    [code]: message\n  });\n  const factory = new ErrorFactory('auth', 'Firebase', errorMap);\n  return factory.create(code, {\n    appName: auth.name\n  });\n}\n\nfunction _assertInstanceOf(auth, object, instance) {\n  const constructorInstance = instance;\n\n  if (!(object instanceof constructorInstance)) {\n    if (constructorInstance.name !== object.constructor.name) {\n      _fail(auth, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n    }\n\n    throw _errorWithCustomMessage(auth, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    , `Type of ${object.constructor.name} does not match expected instance.` + `Did you pass a reference from a different Auth SDK?`);\n  }\n}\n\nfunction createErrorInternal(authOrCode, ...rest) {\n  if (typeof authOrCode !== 'string') {\n    const code = rest[0];\n    const fullParams = [...rest.slice(1)];\n\n    if (fullParams[0]) {\n      fullParams[0].appName = authOrCode.name;\n    }\n\n    return authOrCode._errorFactory.create(code, ...fullParams);\n  }\n\n  return _DEFAULT_AUTH_ERROR_FACTORY.create(authOrCode, ...rest);\n}\n\nfunction _assert(assertion, authOrCode, ...rest) {\n  if (!assertion) {\n    throw createErrorInternal(authOrCode, ...rest);\n  }\n}\n/**\r\n * Unconditionally fails, throwing an internal error with the given message.\r\n *\r\n * @param failure type of failure encountered\r\n * @throws Error\r\n */\n\n\nfunction debugFail(failure) {\n  // Log the failure in addition to throw an exception, just in case the\n  // exception is swallowed.\n  const message = `INTERNAL ASSERTION FAILED: ` + failure;\n\n  _logError(message); // NOTE: We don't use FirebaseError here because these are internal failures\n  // that cannot be handled by the user. (Also it would create a circular\n  // dependency between the error and assert modules which doesn't work.)\n\n\n  throw new Error(message);\n}\n/**\r\n * Fails if the given assertion condition is false, throwing an Error with the\r\n * given message if it did.\r\n *\r\n * @param assertion\r\n * @param message\r\n */\n\n\nfunction debugAssert(assertion, message) {\n  if (!assertion) {\n    debugFail(message);\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _getCurrentUrl() {\n  var _a;\n\n  return typeof self !== 'undefined' && ((_a = self.location) === null || _a === void 0 ? void 0 : _a.href) || '';\n}\n\nfunction _isHttpOrHttps() {\n  return _getCurrentScheme() === 'http:' || _getCurrentScheme() === 'https:';\n}\n\nfunction _getCurrentScheme() {\n  var _a;\n\n  return typeof self !== 'undefined' && ((_a = self.location) === null || _a === void 0 ? void 0 : _a.protocol) || null;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Determine whether the browser is working online\r\n */\n\n\nfunction _isOnline() {\n  if (typeof navigator !== 'undefined' && navigator && 'onLine' in navigator && typeof navigator.onLine === 'boolean' && ( // Apply only for traditional web apps and Chrome extensions.\n  // This is especially true for Cordova apps which have unreliable\n  // navigator.onLine behavior unless cordova-plugin-network-information is\n  // installed which overwrites the native navigator.onLine value and\n  // defines navigator.connection.\n  _isHttpOrHttps() || isBrowserExtension() || 'connection' in navigator)) {\n    return navigator.onLine;\n  } // If we can't determine the state, assume it is online.\n\n\n  return true;\n}\n\nfunction _getUserLanguage() {\n  if (typeof navigator === 'undefined') {\n    return null;\n  }\n\n  const navigatorLanguage = navigator;\n  return (// Most reliable, but only supported in Chrome/Firefox.\n    navigatorLanguage.languages && navigatorLanguage.languages[0] || // Supported in most browsers, but returns the language of the browser\n    // UI, not the language set in browser settings.\n    navigatorLanguage.language || // Couldn't determine language.\n    null\n  );\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * A structure to help pick between a range of long and short delay durations\r\n * depending on the current environment. In general, the long delay is used for\r\n * mobile environments whereas short delays are used for desktop environments.\r\n */\n\n\nclass Delay {\n  constructor(shortDelay, longDelay) {\n    this.shortDelay = shortDelay;\n    this.longDelay = longDelay; // Internal error when improperly initialized.\n\n    debugAssert(longDelay > shortDelay, 'Short delay should be less than long delay!');\n    this.isMobile = isMobileCordova() || isReactNative();\n  }\n\n  get() {\n    if (!_isOnline()) {\n      // Pick the shorter timeout.\n      return Math.min(5000\n      /* DelayMin.OFFLINE */\n      , this.shortDelay);\n    } // If running in a mobile environment, return the long delay, otherwise\n    // return the short delay.\n    // This could be improved in the future to dynamically change based on other\n    // variables instead of just reading the current environment.\n\n\n    return this.isMobile ? this.longDelay : this.shortDelay;\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _emulatorUrl(config, path) {\n  debugAssert(config.emulator, 'Emulator should always be set here');\n  const {\n    url\n  } = config.emulator;\n\n  if (!path) {\n    return url;\n  }\n\n  return `${url}${path.startsWith('/') ? path.slice(1) : path}`;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass FetchProvider {\n  static initialize(fetchImpl, headersImpl, responseImpl) {\n    this.fetchImpl = fetchImpl;\n\n    if (headersImpl) {\n      this.headersImpl = headersImpl;\n    }\n\n    if (responseImpl) {\n      this.responseImpl = responseImpl;\n    }\n  }\n\n  static fetch() {\n    if (this.fetchImpl) {\n      return this.fetchImpl;\n    }\n\n    if (typeof self !== 'undefined' && 'fetch' in self) {\n      return self.fetch;\n    }\n\n    debugFail('Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill');\n  }\n\n  static headers() {\n    if (this.headersImpl) {\n      return this.headersImpl;\n    }\n\n    if (typeof self !== 'undefined' && 'Headers' in self) {\n      return self.Headers;\n    }\n\n    debugFail('Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill');\n  }\n\n  static response() {\n    if (this.responseImpl) {\n      return this.responseImpl;\n    }\n\n    if (typeof self !== 'undefined' && 'Response' in self) {\n      return self.Response;\n    }\n\n    debugFail('Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill');\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Map from errors returned by the server to errors to developer visible errors\r\n */\n\n\nconst SERVER_ERROR_MAP = {\n  // Custom token errors.\n  [\"CREDENTIAL_MISMATCH\"\n  /* ServerError.CREDENTIAL_MISMATCH */\n  ]: \"custom-token-mismatch\"\n  /* AuthErrorCode.CREDENTIAL_MISMATCH */\n  ,\n  // This can only happen if the SDK sends a bad request.\n  [\"MISSING_CUSTOM_TOKEN\"\n  /* ServerError.MISSING_CUSTOM_TOKEN */\n  ]: \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  ,\n  // Create Auth URI errors.\n  [\"INVALID_IDENTIFIER\"\n  /* ServerError.INVALID_IDENTIFIER */\n  ]: \"invalid-email\"\n  /* AuthErrorCode.INVALID_EMAIL */\n  ,\n  // This can only happen if the SDK sends a bad request.\n  [\"MISSING_CONTINUE_URI\"\n  /* ServerError.MISSING_CONTINUE_URI */\n  ]: \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  ,\n  // Sign in with email and password errors (some apply to sign up too).\n  [\"INVALID_PASSWORD\"\n  /* ServerError.INVALID_PASSWORD */\n  ]: \"wrong-password\"\n  /* AuthErrorCode.INVALID_PASSWORD */\n  ,\n  // This can only happen if the SDK sends a bad request.\n  [\"MISSING_PASSWORD\"\n  /* ServerError.MISSING_PASSWORD */\n  ]: \"missing-password\"\n  /* AuthErrorCode.MISSING_PASSWORD */\n  ,\n  // Sign up with email and password errors.\n  [\"EMAIL_EXISTS\"\n  /* ServerError.EMAIL_EXISTS */\n  ]: \"email-already-in-use\"\n  /* AuthErrorCode.EMAIL_EXISTS */\n  ,\n  [\"PASSWORD_LOGIN_DISABLED\"\n  /* ServerError.PASSWORD_LOGIN_DISABLED */\n  ]: \"operation-not-allowed\"\n  /* AuthErrorCode.OPERATION_NOT_ALLOWED */\n  ,\n  // Verify assertion for sign in with credential errors:\n  [\"INVALID_IDP_RESPONSE\"\n  /* ServerError.INVALID_IDP_RESPONSE */\n  ]: \"invalid-credential\"\n  /* AuthErrorCode.INVALID_IDP_RESPONSE */\n  ,\n  [\"INVALID_PENDING_TOKEN\"\n  /* ServerError.INVALID_PENDING_TOKEN */\n  ]: \"invalid-credential\"\n  /* AuthErrorCode.INVALID_IDP_RESPONSE */\n  ,\n  [\"FEDERATED_USER_ID_ALREADY_LINKED\"\n  /* ServerError.FEDERATED_USER_ID_ALREADY_LINKED */\n  ]: \"credential-already-in-use\"\n  /* AuthErrorCode.CREDENTIAL_ALREADY_IN_USE */\n  ,\n  // This can only happen if the SDK sends a bad request.\n  [\"MISSING_REQ_TYPE\"\n  /* ServerError.MISSING_REQ_TYPE */\n  ]: \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  ,\n  // Send Password reset email errors:\n  [\"EMAIL_NOT_FOUND\"\n  /* ServerError.EMAIL_NOT_FOUND */\n  ]: \"user-not-found\"\n  /* AuthErrorCode.USER_DELETED */\n  ,\n  [\"RESET_PASSWORD_EXCEED_LIMIT\"\n  /* ServerError.RESET_PASSWORD_EXCEED_LIMIT */\n  ]: \"too-many-requests\"\n  /* AuthErrorCode.TOO_MANY_ATTEMPTS_TRY_LATER */\n  ,\n  [\"EXPIRED_OOB_CODE\"\n  /* ServerError.EXPIRED_OOB_CODE */\n  ]: \"expired-action-code\"\n  /* AuthErrorCode.EXPIRED_OOB_CODE */\n  ,\n  [\"INVALID_OOB_CODE\"\n  /* ServerError.INVALID_OOB_CODE */\n  ]: \"invalid-action-code\"\n  /* AuthErrorCode.INVALID_OOB_CODE */\n  ,\n  // This can only happen if the SDK sends a bad request.\n  [\"MISSING_OOB_CODE\"\n  /* ServerError.MISSING_OOB_CODE */\n  ]: \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  ,\n  // Operations that require ID token in request:\n  [\"CREDENTIAL_TOO_OLD_LOGIN_AGAIN\"\n  /* ServerError.CREDENTIAL_TOO_OLD_LOGIN_AGAIN */\n  ]: \"requires-recent-login\"\n  /* AuthErrorCode.CREDENTIAL_TOO_OLD_LOGIN_AGAIN */\n  ,\n  [\"INVALID_ID_TOKEN\"\n  /* ServerError.INVALID_ID_TOKEN */\n  ]: \"invalid-user-token\"\n  /* AuthErrorCode.INVALID_AUTH */\n  ,\n  [\"TOKEN_EXPIRED\"\n  /* ServerError.TOKEN_EXPIRED */\n  ]: \"user-token-expired\"\n  /* AuthErrorCode.TOKEN_EXPIRED */\n  ,\n  [\"USER_NOT_FOUND\"\n  /* ServerError.USER_NOT_FOUND */\n  ]: \"user-token-expired\"\n  /* AuthErrorCode.TOKEN_EXPIRED */\n  ,\n  // Other errors.\n  [\"TOO_MANY_ATTEMPTS_TRY_LATER\"\n  /* ServerError.TOO_MANY_ATTEMPTS_TRY_LATER */\n  ]: \"too-many-requests\"\n  /* AuthErrorCode.TOO_MANY_ATTEMPTS_TRY_LATER */\n  ,\n  // Phone Auth related errors.\n  [\"INVALID_CODE\"\n  /* ServerError.INVALID_CODE */\n  ]: \"invalid-verification-code\"\n  /* AuthErrorCode.INVALID_CODE */\n  ,\n  [\"INVALID_SESSION_INFO\"\n  /* ServerError.INVALID_SESSION_INFO */\n  ]: \"invalid-verification-id\"\n  /* AuthErrorCode.INVALID_SESSION_INFO */\n  ,\n  [\"INVALID_TEMPORARY_PROOF\"\n  /* ServerError.INVALID_TEMPORARY_PROOF */\n  ]: \"invalid-credential\"\n  /* AuthErrorCode.INVALID_IDP_RESPONSE */\n  ,\n  [\"MISSING_SESSION_INFO\"\n  /* ServerError.MISSING_SESSION_INFO */\n  ]: \"missing-verification-id\"\n  /* AuthErrorCode.MISSING_SESSION_INFO */\n  ,\n  [\"SESSION_EXPIRED\"\n  /* ServerError.SESSION_EXPIRED */\n  ]: \"code-expired\"\n  /* AuthErrorCode.CODE_EXPIRED */\n  ,\n  // Other action code errors when additional settings passed.\n  // MISSING_CONTINUE_URI is getting mapped to INTERNAL_ERROR above.\n  // This is OK as this error will be caught by client side validation.\n  [\"MISSING_ANDROID_PACKAGE_NAME\"\n  /* ServerError.MISSING_ANDROID_PACKAGE_NAME */\n  ]: \"missing-android-pkg-name\"\n  /* AuthErrorCode.MISSING_ANDROID_PACKAGE_NAME */\n  ,\n  [\"UNAUTHORIZED_DOMAIN\"\n  /* ServerError.UNAUTHORIZED_DOMAIN */\n  ]: \"unauthorized-continue-uri\"\n  /* AuthErrorCode.UNAUTHORIZED_DOMAIN */\n  ,\n  // getProjectConfig errors when clientId is passed.\n  [\"INVALID_OAUTH_CLIENT_ID\"\n  /* ServerError.INVALID_OAUTH_CLIENT_ID */\n  ]: \"invalid-oauth-client-id\"\n  /* AuthErrorCode.INVALID_OAUTH_CLIENT_ID */\n  ,\n  // User actions (sign-up or deletion) disabled errors.\n  [\"ADMIN_ONLY_OPERATION\"\n  /* ServerError.ADMIN_ONLY_OPERATION */\n  ]: \"admin-restricted-operation\"\n  /* AuthErrorCode.ADMIN_ONLY_OPERATION */\n  ,\n  // Multi factor related errors.\n  [\"INVALID_MFA_PENDING_CREDENTIAL\"\n  /* ServerError.INVALID_MFA_PENDING_CREDENTIAL */\n  ]: \"invalid-multi-factor-session\"\n  /* AuthErrorCode.INVALID_MFA_SESSION */\n  ,\n  [\"MFA_ENROLLMENT_NOT_FOUND\"\n  /* ServerError.MFA_ENROLLMENT_NOT_FOUND */\n  ]: \"multi-factor-info-not-found\"\n  /* AuthErrorCode.MFA_INFO_NOT_FOUND */\n  ,\n  [\"MISSING_MFA_ENROLLMENT_ID\"\n  /* ServerError.MISSING_MFA_ENROLLMENT_ID */\n  ]: \"missing-multi-factor-info\"\n  /* AuthErrorCode.MISSING_MFA_INFO */\n  ,\n  [\"MISSING_MFA_PENDING_CREDENTIAL\"\n  /* ServerError.MISSING_MFA_PENDING_CREDENTIAL */\n  ]: \"missing-multi-factor-session\"\n  /* AuthErrorCode.MISSING_MFA_SESSION */\n  ,\n  [\"SECOND_FACTOR_EXISTS\"\n  /* ServerError.SECOND_FACTOR_EXISTS */\n  ]: \"second-factor-already-in-use\"\n  /* AuthErrorCode.SECOND_FACTOR_ALREADY_ENROLLED */\n  ,\n  [\"SECOND_FACTOR_LIMIT_EXCEEDED\"\n  /* ServerError.SECOND_FACTOR_LIMIT_EXCEEDED */\n  ]: \"maximum-second-factor-count-exceeded\"\n  /* AuthErrorCode.SECOND_FACTOR_LIMIT_EXCEEDED */\n  ,\n  // Blocking functions related errors.\n  [\"BLOCKING_FUNCTION_ERROR_RESPONSE\"\n  /* ServerError.BLOCKING_FUNCTION_ERROR_RESPONSE */\n  ]: \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  ,\n  // Recaptcha related errors.\n  [\"RECAPTCHA_NOT_ENABLED\"\n  /* ServerError.RECAPTCHA_NOT_ENABLED */\n  ]: \"recaptcha-not-enabled\"\n  /* AuthErrorCode.RECAPTCHA_NOT_ENABLED */\n  ,\n  [\"MISSING_RECAPTCHA_TOKEN\"\n  /* ServerError.MISSING_RECAPTCHA_TOKEN */\n  ]: \"missing-recaptcha-token\"\n  /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n  ,\n  [\"INVALID_RECAPTCHA_TOKEN\"\n  /* ServerError.INVALID_RECAPTCHA_TOKEN */\n  ]: \"invalid-recaptcha-token\"\n  /* AuthErrorCode.INVALID_RECAPTCHA_TOKEN */\n  ,\n  [\"INVALID_RECAPTCHA_ACTION\"\n  /* ServerError.INVALID_RECAPTCHA_ACTION */\n  ]: \"invalid-recaptcha-action\"\n  /* AuthErrorCode.INVALID_RECAPTCHA_ACTION */\n  ,\n  [\"MISSING_CLIENT_TYPE\"\n  /* ServerError.MISSING_CLIENT_TYPE */\n  ]: \"missing-client-type\"\n  /* AuthErrorCode.MISSING_CLIENT_TYPE */\n  ,\n  [\"MISSING_RECAPTCHA_VERSION\"\n  /* ServerError.MISSING_RECAPTCHA_VERSION */\n  ]: \"missing-recaptcha-version\"\n  /* AuthErrorCode.MISSING_RECAPTCHA_VERSION */\n  ,\n  [\"INVALID_RECAPTCHA_VERSION\"\n  /* ServerError.INVALID_RECAPTCHA_VERSION */\n  ]: \"invalid-recaptcha-version\"\n  /* AuthErrorCode.INVALID_RECAPTCHA_VERSION */\n  ,\n  [\"INVALID_REQ_TYPE\"\n  /* ServerError.INVALID_REQ_TYPE */\n  ]: \"invalid-req-type\"\n  /* AuthErrorCode.INVALID_REQ_TYPE */\n\n};\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nconst DEFAULT_API_TIMEOUT_MS = new Delay(30000, 60000);\n\nfunction _addTidIfNecessary(auth, request) {\n  if (auth.tenantId && !request.tenantId) {\n    return Object.assign(Object.assign({}, request), {\n      tenantId: auth.tenantId\n    });\n  }\n\n  return request;\n}\n\nfunction _performApiRequest(_x, _x2, _x3, _x4) {\n  return _performApiRequest2.apply(this, arguments);\n}\n\nfunction _performApiRequest2() {\n  _performApiRequest2 = _asyncToGenerator(function* (auth, method, path, request, customErrorMap = {}) {\n    return _performFetchWithErrorHandling(auth, customErrorMap, /*#__PURE__*/_asyncToGenerator(function* () {\n      let body = {};\n      let params = {};\n\n      if (request) {\n        if (method === \"GET\"\n        /* HttpMethod.GET */\n        ) {\n          params = request;\n        } else {\n          body = {\n            body: JSON.stringify(request)\n          };\n        }\n      }\n\n      const query = querystring(Object.assign({\n        key: auth.config.apiKey\n      }, params)).slice(1);\n      const headers = yield auth._getAdditionalHeaders();\n      headers[\"Content-Type\"\n      /* HttpHeader.CONTENT_TYPE */\n      ] = 'application/json';\n\n      if (auth.languageCode) {\n        headers[\"X-Firebase-Locale\"\n        /* HttpHeader.X_FIREBASE_LOCALE */\n        ] = auth.languageCode;\n      }\n\n      return FetchProvider.fetch()(_getFinalTarget(auth, auth.config.apiHost, path, query), Object.assign({\n        method,\n        headers,\n        referrerPolicy: 'no-referrer'\n      }, body));\n    }));\n  });\n  return _performApiRequest2.apply(this, arguments);\n}\n\nfunction _performFetchWithErrorHandling(_x5, _x6, _x7) {\n  return _performFetchWithErrorHandling2.apply(this, arguments);\n}\n\nfunction _performFetchWithErrorHandling2() {\n  _performFetchWithErrorHandling2 = _asyncToGenerator(function* (auth, customErrorMap, fetchFn) {\n    auth._canInitEmulator = false;\n    const errorMap = Object.assign(Object.assign({}, SERVER_ERROR_MAP), customErrorMap);\n\n    try {\n      const networkTimeout = new NetworkTimeout(auth);\n      const response = yield Promise.race([fetchFn(), networkTimeout.promise]); // If we've reached this point, the fetch succeeded and the networkTimeout\n      // didn't throw; clear the network timeout delay so that Node won't hang\n\n      networkTimeout.clearNetworkTimeout();\n      const json = yield response.json();\n\n      if ('needConfirmation' in json) {\n        throw _makeTaggedError(auth, \"account-exists-with-different-credential\"\n        /* AuthErrorCode.NEED_CONFIRMATION */\n        , json);\n      }\n\n      if (response.ok && !('errorMessage' in json)) {\n        return json;\n      } else {\n        const errorMessage = response.ok ? json.errorMessage : json.error.message;\n        const [serverErrorCode, serverErrorMessage] = errorMessage.split(' : ');\n\n        if (serverErrorCode === \"FEDERATED_USER_ID_ALREADY_LINKED\"\n        /* ServerError.FEDERATED_USER_ID_ALREADY_LINKED */\n        ) {\n          throw _makeTaggedError(auth, \"credential-already-in-use\"\n          /* AuthErrorCode.CREDENTIAL_ALREADY_IN_USE */\n          , json);\n        } else if (serverErrorCode === \"EMAIL_EXISTS\"\n        /* ServerError.EMAIL_EXISTS */\n        ) {\n          throw _makeTaggedError(auth, \"email-already-in-use\"\n          /* AuthErrorCode.EMAIL_EXISTS */\n          , json);\n        } else if (serverErrorCode === \"USER_DISABLED\"\n        /* ServerError.USER_DISABLED */\n        ) {\n          throw _makeTaggedError(auth, \"user-disabled\"\n          /* AuthErrorCode.USER_DISABLED */\n          , json);\n        }\n\n        const authError = errorMap[serverErrorCode] || serverErrorCode.toLowerCase().replace(/[_\\s]+/g, '-');\n\n        if (serverErrorMessage) {\n          throw _errorWithCustomMessage(auth, authError, serverErrorMessage);\n        } else {\n          _fail(auth, authError);\n        }\n      }\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        throw e;\n      } // Changing this to a different error code will log user out when there is a network error\n      // because we treat any error other than NETWORK_REQUEST_FAILED as token is invalid.\n      // https://github.com/firebase/firebase-js-sdk/blob/4fbc73610d70be4e0852e7de63a39cb7897e8546/packages/auth/src/core/auth/auth_impl.ts#L309-L316\n\n\n      _fail(auth, \"network-request-failed\"\n      /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n      , {\n        'message': String(e)\n      });\n    }\n  });\n  return _performFetchWithErrorHandling2.apply(this, arguments);\n}\n\nfunction _performSignInRequest(_x8, _x9, _x10, _x11) {\n  return _performSignInRequest2.apply(this, arguments);\n}\n\nfunction _performSignInRequest2() {\n  _performSignInRequest2 = _asyncToGenerator(function* (auth, method, path, request, customErrorMap = {}) {\n    const serverResponse = yield _performApiRequest(auth, method, path, request, customErrorMap);\n\n    if ('mfaPendingCredential' in serverResponse) {\n      _fail(auth, \"multi-factor-auth-required\"\n      /* AuthErrorCode.MFA_REQUIRED */\n      , {\n        _serverResponse: serverResponse\n      });\n    }\n\n    return serverResponse;\n  });\n  return _performSignInRequest2.apply(this, arguments);\n}\n\nfunction _getFinalTarget(auth, host, path, query) {\n  const base = `${host}${path}?${query}`;\n\n  if (!auth.config.emulator) {\n    return `${auth.config.apiScheme}://${base}`;\n  }\n\n  return _emulatorUrl(auth.config, base);\n}\n\nclass NetworkTimeout {\n  constructor(auth) {\n    this.auth = auth; // Node timers and browser timers are fundamentally incompatible, but we\n    // don't care about the value here\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n    this.timer = null;\n    this.promise = new Promise((_, reject) => {\n      this.timer = setTimeout(() => {\n        return reject(_createError(this.auth, \"network-request-failed\"\n        /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n        ));\n      }, DEFAULT_API_TIMEOUT_MS.get());\n    });\n  }\n\n  clearNetworkTimeout() {\n    clearTimeout(this.timer);\n  }\n\n}\n\nfunction _makeTaggedError(auth, code, response) {\n  const errorParams = {\n    appName: auth.name\n  };\n\n  if (response.email) {\n    errorParams.email = response.email;\n  }\n\n  if (response.phoneNumber) {\n    errorParams.phoneNumber = response.phoneNumber;\n  }\n\n  const error = _createError(auth, code, errorParams); // We know customData is defined on error because errorParams is defined\n\n\n  error.customData._tokenResponse = response;\n  return error;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction deleteAccount(_x12, _x13) {\n  return _deleteAccount.apply(this, arguments);\n}\n\nfunction _deleteAccount() {\n  _deleteAccount = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:delete\"\n    /* Endpoint.DELETE_ACCOUNT */\n    , request);\n  });\n  return _deleteAccount.apply(this, arguments);\n}\n\nfunction deleteLinkedAccounts(_x14, _x15) {\n  return _deleteLinkedAccounts.apply(this, arguments);\n}\n\nfunction _deleteLinkedAccounts() {\n  _deleteLinkedAccounts = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:update\"\n    /* Endpoint.SET_ACCOUNT_INFO */\n    , request);\n  });\n  return _deleteLinkedAccounts.apply(this, arguments);\n}\n\nfunction getAccountInfo(_x16, _x17) {\n  return _getAccountInfo.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _getAccountInfo() {\n  _getAccountInfo = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:lookup\"\n    /* Endpoint.GET_ACCOUNT_INFO */\n    , request);\n  });\n  return _getAccountInfo.apply(this, arguments);\n}\n\nfunction utcTimestampToDateString(utcTimestamp) {\n  if (!utcTimestamp) {\n    return undefined;\n  }\n\n  try {\n    // Convert to date object.\n    const date = new Date(Number(utcTimestamp)); // Test date is valid.\n\n    if (!isNaN(date.getTime())) {\n      // Convert to UTC date string.\n      return date.toUTCString();\n    }\n  } catch (e) {// Do nothing. undefined will be returned.\n  }\n\n  return undefined;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Returns a JSON Web Token (JWT) used to identify the user to a Firebase service.\r\n *\r\n * @remarks\r\n * Returns the current token if it has not expired or if it will not expire in the next five\r\n * minutes. Otherwise, this will refresh the token and return a new one.\r\n *\r\n * @param user - The user.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\n\n\nfunction getIdToken(user, forceRefresh = false) {\n  return getModularInstance(user).getIdToken(forceRefresh);\n}\n/**\r\n * Returns a deserialized JSON Web Token (JWT) used to identify the user to a Firebase service.\r\n *\r\n * @remarks\r\n * Returns the current token if it has not expired or if it will not expire in the next five\r\n * minutes. Otherwise, this will refresh the token and return a new one.\r\n *\r\n * @param user - The user.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\n\n\nfunction getIdTokenResult(_x18) {\n  return _getIdTokenResult.apply(this, arguments);\n}\n\nfunction _getIdTokenResult() {\n  _getIdTokenResult = _asyncToGenerator(function* (user, forceRefresh = false) {\n    const userInternal = getModularInstance(user);\n    const token = yield userInternal.getIdToken(forceRefresh);\n\n    const claims = _parseToken(token);\n\n    _assert(claims && claims.exp && claims.auth_time && claims.iat, userInternal.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    const firebase = typeof claims.firebase === 'object' ? claims.firebase : undefined;\n    const signInProvider = firebase === null || firebase === void 0 ? void 0 : firebase['sign_in_provider'];\n    return {\n      claims,\n      token,\n      authTime: utcTimestampToDateString(secondsStringToMilliseconds(claims.auth_time)),\n      issuedAtTime: utcTimestampToDateString(secondsStringToMilliseconds(claims.iat)),\n      expirationTime: utcTimestampToDateString(secondsStringToMilliseconds(claims.exp)),\n      signInProvider: signInProvider || null,\n      signInSecondFactor: (firebase === null || firebase === void 0 ? void 0 : firebase['sign_in_second_factor']) || null\n    };\n  });\n  return _getIdTokenResult.apply(this, arguments);\n}\n\nfunction secondsStringToMilliseconds(seconds) {\n  return Number(seconds) * 1000;\n}\n\nfunction _parseToken(token) {\n  const [algorithm, payload, signature] = token.split('.');\n\n  if (algorithm === undefined || payload === undefined || signature === undefined) {\n    _logError('JWT malformed, contained fewer than 3 sections');\n\n    return null;\n  }\n\n  try {\n    const decoded = base64Decode(payload);\n\n    if (!decoded) {\n      _logError('Failed to decode base64 JWT payload');\n\n      return null;\n    }\n\n    return JSON.parse(decoded);\n  } catch (e) {\n    _logError('Caught error parsing JWT payload as JSON', e === null || e === void 0 ? void 0 : e.toString());\n\n    return null;\n  }\n}\n/**\r\n * Extract expiresIn TTL from a token by subtracting the expiration from the issuance.\r\n */\n\n\nfunction _tokenExpiresIn(token) {\n  const parsedToken = _parseToken(token);\n\n  _assert(parsedToken, \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  );\n\n  _assert(typeof parsedToken.exp !== 'undefined', \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  );\n\n  _assert(typeof parsedToken.iat !== 'undefined', \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  );\n\n  return Number(parsedToken.exp) - Number(parsedToken.iat);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _logoutIfInvalidated(_x19, _x20) {\n  return _logoutIfInvalidated2.apply(this, arguments);\n}\n\nfunction _logoutIfInvalidated2() {\n  _logoutIfInvalidated2 = _asyncToGenerator(function* (user, promise, bypassAuthState = false) {\n    if (bypassAuthState) {\n      return promise;\n    }\n\n    try {\n      return yield promise;\n    } catch (e) {\n      if (e instanceof FirebaseError && isUserInvalidated(e)) {\n        if (user.auth.currentUser === user) {\n          yield user.auth.signOut();\n        }\n      }\n\n      throw e;\n    }\n  });\n  return _logoutIfInvalidated2.apply(this, arguments);\n}\n\nfunction isUserInvalidated({\n  code\n}) {\n  return code === `auth/${\"user-disabled\"\n  /* AuthErrorCode.USER_DISABLED */\n  }` || code === `auth/${\"user-token-expired\"\n  /* AuthErrorCode.TOKEN_EXPIRED */\n  }`;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass ProactiveRefresh {\n  constructor(user) {\n    this.user = user;\n    this.isRunning = false; // Node timers and browser timers return fundamentally different types.\n    // We don't actually care what the value is but TS won't accept unknown and\n    // we can't cast properly in both environments.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n    this.timerId = null;\n    this.errorBackoff = 30000\n    /* Duration.RETRY_BACKOFF_MIN */\n    ;\n  }\n\n  _start() {\n    if (this.isRunning) {\n      return;\n    }\n\n    this.isRunning = true;\n    this.schedule();\n  }\n\n  _stop() {\n    if (!this.isRunning) {\n      return;\n    }\n\n    this.isRunning = false;\n\n    if (this.timerId !== null) {\n      clearTimeout(this.timerId);\n    }\n  }\n\n  getInterval(wasError) {\n    var _a;\n\n    if (wasError) {\n      const interval = this.errorBackoff;\n      this.errorBackoff = Math.min(this.errorBackoff * 2, 960000\n      /* Duration.RETRY_BACKOFF_MAX */\n      );\n      return interval;\n    } else {\n      // Reset the error backoff\n      this.errorBackoff = 30000\n      /* Duration.RETRY_BACKOFF_MIN */\n      ;\n      const expTime = (_a = this.user.stsTokenManager.expirationTime) !== null && _a !== void 0 ? _a : 0;\n      const interval = expTime - Date.now() - 300000\n      /* Duration.OFFSET */\n      ;\n      return Math.max(0, interval);\n    }\n  }\n\n  schedule(wasError = false) {\n    var _this = this;\n\n    if (!this.isRunning) {\n      // Just in case...\n      return;\n    }\n\n    const interval = this.getInterval(wasError);\n    this.timerId = setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {\n      yield _this.iteration();\n    }), interval);\n  }\n\n  iteration() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this2.user.getIdToken(true);\n      } catch (e) {\n        // Only retry on network errors\n        if ((e === null || e === void 0 ? void 0 : e.code) === `auth/${\"network-request-failed\"\n        /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n        }`) {\n          _this2.schedule(\n          /* wasError */\n          true);\n        }\n\n        return;\n      }\n\n      _this2.schedule();\n    })();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass UserMetadata {\n  constructor(createdAt, lastLoginAt) {\n    this.createdAt = createdAt;\n    this.lastLoginAt = lastLoginAt;\n\n    this._initializeTime();\n  }\n\n  _initializeTime() {\n    this.lastSignInTime = utcTimestampToDateString(this.lastLoginAt);\n    this.creationTime = utcTimestampToDateString(this.createdAt);\n  }\n\n  _copy(metadata) {\n    this.createdAt = metadata.createdAt;\n    this.lastLoginAt = metadata.lastLoginAt;\n\n    this._initializeTime();\n  }\n\n  toJSON() {\n    return {\n      createdAt: this.createdAt,\n      lastLoginAt: this.lastLoginAt\n    };\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _reloadWithoutSaving(_x21) {\n  return _reloadWithoutSaving2.apply(this, arguments);\n}\n/**\r\n * Reloads user account data, if signed in.\r\n *\r\n * @param user - The user.\r\n *\r\n * @public\r\n */\n\n\nfunction _reloadWithoutSaving2() {\n  _reloadWithoutSaving2 = _asyncToGenerator(function* (user) {\n    var _a;\n\n    const auth = user.auth;\n    const idToken = yield user.getIdToken();\n    const response = yield _logoutIfInvalidated(user, getAccountInfo(auth, {\n      idToken\n    }));\n\n    _assert(response === null || response === void 0 ? void 0 : response.users.length, auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    const coreAccount = response.users[0];\n\n    user._notifyReloadListener(coreAccount);\n\n    const newProviderData = ((_a = coreAccount.providerUserInfo) === null || _a === void 0 ? void 0 : _a.length) ? extractProviderData(coreAccount.providerUserInfo) : [];\n    const providerData = mergeProviderData(user.providerData, newProviderData); // Preserves the non-nonymous status of the stored user, even if no more\n    // credentials (federated or email/password) are linked to the user. If\n    // the user was previously anonymous, then use provider data to update.\n    // On the other hand, if it was not anonymous before, it should never be\n    // considered anonymous now.\n\n    const oldIsAnonymous = user.isAnonymous;\n    const newIsAnonymous = !(user.email && coreAccount.passwordHash) && !(providerData === null || providerData === void 0 ? void 0 : providerData.length);\n    const isAnonymous = !oldIsAnonymous ? false : newIsAnonymous;\n    const updates = {\n      uid: coreAccount.localId,\n      displayName: coreAccount.displayName || null,\n      photoURL: coreAccount.photoUrl || null,\n      email: coreAccount.email || null,\n      emailVerified: coreAccount.emailVerified || false,\n      phoneNumber: coreAccount.phoneNumber || null,\n      tenantId: coreAccount.tenantId || null,\n      providerData,\n      metadata: new UserMetadata(coreAccount.createdAt, coreAccount.lastLoginAt),\n      isAnonymous\n    };\n    Object.assign(user, updates);\n  });\n  return _reloadWithoutSaving2.apply(this, arguments);\n}\n\nfunction reload(_x22) {\n  return _reload.apply(this, arguments);\n}\n\nfunction _reload() {\n  _reload = _asyncToGenerator(function* (user) {\n    const userInternal = getModularInstance(user);\n    yield _reloadWithoutSaving(userInternal); // Even though the current user hasn't changed, update\n    // current user will trigger a persistence update w/ the\n    // new info.\n\n    yield userInternal.auth._persistUserIfCurrent(userInternal);\n\n    userInternal.auth._notifyListenersIfCurrent(userInternal);\n  });\n  return _reload.apply(this, arguments);\n}\n\nfunction mergeProviderData(original, newData) {\n  const deduped = original.filter(o => !newData.some(n => n.providerId === o.providerId));\n  return [...deduped, ...newData];\n}\n\nfunction extractProviderData(providers) {\n  return providers.map(_a => {\n    var {\n      providerId\n    } = _a,\n        provider = __rest(_a, [\"providerId\"]);\n\n    return {\n      providerId,\n      uid: provider.rawId || '',\n      displayName: provider.displayName || null,\n      email: provider.email || null,\n      phoneNumber: provider.phoneNumber || null,\n      photoURL: provider.photoUrl || null\n    };\n  });\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction requestStsToken(_x23, _x24) {\n  return _requestStsToken.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * We need to mark this class as internal explicitly to exclude it in the public typings, because\r\n * it references AuthInternal which has a circular dependency with UserInternal.\r\n *\r\n * @internal\r\n */\n\n\nfunction _requestStsToken() {\n  _requestStsToken = _asyncToGenerator(function* (auth, refreshToken) {\n    const response = yield _performFetchWithErrorHandling(auth, {}, /*#__PURE__*/_asyncToGenerator(function* () {\n      const body = querystring({\n        'grant_type': 'refresh_token',\n        'refresh_token': refreshToken\n      }).slice(1);\n      const {\n        tokenApiHost,\n        apiKey\n      } = auth.config;\n\n      const url = _getFinalTarget(auth, tokenApiHost, \"/v1/token\"\n      /* Endpoint.TOKEN */\n      , `key=${apiKey}`);\n\n      const headers = yield auth._getAdditionalHeaders();\n      headers[\"Content-Type\"\n      /* HttpHeader.CONTENT_TYPE */\n      ] = 'application/x-www-form-urlencoded';\n      return FetchProvider.fetch()(url, {\n        method: \"POST\"\n        /* HttpMethod.POST */\n        ,\n        headers,\n        body\n      });\n    })); // The response comes back in snake_case. Convert to camel:\n\n    return {\n      accessToken: response.access_token,\n      expiresIn: response.expires_in,\n      refreshToken: response.refresh_token\n    };\n  });\n  return _requestStsToken.apply(this, arguments);\n}\n\nclass StsTokenManager {\n  constructor() {\n    this.refreshToken = null;\n    this.accessToken = null;\n    this.expirationTime = null;\n  }\n\n  get isExpired() {\n    return !this.expirationTime || Date.now() > this.expirationTime - 30000\n    /* Buffer.TOKEN_REFRESH */\n    ;\n  }\n\n  updateFromServerResponse(response) {\n    _assert(response.idToken, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    _assert(typeof response.idToken !== 'undefined', \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    _assert(typeof response.refreshToken !== 'undefined', \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    const expiresIn = 'expiresIn' in response && typeof response.expiresIn !== 'undefined' ? Number(response.expiresIn) : _tokenExpiresIn(response.idToken);\n    this.updateTokensAndExpiration(response.idToken, response.refreshToken, expiresIn);\n  }\n\n  getToken(auth, forceRefresh = false) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      _assert(!_this3.accessToken || _this3.refreshToken, auth, \"user-token-expired\"\n      /* AuthErrorCode.TOKEN_EXPIRED */\n      );\n\n      if (!forceRefresh && _this3.accessToken && !_this3.isExpired) {\n        return _this3.accessToken;\n      }\n\n      if (_this3.refreshToken) {\n        yield _this3.refresh(auth, _this3.refreshToken);\n        return _this3.accessToken;\n      }\n\n      return null;\n    })();\n  }\n\n  clearRefreshToken() {\n    this.refreshToken = null;\n  }\n\n  refresh(auth, oldToken) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      const {\n        accessToken,\n        refreshToken,\n        expiresIn\n      } = yield requestStsToken(auth, oldToken);\n\n      _this4.updateTokensAndExpiration(accessToken, refreshToken, Number(expiresIn));\n    })();\n  }\n\n  updateTokensAndExpiration(accessToken, refreshToken, expiresInSec) {\n    this.refreshToken = refreshToken || null;\n    this.accessToken = accessToken || null;\n    this.expirationTime = Date.now() + expiresInSec * 1000;\n  }\n\n  static fromJSON(appName, object) {\n    const {\n      refreshToken,\n      accessToken,\n      expirationTime\n    } = object;\n    const manager = new StsTokenManager();\n\n    if (refreshToken) {\n      _assert(typeof refreshToken === 'string', \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      , {\n        appName\n      });\n\n      manager.refreshToken = refreshToken;\n    }\n\n    if (accessToken) {\n      _assert(typeof accessToken === 'string', \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      , {\n        appName\n      });\n\n      manager.accessToken = accessToken;\n    }\n\n    if (expirationTime) {\n      _assert(typeof expirationTime === 'number', \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      , {\n        appName\n      });\n\n      manager.expirationTime = expirationTime;\n    }\n\n    return manager;\n  }\n\n  toJSON() {\n    return {\n      refreshToken: this.refreshToken,\n      accessToken: this.accessToken,\n      expirationTime: this.expirationTime\n    };\n  }\n\n  _assign(stsTokenManager) {\n    this.accessToken = stsTokenManager.accessToken;\n    this.refreshToken = stsTokenManager.refreshToken;\n    this.expirationTime = stsTokenManager.expirationTime;\n  }\n\n  _clone() {\n    return Object.assign(new StsTokenManager(), this.toJSON());\n  }\n\n  _performRefresh() {\n    return debugFail('not implemented');\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction assertStringOrUndefined(assertion, appName) {\n  _assert(typeof assertion === 'string' || typeof assertion === 'undefined', \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  , {\n    appName\n  });\n}\n\nclass UserImpl {\n  constructor(_a) {\n    var {\n      uid,\n      auth,\n      stsTokenManager\n    } = _a,\n        opt = __rest(_a, [\"uid\", \"auth\", \"stsTokenManager\"]); // For the user object, provider is always Firebase.\n\n\n    this.providerId = \"firebase\"\n    /* ProviderId.FIREBASE */\n    ;\n    this.proactiveRefresh = new ProactiveRefresh(this);\n    this.reloadUserInfo = null;\n    this.reloadListener = null;\n    this.uid = uid;\n    this.auth = auth;\n    this.stsTokenManager = stsTokenManager;\n    this.accessToken = stsTokenManager.accessToken;\n    this.displayName = opt.displayName || null;\n    this.email = opt.email || null;\n    this.emailVerified = opt.emailVerified || false;\n    this.phoneNumber = opt.phoneNumber || null;\n    this.photoURL = opt.photoURL || null;\n    this.isAnonymous = opt.isAnonymous || false;\n    this.tenantId = opt.tenantId || null;\n    this.providerData = opt.providerData ? [...opt.providerData] : [];\n    this.metadata = new UserMetadata(opt.createdAt || undefined, opt.lastLoginAt || undefined);\n  }\n\n  getIdToken(forceRefresh) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      const accessToken = yield _logoutIfInvalidated(_this5, _this5.stsTokenManager.getToken(_this5.auth, forceRefresh));\n\n      _assert(accessToken, _this5.auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      if (_this5.accessToken !== accessToken) {\n        _this5.accessToken = accessToken;\n        yield _this5.auth._persistUserIfCurrent(_this5);\n\n        _this5.auth._notifyListenersIfCurrent(_this5);\n      }\n\n      return accessToken;\n    })();\n  }\n\n  getIdTokenResult(forceRefresh) {\n    return getIdTokenResult(this, forceRefresh);\n  }\n\n  reload() {\n    return reload(this);\n  }\n\n  _assign(user) {\n    if (this === user) {\n      return;\n    }\n\n    _assert(this.uid === user.uid, this.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    this.displayName = user.displayName;\n    this.photoURL = user.photoURL;\n    this.email = user.email;\n    this.emailVerified = user.emailVerified;\n    this.phoneNumber = user.phoneNumber;\n    this.isAnonymous = user.isAnonymous;\n    this.tenantId = user.tenantId;\n    this.providerData = user.providerData.map(userInfo => Object.assign({}, userInfo));\n\n    this.metadata._copy(user.metadata);\n\n    this.stsTokenManager._assign(user.stsTokenManager);\n  }\n\n  _clone(auth) {\n    const newUser = new UserImpl(Object.assign(Object.assign({}, this), {\n      auth,\n      stsTokenManager: this.stsTokenManager._clone()\n    }));\n\n    newUser.metadata._copy(this.metadata);\n\n    return newUser;\n  }\n\n  _onReload(callback) {\n    // There should only ever be one listener, and that is a single instance of MultiFactorUser\n    _assert(!this.reloadListener, this.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    this.reloadListener = callback;\n\n    if (this.reloadUserInfo) {\n      this._notifyReloadListener(this.reloadUserInfo);\n\n      this.reloadUserInfo = null;\n    }\n  }\n\n  _notifyReloadListener(userInfo) {\n    if (this.reloadListener) {\n      this.reloadListener(userInfo);\n    } else {\n      // If no listener is subscribed yet, save the result so it's available when they do subscribe\n      this.reloadUserInfo = userInfo;\n    }\n  }\n\n  _startProactiveRefresh() {\n    this.proactiveRefresh._start();\n  }\n\n  _stopProactiveRefresh() {\n    this.proactiveRefresh._stop();\n  }\n\n  _updateTokensIfNecessary(response, reload = false) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      let tokensRefreshed = false;\n\n      if (response.idToken && response.idToken !== _this6.stsTokenManager.accessToken) {\n        _this6.stsTokenManager.updateFromServerResponse(response);\n\n        tokensRefreshed = true;\n      }\n\n      if (reload) {\n        yield _reloadWithoutSaving(_this6);\n      }\n\n      yield _this6.auth._persistUserIfCurrent(_this6);\n\n      if (tokensRefreshed) {\n        _this6.auth._notifyListenersIfCurrent(_this6);\n      }\n    })();\n  }\n\n  delete() {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      const idToken = yield _this7.getIdToken();\n      yield _logoutIfInvalidated(_this7, deleteAccount(_this7.auth, {\n        idToken\n      }));\n\n      _this7.stsTokenManager.clearRefreshToken(); // TODO: Determine if cancellable-promises are necessary to use in this class so that delete()\n      //       cancels pending actions...\n\n\n      return _this7.auth.signOut();\n    })();\n  }\n\n  toJSON() {\n    return Object.assign(Object.assign({\n      uid: this.uid,\n      email: this.email || undefined,\n      emailVerified: this.emailVerified,\n      displayName: this.displayName || undefined,\n      isAnonymous: this.isAnonymous,\n      photoURL: this.photoURL || undefined,\n      phoneNumber: this.phoneNumber || undefined,\n      tenantId: this.tenantId || undefined,\n      providerData: this.providerData.map(userInfo => Object.assign({}, userInfo)),\n      stsTokenManager: this.stsTokenManager.toJSON(),\n      // Redirect event ID must be maintained in case there is a pending\n      // redirect event.\n      _redirectEventId: this._redirectEventId\n    }, this.metadata.toJSON()), {\n      // Required for compatibility with the legacy SDK (go/firebase-auth-sdk-persistence-parsing):\n      apiKey: this.auth.config.apiKey,\n      appName: this.auth.name\n    });\n  }\n\n  get refreshToken() {\n    return this.stsTokenManager.refreshToken || '';\n  }\n\n  static _fromJSON(auth, object) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n\n    const displayName = (_a = object.displayName) !== null && _a !== void 0 ? _a : undefined;\n    const email = (_b = object.email) !== null && _b !== void 0 ? _b : undefined;\n    const phoneNumber = (_c = object.phoneNumber) !== null && _c !== void 0 ? _c : undefined;\n    const photoURL = (_d = object.photoURL) !== null && _d !== void 0 ? _d : undefined;\n    const tenantId = (_e = object.tenantId) !== null && _e !== void 0 ? _e : undefined;\n\n    const _redirectEventId = (_f = object._redirectEventId) !== null && _f !== void 0 ? _f : undefined;\n\n    const createdAt = (_g = object.createdAt) !== null && _g !== void 0 ? _g : undefined;\n    const lastLoginAt = (_h = object.lastLoginAt) !== null && _h !== void 0 ? _h : undefined;\n    const {\n      uid,\n      emailVerified,\n      isAnonymous,\n      providerData,\n      stsTokenManager: plainObjectTokenManager\n    } = object;\n\n    _assert(uid && plainObjectTokenManager, auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    const stsTokenManager = StsTokenManager.fromJSON(this.name, plainObjectTokenManager);\n\n    _assert(typeof uid === 'string', auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    assertStringOrUndefined(displayName, auth.name);\n    assertStringOrUndefined(email, auth.name);\n\n    _assert(typeof emailVerified === 'boolean', auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    _assert(typeof isAnonymous === 'boolean', auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    assertStringOrUndefined(phoneNumber, auth.name);\n    assertStringOrUndefined(photoURL, auth.name);\n    assertStringOrUndefined(tenantId, auth.name);\n    assertStringOrUndefined(_redirectEventId, auth.name);\n    assertStringOrUndefined(createdAt, auth.name);\n    assertStringOrUndefined(lastLoginAt, auth.name);\n    const user = new UserImpl({\n      uid,\n      auth,\n      email,\n      emailVerified,\n      displayName,\n      isAnonymous,\n      photoURL,\n      phoneNumber,\n      tenantId,\n      stsTokenManager,\n      createdAt,\n      lastLoginAt\n    });\n\n    if (providerData && Array.isArray(providerData)) {\n      user.providerData = providerData.map(userInfo => Object.assign({}, userInfo));\n    }\n\n    if (_redirectEventId) {\n      user._redirectEventId = _redirectEventId;\n    }\n\n    return user;\n  }\n  /**\r\n   * Initialize a User from an idToken server response\r\n   * @param auth\r\n   * @param idTokenResponse\r\n   */\n\n\n  static _fromIdTokenResponse(auth, idTokenResponse, isAnonymous = false) {\n    return _asyncToGenerator(function* () {\n      const stsTokenManager = new StsTokenManager();\n      stsTokenManager.updateFromServerResponse(idTokenResponse); // Initialize the Firebase Auth user.\n\n      const user = new UserImpl({\n        uid: idTokenResponse.localId,\n        auth,\n        stsTokenManager,\n        isAnonymous\n      }); // Updates the user info and data and resolves with a user instance.\n\n      yield _reloadWithoutSaving(user);\n      return user;\n    })();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst instanceCache = new Map();\n\nfunction _getInstance(cls) {\n  debugAssert(cls instanceof Function, 'Expected a class definition');\n  let instance = instanceCache.get(cls);\n\n  if (instance) {\n    debugAssert(instance instanceof cls, 'Instance stored in cache mismatched with class');\n    return instance;\n  }\n\n  instance = new cls();\n  instanceCache.set(cls, instance);\n  return instance;\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nlet InMemoryPersistence = /*#__PURE__*/(() => {\n  class InMemoryPersistence {\n    constructor() {\n      this.type = \"NONE\"\n      /* PersistenceType.NONE */\n      ;\n      this.storage = {};\n    }\n\n    _isAvailable() {\n      return _asyncToGenerator(function* () {\n        return true;\n      })();\n    }\n\n    _set(key, value) {\n      var _this8 = this;\n\n      return _asyncToGenerator(function* () {\n        _this8.storage[key] = value;\n      })();\n    }\n\n    _get(key) {\n      var _this9 = this;\n\n      return _asyncToGenerator(function* () {\n        const value = _this9.storage[key];\n        return value === undefined ? null : value;\n      })();\n    }\n\n    _remove(key) {\n      var _this10 = this;\n\n      return _asyncToGenerator(function* () {\n        delete _this10.storage[key];\n      })();\n    }\n\n    _addListener(_key, _listener) {\n      // Listeners are not supported for in-memory storage since it cannot be shared across windows/workers\n      return;\n    }\n\n    _removeListener(_key, _listener) {\n      // Listeners are not supported for in-memory storage since it cannot be shared across windows/workers\n      return;\n    }\n\n  }\n\n  InMemoryPersistence.type = 'NONE';\n  /**\r\n   * An implementation of {@link Persistence} of type 'NONE'.\r\n   *\r\n   * @public\r\n   */\n\n  return InMemoryPersistence;\n})();\nconst inMemoryPersistence = InMemoryPersistence;\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nfunction _persistenceKeyName(key, apiKey, appName) {\n  return `${\"firebase\"\n  /* Namespace.PERSISTENCE */\n  }:${key}:${apiKey}:${appName}`;\n}\n\nclass PersistenceUserManager {\n  constructor(persistence, auth, userKey) {\n    this.persistence = persistence;\n    this.auth = auth;\n    this.userKey = userKey;\n    const {\n      config,\n      name\n    } = this.auth;\n    this.fullUserKey = _persistenceKeyName(this.userKey, config.apiKey, name);\n    this.fullPersistenceKey = _persistenceKeyName(\"persistence\"\n    /* KeyName.PERSISTENCE_USER */\n    , config.apiKey, name);\n    this.boundEventHandler = auth._onStorageEvent.bind(auth);\n\n    this.persistence._addListener(this.fullUserKey, this.boundEventHandler);\n  }\n\n  setCurrentUser(user) {\n    return this.persistence._set(this.fullUserKey, user.toJSON());\n  }\n\n  getCurrentUser() {\n    var _this11 = this;\n\n    return _asyncToGenerator(function* () {\n      const blob = yield _this11.persistence._get(_this11.fullUserKey);\n      return blob ? UserImpl._fromJSON(_this11.auth, blob) : null;\n    })();\n  }\n\n  removeCurrentUser() {\n    return this.persistence._remove(this.fullUserKey);\n  }\n\n  savePersistenceForRedirect() {\n    return this.persistence._set(this.fullPersistenceKey, this.persistence.type);\n  }\n\n  setPersistence(newPersistence) {\n    var _this12 = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this12.persistence === newPersistence) {\n        return;\n      }\n\n      const currentUser = yield _this12.getCurrentUser();\n      yield _this12.removeCurrentUser();\n      _this12.persistence = newPersistence;\n\n      if (currentUser) {\n        return _this12.setCurrentUser(currentUser);\n      }\n    })();\n  }\n\n  delete() {\n    this.persistence._removeListener(this.fullUserKey, this.boundEventHandler);\n  }\n\n  static create(auth, persistenceHierarchy, userKey = \"authUser\"\n  /* KeyName.AUTH_USER */\n  ) {\n    return _asyncToGenerator(function* () {\n      if (!persistenceHierarchy.length) {\n        return new PersistenceUserManager(_getInstance(inMemoryPersistence), auth, userKey);\n      } // Eliminate any persistences that are not available\n\n\n      const availablePersistences = (yield Promise.all(persistenceHierarchy.map( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (persistence) {\n          if (yield persistence._isAvailable()) {\n            return persistence;\n          }\n\n          return undefined;\n        });\n\n        return function (_x25) {\n          return _ref2.apply(this, arguments);\n        };\n      }()))).filter(persistence => persistence); // Fall back to the first persistence listed, or in memory if none available\n\n      let selectedPersistence = availablePersistences[0] || _getInstance(inMemoryPersistence);\n\n      const key = _persistenceKeyName(userKey, auth.config.apiKey, auth.name); // Pull out the existing user, setting the chosen persistence to that\n      // persistence if the user exists.\n\n\n      let userToMigrate = null; // Note, here we check for a user in _all_ persistences, not just the\n      // ones deemed available. If we can migrate a user out of a broken\n      // persistence, we will (but only if that persistence supports migration).\n\n      for (const persistence of persistenceHierarchy) {\n        try {\n          const blob = yield persistence._get(key);\n\n          if (blob) {\n            const user = UserImpl._fromJSON(auth, blob); // throws for unparsable blob (wrong format)\n\n\n            if (persistence !== selectedPersistence) {\n              userToMigrate = user;\n            }\n\n            selectedPersistence = persistence;\n            break;\n          }\n        } catch (_a) {}\n      } // If we find the user in a persistence that does support migration, use\n      // that migration path (of only persistences that support migration)\n\n\n      const migrationHierarchy = availablePersistences.filter(p => p._shouldAllowMigration); // If the persistence does _not_ allow migration, just finish off here\n\n      if (!selectedPersistence._shouldAllowMigration || !migrationHierarchy.length) {\n        return new PersistenceUserManager(selectedPersistence, auth, userKey);\n      }\n\n      selectedPersistence = migrationHierarchy[0];\n\n      if (userToMigrate) {\n        // This normally shouldn't throw since chosenPersistence.isAvailable() is true, but if it does\n        // we'll just let it bubble to surface the error.\n        yield selectedPersistence._set(key, userToMigrate.toJSON());\n      } // Attempt to clear the key in other persistences but ignore errors. This helps prevent issues\n      // such as users getting stuck with a previous account after signing out and refreshing the tab.\n\n\n      yield Promise.all(persistenceHierarchy.map( /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (persistence) {\n          if (persistence !== selectedPersistence) {\n            try {\n              yield persistence._remove(key);\n            } catch (_a) {}\n          }\n        });\n\n        return function (_x26) {\n          return _ref3.apply(this, arguments);\n        };\n      }()));\n      return new PersistenceUserManager(selectedPersistence, auth, userKey);\n    })();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Determine the browser for the purposes of reporting usage to the API\r\n */\n\n\nfunction _getBrowserName(userAgent) {\n  const ua = userAgent.toLowerCase();\n\n  if (ua.includes('opera/') || ua.includes('opr/') || ua.includes('opios/')) {\n    return \"Opera\"\n    /* BrowserName.OPERA */\n    ;\n  } else if (_isIEMobile(ua)) {\n    // Windows phone IEMobile browser.\n    return \"IEMobile\"\n    /* BrowserName.IEMOBILE */\n    ;\n  } else if (ua.includes('msie') || ua.includes('trident/')) {\n    return \"IE\"\n    /* BrowserName.IE */\n    ;\n  } else if (ua.includes('edge/')) {\n    return \"Edge\"\n    /* BrowserName.EDGE */\n    ;\n  } else if (_isFirefox(ua)) {\n    return \"Firefox\"\n    /* BrowserName.FIREFOX */\n    ;\n  } else if (ua.includes('silk/')) {\n    return \"Silk\"\n    /* BrowserName.SILK */\n    ;\n  } else if (_isBlackBerry(ua)) {\n    // Blackberry browser.\n    return \"Blackberry\"\n    /* BrowserName.BLACKBERRY */\n    ;\n  } else if (_isWebOS(ua)) {\n    // WebOS default browser.\n    return \"Webos\"\n    /* BrowserName.WEBOS */\n    ;\n  } else if (_isSafari(ua)) {\n    return \"Safari\"\n    /* BrowserName.SAFARI */\n    ;\n  } else if ((ua.includes('chrome/') || _isChromeIOS(ua)) && !ua.includes('edge/')) {\n    return \"Chrome\"\n    /* BrowserName.CHROME */\n    ;\n  } else if (_isAndroid(ua)) {\n    // Android stock browser.\n    return \"Android\"\n    /* BrowserName.ANDROID */\n    ;\n  } else {\n    // Most modern browsers have name/version at end of user agent string.\n    const re = /([a-zA-Z\\d\\.]+)\\/[a-zA-Z\\d\\.]*$/;\n    const matches = userAgent.match(re);\n\n    if ((matches === null || matches === void 0 ? void 0 : matches.length) === 2) {\n      return matches[1];\n    }\n  }\n\n  return \"Other\"\n  /* BrowserName.OTHER */\n  ;\n}\n\nfunction _isFirefox(ua = getUA()) {\n  return /firefox\\//i.test(ua);\n}\n\nfunction _isSafari(userAgent = getUA()) {\n  const ua = userAgent.toLowerCase();\n  return ua.includes('safari/') && !ua.includes('chrome/') && !ua.includes('crios/') && !ua.includes('android');\n}\n\nfunction _isChromeIOS(ua = getUA()) {\n  return /crios\\//i.test(ua);\n}\n\nfunction _isIEMobile(ua = getUA()) {\n  return /iemobile/i.test(ua);\n}\n\nfunction _isAndroid(ua = getUA()) {\n  return /android/i.test(ua);\n}\n\nfunction _isBlackBerry(ua = getUA()) {\n  return /blackberry/i.test(ua);\n}\n\nfunction _isWebOS(ua = getUA()) {\n  return /webos/i.test(ua);\n}\n\nfunction _isIOS(ua = getUA()) {\n  return /iphone|ipad|ipod/i.test(ua) || /macintosh/i.test(ua) && /mobile/i.test(ua);\n}\n\nfunction _isIOS7Or8(ua = getUA()) {\n  return /(iPad|iPhone|iPod).*OS 7_\\d/i.test(ua) || /(iPad|iPhone|iPod).*OS 8_\\d/i.test(ua);\n}\n\nfunction _isIOSStandalone(ua = getUA()) {\n  var _a;\n\n  return _isIOS(ua) && !!((_a = window.navigator) === null || _a === void 0 ? void 0 : _a.standalone);\n}\n\nfunction _isIE10() {\n  return isIE() && document.documentMode === 10;\n}\n\nfunction _isMobileBrowser(ua = getUA()) {\n  // TODO: implement getBrowserName equivalent for OS.\n  return _isIOS(ua) || _isAndroid(ua) || _isWebOS(ua) || _isBlackBerry(ua) || /windows phone/i.test(ua) || _isIEMobile(ua);\n}\n\nfunction _isIframe() {\n  try {\n    // Check that the current window is not the top window.\n    // If so, return true.\n    return !!(window && window !== window.top);\n  } catch (e) {\n    return false;\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/*\r\n * Determine the SDK version string\r\n */\n\n\nfunction _getClientVersion(clientPlatform, frameworks = []) {\n  let reportedPlatform;\n\n  switch (clientPlatform) {\n    case \"Browser\"\n    /* ClientPlatform.BROWSER */\n    :\n      // In a browser environment, report the browser name.\n      reportedPlatform = _getBrowserName(getUA());\n      break;\n\n    case \"Worker\"\n    /* ClientPlatform.WORKER */\n    :\n      // Technically a worker runs from a browser but we need to differentiate a\n      // worker from a browser.\n      // For example: Chrome-Worker/JsCore/4.9.1/FirebaseCore-web.\n      reportedPlatform = `${_getBrowserName(getUA())}-${clientPlatform}`;\n      break;\n\n    default:\n      reportedPlatform = clientPlatform;\n  }\n\n  const reportedFrameworks = frameworks.length ? frameworks.join(',') : 'FirebaseCore-web';\n  /* default value if no other framework is used */\n\n  return `${reportedPlatform}/${\"JsCore\"\n  /* ClientImplementation.CORE */\n  }/${SDK_VERSION}/${reportedFrameworks}`;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction getRecaptchaParams(_x27) {\n  return _getRecaptchaParams.apply(this, arguments);\n}\n\nfunction _getRecaptchaParams() {\n  _getRecaptchaParams = _asyncToGenerator(function* (auth) {\n    return (yield _performApiRequest(auth, \"GET\"\n    /* HttpMethod.GET */\n    , \"/v1/recaptchaParams\"\n    /* Endpoint.GET_RECAPTCHA_PARAM */\n    )).recaptchaSiteKey || '';\n  });\n  return _getRecaptchaParams.apply(this, arguments);\n}\n\nfunction getRecaptchaConfig(_x28, _x29) {\n  return _getRecaptchaConfig.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _getRecaptchaConfig() {\n  _getRecaptchaConfig = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"GET\"\n    /* HttpMethod.GET */\n    , \"/v2/recaptchaConfig\"\n    /* Endpoint.GET_RECAPTCHA_CONFIG */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _getRecaptchaConfig.apply(this, arguments);\n}\n\nfunction isV2(grecaptcha) {\n  return grecaptcha !== undefined && grecaptcha.getResponse !== undefined;\n}\n\nfunction isEnterprise(grecaptcha) {\n  return grecaptcha !== undefined && grecaptcha.enterprise !== undefined;\n}\n\nclass RecaptchaConfig {\n  constructor(response) {\n    /**\r\n     * The reCAPTCHA site key.\r\n     */\n    this.siteKey = '';\n    /**\r\n     * The reCAPTCHA enablement status of the {@link EmailAuthProvider} for the current tenant.\r\n     */\n\n    this.emailPasswordEnabled = false;\n\n    if (response.recaptchaKey === undefined) {\n      throw new Error('recaptchaKey undefined');\n    } // Example response.recaptchaKey: \"projects/proj123/keys/sitekey123\"\n\n\n    this.siteKey = response.recaptchaKey.split('/')[3];\n    this.emailPasswordEnabled = response.recaptchaEnforcementState.some(enforcementState => enforcementState.provider === 'EMAIL_PASSWORD_PROVIDER' && enforcementState.enforcementState !== 'OFF');\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction getScriptParentElement() {\n  var _a, _b;\n\n  return (_b = (_a = document.getElementsByTagName('head')) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : document;\n}\n\nfunction _loadJS(url) {\n  // TODO: consider adding timeout support & cancellation\n  return new Promise((resolve, reject) => {\n    const el = document.createElement('script');\n    el.setAttribute('src', url);\n    el.onload = resolve;\n\n    el.onerror = e => {\n      const error = _createError(\"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      error.customData = e;\n      reject(error);\n    };\n\n    el.type = 'text/javascript';\n    el.charset = 'UTF-8';\n    getScriptParentElement().appendChild(el);\n  });\n}\n\nfunction _generateCallbackName(prefix) {\n  return `__${prefix}${Math.floor(Math.random() * 1000000)}`;\n}\n/* eslint-disable @typescript-eslint/no-require-imports */\n\n\nconst RECAPTCHA_ENTERPRISE_URL = 'https://www.google.com/recaptcha/enterprise.js?render=';\nconst RECAPTCHA_ENTERPRISE_VERIFIER_TYPE = 'recaptcha-enterprise';\nconst FAKE_TOKEN = 'NO_RECAPTCHA';\n\nclass RecaptchaEnterpriseVerifier {\n  /**\r\n   *\r\n   * @param authExtern - The corresponding Firebase {@link Auth} instance.\r\n   *\r\n   */\n  constructor(authExtern) {\n    /**\r\n     * Identifies the type of application verifier (e.g. \"recaptcha-enterprise\").\r\n     */\n    this.type = RECAPTCHA_ENTERPRISE_VERIFIER_TYPE;\n    this.auth = _castAuth(authExtern);\n  }\n  /**\r\n   * Executes the verification process.\r\n   *\r\n   * @returns A Promise for a token that can be used to assert the validity of a request.\r\n   */\n\n\n  verify(action = 'verify', forceRefresh = false) {\n    var _this13 = this;\n\n    return _asyncToGenerator(function* () {\n      function retrieveSiteKey(_x30) {\n        return _retrieveSiteKey.apply(this, arguments);\n      }\n\n      function _retrieveSiteKey() {\n        _retrieveSiteKey = _asyncToGenerator(function* (auth) {\n          if (!forceRefresh) {\n            if (auth.tenantId == null && auth._agentRecaptchaConfig != null) {\n              return auth._agentRecaptchaConfig.siteKey;\n            }\n\n            if (auth.tenantId != null && auth._tenantRecaptchaConfigs[auth.tenantId] !== undefined) {\n              return auth._tenantRecaptchaConfigs[auth.tenantId].siteKey;\n            }\n          }\n\n          return new Promise( /*#__PURE__*/function () {\n            var _ref4 = _asyncToGenerator(function* (resolve, reject) {\n              getRecaptchaConfig(auth, {\n                clientType: \"CLIENT_TYPE_WEB\"\n                /* RecaptchaClientType.WEB */\n                ,\n                version: \"RECAPTCHA_ENTERPRISE\"\n                /* RecaptchaVersion.ENTERPRISE */\n\n              }).then(response => {\n                if (response.recaptchaKey === undefined) {\n                  reject(new Error('recaptcha Enterprise site key undefined'));\n                } else {\n                  const config = new RecaptchaConfig(response);\n\n                  if (auth.tenantId == null) {\n                    auth._agentRecaptchaConfig = config;\n                  } else {\n                    auth._tenantRecaptchaConfigs[auth.tenantId] = config;\n                  }\n\n                  return resolve(config.siteKey);\n                }\n              }).catch(error => {\n                reject(error);\n              });\n            });\n\n            return function (_x31, _x32) {\n              return _ref4.apply(this, arguments);\n            };\n          }());\n        });\n        return _retrieveSiteKey.apply(this, arguments);\n      }\n\n      function retrieveRecaptchaToken(siteKey, resolve, reject) {\n        const grecaptcha = window.grecaptcha;\n\n        if (isEnterprise(grecaptcha)) {\n          grecaptcha.enterprise.ready(() => {\n            grecaptcha.enterprise.execute(siteKey, {\n              action\n            }).then(token => {\n              resolve(token);\n            }).catch(() => {\n              resolve(FAKE_TOKEN);\n            });\n          });\n        } else {\n          reject(Error('No reCAPTCHA enterprise script loaded.'));\n        }\n      }\n\n      return new Promise((resolve, reject) => {\n        retrieveSiteKey(_this13.auth).then(siteKey => {\n          if (!forceRefresh && isEnterprise(window.grecaptcha)) {\n            retrieveRecaptchaToken(siteKey, resolve, reject);\n          } else {\n            if (typeof window === 'undefined') {\n              reject(new Error('RecaptchaVerifier is only supported in browser'));\n              return;\n            }\n\n            _loadJS(RECAPTCHA_ENTERPRISE_URL + siteKey).then(() => {\n              retrieveRecaptchaToken(siteKey, resolve, reject);\n            }).catch(error => {\n              reject(error);\n            });\n          }\n        }).catch(error => {\n          reject(error);\n        });\n      });\n    })();\n  }\n\n}\n\nfunction injectRecaptchaFields(_x33, _x34, _x35) {\n  return _injectRecaptchaFields.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _injectRecaptchaFields() {\n  _injectRecaptchaFields = _asyncToGenerator(function* (auth, request, action, captchaResp = false) {\n    const verifier = new RecaptchaEnterpriseVerifier(auth);\n    let captchaResponse;\n\n    try {\n      captchaResponse = yield verifier.verify(action);\n    } catch (error) {\n      captchaResponse = yield verifier.verify(action, true);\n    }\n\n    const newRequest = Object.assign({}, request);\n\n    if (!captchaResp) {\n      Object.assign(newRequest, {\n        captchaResponse\n      });\n    } else {\n      Object.assign(newRequest, {\n        'captchaResp': captchaResponse\n      });\n    }\n\n    Object.assign(newRequest, {\n      'clientType': \"CLIENT_TYPE_WEB\"\n      /* RecaptchaClientType.WEB */\n\n    });\n    Object.assign(newRequest, {\n      'recaptchaVersion': \"RECAPTCHA_ENTERPRISE\"\n      /* RecaptchaVersion.ENTERPRISE */\n\n    });\n    return newRequest;\n  });\n  return _injectRecaptchaFields.apply(this, arguments);\n}\n\nclass AuthMiddlewareQueue {\n  constructor(auth) {\n    this.auth = auth;\n    this.queue = [];\n  }\n\n  pushCallback(callback, onAbort) {\n    // The callback could be sync or async. Wrap it into a\n    // function that is always async.\n    const wrappedCallback = user => new Promise((resolve, reject) => {\n      try {\n        const result = callback(user); // Either resolve with existing promise or wrap a non-promise\n        // return value into a promise.\n\n        resolve(result);\n      } catch (e) {\n        // Sync callback throws.\n        reject(e);\n      }\n    }); // Attach the onAbort if present\n\n\n    wrappedCallback.onAbort = onAbort;\n    this.queue.push(wrappedCallback);\n    const index = this.queue.length - 1;\n    return () => {\n      // Unsubscribe. Replace with no-op. Do not remove from array, or it will disturb\n      // indexing of other elements.\n      this.queue[index] = () => Promise.resolve();\n    };\n  }\n\n  runMiddleware(nextUser) {\n    var _this14 = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this14.auth.currentUser === nextUser) {\n        return;\n      } // While running the middleware, build a temporary stack of onAbort\n      // callbacks to call if one middleware callback rejects.\n\n\n      const onAbortStack = [];\n\n      try {\n        for (const beforeStateCallback of _this14.queue) {\n          yield beforeStateCallback(nextUser); // Only push the onAbort if the callback succeeds\n\n          if (beforeStateCallback.onAbort) {\n            onAbortStack.push(beforeStateCallback.onAbort);\n          }\n        }\n      } catch (e) {\n        // Run all onAbort, with separate try/catch to ignore any errors and\n        // continue\n        onAbortStack.reverse();\n\n        for (const onAbort of onAbortStack) {\n          try {\n            onAbort();\n          } catch (_) {\n            /* swallow error */\n          }\n        }\n\n        throw _this14.auth._errorFactory.create(\"login-blocked\"\n        /* AuthErrorCode.LOGIN_BLOCKED */\n        , {\n          originalMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n      }\n    })();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass AuthImpl {\n  constructor(app, heartbeatServiceProvider, appCheckServiceProvider, config) {\n    this.app = app;\n    this.heartbeatServiceProvider = heartbeatServiceProvider;\n    this.appCheckServiceProvider = appCheckServiceProvider;\n    this.config = config;\n    this.currentUser = null;\n    this.emulatorConfig = null;\n    this.operations = Promise.resolve();\n    this.authStateSubscription = new Subscription(this);\n    this.idTokenSubscription = new Subscription(this);\n    this.beforeStateQueue = new AuthMiddlewareQueue(this);\n    this.redirectUser = null;\n    this.isProactiveRefreshEnabled = false; // Any network calls will set this to true and prevent subsequent emulator\n    // initialization\n\n    this._canInitEmulator = true;\n    this._isInitialized = false;\n    this._deleted = false;\n    this._initializationPromise = null;\n    this._popupRedirectResolver = null;\n    this._errorFactory = _DEFAULT_AUTH_ERROR_FACTORY;\n    this._agentRecaptchaConfig = null;\n    this._tenantRecaptchaConfigs = {}; // Tracks the last notified UID for state change listeners to prevent\n    // repeated calls to the callbacks. Undefined means it's never been\n    // called, whereas null means it's been called with a signed out user\n\n    this.lastNotifiedUid = undefined;\n    this.languageCode = null;\n    this.tenantId = null;\n    this.settings = {\n      appVerificationDisabledForTesting: false\n    };\n    this.frameworks = [];\n    this.name = app.name;\n    this.clientVersion = config.sdkClientVersion;\n  }\n\n  _initializeWithPersistence(persistenceHierarchy, popupRedirectResolver) {\n    var _this15 = this;\n\n    if (popupRedirectResolver) {\n      this._popupRedirectResolver = _getInstance(popupRedirectResolver);\n    } // Have to check for app deletion throughout initialization (after each\n    // promise resolution)\n\n\n    this._initializationPromise = this.queue( /*#__PURE__*/_asyncToGenerator(function* () {\n      var _a, _b;\n\n      if (_this15._deleted) {\n        return;\n      }\n\n      _this15.persistenceManager = yield PersistenceUserManager.create(_this15, persistenceHierarchy);\n\n      if (_this15._deleted) {\n        return;\n      } // Initialize the resolver early if necessary (only applicable to web:\n      // this will cause the iframe to load immediately in certain cases)\n\n\n      if ((_a = _this15._popupRedirectResolver) === null || _a === void 0 ? void 0 : _a._shouldInitProactively) {\n        // If this fails, don't halt auth loading\n        try {\n          yield _this15._popupRedirectResolver._initialize(_this15);\n        } catch (e) {\n          /* Ignore the error */\n        }\n      }\n\n      yield _this15.initializeCurrentUser(popupRedirectResolver);\n      _this15.lastNotifiedUid = ((_b = _this15.currentUser) === null || _b === void 0 ? void 0 : _b.uid) || null;\n\n      if (_this15._deleted) {\n        return;\n      }\n\n      _this15._isInitialized = true;\n    }));\n    return this._initializationPromise;\n  }\n  /**\r\n   * If the persistence is changed in another window, the user manager will let us know\r\n   */\n\n\n  _onStorageEvent() {\n    var _this16 = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this16._deleted) {\n        return;\n      }\n\n      const user = yield _this16.assertedPersistence.getCurrentUser();\n\n      if (!_this16.currentUser && !user) {\n        // No change, do nothing (was signed out and remained signed out).\n        return;\n      } // If the same user is to be synchronized.\n\n\n      if (_this16.currentUser && user && _this16.currentUser.uid === user.uid) {\n        // Data update, simply copy data changes.\n        _this16._currentUser._assign(user); // If tokens changed from previous user tokens, this will trigger\n        // notifyAuthListeners_.\n\n\n        yield _this16.currentUser.getIdToken();\n        return;\n      } // Update current Auth state. Either a new login or logout.\n      // Skip blocking callbacks, they should not apply to a change in another tab.\n\n\n      yield _this16._updateCurrentUser(user,\n      /* skipBeforeStateCallbacks */\n      true);\n    })();\n  }\n\n  initializeCurrentUser(popupRedirectResolver) {\n    var _this17 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a; // First check to see if we have a pending redirect event.\n\n\n      const previouslyStoredUser = yield _this17.assertedPersistence.getCurrentUser();\n      let futureCurrentUser = previouslyStoredUser;\n      let needsTocheckMiddleware = false;\n\n      if (popupRedirectResolver && _this17.config.authDomain) {\n        yield _this17.getOrInitRedirectPersistenceManager();\n        const redirectUserEventId = (_a = _this17.redirectUser) === null || _a === void 0 ? void 0 : _a._redirectEventId;\n        const storedUserEventId = futureCurrentUser === null || futureCurrentUser === void 0 ? void 0 : futureCurrentUser._redirectEventId;\n        const result = yield _this17.tryRedirectSignIn(popupRedirectResolver); // If the stored user (i.e. the old \"currentUser\") has a redirectId that\n        // matches the redirect user, then we want to initially sign in with the\n        // new user object from result.\n        // TODO(samgho): More thoroughly test all of this\n\n        if ((!redirectUserEventId || redirectUserEventId === storedUserEventId) && (result === null || result === void 0 ? void 0 : result.user)) {\n          futureCurrentUser = result.user;\n          needsTocheckMiddleware = true;\n        }\n      } // If no user in persistence, there is no current user. Set to null.\n\n\n      if (!futureCurrentUser) {\n        return _this17.directlySetCurrentUser(null);\n      }\n\n      if (!futureCurrentUser._redirectEventId) {\n        // This isn't a redirect link operation, we can reload and bail.\n        // First though, ensure that we check the middleware is happy.\n        if (needsTocheckMiddleware) {\n          try {\n            yield _this17.beforeStateQueue.runMiddleware(futureCurrentUser);\n          } catch (e) {\n            futureCurrentUser = previouslyStoredUser; // We know this is available since the bit is only set when the\n            // resolver is available\n\n            _this17._popupRedirectResolver._overrideRedirectResult(_this17, () => Promise.reject(e));\n          }\n        }\n\n        if (futureCurrentUser) {\n          return _this17.reloadAndSetCurrentUserOrClear(futureCurrentUser);\n        } else {\n          return _this17.directlySetCurrentUser(null);\n        }\n      }\n\n      _assert(_this17._popupRedirectResolver, _this17, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      yield _this17.getOrInitRedirectPersistenceManager(); // If the redirect user's event ID matches the current user's event ID,\n      // DO NOT reload the current user, otherwise they'll be cleared from storage.\n      // This is important for the reauthenticateWithRedirect() flow.\n\n      if (_this17.redirectUser && _this17.redirectUser._redirectEventId === futureCurrentUser._redirectEventId) {\n        return _this17.directlySetCurrentUser(futureCurrentUser);\n      }\n\n      return _this17.reloadAndSetCurrentUserOrClear(futureCurrentUser);\n    })();\n  }\n\n  tryRedirectSignIn(redirectResolver) {\n    var _this18 = this;\n\n    return _asyncToGenerator(function* () {\n      // The redirect user needs to be checked (and signed in if available)\n      // during auth initialization. All of the normal sign in and link/reauth\n      // flows call back into auth and push things onto the promise queue. We\n      // need to await the result of the redirect sign in *inside the promise\n      // queue*. This presents a problem: we run into deadlock. See:\n      //    ┌> [Initialization] ─────┐\n      //    ┌> [<other queue tasks>] │\n      //    └─ [getRedirectResult] <─┘\n      //    where [] are tasks on the queue and arrows denote awaits\n      // Initialization will never complete because it's waiting on something\n      // that's waiting for initialization to complete!\n      //\n      // Instead, this method calls getRedirectResult() (stored in\n      // _completeRedirectFn) with an optional parameter that instructs all of\n      // the underlying auth operations to skip anything that mutates auth state.\n      let result = null;\n\n      try {\n        // We know this._popupRedirectResolver is set since redirectResolver\n        // is passed in. The _completeRedirectFn expects the unwrapped extern.\n        result = yield _this18._popupRedirectResolver._completeRedirectFn(_this18, redirectResolver, true);\n      } catch (e) {\n        // Swallow any errors here; the code can retrieve them in\n        // getRedirectResult().\n        yield _this18._setRedirectUser(null);\n      }\n\n      return result;\n    })();\n  }\n\n  reloadAndSetCurrentUserOrClear(user) {\n    var _this19 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _reloadWithoutSaving(user);\n      } catch (e) {\n        if ((e === null || e === void 0 ? void 0 : e.code) !== `auth/${\"network-request-failed\"\n        /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n        }`) {\n          // Something's wrong with the user's token. Log them out and remove\n          // them from storage\n          return _this19.directlySetCurrentUser(null);\n        }\n      }\n\n      return _this19.directlySetCurrentUser(user);\n    })();\n  }\n\n  useDeviceLanguage() {\n    this.languageCode = _getUserLanguage();\n  }\n\n  _delete() {\n    var _this20 = this;\n\n    return _asyncToGenerator(function* () {\n      _this20._deleted = true;\n    })();\n  }\n\n  updateCurrentUser(userExtern) {\n    var _this21 = this;\n\n    return _asyncToGenerator(function* () {\n      // The public updateCurrentUser method needs to make a copy of the user,\n      // and also check that the project matches\n      const user = userExtern ? getModularInstance(userExtern) : null;\n\n      if (user) {\n        _assert(user.auth.config.apiKey === _this21.config.apiKey, _this21, \"invalid-user-token\"\n        /* AuthErrorCode.INVALID_AUTH */\n        );\n      }\n\n      return _this21._updateCurrentUser(user && user._clone(_this21));\n    })();\n  }\n\n  _updateCurrentUser(user, skipBeforeStateCallbacks = false) {\n    var _this22 = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this22._deleted) {\n        return;\n      }\n\n      if (user) {\n        _assert(_this22.tenantId === user.tenantId, _this22, \"tenant-id-mismatch\"\n        /* AuthErrorCode.TENANT_ID_MISMATCH */\n        );\n      }\n\n      if (!skipBeforeStateCallbacks) {\n        yield _this22.beforeStateQueue.runMiddleware(user);\n      }\n\n      return _this22.queue( /*#__PURE__*/_asyncToGenerator(function* () {\n        yield _this22.directlySetCurrentUser(user);\n\n        _this22.notifyAuthListeners();\n      }));\n    })();\n  }\n\n  signOut() {\n    var _this23 = this;\n\n    return _asyncToGenerator(function* () {\n      // Run first, to block _setRedirectUser() if any callbacks fail.\n      yield _this23.beforeStateQueue.runMiddleware(null); // Clear the redirect user when signOut is called\n\n      if (_this23.redirectPersistenceManager || _this23._popupRedirectResolver) {\n        yield _this23._setRedirectUser(null);\n      } // Prevent callbacks from being called again in _updateCurrentUser, as\n      // they were already called in the first line.\n\n\n      return _this23._updateCurrentUser(null,\n      /* skipBeforeStateCallbacks */\n      true);\n    })();\n  }\n\n  setPersistence(persistence) {\n    var _this24 = this;\n\n    return this.queue( /*#__PURE__*/_asyncToGenerator(function* () {\n      yield _this24.assertedPersistence.setPersistence(_getInstance(persistence));\n    }));\n  }\n\n  initializeRecaptchaConfig() {\n    var _this25 = this;\n\n    return _asyncToGenerator(function* () {\n      const response = yield getRecaptchaConfig(_this25, {\n        clientType: \"CLIENT_TYPE_WEB\"\n        /* RecaptchaClientType.WEB */\n        ,\n        version: \"RECAPTCHA_ENTERPRISE\"\n        /* RecaptchaVersion.ENTERPRISE */\n\n      });\n      const config = new RecaptchaConfig(response);\n\n      if (_this25.tenantId == null) {\n        _this25._agentRecaptchaConfig = config;\n      } else {\n        _this25._tenantRecaptchaConfigs[_this25.tenantId] = config;\n      }\n\n      if (config.emailPasswordEnabled) {\n        const verifier = new RecaptchaEnterpriseVerifier(_this25);\n        void verifier.verify();\n      }\n    })();\n  }\n\n  _getRecaptchaConfig() {\n    if (this.tenantId == null) {\n      return this._agentRecaptchaConfig;\n    } else {\n      return this._tenantRecaptchaConfigs[this.tenantId];\n    }\n  }\n\n  _getPersistence() {\n    return this.assertedPersistence.persistence.type;\n  }\n\n  _updateErrorMap(errorMap) {\n    this._errorFactory = new ErrorFactory('auth', 'Firebase', errorMap());\n  }\n\n  onAuthStateChanged(nextOrObserver, error, completed) {\n    return this.registerStateListener(this.authStateSubscription, nextOrObserver, error, completed);\n  }\n\n  beforeAuthStateChanged(callback, onAbort) {\n    return this.beforeStateQueue.pushCallback(callback, onAbort);\n  }\n\n  onIdTokenChanged(nextOrObserver, error, completed) {\n    return this.registerStateListener(this.idTokenSubscription, nextOrObserver, error, completed);\n  }\n\n  toJSON() {\n    var _a;\n\n    return {\n      apiKey: this.config.apiKey,\n      authDomain: this.config.authDomain,\n      appName: this.name,\n      currentUser: (_a = this._currentUser) === null || _a === void 0 ? void 0 : _a.toJSON()\n    };\n  }\n\n  _setRedirectUser(user, popupRedirectResolver) {\n    var _this26 = this;\n\n    return _asyncToGenerator(function* () {\n      const redirectManager = yield _this26.getOrInitRedirectPersistenceManager(popupRedirectResolver);\n      return user === null ? redirectManager.removeCurrentUser() : redirectManager.setCurrentUser(user);\n    })();\n  }\n\n  getOrInitRedirectPersistenceManager(popupRedirectResolver) {\n    var _this27 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this27.redirectPersistenceManager) {\n        const resolver = popupRedirectResolver && _getInstance(popupRedirectResolver) || _this27._popupRedirectResolver;\n\n        _assert(resolver, _this27, \"argument-error\"\n        /* AuthErrorCode.ARGUMENT_ERROR */\n        );\n\n        _this27.redirectPersistenceManager = yield PersistenceUserManager.create(_this27, [_getInstance(resolver._redirectPersistence)], \"redirectUser\"\n        /* KeyName.REDIRECT_USER */\n        );\n        _this27.redirectUser = yield _this27.redirectPersistenceManager.getCurrentUser();\n      }\n\n      return _this27.redirectPersistenceManager;\n    })();\n  }\n\n  _redirectUserForId(id) {\n    var _this28 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b; // Make sure we've cleared any pending persistence actions if we're not in\n      // the initializer\n\n\n      if (_this28._isInitialized) {\n        yield _this28.queue( /*#__PURE__*/_asyncToGenerator(function* () {}));\n      }\n\n      if (((_a = _this28._currentUser) === null || _a === void 0 ? void 0 : _a._redirectEventId) === id) {\n        return _this28._currentUser;\n      }\n\n      if (((_b = _this28.redirectUser) === null || _b === void 0 ? void 0 : _b._redirectEventId) === id) {\n        return _this28.redirectUser;\n      }\n\n      return null;\n    })();\n  }\n\n  _persistUserIfCurrent(user) {\n    var _this29 = this;\n\n    return _asyncToGenerator(function* () {\n      if (user === _this29.currentUser) {\n        return _this29.queue( /*#__PURE__*/_asyncToGenerator(function* () {\n          return _this29.directlySetCurrentUser(user);\n        }));\n      }\n    })();\n  }\n  /** Notifies listeners only if the user is current */\n\n\n  _notifyListenersIfCurrent(user) {\n    if (user === this.currentUser) {\n      this.notifyAuthListeners();\n    }\n  }\n\n  _key() {\n    return `${this.config.authDomain}:${this.config.apiKey}:${this.name}`;\n  }\n\n  _startProactiveRefresh() {\n    this.isProactiveRefreshEnabled = true;\n\n    if (this.currentUser) {\n      this._currentUser._startProactiveRefresh();\n    }\n  }\n\n  _stopProactiveRefresh() {\n    this.isProactiveRefreshEnabled = false;\n\n    if (this.currentUser) {\n      this._currentUser._stopProactiveRefresh();\n    }\n  }\n  /** Returns the current user cast as the internal type */\n\n\n  get _currentUser() {\n    return this.currentUser;\n  }\n\n  notifyAuthListeners() {\n    var _a, _b;\n\n    if (!this._isInitialized) {\n      return;\n    }\n\n    this.idTokenSubscription.next(this.currentUser);\n    const currentUid = (_b = (_a = this.currentUser) === null || _a === void 0 ? void 0 : _a.uid) !== null && _b !== void 0 ? _b : null;\n\n    if (this.lastNotifiedUid !== currentUid) {\n      this.lastNotifiedUid = currentUid;\n      this.authStateSubscription.next(this.currentUser);\n    }\n  }\n\n  registerStateListener(subscription, nextOrObserver, error, completed) {\n    if (this._deleted) {\n      return () => {};\n    }\n\n    const cb = typeof nextOrObserver === 'function' ? nextOrObserver : nextOrObserver.next.bind(nextOrObserver);\n    const promise = this._isInitialized ? Promise.resolve() : this._initializationPromise;\n\n    _assert(promise, this, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    ); // The callback needs to be called asynchronously per the spec.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n\n\n    promise.then(() => cb(this.currentUser));\n\n    if (typeof nextOrObserver === 'function') {\n      return subscription.addObserver(nextOrObserver, error, completed);\n    } else {\n      return subscription.addObserver(nextOrObserver);\n    }\n  }\n  /**\r\n   * Unprotected (from race conditions) method to set the current user. This\r\n   * should only be called from within a queued callback. This is necessary\r\n   * because the queue shouldn't rely on another queued callback.\r\n   */\n\n\n  directlySetCurrentUser(user) {\n    var _this30 = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this30.currentUser && _this30.currentUser !== user) {\n        _this30._currentUser._stopProactiveRefresh();\n      }\n\n      if (user && _this30.isProactiveRefreshEnabled) {\n        user._startProactiveRefresh();\n      }\n\n      _this30.currentUser = user;\n\n      if (user) {\n        yield _this30.assertedPersistence.setCurrentUser(user);\n      } else {\n        yield _this30.assertedPersistence.removeCurrentUser();\n      }\n    })();\n  }\n\n  queue(action) {\n    // In case something errors, the callback still should be called in order\n    // to keep the promise chain alive\n    this.operations = this.operations.then(action, action);\n    return this.operations;\n  }\n\n  get assertedPersistence() {\n    _assert(this.persistenceManager, this, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    return this.persistenceManager;\n  }\n\n  _logFramework(framework) {\n    if (!framework || this.frameworks.includes(framework)) {\n      return;\n    }\n\n    this.frameworks.push(framework); // Sort alphabetically so that \"FirebaseCore-web,FirebaseUI-web\" and\n    // \"FirebaseUI-web,FirebaseCore-web\" aren't viewed as different.\n\n    this.frameworks.sort();\n    this.clientVersion = _getClientVersion(this.config.clientPlatform, this._getFrameworks());\n  }\n\n  _getFrameworks() {\n    return this.frameworks;\n  }\n\n  _getAdditionalHeaders() {\n    var _this31 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a; // Additional headers on every request\n\n\n      const headers = {\n        [\"X-Client-Version\"\n        /* HttpHeader.X_CLIENT_VERSION */\n        ]: _this31.clientVersion\n      };\n\n      if (_this31.app.options.appId) {\n        headers[\"X-Firebase-gmpid\"\n        /* HttpHeader.X_FIREBASE_GMPID */\n        ] = _this31.app.options.appId;\n      } // If the heartbeat service exists, add the heartbeat string\n\n\n      const heartbeatsHeader = yield (_a = _this31.heartbeatServiceProvider.getImmediate({\n        optional: true\n      })) === null || _a === void 0 ? void 0 : _a.getHeartbeatsHeader();\n\n      if (heartbeatsHeader) {\n        headers[\"X-Firebase-Client\"\n        /* HttpHeader.X_FIREBASE_CLIENT */\n        ] = heartbeatsHeader;\n      } // If the App Check service exists, add the App Check token in the headers\n\n\n      const appCheckToken = yield _this31._getAppCheckToken();\n\n      if (appCheckToken) {\n        headers[\"X-Firebase-AppCheck\"\n        /* HttpHeader.X_FIREBASE_APP_CHECK */\n        ] = appCheckToken;\n      }\n\n      return headers;\n    })();\n  }\n\n  _getAppCheckToken() {\n    var _this32 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      const appCheckTokenResult = yield (_a = _this32.appCheckServiceProvider.getImmediate({\n        optional: true\n      })) === null || _a === void 0 ? void 0 : _a.getToken();\n\n      if (appCheckTokenResult === null || appCheckTokenResult === void 0 ? void 0 : appCheckTokenResult.error) {\n        // Context: appCheck.getToken() will never throw even if an error happened.\n        // In the error case, a dummy token will be returned along with an error field describing\n        // the error. In general, we shouldn't care about the error condition and just use\n        // the token (actual or dummy) to send requests.\n        _logWarn(`Error while retrieving App Check token: ${appCheckTokenResult.error}`);\n      }\n\n      return appCheckTokenResult === null || appCheckTokenResult === void 0 ? void 0 : appCheckTokenResult.token;\n    })();\n  }\n\n}\n/**\r\n * Method to be used to cast down to our private implmentation of Auth.\r\n * It will also handle unwrapping from the compat type if necessary\r\n *\r\n * @param auth Auth object passed in from developer\r\n */\n\n\nfunction _castAuth(auth) {\n  return getModularInstance(auth);\n}\n/** Helper class to wrap subscriber logic */\n\n\nclass Subscription {\n  constructor(auth) {\n    this.auth = auth;\n    this.observer = null;\n    this.addObserver = createSubscribe(observer => this.observer = observer);\n  }\n\n  get next() {\n    _assert(this.observer, this.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    return this.observer.next.bind(this.observer);\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Initializes an {@link Auth} instance with fine-grained control over\r\n * {@link Dependencies}.\r\n *\r\n * @remarks\r\n *\r\n * This function allows more control over the {@link Auth} instance than\r\n * {@link getAuth}. `getAuth` uses platform-specific defaults to supply\r\n * the {@link Dependencies}. In general, `getAuth` is the easiest way to\r\n * initialize Auth and works for most use cases. Use `initializeAuth` if you\r\n * need control over which persistence layer is used, or to minimize bundle\r\n * size if you're not using either `signInWithPopup` or `signInWithRedirect`.\r\n *\r\n * For example, if your app only uses anonymous accounts and you only want\r\n * accounts saved for the current session, initialize `Auth` with:\r\n *\r\n * ```js\r\n * const auth = initializeAuth(app, {\r\n *   persistence: browserSessionPersistence,\r\n *   popupRedirectResolver: undefined,\r\n * });\r\n * ```\r\n *\r\n * @public\r\n */\n\n\nfunction initializeAuth(app, deps) {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    const auth = provider.getImmediate();\n    const initialOptions = provider.getOptions();\n\n    if (deepEqual(initialOptions, deps !== null && deps !== void 0 ? deps : {})) {\n      return auth;\n    } else {\n      _fail(auth, \"already-initialized\"\n      /* AuthErrorCode.ALREADY_INITIALIZED */\n      );\n    }\n  }\n\n  const auth = provider.initialize({\n    options: deps\n  });\n  return auth;\n}\n\nfunction _initializeAuthInstance(auth, deps) {\n  const persistence = (deps === null || deps === void 0 ? void 0 : deps.persistence) || [];\n  const hierarchy = (Array.isArray(persistence) ? persistence : [persistence]).map(_getInstance);\n\n  if (deps === null || deps === void 0 ? void 0 : deps.errorMap) {\n    auth._updateErrorMap(deps.errorMap);\n  } // This promise is intended to float; auth initialization happens in the\n  // background, meanwhile the auth object may be used by the app.\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n\n\n  auth._initializeWithPersistence(hierarchy, deps === null || deps === void 0 ? void 0 : deps.popupRedirectResolver);\n}\n/**\r\n * Changes the {@link Auth} instance to communicate with the Firebase Auth Emulator, instead of production\r\n * Firebase Auth services.\r\n *\r\n * @remarks\r\n * This must be called synchronously immediately following the first call to\r\n * {@link initializeAuth}.  Do not use with production credentials as emulator\r\n * traffic is not encrypted.\r\n *\r\n *\r\n * @example\r\n * ```javascript\r\n * connectAuthEmulator(auth, 'http://127.0.0.1:9099', { disableWarnings: true });\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param url - The URL at which the emulator is running (eg, 'http://localhost:9099').\r\n * @param options - Optional. `options.disableWarnings` defaults to `false`. Set it to\r\n * `true` to disable the warning banner attached to the DOM.\r\n *\r\n * @public\r\n */\n\n\nfunction connectAuthEmulator(auth, url, options) {\n  const authInternal = _castAuth(auth);\n\n  _assert(authInternal._canInitEmulator, authInternal, \"emulator-config-failed\"\n  /* AuthErrorCode.EMULATOR_CONFIG_FAILED */\n  );\n\n  _assert(/^https?:\\/\\//.test(url), authInternal, \"invalid-emulator-scheme\"\n  /* AuthErrorCode.INVALID_EMULATOR_SCHEME */\n  );\n\n  const disableWarnings = !!(options === null || options === void 0 ? void 0 : options.disableWarnings);\n  const protocol = extractProtocol(url);\n  const {\n    host,\n    port\n  } = extractHostAndPort(url);\n  const portStr = port === null ? '' : `:${port}`; // Always replace path with \"/\" (even if input url had no path at all, or had a different one).\n\n  authInternal.config.emulator = {\n    url: `${protocol}//${host}${portStr}/`\n  };\n  authInternal.settings.appVerificationDisabledForTesting = true;\n  authInternal.emulatorConfig = Object.freeze({\n    host,\n    port,\n    protocol: protocol.replace(':', ''),\n    options: Object.freeze({\n      disableWarnings\n    })\n  });\n\n  if (!disableWarnings) {\n    emitEmulatorWarning();\n  }\n}\n\nfunction extractProtocol(url) {\n  const protocolEnd = url.indexOf(':');\n  return protocolEnd < 0 ? '' : url.substr(0, protocolEnd + 1);\n}\n\nfunction extractHostAndPort(url) {\n  const protocol = extractProtocol(url);\n  const authority = /(\\/\\/)?([^?#/]+)/.exec(url.substr(protocol.length)); // Between // and /, ? or #.\n\n  if (!authority) {\n    return {\n      host: '',\n      port: null\n    };\n  }\n\n  const hostAndPort = authority[2].split('@').pop() || ''; // Strip out \"username:password@\".\n\n  const bracketedIPv6 = /^(\\[[^\\]]+\\])(:|$)/.exec(hostAndPort);\n\n  if (bracketedIPv6) {\n    const host = bracketedIPv6[1];\n    return {\n      host,\n      port: parsePort(hostAndPort.substr(host.length + 1))\n    };\n  } else {\n    const [host, port] = hostAndPort.split(':');\n    return {\n      host,\n      port: parsePort(port)\n    };\n  }\n}\n\nfunction parsePort(portStr) {\n  if (!portStr) {\n    return null;\n  }\n\n  const port = Number(portStr);\n\n  if (isNaN(port)) {\n    return null;\n  }\n\n  return port;\n}\n\nfunction emitEmulatorWarning() {\n  function attachBanner() {\n    const el = document.createElement('p');\n    const sty = el.style;\n    el.innerText = 'Running in emulator mode. Do not use with production credentials.';\n    sty.position = 'fixed';\n    sty.width = '100%';\n    sty.backgroundColor = '#ffffff';\n    sty.border = '.1em solid #000000';\n    sty.color = '#b50000';\n    sty.bottom = '0px';\n    sty.left = '0px';\n    sty.margin = '0px';\n    sty.zIndex = '10000';\n    sty.textAlign = 'center';\n    el.classList.add('firebase-emulator-warning');\n    document.body.appendChild(el);\n  }\n\n  if (typeof console !== 'undefined' && typeof console.info === 'function') {\n    console.info('WARNING: You are using the Auth Emulator,' + ' which is intended for local testing only.  Do not use with' + ' production credentials.');\n  }\n\n  if (typeof window !== 'undefined' && typeof document !== 'undefined') {\n    if (document.readyState === 'loading') {\n      window.addEventListener('DOMContentLoaded', attachBanner);\n    } else {\n      attachBanner();\n    }\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Interface that represents the credentials returned by an {@link AuthProvider}.\r\n *\r\n * @remarks\r\n * Implementations specify the details about each auth provider's credential requirements.\r\n *\r\n * @public\r\n */\n\n\nclass AuthCredential {\n  /** @internal */\n  constructor(\n  /**\r\n   * The authentication provider ID for the credential.\r\n   *\r\n   * @remarks\r\n   * For example, 'facebook.com', or 'google.com'.\r\n   */\n  providerId,\n  /**\r\n   * The authentication sign in method for the credential.\r\n   *\r\n   * @remarks\r\n   * For example, {@link SignInMethod}.EMAIL_PASSWORD, or\r\n   * {@link SignInMethod}.EMAIL_LINK. This corresponds to the sign-in method\r\n   * identifier as returned in {@link fetchSignInMethodsForEmail}.\r\n   */\n  signInMethod) {\n    this.providerId = providerId;\n    this.signInMethod = signInMethod;\n  }\n  /**\r\n   * Returns a JSON-serializable representation of this object.\r\n   *\r\n   * @returns a JSON-serializable representation of this object.\r\n   */\n\n\n  toJSON() {\n    return debugFail('not implemented');\n  }\n  /** @internal */\n\n\n  _getIdTokenResponse(_auth) {\n    return debugFail('not implemented');\n  }\n  /** @internal */\n\n\n  _linkToIdToken(_auth, _idToken) {\n    return debugFail('not implemented');\n  }\n  /** @internal */\n\n\n  _getReauthenticationResolver(_auth) {\n    return debugFail('not implemented');\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction resetPassword(_x36, _x37) {\n  return _resetPassword.apply(this, arguments);\n}\n\nfunction _resetPassword() {\n  _resetPassword = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:resetPassword\"\n    /* Endpoint.RESET_PASSWORD */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _resetPassword.apply(this, arguments);\n}\n\nfunction updateEmailPassword(_x38, _x39) {\n  return _updateEmailPassword.apply(this, arguments);\n}\n\nfunction _updateEmailPassword() {\n  _updateEmailPassword = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:update\"\n    /* Endpoint.SET_ACCOUNT_INFO */\n    , request);\n  });\n  return _updateEmailPassword.apply(this, arguments);\n}\n\nfunction applyActionCode$1(_x40, _x41) {\n  return _applyActionCode$.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _applyActionCode$() {\n  _applyActionCode$ = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:update\"\n    /* Endpoint.SET_ACCOUNT_INFO */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _applyActionCode$.apply(this, arguments);\n}\n\nfunction signInWithPassword(_x42, _x43) {\n  return _signInWithPassword.apply(this, arguments);\n}\n\nfunction _signInWithPassword() {\n  _signInWithPassword = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithPassword\"\n    /* Endpoint.SIGN_IN_WITH_PASSWORD */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithPassword.apply(this, arguments);\n}\n\nfunction sendOobCode(_x44, _x45) {\n  return _sendOobCode.apply(this, arguments);\n}\n\nfunction _sendOobCode() {\n  _sendOobCode = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:sendOobCode\"\n    /* Endpoint.SEND_OOB_CODE */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _sendOobCode.apply(this, arguments);\n}\n\nfunction sendEmailVerification$1(_x46, _x47) {\n  return _sendEmailVerification$.apply(this, arguments);\n}\n\nfunction _sendEmailVerification$() {\n  _sendEmailVerification$ = _asyncToGenerator(function* (auth, request) {\n    return sendOobCode(auth, request);\n  });\n  return _sendEmailVerification$.apply(this, arguments);\n}\n\nfunction sendPasswordResetEmail$1(_x48, _x49) {\n  return _sendPasswordResetEmail$.apply(this, arguments);\n}\n\nfunction _sendPasswordResetEmail$() {\n  _sendPasswordResetEmail$ = _asyncToGenerator(function* (auth, request) {\n    return sendOobCode(auth, request);\n  });\n  return _sendPasswordResetEmail$.apply(this, arguments);\n}\n\nfunction sendSignInLinkToEmail$1(_x50, _x51) {\n  return _sendSignInLinkToEmail$.apply(this, arguments);\n}\n\nfunction _sendSignInLinkToEmail$() {\n  _sendSignInLinkToEmail$ = _asyncToGenerator(function* (auth, request) {\n    return sendOobCode(auth, request);\n  });\n  return _sendSignInLinkToEmail$.apply(this, arguments);\n}\n\nfunction verifyAndChangeEmail(_x52, _x53) {\n  return _verifyAndChangeEmail.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _verifyAndChangeEmail() {\n  _verifyAndChangeEmail = _asyncToGenerator(function* (auth, request) {\n    return sendOobCode(auth, request);\n  });\n  return _verifyAndChangeEmail.apply(this, arguments);\n}\n\nfunction signInWithEmailLink$1(_x54, _x55) {\n  return _signInWithEmailLink$.apply(this, arguments);\n}\n\nfunction _signInWithEmailLink$() {\n  _signInWithEmailLink$ = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithEmailLink\"\n    /* Endpoint.SIGN_IN_WITH_EMAIL_LINK */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithEmailLink$.apply(this, arguments);\n}\n\nfunction signInWithEmailLinkForLinking(_x56, _x57) {\n  return _signInWithEmailLinkForLinking.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Interface that represents the credentials returned by {@link EmailAuthProvider} for\r\n * {@link ProviderId}.PASSWORD\r\n *\r\n * @remarks\r\n * Covers both {@link SignInMethod}.EMAIL_PASSWORD and\r\n * {@link SignInMethod}.EMAIL_LINK.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithEmailLinkForLinking() {\n  _signInWithEmailLinkForLinking = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithEmailLink\"\n    /* Endpoint.SIGN_IN_WITH_EMAIL_LINK */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithEmailLinkForLinking.apply(this, arguments);\n}\n\nclass EmailAuthCredential extends AuthCredential {\n  /** @internal */\n  constructor(\n  /** @internal */\n  _email,\n  /** @internal */\n  _password, signInMethod,\n  /** @internal */\n  _tenantId = null) {\n    super(\"password\"\n    /* ProviderId.PASSWORD */\n    , signInMethod);\n    this._email = _email;\n    this._password = _password;\n    this._tenantId = _tenantId;\n  }\n  /** @internal */\n\n\n  static _fromEmailAndPassword(email, password) {\n    return new EmailAuthCredential(email, password, \"password\"\n    /* SignInMethod.EMAIL_PASSWORD */\n    );\n  }\n  /** @internal */\n\n\n  static _fromEmailAndCode(email, oobCode, tenantId = null) {\n    return new EmailAuthCredential(email, oobCode, \"emailLink\"\n    /* SignInMethod.EMAIL_LINK */\n    , tenantId);\n  }\n  /** {@inheritdoc AuthCredential.toJSON} */\n\n\n  toJSON() {\n    return {\n      email: this._email,\n      password: this._password,\n      signInMethod: this.signInMethod,\n      tenantId: this._tenantId\n    };\n  }\n  /**\r\n   * Static method to deserialize a JSON representation of an object into an {@link  AuthCredential}.\r\n   *\r\n   * @param json - Either `object` or the stringified representation of the object. When string is\r\n   * provided, `JSON.parse` would be called first.\r\n   *\r\n   * @returns If the JSON input does not represent an {@link AuthCredential}, null is returned.\r\n   */\n\n\n  static fromJSON(json) {\n    const obj = typeof json === 'string' ? JSON.parse(json) : json;\n\n    if ((obj === null || obj === void 0 ? void 0 : obj.email) && (obj === null || obj === void 0 ? void 0 : obj.password)) {\n      if (obj.signInMethod === \"password\"\n      /* SignInMethod.EMAIL_PASSWORD */\n      ) {\n        return this._fromEmailAndPassword(obj.email, obj.password);\n      } else if (obj.signInMethod === \"emailLink\"\n      /* SignInMethod.EMAIL_LINK */\n      ) {\n        return this._fromEmailAndCode(obj.email, obj.password, obj.tenantId);\n      }\n    }\n\n    return null;\n  }\n  /** @internal */\n\n\n  _getIdTokenResponse(auth) {\n    var _this33 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      switch (_this33.signInMethod) {\n        case \"password\"\n        /* SignInMethod.EMAIL_PASSWORD */\n        :\n          const request = {\n            returnSecureToken: true,\n            email: _this33._email,\n            password: _this33._password,\n            clientType: \"CLIENT_TYPE_WEB\"\n            /* RecaptchaClientType.WEB */\n\n          };\n\n          if ((_a = auth._getRecaptchaConfig()) === null || _a === void 0 ? void 0 : _a.emailPasswordEnabled) {\n            const requestWithRecaptcha = yield injectRecaptchaFields(auth, request, \"signInWithPassword\"\n            /* RecaptchaActionName.SIGN_IN_WITH_PASSWORD */\n            );\n            return signInWithPassword(auth, requestWithRecaptcha);\n          } else {\n            return signInWithPassword(auth, request).catch( /*#__PURE__*/function () {\n              var _ref10 = _asyncToGenerator(function* (error) {\n                if (error.code === `auth/${\"missing-recaptcha-token\"\n                /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n                }`) {\n                  console.log('Sign-in with email address and password is protected by reCAPTCHA for this project. Automatically triggering the reCAPTCHA flow and restarting the sign-in flow.');\n                  const requestWithRecaptcha = yield injectRecaptchaFields(auth, request, \"signInWithPassword\"\n                  /* RecaptchaActionName.SIGN_IN_WITH_PASSWORD */\n                  );\n                  return signInWithPassword(auth, requestWithRecaptcha);\n                } else {\n                  return Promise.reject(error);\n                }\n              });\n\n              return function (_x58) {\n                return _ref10.apply(this, arguments);\n              };\n            }());\n          }\n\n        case \"emailLink\"\n        /* SignInMethod.EMAIL_LINK */\n        :\n          return signInWithEmailLink$1(auth, {\n            email: _this33._email,\n            oobCode: _this33._password\n          });\n\n        default:\n          _fail(auth, \"internal-error\"\n          /* AuthErrorCode.INTERNAL_ERROR */\n          );\n\n      }\n    })();\n  }\n  /** @internal */\n\n\n  _linkToIdToken(auth, idToken) {\n    var _this34 = this;\n\n    return _asyncToGenerator(function* () {\n      switch (_this34.signInMethod) {\n        case \"password\"\n        /* SignInMethod.EMAIL_PASSWORD */\n        :\n          return updateEmailPassword(auth, {\n            idToken,\n            returnSecureToken: true,\n            email: _this34._email,\n            password: _this34._password\n          });\n\n        case \"emailLink\"\n        /* SignInMethod.EMAIL_LINK */\n        :\n          return signInWithEmailLinkForLinking(auth, {\n            idToken,\n            email: _this34._email,\n            oobCode: _this34._password\n          });\n\n        default:\n          _fail(auth, \"internal-error\"\n          /* AuthErrorCode.INTERNAL_ERROR */\n          );\n\n      }\n    })();\n  }\n  /** @internal */\n\n\n  _getReauthenticationResolver(auth) {\n    return this._getIdTokenResponse(auth);\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction signInWithIdp(_x59, _x60) {\n  return _signInWithIdp.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _signInWithIdp() {\n  _signInWithIdp = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithIdp\"\n    /* Endpoint.SIGN_IN_WITH_IDP */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithIdp.apply(this, arguments);\n}\n\nconst IDP_REQUEST_URI$1 = 'http://localhost';\n/**\r\n * Represents the OAuth credentials returned by an {@link OAuthProvider}.\r\n *\r\n * @remarks\r\n * Implementations specify the details about each auth provider's credential requirements.\r\n *\r\n * @public\r\n */\n\nclass OAuthCredential extends AuthCredential {\n  constructor() {\n    super(...arguments);\n    this.pendingToken = null;\n  }\n  /** @internal */\n\n\n  static _fromParams(params) {\n    const cred = new OAuthCredential(params.providerId, params.signInMethod);\n\n    if (params.idToken || params.accessToken) {\n      // OAuth 2 and either ID token or access token.\n      if (params.idToken) {\n        cred.idToken = params.idToken;\n      }\n\n      if (params.accessToken) {\n        cred.accessToken = params.accessToken;\n      } // Add nonce if available and no pendingToken is present.\n\n\n      if (params.nonce && !params.pendingToken) {\n        cred.nonce = params.nonce;\n      }\n\n      if (params.pendingToken) {\n        cred.pendingToken = params.pendingToken;\n      }\n    } else if (params.oauthToken && params.oauthTokenSecret) {\n      // OAuth 1 and OAuth token with token secret\n      cred.accessToken = params.oauthToken;\n      cred.secret = params.oauthTokenSecret;\n    } else {\n      _fail(\"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n    }\n\n    return cred;\n  }\n  /** {@inheritdoc AuthCredential.toJSON}  */\n\n\n  toJSON() {\n    return {\n      idToken: this.idToken,\n      accessToken: this.accessToken,\n      secret: this.secret,\n      nonce: this.nonce,\n      pendingToken: this.pendingToken,\n      providerId: this.providerId,\n      signInMethod: this.signInMethod\n    };\n  }\n  /**\r\n   * Static method to deserialize a JSON representation of an object into an\r\n   * {@link  AuthCredential}.\r\n   *\r\n   * @param json - Input can be either Object or the stringified representation of the object.\r\n   * When string is provided, JSON.parse would be called first.\r\n   *\r\n   * @returns If the JSON input does not represent an {@link  AuthCredential}, null is returned.\r\n   */\n\n\n  static fromJSON(json) {\n    const obj = typeof json === 'string' ? JSON.parse(json) : json;\n\n    const {\n      providerId,\n      signInMethod\n    } = obj,\n          rest = __rest(obj, [\"providerId\", \"signInMethod\"]);\n\n    if (!providerId || !signInMethod) {\n      return null;\n    }\n\n    const cred = new OAuthCredential(providerId, signInMethod);\n    cred.idToken = rest.idToken || undefined;\n    cred.accessToken = rest.accessToken || undefined;\n    cred.secret = rest.secret;\n    cred.nonce = rest.nonce;\n    cred.pendingToken = rest.pendingToken || null;\n    return cred;\n  }\n  /** @internal */\n\n\n  _getIdTokenResponse(auth) {\n    const request = this.buildRequest();\n    return signInWithIdp(auth, request);\n  }\n  /** @internal */\n\n\n  _linkToIdToken(auth, idToken) {\n    const request = this.buildRequest();\n    request.idToken = idToken;\n    return signInWithIdp(auth, request);\n  }\n  /** @internal */\n\n\n  _getReauthenticationResolver(auth) {\n    const request = this.buildRequest();\n    request.autoCreate = false;\n    return signInWithIdp(auth, request);\n  }\n\n  buildRequest() {\n    const request = {\n      requestUri: IDP_REQUEST_URI$1,\n      returnSecureToken: true\n    };\n\n    if (this.pendingToken) {\n      request.pendingToken = this.pendingToken;\n    } else {\n      const postBody = {};\n\n      if (this.idToken) {\n        postBody['id_token'] = this.idToken;\n      }\n\n      if (this.accessToken) {\n        postBody['access_token'] = this.accessToken;\n      }\n\n      if (this.secret) {\n        postBody['oauth_token_secret'] = this.secret;\n      }\n\n      postBody['providerId'] = this.providerId;\n\n      if (this.nonce && !this.pendingToken) {\n        postBody['nonce'] = this.nonce;\n      }\n\n      request.postBody = querystring(postBody);\n    }\n\n    return request;\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction sendPhoneVerificationCode(_x61, _x62) {\n  return _sendPhoneVerificationCode.apply(this, arguments);\n}\n\nfunction _sendPhoneVerificationCode() {\n  _sendPhoneVerificationCode = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:sendVerificationCode\"\n    /* Endpoint.SEND_VERIFICATION_CODE */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _sendPhoneVerificationCode.apply(this, arguments);\n}\n\nfunction signInWithPhoneNumber$1(_x63, _x64) {\n  return _signInWithPhoneNumber$.apply(this, arguments);\n}\n\nfunction _signInWithPhoneNumber$() {\n  _signInWithPhoneNumber$ = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithPhoneNumber\"\n    /* Endpoint.SIGN_IN_WITH_PHONE_NUMBER */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithPhoneNumber$.apply(this, arguments);\n}\n\nfunction linkWithPhoneNumber$1(_x65, _x66) {\n  return _linkWithPhoneNumber$.apply(this, arguments);\n}\n\nfunction _linkWithPhoneNumber$() {\n  _linkWithPhoneNumber$ = _asyncToGenerator(function* (auth, request) {\n    const response = yield _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithPhoneNumber\"\n    /* Endpoint.SIGN_IN_WITH_PHONE_NUMBER */\n    , _addTidIfNecessary(auth, request));\n\n    if (response.temporaryProof) {\n      throw _makeTaggedError(auth, \"account-exists-with-different-credential\"\n      /* AuthErrorCode.NEED_CONFIRMATION */\n      , response);\n    }\n\n    return response;\n  });\n  return _linkWithPhoneNumber$.apply(this, arguments);\n}\n\nconst VERIFY_PHONE_NUMBER_FOR_EXISTING_ERROR_MAP_ = {\n  [\"USER_NOT_FOUND\"\n  /* ServerError.USER_NOT_FOUND */\n  ]: \"user-not-found\"\n  /* AuthErrorCode.USER_DELETED */\n\n};\n\nfunction verifyPhoneNumberForExisting(_x67, _x68) {\n  return _verifyPhoneNumberForExisting.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Represents the credentials returned by {@link PhoneAuthProvider}.\r\n *\r\n * @public\r\n */\n\n\nfunction _verifyPhoneNumberForExisting() {\n  _verifyPhoneNumberForExisting = _asyncToGenerator(function* (auth, request) {\n    const apiRequest = Object.assign(Object.assign({}, request), {\n      operation: 'REAUTH'\n    });\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithPhoneNumber\"\n    /* Endpoint.SIGN_IN_WITH_PHONE_NUMBER */\n    , _addTidIfNecessary(auth, apiRequest), VERIFY_PHONE_NUMBER_FOR_EXISTING_ERROR_MAP_);\n  });\n  return _verifyPhoneNumberForExisting.apply(this, arguments);\n}\n\nclass PhoneAuthCredential extends AuthCredential {\n  constructor(params) {\n    super(\"phone\"\n    /* ProviderId.PHONE */\n    , \"phone\"\n    /* SignInMethod.PHONE */\n    );\n    this.params = params;\n  }\n  /** @internal */\n\n\n  static _fromVerification(verificationId, verificationCode) {\n    return new PhoneAuthCredential({\n      verificationId,\n      verificationCode\n    });\n  }\n  /** @internal */\n\n\n  static _fromTokenResponse(phoneNumber, temporaryProof) {\n    return new PhoneAuthCredential({\n      phoneNumber,\n      temporaryProof\n    });\n  }\n  /** @internal */\n\n\n  _getIdTokenResponse(auth) {\n    return signInWithPhoneNumber$1(auth, this._makeVerificationRequest());\n  }\n  /** @internal */\n\n\n  _linkToIdToken(auth, idToken) {\n    return linkWithPhoneNumber$1(auth, Object.assign({\n      idToken\n    }, this._makeVerificationRequest()));\n  }\n  /** @internal */\n\n\n  _getReauthenticationResolver(auth) {\n    return verifyPhoneNumberForExisting(auth, this._makeVerificationRequest());\n  }\n  /** @internal */\n\n\n  _makeVerificationRequest() {\n    const {\n      temporaryProof,\n      phoneNumber,\n      verificationId,\n      verificationCode\n    } = this.params;\n\n    if (temporaryProof && phoneNumber) {\n      return {\n        temporaryProof,\n        phoneNumber\n      };\n    }\n\n    return {\n      sessionInfo: verificationId,\n      code: verificationCode\n    };\n  }\n  /** {@inheritdoc AuthCredential.toJSON} */\n\n\n  toJSON() {\n    const obj = {\n      providerId: this.providerId\n    };\n\n    if (this.params.phoneNumber) {\n      obj.phoneNumber = this.params.phoneNumber;\n    }\n\n    if (this.params.temporaryProof) {\n      obj.temporaryProof = this.params.temporaryProof;\n    }\n\n    if (this.params.verificationCode) {\n      obj.verificationCode = this.params.verificationCode;\n    }\n\n    if (this.params.verificationId) {\n      obj.verificationId = this.params.verificationId;\n    }\n\n    return obj;\n  }\n  /** Generates a phone credential based on a plain object or a JSON string. */\n\n\n  static fromJSON(json) {\n    if (typeof json === 'string') {\n      json = JSON.parse(json);\n    }\n\n    const {\n      verificationId,\n      verificationCode,\n      phoneNumber,\n      temporaryProof\n    } = json;\n\n    if (!verificationCode && !verificationId && !phoneNumber && !temporaryProof) {\n      return null;\n    }\n\n    return new PhoneAuthCredential({\n      verificationId,\n      verificationCode,\n      phoneNumber,\n      temporaryProof\n    });\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Maps the mode string in action code URL to Action Code Info operation.\r\n *\r\n * @param mode\r\n */\n\n\nfunction parseMode(mode) {\n  switch (mode) {\n    case 'recoverEmail':\n      return \"RECOVER_EMAIL\"\n      /* ActionCodeOperation.RECOVER_EMAIL */\n      ;\n\n    case 'resetPassword':\n      return \"PASSWORD_RESET\"\n      /* ActionCodeOperation.PASSWORD_RESET */\n      ;\n\n    case 'signIn':\n      return \"EMAIL_SIGNIN\"\n      /* ActionCodeOperation.EMAIL_SIGNIN */\n      ;\n\n    case 'verifyEmail':\n      return \"VERIFY_EMAIL\"\n      /* ActionCodeOperation.VERIFY_EMAIL */\n      ;\n\n    case 'verifyAndChangeEmail':\n      return \"VERIFY_AND_CHANGE_EMAIL\"\n      /* ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL */\n      ;\n\n    case 'revertSecondFactorAddition':\n      return \"REVERT_SECOND_FACTOR_ADDITION\"\n      /* ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION */\n      ;\n\n    default:\n      return null;\n  }\n}\n/**\r\n * Helper to parse FDL links\r\n *\r\n * @param url\r\n */\n\n\nfunction parseDeepLink(url) {\n  const link = querystringDecode(extractQuerystring(url))['link']; // Double link case (automatic redirect).\n\n  const doubleDeepLink = link ? querystringDecode(extractQuerystring(link))['deep_link_id'] : null; // iOS custom scheme links.\n\n  const iOSDeepLink = querystringDecode(extractQuerystring(url))['deep_link_id'];\n  const iOSDoubleDeepLink = iOSDeepLink ? querystringDecode(extractQuerystring(iOSDeepLink))['link'] : null;\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n/**\r\n * A utility class to parse email action URLs such as password reset, email verification,\r\n * email link sign in, etc.\r\n *\r\n * @public\r\n */\n\n\nclass ActionCodeURL {\n  /**\r\n   * @param actionLink - The link from which to extract the URL.\r\n   * @returns The {@link ActionCodeURL} object, or null if the link is invalid.\r\n   *\r\n   * @internal\r\n   */\n  constructor(actionLink) {\n    var _a, _b, _c, _d, _e, _f;\n\n    const searchParams = querystringDecode(extractQuerystring(actionLink));\n    const apiKey = (_a = searchParams[\"apiKey\"\n    /* QueryField.API_KEY */\n    ]) !== null && _a !== void 0 ? _a : null;\n    const code = (_b = searchParams[\"oobCode\"\n    /* QueryField.CODE */\n    ]) !== null && _b !== void 0 ? _b : null;\n    const operation = parseMode((_c = searchParams[\"mode\"\n    /* QueryField.MODE */\n    ]) !== null && _c !== void 0 ? _c : null); // Validate API key, code and mode.\n\n    _assert(apiKey && code && operation, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    this.apiKey = apiKey;\n    this.operation = operation;\n    this.code = code;\n    this.continueUrl = (_d = searchParams[\"continueUrl\"\n    /* QueryField.CONTINUE_URL */\n    ]) !== null && _d !== void 0 ? _d : null;\n    this.languageCode = (_e = searchParams[\"languageCode\"\n    /* QueryField.LANGUAGE_CODE */\n    ]) !== null && _e !== void 0 ? _e : null;\n    this.tenantId = (_f = searchParams[\"tenantId\"\n    /* QueryField.TENANT_ID */\n    ]) !== null && _f !== void 0 ? _f : null;\n  }\n  /**\r\n   * Parses the email action link string and returns an {@link ActionCodeURL} if the link is valid,\r\n   * otherwise returns null.\r\n   *\r\n   * @param link  - The email action link string.\r\n   * @returns The {@link ActionCodeURL} object, or null if the link is invalid.\r\n   *\r\n   * @public\r\n   */\n\n\n  static parseLink(link) {\n    const actionLink = parseDeepLink(link);\n\n    try {\n      return new ActionCodeURL(actionLink);\n    } catch (_a) {\n      return null;\n    }\n  }\n\n}\n/**\r\n * Parses the email action link string and returns an {@link ActionCodeURL} if\r\n * the link is valid, otherwise returns null.\r\n *\r\n * @public\r\n */\n\n\nfunction parseActionCodeURL(link) {\n  return ActionCodeURL.parseLink(link);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Provider for generating {@link EmailAuthCredential}.\r\n *\r\n * @public\r\n */\n\n\nlet EmailAuthProvider = /*#__PURE__*/(() => {\n  class EmailAuthProvider {\n    constructor() {\n      /**\r\n       * Always set to {@link ProviderId}.PASSWORD, even for email link.\r\n       */\n      this.providerId = EmailAuthProvider.PROVIDER_ID;\n    }\n    /**\r\n     * Initialize an {@link AuthCredential} using an email and password.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * const authCredential = EmailAuthProvider.credential(email, password);\r\n     * const userCredential = await signInWithCredential(auth, authCredential);\r\n     * ```\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * const userCredential = await signInWithEmailAndPassword(auth, email, password);\r\n     * ```\r\n     *\r\n     * @param email - Email address.\r\n     * @param password - User account password.\r\n     * @returns The auth provider credential.\r\n     */\n\n\n    static credential(email, password) {\n      return EmailAuthCredential._fromEmailAndPassword(email, password);\n    }\n    /**\r\n     * Initialize an {@link AuthCredential} using an email and an email link after a sign in with\r\n     * email link operation.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * const authCredential = EmailAuthProvider.credentialWithLink(auth, email, emailLink);\r\n     * const userCredential = await signInWithCredential(auth, authCredential);\r\n     * ```\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * await sendSignInLinkToEmail(auth, email);\r\n     * // Obtain emailLink from user.\r\n     * const userCredential = await signInWithEmailLink(auth, email, emailLink);\r\n     * ```\r\n     *\r\n     * @param auth - The {@link Auth} instance used to verify the link.\r\n     * @param email - Email address.\r\n     * @param emailLink - Sign-in email link.\r\n     * @returns - The auth provider credential.\r\n     */\n\n\n    static credentialWithLink(email, emailLink) {\n      const actionCodeUrl = ActionCodeURL.parseLink(emailLink);\n\n      _assert(actionCodeUrl, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      return EmailAuthCredential._fromEmailAndCode(email, actionCodeUrl.code, actionCodeUrl.tenantId);\n    }\n\n  }\n\n  /**\r\n   * Always set to {@link ProviderId}.PASSWORD, even for email link.\r\n   */\n\n  /**\r\n   * Always set to {@link SignInMethod}.EMAIL_PASSWORD.\r\n   */\n\n  /**\r\n   * Always set to {@link SignInMethod}.EMAIL_LINK.\r\n   */\n  EmailAuthProvider.PROVIDER_ID = \"password\"\n  /* ProviderId.PASSWORD */\n  ;\n  EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD = \"password\"\n  /* SignInMethod.EMAIL_PASSWORD */\n  ;\n  EmailAuthProvider.EMAIL_LINK_SIGN_IN_METHOD = \"emailLink\"\n  /* SignInMethod.EMAIL_LINK */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  /**\r\n   * The base class for all Federated providers (OAuth (including OIDC), SAML).\r\n   *\r\n   * This class is not meant to be instantiated directly.\r\n   *\r\n   * @public\r\n   */\n\n  return EmailAuthProvider;\n})();\n\nclass FederatedAuthProvider {\n  /**\r\n   * Constructor for generic OAuth providers.\r\n   *\r\n   * @param providerId - Provider for which credentials should be generated.\r\n   */\n  constructor(providerId) {\n    this.providerId = providerId;\n    /** @internal */\n\n    this.defaultLanguageCode = null;\n    /** @internal */\n\n    this.customParameters = {};\n  }\n  /**\r\n   * Set the language gode.\r\n   *\r\n   * @param languageCode - language code\r\n   */\n\n\n  setDefaultLanguage(languageCode) {\n    this.defaultLanguageCode = languageCode;\n  }\n  /**\r\n   * Sets the OAuth custom parameters to pass in an OAuth request for popup and redirect sign-in\r\n   * operations.\r\n   *\r\n   * @remarks\r\n   * For a detailed list, check the reserved required OAuth 2.0 parameters such as `client_id`,\r\n   * `redirect_uri`, `scope`, `response_type`, and `state` are not allowed and will be ignored.\r\n   *\r\n   * @param customOAuthParameters - The custom OAuth parameters to pass in the OAuth request.\r\n   */\n\n\n  setCustomParameters(customOAuthParameters) {\n    this.customParameters = customOAuthParameters;\n    return this;\n  }\n  /**\r\n   * Retrieve the current list of {@link CustomParameters}.\r\n   */\n\n\n  getCustomParameters() {\n    return this.customParameters;\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Common code to all OAuth providers. This is separate from the\r\n * {@link OAuthProvider} so that child providers (like\r\n * {@link GoogleAuthProvider}) don't inherit the `credential` instance method.\r\n * Instead, they rely on a static `credential` method.\r\n */\n\n\nclass BaseOAuthProvider extends FederatedAuthProvider {\n  constructor() {\n    super(...arguments);\n    /** @internal */\n\n    this.scopes = [];\n  }\n  /**\r\n   * Add an OAuth scope to the credential.\r\n   *\r\n   * @param scope - Provider OAuth scope to add.\r\n   */\n\n\n  addScope(scope) {\n    // If not already added, add scope to list.\n    if (!this.scopes.includes(scope)) {\n      this.scopes.push(scope);\n    }\n\n    return this;\n  }\n  /**\r\n   * Retrieve the current list of OAuth scopes.\r\n   */\n\n\n  getScopes() {\n    return [...this.scopes];\n  }\n\n}\n/**\r\n * Provider for generating generic {@link OAuthCredential}.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new OAuthProvider('google.com');\r\n * // Start a sign in process for an unauthenticated user.\r\n * provider.addScope('profile');\r\n * provider.addScope('email');\r\n * await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * if (result) {\r\n *   // This is the signed-in user\r\n *   const user = result.user;\r\n *   // This gives you a OAuth Access Token for the provider.\r\n *   const credential = provider.credentialFromResult(auth, result);\r\n *   const token = credential.accessToken;\r\n * }\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a popup.\r\n * const provider = new OAuthProvider('google.com');\r\n * provider.addScope('profile');\r\n * provider.addScope('email');\r\n * const result = await signInWithPopup(auth, provider);\r\n *\r\n * // The signed-in user info.\r\n * const user = result.user;\r\n * // This gives you a OAuth Access Token for the provider.\r\n * const credential = provider.credentialFromResult(auth, result);\r\n * const token = credential.accessToken;\r\n * ```\r\n * @public\r\n */\n\n\nclass OAuthProvider extends BaseOAuthProvider {\n  /**\r\n   * Creates an {@link OAuthCredential} from a JSON string or a plain object.\r\n   * @param json - A plain object or a JSON string\r\n   */\n  static credentialFromJSON(json) {\n    const obj = typeof json === 'string' ? JSON.parse(json) : json;\n\n    _assert('providerId' in obj && 'signInMethod' in obj, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    return OAuthCredential._fromParams(obj);\n  }\n  /**\r\n   * Creates a {@link OAuthCredential} from a generic OAuth provider's access token or ID token.\r\n   *\r\n   * @remarks\r\n   * The raw nonce is required when an ID token with a nonce field is provided. The SHA-256 hash of\r\n   * the raw nonce must match the nonce field in the ID token.\r\n   *\r\n   * @example\r\n   * ```javascript\r\n   * // `googleUser` from the onsuccess Google Sign In callback.\r\n   * // Initialize a generate OAuth provider with a `google.com` providerId.\r\n   * const provider = new OAuthProvider('google.com');\r\n   * const credential = provider.credential({\r\n   *   idToken: googleUser.getAuthResponse().id_token,\r\n   * });\r\n   * const result = await signInWithCredential(credential);\r\n   * ```\r\n   *\r\n   * @param params - Either the options object containing the ID token, access token and raw nonce\r\n   * or the ID token string.\r\n   */\n\n\n  credential(params) {\n    return this._credential(Object.assign(Object.assign({}, params), {\n      nonce: params.rawNonce\n    }));\n  }\n  /** An internal credential method that accepts more permissive options */\n\n\n  _credential(params) {\n    _assert(params.idToken || params.accessToken, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    ); // For OAuthCredential, sign in method is same as providerId.\n\n\n    return OAuthCredential._fromParams(Object.assign(Object.assign({}, params), {\n      providerId: this.providerId,\n      signInMethod: this.providerId\n    }));\n  }\n  /**\r\n   * Used to extract the underlying {@link OAuthCredential} from a {@link UserCredential}.\r\n   *\r\n   * @param userCredential - The user credential.\r\n   */\n\n\n  static credentialFromResult(userCredential) {\n    return OAuthProvider.oauthCredentialFromTaggedObject(userCredential);\n  }\n  /**\r\n   * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n   * thrown during a sign-in, link, or reauthenticate operation.\r\n   *\r\n   * @param userCredential - The user credential.\r\n   */\n\n\n  static credentialFromError(error) {\n    return OAuthProvider.oauthCredentialFromTaggedObject(error.customData || {});\n  }\n\n  static oauthCredentialFromTaggedObject({\n    _tokenResponse: tokenResponse\n  }) {\n    if (!tokenResponse) {\n      return null;\n    }\n\n    const {\n      oauthIdToken,\n      oauthAccessToken,\n      oauthTokenSecret,\n      pendingToken,\n      nonce,\n      providerId\n    } = tokenResponse;\n\n    if (!oauthAccessToken && !oauthTokenSecret && !oauthIdToken && !pendingToken) {\n      return null;\n    }\n\n    if (!providerId) {\n      return null;\n    }\n\n    try {\n      return new OAuthProvider(providerId)._credential({\n        idToken: oauthIdToken,\n        accessToken: oauthAccessToken,\n        nonce,\n        pendingToken\n      });\n    } catch (e) {\n      return null;\n    }\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Provider for generating an {@link OAuthCredential} for {@link ProviderId}.FACEBOOK.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new FacebookAuthProvider();\r\n * // Start a sign in process for an unauthenticated user.\r\n * provider.addScope('user_birthday');\r\n * await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * if (result) {\r\n *   // This is the signed-in user\r\n *   const user = result.user;\r\n *   // This gives you a Facebook Access Token.\r\n *   const credential = FacebookAuthProvider.credentialFromResult(result);\r\n *   const token = credential.accessToken;\r\n * }\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a popup.\r\n * const provider = new FacebookAuthProvider();\r\n * provider.addScope('user_birthday');\r\n * const result = await signInWithPopup(auth, provider);\r\n *\r\n * // The signed-in user info.\r\n * const user = result.user;\r\n * // This gives you a Facebook Access Token.\r\n * const credential = FacebookAuthProvider.credentialFromResult(result);\r\n * const token = credential.accessToken;\r\n * ```\r\n *\r\n * @public\r\n */\n\n\nlet FacebookAuthProvider = /*#__PURE__*/(() => {\n  class FacebookAuthProvider extends BaseOAuthProvider {\n    constructor() {\n      super(\"facebook.com\"\n      /* ProviderId.FACEBOOK */\n      );\n    }\n    /**\r\n     * Creates a credential for Facebook.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * // `event` from the Facebook auth.authResponseChange callback.\r\n     * const credential = FacebookAuthProvider.credential(event.authResponse.accessToken);\r\n     * const result = await signInWithCredential(credential);\r\n     * ```\r\n     *\r\n     * @param accessToken - Facebook access token.\r\n     */\n\n\n    static credential(accessToken) {\n      return OAuthCredential._fromParams({\n        providerId: FacebookAuthProvider.PROVIDER_ID,\n        signInMethod: FacebookAuthProvider.FACEBOOK_SIGN_IN_METHOD,\n        accessToken\n      });\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link UserCredential}.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromResult(userCredential) {\n      return FacebookAuthProvider.credentialFromTaggedObject(userCredential);\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n     * thrown during a sign-in, link, or reauthenticate operation.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromError(error) {\n      return FacebookAuthProvider.credentialFromTaggedObject(error.customData || {});\n    }\n\n    static credentialFromTaggedObject({\n      _tokenResponse: tokenResponse\n    }) {\n      if (!tokenResponse || !('oauthAccessToken' in tokenResponse)) {\n        return null;\n      }\n\n      if (!tokenResponse.oauthAccessToken) {\n        return null;\n      }\n\n      try {\n        return FacebookAuthProvider.credential(tokenResponse.oauthAccessToken);\n      } catch (_a) {\n        return null;\n      }\n    }\n\n  }\n\n  /** Always set to {@link SignInMethod}.FACEBOOK. */\n\n  /** Always set to {@link ProviderId}.FACEBOOK. */\n  FacebookAuthProvider.FACEBOOK_SIGN_IN_METHOD = \"facebook.com\"\n  /* SignInMethod.FACEBOOK */\n  ;\n  FacebookAuthProvider.PROVIDER_ID = \"facebook.com\"\n  /* ProviderId.FACEBOOK */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  /**\r\n   * Provider for generating an an {@link OAuthCredential} for {@link ProviderId}.GOOGLE.\r\n   *\r\n   * @example\r\n   * ```javascript\r\n   * // Sign in using a redirect.\r\n   * const provider = new GoogleAuthProvider();\r\n   * // Start a sign in process for an unauthenticated user.\r\n   * provider.addScope('profile');\r\n   * provider.addScope('email');\r\n   * await signInWithRedirect(auth, provider);\r\n   * // This will trigger a full page redirect away from your app\r\n   *\r\n   * // After returning from the redirect when your app initializes you can obtain the result\r\n   * const result = await getRedirectResult(auth);\r\n   * if (result) {\r\n   *   // This is the signed-in user\r\n   *   const user = result.user;\r\n   *   // This gives you a Google Access Token.\r\n   *   const credential = GoogleAuthProvider.credentialFromResult(result);\r\n   *   const token = credential.accessToken;\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * ```javascript\r\n   * // Sign in using a popup.\r\n   * const provider = new GoogleAuthProvider();\r\n   * provider.addScope('profile');\r\n   * provider.addScope('email');\r\n   * const result = await signInWithPopup(auth, provider);\r\n   *\r\n   * // The signed-in user info.\r\n   * const user = result.user;\r\n   * // This gives you a Google Access Token.\r\n   * const credential = GoogleAuthProvider.credentialFromResult(result);\r\n   * const token = credential.accessToken;\r\n   * ```\r\n   *\r\n   * @public\r\n   */\n\n  return FacebookAuthProvider;\n})();\nlet GoogleAuthProvider = /*#__PURE__*/(() => {\n  class GoogleAuthProvider extends BaseOAuthProvider {\n    constructor() {\n      super(\"google.com\"\n      /* ProviderId.GOOGLE */\n      );\n      this.addScope('profile');\n    }\n    /**\r\n     * Creates a credential for Google. At least one of ID token and access token is required.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * // \\`googleUser\\` from the onsuccess Google Sign In callback.\r\n     * const credential = GoogleAuthProvider.credential(googleUser.getAuthResponse().id_token);\r\n     * const result = await signInWithCredential(credential);\r\n     * ```\r\n     *\r\n     * @param idToken - Google ID token.\r\n     * @param accessToken - Google access token.\r\n     */\n\n\n    static credential(idToken, accessToken) {\n      return OAuthCredential._fromParams({\n        providerId: GoogleAuthProvider.PROVIDER_ID,\n        signInMethod: GoogleAuthProvider.GOOGLE_SIGN_IN_METHOD,\n        idToken,\n        accessToken\n      });\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link UserCredential}.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromResult(userCredential) {\n      return GoogleAuthProvider.credentialFromTaggedObject(userCredential);\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n     * thrown during a sign-in, link, or reauthenticate operation.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromError(error) {\n      return GoogleAuthProvider.credentialFromTaggedObject(error.customData || {});\n    }\n\n    static credentialFromTaggedObject({\n      _tokenResponse: tokenResponse\n    }) {\n      if (!tokenResponse) {\n        return null;\n      }\n\n      const {\n        oauthIdToken,\n        oauthAccessToken\n      } = tokenResponse;\n\n      if (!oauthIdToken && !oauthAccessToken) {\n        // This could be an oauth 1 credential or a phone credential\n        return null;\n      }\n\n      try {\n        return GoogleAuthProvider.credential(oauthIdToken, oauthAccessToken);\n      } catch (_a) {\n        return null;\n      }\n    }\n\n  }\n\n  /** Always set to {@link SignInMethod}.GOOGLE. */\n\n  /** Always set to {@link ProviderId}.GOOGLE. */\n  GoogleAuthProvider.GOOGLE_SIGN_IN_METHOD = \"google.com\"\n  /* SignInMethod.GOOGLE */\n  ;\n  GoogleAuthProvider.PROVIDER_ID = \"google.com\"\n  /* ProviderId.GOOGLE */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  /**\r\n   * Provider for generating an {@link OAuthCredential} for {@link ProviderId}.GITHUB.\r\n   *\r\n   * @remarks\r\n   * GitHub requires an OAuth 2.0 redirect, so you can either handle the redirect directly, or use\r\n   * the {@link signInWithPopup} handler:\r\n   *\r\n   * @example\r\n   * ```javascript\r\n   * // Sign in using a redirect.\r\n   * const provider = new GithubAuthProvider();\r\n   * // Start a sign in process for an unauthenticated user.\r\n   * provider.addScope('repo');\r\n   * await signInWithRedirect(auth, provider);\r\n   * // This will trigger a full page redirect away from your app\r\n   *\r\n   * // After returning from the redirect when your app initializes you can obtain the result\r\n   * const result = await getRedirectResult(auth);\r\n   * if (result) {\r\n   *   // This is the signed-in user\r\n   *   const user = result.user;\r\n   *   // This gives you a Github Access Token.\r\n   *   const credential = GithubAuthProvider.credentialFromResult(result);\r\n   *   const token = credential.accessToken;\r\n   * }\r\n   * ```\r\n   *\r\n   * @example\r\n   * ```javascript\r\n   * // Sign in using a popup.\r\n   * const provider = new GithubAuthProvider();\r\n   * provider.addScope('repo');\r\n   * const result = await signInWithPopup(auth, provider);\r\n   *\r\n   * // The signed-in user info.\r\n   * const user = result.user;\r\n   * // This gives you a Github Access Token.\r\n   * const credential = GithubAuthProvider.credentialFromResult(result);\r\n   * const token = credential.accessToken;\r\n   * ```\r\n   * @public\r\n   */\n\n  return GoogleAuthProvider;\n})();\nlet GithubAuthProvider = /*#__PURE__*/(() => {\n  class GithubAuthProvider extends BaseOAuthProvider {\n    constructor() {\n      super(\"github.com\"\n      /* ProviderId.GITHUB */\n      );\n    }\n    /**\r\n     * Creates a credential for Github.\r\n     *\r\n     * @param accessToken - Github access token.\r\n     */\n\n\n    static credential(accessToken) {\n      return OAuthCredential._fromParams({\n        providerId: GithubAuthProvider.PROVIDER_ID,\n        signInMethod: GithubAuthProvider.GITHUB_SIGN_IN_METHOD,\n        accessToken\n      });\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link UserCredential}.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromResult(userCredential) {\n      return GithubAuthProvider.credentialFromTaggedObject(userCredential);\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n     * thrown during a sign-in, link, or reauthenticate operation.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromError(error) {\n      return GithubAuthProvider.credentialFromTaggedObject(error.customData || {});\n    }\n\n    static credentialFromTaggedObject({\n      _tokenResponse: tokenResponse\n    }) {\n      if (!tokenResponse || !('oauthAccessToken' in tokenResponse)) {\n        return null;\n      }\n\n      if (!tokenResponse.oauthAccessToken) {\n        return null;\n      }\n\n      try {\n        return GithubAuthProvider.credential(tokenResponse.oauthAccessToken);\n      } catch (_a) {\n        return null;\n      }\n    }\n\n  }\n\n  /** Always set to {@link SignInMethod}.GITHUB. */\n\n  /** Always set to {@link ProviderId}.GITHUB. */\n  GithubAuthProvider.GITHUB_SIGN_IN_METHOD = \"github.com\"\n  /* SignInMethod.GITHUB */\n  ;\n  GithubAuthProvider.PROVIDER_ID = \"github.com\"\n  /* ProviderId.GITHUB */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  return GithubAuthProvider;\n})();\nconst IDP_REQUEST_URI = 'http://localhost';\n/**\r\n * @public\r\n */\n\nclass SAMLAuthCredential extends AuthCredential {\n  /** @internal */\n  constructor(providerId, pendingToken) {\n    super(providerId, providerId);\n    this.pendingToken = pendingToken;\n  }\n  /** @internal */\n\n\n  _getIdTokenResponse(auth) {\n    const request = this.buildRequest();\n    return signInWithIdp(auth, request);\n  }\n  /** @internal */\n\n\n  _linkToIdToken(auth, idToken) {\n    const request = this.buildRequest();\n    request.idToken = idToken;\n    return signInWithIdp(auth, request);\n  }\n  /** @internal */\n\n\n  _getReauthenticationResolver(auth) {\n    const request = this.buildRequest();\n    request.autoCreate = false;\n    return signInWithIdp(auth, request);\n  }\n  /** {@inheritdoc AuthCredential.toJSON}  */\n\n\n  toJSON() {\n    return {\n      signInMethod: this.signInMethod,\n      providerId: this.providerId,\n      pendingToken: this.pendingToken\n    };\n  }\n  /**\r\n   * Static method to deserialize a JSON representation of an object into an\r\n   * {@link  AuthCredential}.\r\n   *\r\n   * @param json - Input can be either Object or the stringified representation of the object.\r\n   * When string is provided, JSON.parse would be called first.\r\n   *\r\n   * @returns If the JSON input does not represent an {@link  AuthCredential}, null is returned.\r\n   */\n\n\n  static fromJSON(json) {\n    const obj = typeof json === 'string' ? JSON.parse(json) : json;\n    const {\n      providerId,\n      signInMethod,\n      pendingToken\n    } = obj;\n\n    if (!providerId || !signInMethod || !pendingToken || providerId !== signInMethod) {\n      return null;\n    }\n\n    return new SAMLAuthCredential(providerId, pendingToken);\n  }\n  /**\r\n   * Helper static method to avoid exposing the constructor to end users.\r\n   *\r\n   * @internal\r\n   */\n\n\n  static _create(providerId, pendingToken) {\n    return new SAMLAuthCredential(providerId, pendingToken);\n  }\n\n  buildRequest() {\n    return {\n      requestUri: IDP_REQUEST_URI,\n      returnSecureToken: true,\n      pendingToken: this.pendingToken\n    };\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst SAML_PROVIDER_PREFIX = 'saml.';\n/**\r\n * An {@link AuthProvider} for SAML.\r\n *\r\n * @public\r\n */\n\nclass SAMLAuthProvider extends FederatedAuthProvider {\n  /**\r\n   * Constructor. The providerId must start with \"saml.\"\r\n   * @param providerId - SAML provider ID.\r\n   */\n  constructor(providerId) {\n    _assert(providerId.startsWith(SAML_PROVIDER_PREFIX), \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    super(providerId);\n  }\n  /**\r\n   * Generates an {@link AuthCredential} from a {@link UserCredential} after a\r\n   * successful SAML flow completes.\r\n   *\r\n   * @remarks\r\n   *\r\n   * For example, to get an {@link AuthCredential}, you could write the\r\n   * following code:\r\n   *\r\n   * ```js\r\n   * const userCredential = await signInWithPopup(auth, samlProvider);\r\n   * const credential = SAMLAuthProvider.credentialFromResult(userCredential);\r\n   * ```\r\n   *\r\n   * @param userCredential - The user credential.\r\n   */\n\n\n  static credentialFromResult(userCredential) {\n    return SAMLAuthProvider.samlCredentialFromTaggedObject(userCredential);\n  }\n  /**\r\n   * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n   * thrown during a sign-in, link, or reauthenticate operation.\r\n   *\r\n   * @param userCredential - The user credential.\r\n   */\n\n\n  static credentialFromError(error) {\n    return SAMLAuthProvider.samlCredentialFromTaggedObject(error.customData || {});\n  }\n  /**\r\n   * Creates an {@link AuthCredential} from a JSON string or a plain object.\r\n   * @param json - A plain object or a JSON string\r\n   */\n\n\n  static credentialFromJSON(json) {\n    const credential = SAMLAuthCredential.fromJSON(json);\n\n    _assert(credential, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    return credential;\n  }\n\n  static samlCredentialFromTaggedObject({\n    _tokenResponse: tokenResponse\n  }) {\n    if (!tokenResponse) {\n      return null;\n    }\n\n    const {\n      pendingToken,\n      providerId\n    } = tokenResponse;\n\n    if (!pendingToken || !providerId) {\n      return null;\n    }\n\n    try {\n      return SAMLAuthCredential._create(providerId, pendingToken);\n    } catch (e) {\n      return null;\n    }\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Provider for generating an {@link OAuthCredential} for {@link ProviderId}.TWITTER.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new TwitterAuthProvider();\r\n * // Start a sign in process for an unauthenticated user.\r\n * await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * if (result) {\r\n *   // This is the signed-in user\r\n *   const user = result.user;\r\n *   // This gives you a Twitter Access Token and Secret.\r\n *   const credential = TwitterAuthProvider.credentialFromResult(result);\r\n *   const token = credential.accessToken;\r\n *   const secret = credential.secret;\r\n * }\r\n * ```\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a popup.\r\n * const provider = new TwitterAuthProvider();\r\n * const result = await signInWithPopup(auth, provider);\r\n *\r\n * // The signed-in user info.\r\n * const user = result.user;\r\n * // This gives you a Twitter Access Token and Secret.\r\n * const credential = TwitterAuthProvider.credentialFromResult(result);\r\n * const token = credential.accessToken;\r\n * const secret = credential.secret;\r\n * ```\r\n *\r\n * @public\r\n */\n\n\nlet TwitterAuthProvider = /*#__PURE__*/(() => {\n  class TwitterAuthProvider extends BaseOAuthProvider {\n    constructor() {\n      super(\"twitter.com\"\n      /* ProviderId.TWITTER */\n      );\n    }\n    /**\r\n     * Creates a credential for Twitter.\r\n     *\r\n     * @param token - Twitter access token.\r\n     * @param secret - Twitter secret.\r\n     */\n\n\n    static credential(token, secret) {\n      return OAuthCredential._fromParams({\n        providerId: TwitterAuthProvider.PROVIDER_ID,\n        signInMethod: TwitterAuthProvider.TWITTER_SIGN_IN_METHOD,\n        oauthToken: token,\n        oauthTokenSecret: secret\n      });\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link UserCredential}.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromResult(userCredential) {\n      return TwitterAuthProvider.credentialFromTaggedObject(userCredential);\n    }\n    /**\r\n     * Used to extract the underlying {@link OAuthCredential} from a {@link AuthError} which was\r\n     * thrown during a sign-in, link, or reauthenticate operation.\r\n     *\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromError(error) {\n      return TwitterAuthProvider.credentialFromTaggedObject(error.customData || {});\n    }\n\n    static credentialFromTaggedObject({\n      _tokenResponse: tokenResponse\n    }) {\n      if (!tokenResponse) {\n        return null;\n      }\n\n      const {\n        oauthAccessToken,\n        oauthTokenSecret\n      } = tokenResponse;\n\n      if (!oauthAccessToken || !oauthTokenSecret) {\n        return null;\n      }\n\n      try {\n        return TwitterAuthProvider.credential(oauthAccessToken, oauthTokenSecret);\n      } catch (_a) {\n        return null;\n      }\n    }\n\n  }\n\n  /** Always set to {@link SignInMethod}.TWITTER. */\n\n  /** Always set to {@link ProviderId}.TWITTER. */\n  TwitterAuthProvider.TWITTER_SIGN_IN_METHOD = \"twitter.com\"\n  /* SignInMethod.TWITTER */\n  ;\n  TwitterAuthProvider.PROVIDER_ID = \"twitter.com\"\n  /* ProviderId.TWITTER */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  return TwitterAuthProvider;\n})();\n\nfunction signUp(_x69, _x70) {\n  return _signUp.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _signUp() {\n  _signUp = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signUp\"\n    /* Endpoint.SIGN_UP */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signUp.apply(this, arguments);\n}\n\nclass UserCredentialImpl {\n  constructor(params) {\n    this.user = params.user;\n    this.providerId = params.providerId;\n    this._tokenResponse = params._tokenResponse;\n    this.operationType = params.operationType;\n  }\n\n  static _fromIdTokenResponse(auth, operationType, idTokenResponse, isAnonymous = false) {\n    return _asyncToGenerator(function* () {\n      const user = yield UserImpl._fromIdTokenResponse(auth, idTokenResponse, isAnonymous);\n      const providerId = providerIdForResponse(idTokenResponse);\n      const userCred = new UserCredentialImpl({\n        user,\n        providerId,\n        _tokenResponse: idTokenResponse,\n        operationType\n      });\n      return userCred;\n    })();\n  }\n\n  static _forOperation(user, operationType, response) {\n    return _asyncToGenerator(function* () {\n      yield user._updateTokensIfNecessary(response,\n      /* reload */\n      true);\n      const providerId = providerIdForResponse(response);\n      return new UserCredentialImpl({\n        user,\n        providerId,\n        _tokenResponse: response,\n        operationType\n      });\n    })();\n  }\n\n}\n\nfunction providerIdForResponse(response) {\n  if (response.providerId) {\n    return response.providerId;\n  }\n\n  if ('phoneNumber' in response) {\n    return \"phone\"\n    /* ProviderId.PHONE */\n    ;\n  }\n\n  return null;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Asynchronously signs in as an anonymous user.\r\n *\r\n * @remarks\r\n * If there is already an anonymous user signed in, that user will be returned; otherwise, a\r\n * new anonymous user identity will be created and returned.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n *\r\n * @public\r\n */\n\n\nfunction signInAnonymously(_x71) {\n  return _signInAnonymously.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _signInAnonymously() {\n  _signInAnonymously = _asyncToGenerator(function* (auth) {\n    var _a;\n\n    const authInternal = _castAuth(auth);\n\n    yield authInternal._initializationPromise;\n\n    if ((_a = authInternal.currentUser) === null || _a === void 0 ? void 0 : _a.isAnonymous) {\n      // If an anonymous user is already signed in, no need to sign them in again.\n      return new UserCredentialImpl({\n        user: authInternal.currentUser,\n        providerId: null,\n        operationType: \"signIn\"\n        /* OperationType.SIGN_IN */\n\n      });\n    }\n\n    const response = yield signUp(authInternal, {\n      returnSecureToken: true\n    });\n    const userCredential = yield UserCredentialImpl._fromIdTokenResponse(authInternal, \"signIn\"\n    /* OperationType.SIGN_IN */\n    , response, true);\n    yield authInternal._updateCurrentUser(userCredential.user);\n    return userCredential;\n  });\n  return _signInAnonymously.apply(this, arguments);\n}\n\nclass MultiFactorError extends FirebaseError {\n  constructor(auth, error, operationType, user) {\n    var _a;\n\n    super(error.code, error.message);\n    this.operationType = operationType;\n    this.user = user; // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n\n    Object.setPrototypeOf(this, MultiFactorError.prototype);\n    this.customData = {\n      appName: auth.name,\n      tenantId: (_a = auth.tenantId) !== null && _a !== void 0 ? _a : undefined,\n      _serverResponse: error.customData._serverResponse,\n      operationType\n    };\n  }\n\n  static _fromErrorAndOperation(auth, error, operationType, user) {\n    return new MultiFactorError(auth, error, operationType, user);\n  }\n\n}\n\nfunction _processCredentialSavingMfaContextIfNecessary(auth, operationType, credential, user) {\n  const idTokenProvider = operationType === \"reauthenticate\"\n  /* OperationType.REAUTHENTICATE */\n  ? credential._getReauthenticationResolver(auth) : credential._getIdTokenResponse(auth);\n  return idTokenProvider.catch(error => {\n    if (error.code === `auth/${\"multi-factor-auth-required\"\n    /* AuthErrorCode.MFA_REQUIRED */\n    }`) {\n      throw MultiFactorError._fromErrorAndOperation(auth, error, operationType, user);\n    }\n\n    throw error;\n  });\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Takes a set of UserInfo provider data and converts it to a set of names\r\n */\n\n\nfunction providerDataAsNames(providerData) {\n  return new Set(providerData.map(({\n    providerId\n  }) => providerId).filter(pid => !!pid));\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Unlinks a provider from a user account.\r\n *\r\n * @param user - The user.\r\n * @param providerId - The provider to unlink.\r\n *\r\n * @public\r\n */\n\n\nfunction unlink(_x72, _x73) {\n  return _unlink.apply(this, arguments);\n}\n\nfunction _unlink() {\n  _unlink = _asyncToGenerator(function* (user, providerId) {\n    const userInternal = getModularInstance(user);\n    yield _assertLinkedStatus(true, userInternal, providerId);\n    const {\n      providerUserInfo\n    } = yield deleteLinkedAccounts(userInternal.auth, {\n      idToken: yield userInternal.getIdToken(),\n      deleteProvider: [providerId]\n    });\n    const providersLeft = providerDataAsNames(providerUserInfo || []);\n    userInternal.providerData = userInternal.providerData.filter(pd => providersLeft.has(pd.providerId));\n\n    if (!providersLeft.has(\"phone\"\n    /* ProviderId.PHONE */\n    )) {\n      userInternal.phoneNumber = null;\n    }\n\n    yield userInternal.auth._persistUserIfCurrent(userInternal);\n    return userInternal;\n  });\n  return _unlink.apply(this, arguments);\n}\n\nfunction _link$1(_x74, _x75) {\n  return _link$.apply(this, arguments);\n}\n\nfunction _link$() {\n  _link$ = _asyncToGenerator(function* (user, credential, bypassAuthState = false) {\n    const response = yield _logoutIfInvalidated(user, credential._linkToIdToken(user.auth, yield user.getIdToken()), bypassAuthState);\n    return UserCredentialImpl._forOperation(user, \"link\"\n    /* OperationType.LINK */\n    , response);\n  });\n  return _link$.apply(this, arguments);\n}\n\nfunction _assertLinkedStatus(_x76, _x77, _x78) {\n  return _assertLinkedStatus2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _assertLinkedStatus2() {\n  _assertLinkedStatus2 = _asyncToGenerator(function* (expected, user, provider) {\n    yield _reloadWithoutSaving(user);\n    const providerIds = providerDataAsNames(user.providerData);\n    const code = expected === false ? \"provider-already-linked\"\n    /* AuthErrorCode.PROVIDER_ALREADY_LINKED */\n    : \"no-such-provider\"\n    /* AuthErrorCode.NO_SUCH_PROVIDER */\n    ;\n\n    _assert(providerIds.has(provider) === expected, user.auth, code);\n  });\n  return _assertLinkedStatus2.apply(this, arguments);\n}\n\nfunction _reauthenticate(_x79, _x80) {\n  return _reauthenticate2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _reauthenticate2() {\n  _reauthenticate2 = _asyncToGenerator(function* (user, credential, bypassAuthState = false) {\n    const {\n      auth\n    } = user;\n    const operationType = \"reauthenticate\"\n    /* OperationType.REAUTHENTICATE */\n    ;\n\n    try {\n      const response = yield _logoutIfInvalidated(user, _processCredentialSavingMfaContextIfNecessary(auth, operationType, credential, user), bypassAuthState);\n\n      _assert(response.idToken, auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      const parsed = _parseToken(response.idToken);\n\n      _assert(parsed, auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      const {\n        sub: localId\n      } = parsed;\n\n      _assert(user.uid === localId, auth, \"user-mismatch\"\n      /* AuthErrorCode.USER_MISMATCH */\n      );\n\n      return UserCredentialImpl._forOperation(user, operationType, response);\n    } catch (e) {\n      // Convert user deleted error into user mismatch\n      if ((e === null || e === void 0 ? void 0 : e.code) === `auth/${\"user-not-found\"\n      /* AuthErrorCode.USER_DELETED */\n      }`) {\n        _fail(auth, \"user-mismatch\"\n        /* AuthErrorCode.USER_MISMATCH */\n        );\n      }\n\n      throw e;\n    }\n  });\n  return _reauthenticate2.apply(this, arguments);\n}\n\nfunction _signInWithCredential(_x81, _x82) {\n  return _signInWithCredential2.apply(this, arguments);\n}\n/**\r\n * Asynchronously signs in with the given credentials.\r\n *\r\n * @remarks\r\n * An {@link AuthProvider} can be used to generate the credential.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param credential - The auth credential.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithCredential2() {\n  _signInWithCredential2 = _asyncToGenerator(function* (auth, credential, bypassAuthState = false) {\n    const operationType = \"signIn\"\n    /* OperationType.SIGN_IN */\n    ;\n    const response = yield _processCredentialSavingMfaContextIfNecessary(auth, operationType, credential);\n    const userCredential = yield UserCredentialImpl._fromIdTokenResponse(auth, operationType, response);\n\n    if (!bypassAuthState) {\n      yield auth._updateCurrentUser(userCredential.user);\n    }\n\n    return userCredential;\n  });\n  return _signInWithCredential2.apply(this, arguments);\n}\n\nfunction signInWithCredential(_x83, _x84) {\n  return _signInWithCredential3.apply(this, arguments);\n}\n/**\r\n * Links the user account with the given credentials.\r\n *\r\n * @remarks\r\n * An {@link AuthProvider} can be used to generate the credential.\r\n *\r\n * @param user - The user.\r\n * @param credential - The auth credential.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithCredential3() {\n  _signInWithCredential3 = _asyncToGenerator(function* (auth, credential) {\n    return _signInWithCredential(_castAuth(auth), credential);\n  });\n  return _signInWithCredential3.apply(this, arguments);\n}\n\nfunction linkWithCredential(_x85, _x86) {\n  return _linkWithCredential.apply(this, arguments);\n}\n/**\r\n * Re-authenticates a user using a fresh credential.\r\n *\r\n * @remarks\r\n * Use before operations such as {@link updatePassword} that require tokens from recent sign-in\r\n * attempts. This method can be used to recover from a `CREDENTIAL_TOO_OLD_LOGIN_AGAIN` error\r\n * or a `TOKEN_EXPIRED` error.\r\n *\r\n * @param user - The user.\r\n * @param credential - The auth credential.\r\n *\r\n * @public\r\n */\n\n\nfunction _linkWithCredential() {\n  _linkWithCredential = _asyncToGenerator(function* (user, credential) {\n    const userInternal = getModularInstance(user);\n    yield _assertLinkedStatus(false, userInternal, credential.providerId);\n    return _link$1(userInternal, credential);\n  });\n  return _linkWithCredential.apply(this, arguments);\n}\n\nfunction reauthenticateWithCredential(_x87, _x88) {\n  return _reauthenticateWithCredential.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _reauthenticateWithCredential() {\n  _reauthenticateWithCredential = _asyncToGenerator(function* (user, credential) {\n    return _reauthenticate(getModularInstance(user), credential);\n  });\n  return _reauthenticateWithCredential.apply(this, arguments);\n}\n\nfunction signInWithCustomToken$1(_x89, _x90) {\n  return _signInWithCustomToken$.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Asynchronously signs in using a custom token.\r\n *\r\n * @remarks\r\n * Custom tokens are used to integrate Firebase Auth with existing auth systems, and must\r\n * be generated by an auth backend using the\r\n * {@link https://firebase.google.com/docs/reference/admin/node/admin.auth.Auth#createcustomtoken | createCustomToken}\r\n * method in the {@link https://firebase.google.com/docs/auth/admin | Admin SDK} .\r\n *\r\n * Fails with an error if the token is invalid, expired, or not accepted by the Firebase Auth service.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param customToken - The custom token to sign in with.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithCustomToken$() {\n  _signInWithCustomToken$ = _asyncToGenerator(function* (auth, request) {\n    return _performSignInRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:signInWithCustomToken\"\n    /* Endpoint.SIGN_IN_WITH_CUSTOM_TOKEN */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _signInWithCustomToken$.apply(this, arguments);\n}\n\nfunction signInWithCustomToken(_x91, _x92) {\n  return _signInWithCustomToken.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _signInWithCustomToken() {\n  _signInWithCustomToken = _asyncToGenerator(function* (auth, customToken) {\n    const authInternal = _castAuth(auth);\n\n    const response = yield signInWithCustomToken$1(authInternal, {\n      token: customToken,\n      returnSecureToken: true\n    });\n    const cred = yield UserCredentialImpl._fromIdTokenResponse(authInternal, \"signIn\"\n    /* OperationType.SIGN_IN */\n    , response);\n    yield authInternal._updateCurrentUser(cred.user);\n    return cred;\n  });\n  return _signInWithCustomToken.apply(this, arguments);\n}\n\nclass MultiFactorInfoImpl {\n  constructor(factorId, response) {\n    this.factorId = factorId;\n    this.uid = response.mfaEnrollmentId;\n    this.enrollmentTime = new Date(response.enrolledAt).toUTCString();\n    this.displayName = response.displayName;\n  }\n\n  static _fromServerResponse(auth, enrollment) {\n    if ('phoneInfo' in enrollment) {\n      return PhoneMultiFactorInfoImpl._fromServerResponse(auth, enrollment);\n    } else if ('totpInfo' in enrollment) {\n      return TotpMultiFactorInfoImpl._fromServerResponse(auth, enrollment);\n    }\n\n    return _fail(auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n  }\n\n}\n\nclass PhoneMultiFactorInfoImpl extends MultiFactorInfoImpl {\n  constructor(response) {\n    super(\"phone\"\n    /* FactorId.PHONE */\n    , response);\n    this.phoneNumber = response.phoneInfo;\n  }\n\n  static _fromServerResponse(_auth, enrollment) {\n    return new PhoneMultiFactorInfoImpl(enrollment);\n  }\n\n}\n\nclass TotpMultiFactorInfoImpl extends MultiFactorInfoImpl {\n  constructor(response) {\n    super(\"totp\"\n    /* FactorId.TOTP */\n    , response);\n  }\n\n  static _fromServerResponse(_auth, enrollment) {\n    return new TotpMultiFactorInfoImpl(enrollment);\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _setActionCodeSettingsOnRequest(auth, request, actionCodeSettings) {\n  var _a;\n\n  _assert(((_a = actionCodeSettings.url) === null || _a === void 0 ? void 0 : _a.length) > 0, auth, \"invalid-continue-uri\"\n  /* AuthErrorCode.INVALID_CONTINUE_URI */\n  );\n\n  _assert(typeof actionCodeSettings.dynamicLinkDomain === 'undefined' || actionCodeSettings.dynamicLinkDomain.length > 0, auth, \"invalid-dynamic-link-domain\"\n  /* AuthErrorCode.INVALID_DYNAMIC_LINK_DOMAIN */\n  );\n\n  request.continueUrl = actionCodeSettings.url;\n  request.dynamicLinkDomain = actionCodeSettings.dynamicLinkDomain;\n  request.canHandleCodeInApp = actionCodeSettings.handleCodeInApp;\n\n  if (actionCodeSettings.iOS) {\n    _assert(actionCodeSettings.iOS.bundleId.length > 0, auth, \"missing-ios-bundle-id\"\n    /* AuthErrorCode.MISSING_IOS_BUNDLE_ID */\n    );\n\n    request.iOSBundleId = actionCodeSettings.iOS.bundleId;\n  }\n\n  if (actionCodeSettings.android) {\n    _assert(actionCodeSettings.android.packageName.length > 0, auth, \"missing-android-pkg-name\"\n    /* AuthErrorCode.MISSING_ANDROID_PACKAGE_NAME */\n    );\n\n    request.androidInstallApp = actionCodeSettings.android.installApp;\n    request.androidMinimumVersionCode = actionCodeSettings.android.minimumVersion;\n    request.androidPackageName = actionCodeSettings.android.packageName;\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Sends a password reset email to the given email address.\r\n *\r\n * @remarks\r\n * To complete the password reset, call {@link confirmPasswordReset} with the code supplied in\r\n * the email sent to the user, along with the new password specified by the user.\r\n *\r\n * @example\r\n * ```javascript\r\n * const actionCodeSettings = {\r\n *   url: 'https://www.example.com/?email=<EMAIL>',\r\n *   iOS: {\r\n *      bundleId: 'com.example.ios'\r\n *   },\r\n *   android: {\r\n *     packageName: 'com.example.android',\r\n *     installApp: true,\r\n *     minimumVersion: '12'\r\n *   },\r\n *   handleCodeInApp: true\r\n * };\r\n * await sendPasswordResetEmail(auth, '<EMAIL>', actionCodeSettings);\r\n * // Obtain code from user.\r\n * await confirmPasswordReset('<EMAIL>', code);\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param email - The user's email address.\r\n * @param actionCodeSettings - The {@link ActionCodeSettings}.\r\n *\r\n * @public\r\n */\n\n\nfunction sendPasswordResetEmail(_x93, _x94, _x95) {\n  return _sendPasswordResetEmail.apply(this, arguments);\n}\n/**\r\n * Completes the password reset process, given a confirmation code and new password.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param oobCode - A confirmation code sent to the user.\r\n * @param newPassword - The new password.\r\n *\r\n * @public\r\n */\n\n\nfunction _sendPasswordResetEmail() {\n  _sendPasswordResetEmail = _asyncToGenerator(function* (auth, email, actionCodeSettings) {\n    var _a;\n\n    const authInternal = _castAuth(auth);\n\n    const request = {\n      requestType: \"PASSWORD_RESET\"\n      /* ActionCodeOperation.PASSWORD_RESET */\n      ,\n      email,\n      clientType: \"CLIENT_TYPE_WEB\"\n      /* RecaptchaClientType.WEB */\n\n    };\n\n    if ((_a = authInternal._getRecaptchaConfig()) === null || _a === void 0 ? void 0 : _a.emailPasswordEnabled) {\n      const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"getOobCode\"\n      /* RecaptchaActionName.GET_OOB_CODE */\n      , true);\n\n      if (actionCodeSettings) {\n        _setActionCodeSettingsOnRequest(authInternal, requestWithRecaptcha, actionCodeSettings);\n      }\n\n      yield sendPasswordResetEmail$1(authInternal, requestWithRecaptcha);\n    } else {\n      if (actionCodeSettings) {\n        _setActionCodeSettingsOnRequest(authInternal, request, actionCodeSettings);\n      }\n\n      yield sendPasswordResetEmail$1(authInternal, request).catch( /*#__PURE__*/function () {\n        var _ref24 = _asyncToGenerator(function* (error) {\n          if (error.code === `auth/${\"missing-recaptcha-token\"\n          /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n          }`) {\n            console.log('Password resets are protected by reCAPTCHA for this project. Automatically triggering the reCAPTCHA flow and restarting the password reset flow.');\n            const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"getOobCode\"\n            /* RecaptchaActionName.GET_OOB_CODE */\n            , true);\n\n            if (actionCodeSettings) {\n              _setActionCodeSettingsOnRequest(authInternal, requestWithRecaptcha, actionCodeSettings);\n            }\n\n            yield sendPasswordResetEmail$1(authInternal, requestWithRecaptcha);\n          } else {\n            return Promise.reject(error);\n          }\n        });\n\n        return function (_x197) {\n          return _ref24.apply(this, arguments);\n        };\n      }());\n    }\n  });\n  return _sendPasswordResetEmail.apply(this, arguments);\n}\n\nfunction confirmPasswordReset(_x96, _x97, _x98) {\n  return _confirmPasswordReset.apply(this, arguments);\n}\n/**\r\n * Applies a verification code sent to the user by email or other out-of-band mechanism.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param oobCode - A verification code sent to the user.\r\n *\r\n * @public\r\n */\n\n\nfunction _confirmPasswordReset() {\n  _confirmPasswordReset = _asyncToGenerator(function* (auth, oobCode, newPassword) {\n    yield resetPassword(getModularInstance(auth), {\n      oobCode,\n      newPassword\n    }); // Do not return the email.\n  });\n  return _confirmPasswordReset.apply(this, arguments);\n}\n\nfunction applyActionCode(_x99, _x100) {\n  return _applyActionCode.apply(this, arguments);\n}\n/**\r\n * Checks a verification code sent to the user by email or other out-of-band mechanism.\r\n *\r\n * @returns metadata about the code.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param oobCode - A verification code sent to the user.\r\n *\r\n * @public\r\n */\n\n\nfunction _applyActionCode() {\n  _applyActionCode = _asyncToGenerator(function* (auth, oobCode) {\n    yield applyActionCode$1(getModularInstance(auth), {\n      oobCode\n    });\n  });\n  return _applyActionCode.apply(this, arguments);\n}\n\nfunction checkActionCode(_x101, _x102) {\n  return _checkActionCode.apply(this, arguments);\n}\n/**\r\n * Checks a password reset code sent to the user by email or other out-of-band mechanism.\r\n *\r\n * @returns the user's email address if valid.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param code - A verification code sent to the user.\r\n *\r\n * @public\r\n */\n\n\nfunction _checkActionCode() {\n  _checkActionCode = _asyncToGenerator(function* (auth, oobCode) {\n    const authModular = getModularInstance(auth);\n    const response = yield resetPassword(authModular, {\n      oobCode\n    }); // Email could be empty only if the request type is EMAIL_SIGNIN or\n    // VERIFY_AND_CHANGE_EMAIL.\n    // New email should not be empty if the request type is\n    // VERIFY_AND_CHANGE_EMAIL.\n    // Multi-factor info could not be empty if the request type is\n    // REVERT_SECOND_FACTOR_ADDITION.\n\n    const operation = response.requestType;\n\n    _assert(operation, authModular, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    switch (operation) {\n      case \"EMAIL_SIGNIN\"\n      /* ActionCodeOperation.EMAIL_SIGNIN */\n      :\n        break;\n\n      case \"VERIFY_AND_CHANGE_EMAIL\"\n      /* ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL */\n      :\n        _assert(response.newEmail, authModular, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n        break;\n\n      case \"REVERT_SECOND_FACTOR_ADDITION\"\n      /* ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION */\n      :\n        _assert(response.mfaInfo, authModular, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n      // fall through\n\n      default:\n        _assert(response.email, authModular, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n    } // The multi-factor info for revert second factor addition\n\n\n    let multiFactorInfo = null;\n\n    if (response.mfaInfo) {\n      multiFactorInfo = MultiFactorInfoImpl._fromServerResponse(_castAuth(authModular), response.mfaInfo);\n    }\n\n    return {\n      data: {\n        email: (response.requestType === \"VERIFY_AND_CHANGE_EMAIL\"\n        /* ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL */\n        ? response.newEmail : response.email) || null,\n        previousEmail: (response.requestType === \"VERIFY_AND_CHANGE_EMAIL\"\n        /* ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL */\n        ? response.email : response.newEmail) || null,\n        multiFactorInfo\n      },\n      operation\n    };\n  });\n  return _checkActionCode.apply(this, arguments);\n}\n\nfunction verifyPasswordResetCode(_x103, _x104) {\n  return _verifyPasswordResetCode.apply(this, arguments);\n}\n/**\r\n * Creates a new user account associated with the specified email address and password.\r\n *\r\n * @remarks\r\n * On successful creation of the user account, this user will also be signed in to your application.\r\n *\r\n * User account creation can fail if the account already exists or the password is invalid.\r\n *\r\n * Note: The email address acts as a unique identifier for the user and enables an email-based\r\n * password reset. This function will create a new user account and set the initial user password.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param email - The user's email address.\r\n * @param password - The user's chosen password.\r\n *\r\n * @public\r\n */\n\n\nfunction _verifyPasswordResetCode() {\n  _verifyPasswordResetCode = _asyncToGenerator(function* (auth, code) {\n    const {\n      data\n    } = yield checkActionCode(getModularInstance(auth), code); // Email should always be present since a code was sent to it\n\n    return data.email;\n  });\n  return _verifyPasswordResetCode.apply(this, arguments);\n}\n\nfunction createUserWithEmailAndPassword(_x105, _x106, _x107) {\n  return _createUserWithEmailAndPassword.apply(this, arguments);\n}\n/**\r\n * Asynchronously signs in using an email and password.\r\n *\r\n * @remarks\r\n * Fails with an error if the email address and password do not match.\r\n *\r\n * Note: The user's password is NOT the password used to access the user's email account. The\r\n * email address serves as a unique identifier for the user, and the password is used to access\r\n * the user's account in your Firebase project. See also: {@link createUserWithEmailAndPassword}.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param email - The users email address.\r\n * @param password - The users password.\r\n *\r\n * @public\r\n */\n\n\nfunction _createUserWithEmailAndPassword() {\n  _createUserWithEmailAndPassword = _asyncToGenerator(function* (auth, email, password) {\n    var _a;\n\n    const authInternal = _castAuth(auth);\n\n    const request = {\n      returnSecureToken: true,\n      email,\n      password,\n      clientType: \"CLIENT_TYPE_WEB\"\n      /* RecaptchaClientType.WEB */\n\n    };\n    let signUpResponse;\n\n    if ((_a = authInternal._getRecaptchaConfig()) === null || _a === void 0 ? void 0 : _a.emailPasswordEnabled) {\n      const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"signUpPassword\"\n      /* RecaptchaActionName.SIGN_UP_PASSWORD */\n      );\n      signUpResponse = signUp(authInternal, requestWithRecaptcha);\n    } else {\n      signUpResponse = signUp(authInternal, request).catch( /*#__PURE__*/function () {\n        var _ref25 = _asyncToGenerator(function* (error) {\n          if (error.code === `auth/${\"missing-recaptcha-token\"\n          /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n          }`) {\n            console.log('Sign-up is protected by reCAPTCHA for this project. Automatically triggering the reCAPTCHA flow and restarting the sign-up flow.');\n            const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"signUpPassword\"\n            /* RecaptchaActionName.SIGN_UP_PASSWORD */\n            );\n            return signUp(authInternal, requestWithRecaptcha);\n          } else {\n            return Promise.reject(error);\n          }\n        });\n\n        return function (_x198) {\n          return _ref25.apply(this, arguments);\n        };\n      }());\n    }\n\n    const response = yield signUpResponse.catch(error => {\n      return Promise.reject(error);\n    });\n    const userCredential = yield UserCredentialImpl._fromIdTokenResponse(authInternal, \"signIn\"\n    /* OperationType.SIGN_IN */\n    , response);\n    yield authInternal._updateCurrentUser(userCredential.user);\n    return userCredential;\n  });\n  return _createUserWithEmailAndPassword.apply(this, arguments);\n}\n\nfunction signInWithEmailAndPassword(auth, email, password) {\n  return signInWithCredential(getModularInstance(auth), EmailAuthProvider.credential(email, password));\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Sends a sign-in email link to the user with the specified email.\r\n *\r\n * @remarks\r\n * The sign-in operation has to always be completed in the app unlike other out of band email\r\n * actions (password reset and email verifications). This is because, at the end of the flow,\r\n * the user is expected to be signed in and their Auth state persisted within the app.\r\n *\r\n * To complete sign in with the email link, call {@link signInWithEmailLink} with the email\r\n * address and the email link supplied in the email sent to the user.\r\n *\r\n * @example\r\n * ```javascript\r\n * const actionCodeSettings = {\r\n *   url: 'https://www.example.com/?email=<EMAIL>',\r\n *   iOS: {\r\n *      bundleId: 'com.example.ios'\r\n *   },\r\n *   android: {\r\n *     packageName: 'com.example.android',\r\n *     installApp: true,\r\n *     minimumVersion: '12'\r\n *   },\r\n *   handleCodeInApp: true\r\n * };\r\n * await sendSignInLinkToEmail(auth, '<EMAIL>', actionCodeSettings);\r\n * // Obtain emailLink from the user.\r\n * if(isSignInWithEmailLink(auth, emailLink)) {\r\n *   await signInWithEmailLink(auth, '<EMAIL>', emailLink);\r\n * }\r\n * ```\r\n *\r\n * @param authInternal - The {@link Auth} instance.\r\n * @param email - The user's email address.\r\n * @param actionCodeSettings - The {@link ActionCodeSettings}.\r\n *\r\n * @public\r\n */\n\n\nfunction sendSignInLinkToEmail(_x108, _x109, _x110) {\n  return _sendSignInLinkToEmail.apply(this, arguments);\n}\n/**\r\n * Checks if an incoming link is a sign-in with email link suitable for {@link signInWithEmailLink}.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param emailLink - The link sent to the user's email address.\r\n *\r\n * @public\r\n */\n\n\nfunction _sendSignInLinkToEmail() {\n  _sendSignInLinkToEmail = _asyncToGenerator(function* (auth, email, actionCodeSettings) {\n    var _a;\n\n    const authInternal = _castAuth(auth);\n\n    const request = {\n      requestType: \"EMAIL_SIGNIN\"\n      /* ActionCodeOperation.EMAIL_SIGNIN */\n      ,\n      email,\n      clientType: \"CLIENT_TYPE_WEB\"\n      /* RecaptchaClientType.WEB */\n\n    };\n\n    function setActionCodeSettings(request, actionCodeSettings) {\n      _assert(actionCodeSettings.handleCodeInApp, authInternal, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      if (actionCodeSettings) {\n        _setActionCodeSettingsOnRequest(authInternal, request, actionCodeSettings);\n      }\n    }\n\n    if ((_a = authInternal._getRecaptchaConfig()) === null || _a === void 0 ? void 0 : _a.emailPasswordEnabled) {\n      const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"getOobCode\"\n      /* RecaptchaActionName.GET_OOB_CODE */\n      , true);\n      setActionCodeSettings(requestWithRecaptcha, actionCodeSettings);\n      yield sendSignInLinkToEmail$1(authInternal, requestWithRecaptcha);\n    } else {\n      setActionCodeSettings(request, actionCodeSettings);\n      yield sendSignInLinkToEmail$1(authInternal, request).catch( /*#__PURE__*/function () {\n        var _ref26 = _asyncToGenerator(function* (error) {\n          if (error.code === `auth/${\"missing-recaptcha-token\"\n          /* AuthErrorCode.MISSING_RECAPTCHA_TOKEN */\n          }`) {\n            console.log('Email link sign-in is protected by reCAPTCHA for this project. Automatically triggering the reCAPTCHA flow and restarting the sign-in flow.');\n            const requestWithRecaptcha = yield injectRecaptchaFields(authInternal, request, \"getOobCode\"\n            /* RecaptchaActionName.GET_OOB_CODE */\n            , true);\n            setActionCodeSettings(requestWithRecaptcha, actionCodeSettings);\n            yield sendSignInLinkToEmail$1(authInternal, requestWithRecaptcha);\n          } else {\n            return Promise.reject(error);\n          }\n        });\n\n        return function (_x199) {\n          return _ref26.apply(this, arguments);\n        };\n      }());\n    }\n  });\n  return _sendSignInLinkToEmail.apply(this, arguments);\n}\n\nfunction isSignInWithEmailLink(auth, emailLink) {\n  const actionCodeUrl = ActionCodeURL.parseLink(emailLink);\n  return (actionCodeUrl === null || actionCodeUrl === void 0 ? void 0 : actionCodeUrl.operation) === \"EMAIL_SIGNIN\"\n  /* ActionCodeOperation.EMAIL_SIGNIN */\n  ;\n}\n/**\r\n * Asynchronously signs in using an email and sign-in email link.\r\n *\r\n * @remarks\r\n * If no link is passed, the link is inferred from the current URL.\r\n *\r\n * Fails with an error if the email address is invalid or OTP in email link expires.\r\n *\r\n * Note: Confirm the link is a sign-in email link before calling this method firebase.auth.Auth.isSignInWithEmailLink.\r\n *\r\n * @example\r\n * ```javascript\r\n * const actionCodeSettings = {\r\n *   url: 'https://www.example.com/?email=<EMAIL>',\r\n *   iOS: {\r\n *      bundleId: 'com.example.ios'\r\n *   },\r\n *   android: {\r\n *     packageName: 'com.example.android',\r\n *     installApp: true,\r\n *     minimumVersion: '12'\r\n *   },\r\n *   handleCodeInApp: true\r\n * };\r\n * await sendSignInLinkToEmail(auth, '<EMAIL>', actionCodeSettings);\r\n * // Obtain emailLink from the user.\r\n * if(isSignInWithEmailLink(auth, emailLink)) {\r\n *   await signInWithEmailLink(auth, '<EMAIL>', emailLink);\r\n * }\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param email - The user's email address.\r\n * @param emailLink - The link sent to the user's email address.\r\n *\r\n * @public\r\n */\n\n\nfunction signInWithEmailLink(_x111, _x112, _x113) {\n  return _signInWithEmailLink.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _signInWithEmailLink() {\n  _signInWithEmailLink = _asyncToGenerator(function* (auth, email, emailLink) {\n    const authModular = getModularInstance(auth);\n    const credential = EmailAuthProvider.credentialWithLink(email, emailLink || _getCurrentUrl()); // Check if the tenant ID in the email link matches the tenant ID on Auth\n    // instance.\n\n    _assert(credential._tenantId === (authModular.tenantId || null), authModular, \"tenant-id-mismatch\"\n    /* AuthErrorCode.TENANT_ID_MISMATCH */\n    );\n\n    return signInWithCredential(authModular, credential);\n  });\n  return _signInWithEmailLink.apply(this, arguments);\n}\n\nfunction createAuthUri(_x114, _x115) {\n  return _createAuthUri.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Gets the list of possible sign in methods for the given email address.\r\n *\r\n * @remarks\r\n * This is useful to differentiate methods of sign-in for the same provider, eg.\r\n * {@link EmailAuthProvider} which has 2 methods of sign-in,\r\n * {@link SignInMethod}.EMAIL_PASSWORD and\r\n * {@link SignInMethod}.EMAIL_LINK.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param email - The user's email address.\r\n *\r\n * @public\r\n */\n\n\nfunction _createAuthUri() {\n  _createAuthUri = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:createAuthUri\"\n    /* Endpoint.CREATE_AUTH_URI */\n    , _addTidIfNecessary(auth, request));\n  });\n  return _createAuthUri.apply(this, arguments);\n}\n\nfunction fetchSignInMethodsForEmail(_x116, _x117) {\n  return _fetchSignInMethodsForEmail.apply(this, arguments);\n}\n/**\r\n * Sends a verification email to a user.\r\n *\r\n * @remarks\r\n * The verification process is completed by calling {@link applyActionCode}.\r\n *\r\n * @example\r\n * ```javascript\r\n * const actionCodeSettings = {\r\n *   url: 'https://www.example.com/?email=<EMAIL>',\r\n *   iOS: {\r\n *      bundleId: 'com.example.ios'\r\n *   },\r\n *   android: {\r\n *     packageName: 'com.example.android',\r\n *     installApp: true,\r\n *     minimumVersion: '12'\r\n *   },\r\n *   handleCodeInApp: true\r\n * };\r\n * await sendEmailVerification(user, actionCodeSettings);\r\n * // Obtain code from the user.\r\n * await applyActionCode(auth, code);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param actionCodeSettings - The {@link ActionCodeSettings}.\r\n *\r\n * @public\r\n */\n\n\nfunction _fetchSignInMethodsForEmail() {\n  _fetchSignInMethodsForEmail = _asyncToGenerator(function* (auth, email) {\n    // createAuthUri returns an error if continue URI is not http or https.\n    // For environments like Cordova, Chrome extensions, native frameworks, file\n    // systems, etc, use http://localhost as continue URL.\n    const continueUri = _isHttpOrHttps() ? _getCurrentUrl() : 'http://localhost';\n    const request = {\n      identifier: email,\n      continueUri\n    };\n    const {\n      signinMethods\n    } = yield createAuthUri(getModularInstance(auth), request);\n    return signinMethods || [];\n  });\n  return _fetchSignInMethodsForEmail.apply(this, arguments);\n}\n\nfunction sendEmailVerification(_x118, _x119) {\n  return _sendEmailVerification.apply(this, arguments);\n}\n/**\r\n * Sends a verification email to a new email address.\r\n *\r\n * @remarks\r\n * The user's email will be updated to the new one after being verified.\r\n *\r\n * If you have a custom email action handler, you can complete the verification process by calling\r\n * {@link applyActionCode}.\r\n *\r\n * @example\r\n * ```javascript\r\n * const actionCodeSettings = {\r\n *   url: 'https://www.example.com/?email=<EMAIL>',\r\n *   iOS: {\r\n *      bundleId: 'com.example.ios'\r\n *   },\r\n *   android: {\r\n *     packageName: 'com.example.android',\r\n *     installApp: true,\r\n *     minimumVersion: '12'\r\n *   },\r\n *   handleCodeInApp: true\r\n * };\r\n * await verifyBeforeUpdateEmail(user, '<EMAIL>', actionCodeSettings);\r\n * // Obtain code from the user.\r\n * await applyActionCode(auth, code);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param newEmail - The new email address to be verified before update.\r\n * @param actionCodeSettings - The {@link ActionCodeSettings}.\r\n *\r\n * @public\r\n */\n\n\nfunction _sendEmailVerification() {\n  _sendEmailVerification = _asyncToGenerator(function* (user, actionCodeSettings) {\n    const userInternal = getModularInstance(user);\n    const idToken = yield user.getIdToken();\n    const request = {\n      requestType: \"VERIFY_EMAIL\"\n      /* ActionCodeOperation.VERIFY_EMAIL */\n      ,\n      idToken\n    };\n\n    if (actionCodeSettings) {\n      _setActionCodeSettingsOnRequest(userInternal.auth, request, actionCodeSettings);\n    }\n\n    const {\n      email\n    } = yield sendEmailVerification$1(userInternal.auth, request);\n\n    if (email !== user.email) {\n      yield user.reload();\n    }\n  });\n  return _sendEmailVerification.apply(this, arguments);\n}\n\nfunction verifyBeforeUpdateEmail(_x120, _x121, _x122) {\n  return _verifyBeforeUpdateEmail.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _verifyBeforeUpdateEmail() {\n  _verifyBeforeUpdateEmail = _asyncToGenerator(function* (user, newEmail, actionCodeSettings) {\n    const userInternal = getModularInstance(user);\n    const idToken = yield user.getIdToken();\n    const request = {\n      requestType: \"VERIFY_AND_CHANGE_EMAIL\"\n      /* ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL */\n      ,\n      idToken,\n      newEmail\n    };\n\n    if (actionCodeSettings) {\n      _setActionCodeSettingsOnRequest(userInternal.auth, request, actionCodeSettings);\n    }\n\n    const {\n      email\n    } = yield verifyAndChangeEmail(userInternal.auth, request);\n\n    if (email !== user.email) {\n      // If the local copy of the email on user is outdated, reload the\n      // user.\n      yield user.reload();\n    }\n  });\n  return _verifyBeforeUpdateEmail.apply(this, arguments);\n}\n\nfunction updateProfile$1(_x123, _x124) {\n  return _updateProfile$.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Updates a user's profile data.\r\n *\r\n * @param user - The user.\r\n * @param profile - The profile's `displayName` and `photoURL` to update.\r\n *\r\n * @public\r\n */\n\n\nfunction _updateProfile$() {\n  _updateProfile$ = _asyncToGenerator(function* (auth, request) {\n    return _performApiRequest(auth, \"POST\"\n    /* HttpMethod.POST */\n    , \"/v1/accounts:update\"\n    /* Endpoint.SET_ACCOUNT_INFO */\n    , request);\n  });\n  return _updateProfile$.apply(this, arguments);\n}\n\nfunction updateProfile(_x125, _x126) {\n  return _updateProfile.apply(this, arguments);\n}\n/**\r\n * Updates the user's email address.\r\n *\r\n * @remarks\r\n * An email will be sent to the original email address (if it was set) that allows to revoke the\r\n * email address change, in order to protect them from account hijacking.\r\n *\r\n * Important: this is a security sensitive operation that requires the user to have recently signed\r\n * in. If this requirement isn't met, ask the user to authenticate again and then call\r\n * {@link reauthenticateWithCredential}.\r\n *\r\n * @param user - The user.\r\n * @param newEmail - The new email address.\r\n *\r\n * @public\r\n */\n\n\nfunction _updateProfile() {\n  _updateProfile = _asyncToGenerator(function* (user, {\n    displayName,\n    photoURL: photoUrl\n  }) {\n    if (displayName === undefined && photoUrl === undefined) {\n      return;\n    }\n\n    const userInternal = getModularInstance(user);\n    const idToken = yield userInternal.getIdToken();\n    const profileRequest = {\n      idToken,\n      displayName,\n      photoUrl,\n      returnSecureToken: true\n    };\n    const response = yield _logoutIfInvalidated(userInternal, updateProfile$1(userInternal.auth, profileRequest));\n    userInternal.displayName = response.displayName || null;\n    userInternal.photoURL = response.photoUrl || null; // Update the password provider as well\n\n    const passwordProvider = userInternal.providerData.find(({\n      providerId\n    }) => providerId === \"password\"\n    /* ProviderId.PASSWORD */\n    );\n\n    if (passwordProvider) {\n      passwordProvider.displayName = userInternal.displayName;\n      passwordProvider.photoURL = userInternal.photoURL;\n    }\n\n    yield userInternal._updateTokensIfNecessary(response);\n  });\n  return _updateProfile.apply(this, arguments);\n}\n\nfunction updateEmail(user, newEmail) {\n  return updateEmailOrPassword(getModularInstance(user), newEmail, null);\n}\n/**\r\n * Updates the user's password.\r\n *\r\n * @remarks\r\n * Important: this is a security sensitive operation that requires the user to have recently signed\r\n * in. If this requirement isn't met, ask the user to authenticate again and then call\r\n * {@link reauthenticateWithCredential}.\r\n *\r\n * @param user - The user.\r\n * @param newPassword - The new password.\r\n *\r\n * @public\r\n */\n\n\nfunction updatePassword(user, newPassword) {\n  return updateEmailOrPassword(getModularInstance(user), null, newPassword);\n}\n\nfunction updateEmailOrPassword(_x127, _x128, _x129) {\n  return _updateEmailOrPassword.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Parse the `AdditionalUserInfo` from the ID token response.\r\n *\r\n */\n\n\nfunction _updateEmailOrPassword() {\n  _updateEmailOrPassword = _asyncToGenerator(function* (user, email, password) {\n    const {\n      auth\n    } = user;\n    const idToken = yield user.getIdToken();\n    const request = {\n      idToken,\n      returnSecureToken: true\n    };\n\n    if (email) {\n      request.email = email;\n    }\n\n    if (password) {\n      request.password = password;\n    }\n\n    const response = yield _logoutIfInvalidated(user, updateEmailPassword(auth, request));\n    yield user._updateTokensIfNecessary(response,\n    /* reload */\n    true);\n  });\n  return _updateEmailOrPassword.apply(this, arguments);\n}\n\nfunction _fromIdTokenResponse(idTokenResponse) {\n  var _a, _b;\n\n  if (!idTokenResponse) {\n    return null;\n  }\n\n  const {\n    providerId\n  } = idTokenResponse;\n  const profile = idTokenResponse.rawUserInfo ? JSON.parse(idTokenResponse.rawUserInfo) : {};\n  const isNewUser = idTokenResponse.isNewUser || idTokenResponse.kind === \"identitytoolkit#SignupNewUserResponse\"\n  /* IdTokenResponseKind.SignupNewUser */\n  ;\n\n  if (!providerId && (idTokenResponse === null || idTokenResponse === void 0 ? void 0 : idTokenResponse.idToken)) {\n    const signInProvider = (_b = (_a = _parseToken(idTokenResponse.idToken)) === null || _a === void 0 ? void 0 : _a.firebase) === null || _b === void 0 ? void 0 : _b['sign_in_provider'];\n\n    if (signInProvider) {\n      const filteredProviderId = signInProvider !== \"anonymous\"\n      /* ProviderId.ANONYMOUS */\n      && signInProvider !== \"custom\"\n      /* ProviderId.CUSTOM */\n      ? signInProvider : null; // Uses generic class in accordance with the legacy SDK.\n\n      return new GenericAdditionalUserInfo(isNewUser, filteredProviderId);\n    }\n  }\n\n  if (!providerId) {\n    return null;\n  }\n\n  switch (providerId) {\n    case \"facebook.com\"\n    /* ProviderId.FACEBOOK */\n    :\n      return new FacebookAdditionalUserInfo(isNewUser, profile);\n\n    case \"github.com\"\n    /* ProviderId.GITHUB */\n    :\n      return new GithubAdditionalUserInfo(isNewUser, profile);\n\n    case \"google.com\"\n    /* ProviderId.GOOGLE */\n    :\n      return new GoogleAdditionalUserInfo(isNewUser, profile);\n\n    case \"twitter.com\"\n    /* ProviderId.TWITTER */\n    :\n      return new TwitterAdditionalUserInfo(isNewUser, profile, idTokenResponse.screenName || null);\n\n    case \"custom\"\n    /* ProviderId.CUSTOM */\n    :\n    case \"anonymous\"\n    /* ProviderId.ANONYMOUS */\n    :\n      return new GenericAdditionalUserInfo(isNewUser, null);\n\n    default:\n      return new GenericAdditionalUserInfo(isNewUser, providerId, profile);\n  }\n}\n\nclass GenericAdditionalUserInfo {\n  constructor(isNewUser, providerId, profile = {}) {\n    this.isNewUser = isNewUser;\n    this.providerId = providerId;\n    this.profile = profile;\n  }\n\n}\n\nclass FederatedAdditionalUserInfoWithUsername extends GenericAdditionalUserInfo {\n  constructor(isNewUser, providerId, profile, username) {\n    super(isNewUser, providerId, profile);\n    this.username = username;\n  }\n\n}\n\nclass FacebookAdditionalUserInfo extends GenericAdditionalUserInfo {\n  constructor(isNewUser, profile) {\n    super(isNewUser, \"facebook.com\"\n    /* ProviderId.FACEBOOK */\n    , profile);\n  }\n\n}\n\nclass GithubAdditionalUserInfo extends FederatedAdditionalUserInfoWithUsername {\n  constructor(isNewUser, profile) {\n    super(isNewUser, \"github.com\"\n    /* ProviderId.GITHUB */\n    , profile, typeof (profile === null || profile === void 0 ? void 0 : profile.login) === 'string' ? profile === null || profile === void 0 ? void 0 : profile.login : null);\n  }\n\n}\n\nclass GoogleAdditionalUserInfo extends GenericAdditionalUserInfo {\n  constructor(isNewUser, profile) {\n    super(isNewUser, \"google.com\"\n    /* ProviderId.GOOGLE */\n    , profile);\n  }\n\n}\n\nclass TwitterAdditionalUserInfo extends FederatedAdditionalUserInfoWithUsername {\n  constructor(isNewUser, profile, screenName) {\n    super(isNewUser, \"twitter.com\"\n    /* ProviderId.TWITTER */\n    , profile, screenName);\n  }\n\n}\n/**\r\n * Extracts provider specific {@link AdditionalUserInfo} for the given credential.\r\n *\r\n * @param userCredential - The user credential.\r\n *\r\n * @public\r\n */\n\n\nfunction getAdditionalUserInfo(userCredential) {\n  const {\n    user,\n    _tokenResponse\n  } = userCredential;\n\n  if (user.isAnonymous && !_tokenResponse) {\n    // Handle the special case where signInAnonymously() gets called twice.\n    // No network call is made so there's nothing to actually fill this in\n    return {\n      providerId: null,\n      isNewUser: false,\n      profile: null\n    };\n  }\n\n  return _fromIdTokenResponse(_tokenResponse);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Non-optional auth methods.\n\n/**\r\n * Changes the type of persistence on the {@link Auth} instance for the currently saved\r\n * `Auth` session and applies this type of persistence for future sign-in requests, including\r\n * sign-in with redirect requests.\r\n *\r\n * @remarks\r\n * This makes it easy for a user signing in to specify whether their session should be\r\n * remembered or not. It also makes it easier to never persist the `Auth` state for applications\r\n * that are shared by other users or have sensitive data.\r\n *\r\n * @example\r\n * ```javascript\r\n * setPersistence(auth, browserSessionPersistence);\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param persistence - The {@link Persistence} to use.\r\n * @returns A `Promise` that resolves once the persistence change has completed\r\n *\r\n * @public\r\n */\n\n\nfunction setPersistence(auth, persistence) {\n  return getModularInstance(auth).setPersistence(persistence);\n}\n/**\r\n * Loads the reCAPTCHA configuration into the `Auth` instance.\r\n *\r\n * @remarks\r\n * This will load the reCAPTCHA config, which indicates whether the reCAPTCHA\r\n * verification flow should be triggered for each auth provider, into the\r\n * current Auth session.\r\n *\r\n * If initializeRecaptchaConfig() is not invoked, the auth flow will always start\r\n * without reCAPTCHA verification. If the provider is configured to require reCAPTCHA\r\n * verification, the SDK will transparently load the reCAPTCHA config and restart the\r\n * auth flows.\r\n *\r\n * Thus, by calling this optional method, you will reduce the latency of future auth flows.\r\n * Loading the reCAPTCHA config early will also enhance the signal collected by reCAPTCHA.\r\n *\r\n * @example\r\n * ```javascript\r\n * initializeRecaptchaConfig(auth);\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n *\r\n * @public\r\n */\n\n\nfunction initializeRecaptchaConfig(auth) {\n  const authInternal = _castAuth(auth);\n\n  return authInternal.initializeRecaptchaConfig();\n}\n/**\r\n * Adds an observer for changes to the signed-in user's ID token.\r\n *\r\n * @remarks\r\n * This includes sign-in, sign-out, and token refresh events.\r\n * This will not be triggered automatically upon ID token expiration. Use {@link User.getIdToken} to refresh the ID token.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param nextOrObserver - callback triggered on change.\r\n * @param error - Deprecated. This callback is never triggered. Errors\r\n * on signing in/out can be caught in promises returned from\r\n * sign-in/sign-out functions.\r\n * @param completed - Deprecated. This callback is never triggered.\r\n *\r\n * @public\r\n */\n\n\nfunction onIdTokenChanged(auth, nextOrObserver, error, completed) {\n  return getModularInstance(auth).onIdTokenChanged(nextOrObserver, error, completed);\n}\n/**\r\n * Adds a blocking callback that runs before an auth state change\r\n * sets a new user.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param callback - callback triggered before new user value is set.\r\n *   If this throws, it blocks the user from being set.\r\n * @param onAbort - callback triggered if a later `beforeAuthStateChanged()`\r\n *   callback throws, allowing you to undo any side effects.\r\n */\n\n\nfunction beforeAuthStateChanged(auth, callback, onAbort) {\n  return getModularInstance(auth).beforeAuthStateChanged(callback, onAbort);\n}\n/**\r\n * Adds an observer for changes to the user's sign-in state.\r\n *\r\n * @remarks\r\n * To keep the old behavior, see {@link onIdTokenChanged}.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param nextOrObserver - callback triggered on change.\r\n * @param error - Deprecated. This callback is never triggered. Errors\r\n * on signing in/out can be caught in promises returned from\r\n * sign-in/sign-out functions.\r\n * @param completed - Deprecated. This callback is never triggered.\r\n *\r\n * @public\r\n */\n\n\nfunction onAuthStateChanged(auth, nextOrObserver, error, completed) {\n  return getModularInstance(auth).onAuthStateChanged(nextOrObserver, error, completed);\n}\n/**\r\n * Sets the current language to the default device/browser preference.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n *\r\n * @public\r\n */\n\n\nfunction useDeviceLanguage(auth) {\n  getModularInstance(auth).useDeviceLanguage();\n}\n/**\r\n * Asynchronously sets the provided user as {@link Auth.currentUser} on the\r\n * {@link Auth} instance.\r\n *\r\n * @remarks\r\n * A new instance copy of the user provided will be made and set as currentUser.\r\n *\r\n * This will trigger {@link onAuthStateChanged} and {@link onIdTokenChanged} listeners\r\n * like other sign in methods.\r\n *\r\n * The operation fails with an error if the user to be updated belongs to a different Firebase\r\n * project.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param user - The new {@link User}.\r\n *\r\n * @public\r\n */\n\n\nfunction updateCurrentUser(auth, user) {\n  return getModularInstance(auth).updateCurrentUser(user);\n}\n/**\r\n * Signs out the current user.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n *\r\n * @public\r\n */\n\n\nfunction signOut(auth) {\n  return getModularInstance(auth).signOut();\n}\n/**\r\n * Deletes and signs out the user.\r\n *\r\n * @remarks\r\n * Important: this is a security-sensitive operation that requires the user to have recently\r\n * signed in. If this requirement isn't met, ask the user to authenticate again and then call\r\n * {@link reauthenticateWithCredential}.\r\n *\r\n * @param user - The user.\r\n *\r\n * @public\r\n */\n\n\nfunction deleteUser(_x130) {\n  return _deleteUser.apply(this, arguments);\n}\n\nfunction _deleteUser() {\n  _deleteUser = _asyncToGenerator(function* (user) {\n    return getModularInstance(user).delete();\n  });\n  return _deleteUser.apply(this, arguments);\n}\n\nclass MultiFactorSessionImpl {\n  constructor(type, credential, auth) {\n    this.type = type;\n    this.credential = credential;\n    this.auth = auth;\n  }\n\n  static _fromIdtoken(idToken, auth) {\n    return new MultiFactorSessionImpl(\"enroll\"\n    /* MultiFactorSessionType.ENROLL */\n    , idToken, auth);\n  }\n\n  static _fromMfaPendingCredential(mfaPendingCredential) {\n    return new MultiFactorSessionImpl(\"signin\"\n    /* MultiFactorSessionType.SIGN_IN */\n    , mfaPendingCredential);\n  }\n\n  toJSON() {\n    const key = this.type === \"enroll\"\n    /* MultiFactorSessionType.ENROLL */\n    ? 'idToken' : 'pendingCredential';\n    return {\n      multiFactorSession: {\n        [key]: this.credential\n      }\n    };\n  }\n\n  static fromJSON(obj) {\n    var _a, _b;\n\n    if (obj === null || obj === void 0 ? void 0 : obj.multiFactorSession) {\n      if ((_a = obj.multiFactorSession) === null || _a === void 0 ? void 0 : _a.pendingCredential) {\n        return MultiFactorSessionImpl._fromMfaPendingCredential(obj.multiFactorSession.pendingCredential);\n      } else if ((_b = obj.multiFactorSession) === null || _b === void 0 ? void 0 : _b.idToken) {\n        return MultiFactorSessionImpl._fromIdtoken(obj.multiFactorSession.idToken);\n      }\n    }\n\n    return null;\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass MultiFactorResolverImpl {\n  constructor(session, hints, signInResolver) {\n    this.session = session;\n    this.hints = hints;\n    this.signInResolver = signInResolver;\n  }\n  /** @internal */\n\n\n  static _fromError(authExtern, error) {\n    const auth = _castAuth(authExtern);\n\n    const serverResponse = error.customData._serverResponse;\n    const hints = (serverResponse.mfaInfo || []).map(enrollment => MultiFactorInfoImpl._fromServerResponse(auth, enrollment));\n\n    _assert(serverResponse.mfaPendingCredential, auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    const session = MultiFactorSessionImpl._fromMfaPendingCredential(serverResponse.mfaPendingCredential);\n\n    return new MultiFactorResolverImpl(session, hints, /*#__PURE__*/function () {\n      var _ref11 = _asyncToGenerator(function* (assertion) {\n        const mfaResponse = yield assertion._process(auth, session); // Clear out the unneeded fields from the old login response\n\n        delete serverResponse.mfaInfo;\n        delete serverResponse.mfaPendingCredential; // Use in the new token & refresh token in the old response\n\n        const idTokenResponse = Object.assign(Object.assign({}, serverResponse), {\n          idToken: mfaResponse.idToken,\n          refreshToken: mfaResponse.refreshToken\n        }); // TODO: we should collapse this switch statement into UserCredentialImpl._forOperation and have it support the SIGN_IN case\n\n        switch (error.operationType) {\n          case \"signIn\"\n          /* OperationType.SIGN_IN */\n          :\n            const userCredential = yield UserCredentialImpl._fromIdTokenResponse(auth, error.operationType, idTokenResponse);\n            yield auth._updateCurrentUser(userCredential.user);\n            return userCredential;\n\n          case \"reauthenticate\"\n          /* OperationType.REAUTHENTICATE */\n          :\n            _assert(error.user, auth, \"internal-error\"\n            /* AuthErrorCode.INTERNAL_ERROR */\n            );\n\n            return UserCredentialImpl._forOperation(error.user, error.operationType, idTokenResponse);\n\n          default:\n            _fail(auth, \"internal-error\"\n            /* AuthErrorCode.INTERNAL_ERROR */\n            );\n\n        }\n      });\n\n      return function (_x131) {\n        return _ref11.apply(this, arguments);\n      };\n    }());\n  }\n\n  resolveSignIn(assertionExtern) {\n    var _this35 = this;\n\n    return _asyncToGenerator(function* () {\n      const assertion = assertionExtern;\n      return _this35.signInResolver(assertion);\n    })();\n  }\n\n}\n/**\r\n * Provides a {@link MultiFactorResolver} suitable for completion of a\r\n * multi-factor flow.\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param error - The {@link MultiFactorError} raised during a sign-in, or\r\n * reauthentication operation.\r\n *\r\n * @public\r\n */\n\n\nfunction getMultiFactorResolver(auth, error) {\n  var _a;\n\n  const authModular = getModularInstance(auth);\n  const errorInternal = error;\n\n  _assert(error.customData.operationType, authModular, \"argument-error\"\n  /* AuthErrorCode.ARGUMENT_ERROR */\n  );\n\n  _assert((_a = errorInternal.customData._serverResponse) === null || _a === void 0 ? void 0 : _a.mfaPendingCredential, authModular, \"argument-error\"\n  /* AuthErrorCode.ARGUMENT_ERROR */\n  );\n\n  return MultiFactorResolverImpl._fromError(authModular, errorInternal);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction startEnrollPhoneMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaEnrollment:start\"\n  /* Endpoint.START_MFA_ENROLLMENT */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction finalizeEnrollPhoneMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaEnrollment:finalize\"\n  /* Endpoint.FINALIZE_MFA_ENROLLMENT */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction startEnrollTotpMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaEnrollment:start\"\n  /* Endpoint.START_MFA_ENROLLMENT */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction finalizeEnrollTotpMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaEnrollment:finalize\"\n  /* Endpoint.FINALIZE_MFA_ENROLLMENT */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction withdrawMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaEnrollment:withdraw\"\n  /* Endpoint.WITHDRAW_MFA */\n  , _addTidIfNecessary(auth, request));\n}\n\nclass MultiFactorUserImpl {\n  constructor(user) {\n    this.user = user;\n    this.enrolledFactors = [];\n\n    user._onReload(userInfo => {\n      if (userInfo.mfaInfo) {\n        this.enrolledFactors = userInfo.mfaInfo.map(enrollment => MultiFactorInfoImpl._fromServerResponse(user.auth, enrollment));\n      }\n    });\n  }\n\n  static _fromUser(user) {\n    return new MultiFactorUserImpl(user);\n  }\n\n  getSession() {\n    var _this36 = this;\n\n    return _asyncToGenerator(function* () {\n      return MultiFactorSessionImpl._fromIdtoken(yield _this36.user.getIdToken(), _this36.user.auth);\n    })();\n  }\n\n  enroll(assertionExtern, displayName) {\n    var _this37 = this;\n\n    return _asyncToGenerator(function* () {\n      const assertion = assertionExtern;\n      const session = yield _this37.getSession();\n      const finalizeMfaResponse = yield _logoutIfInvalidated(_this37.user, assertion._process(_this37.user.auth, session, displayName)); // New tokens will be issued after enrollment of the new second factors.\n      // They need to be updated on the user.\n\n      yield _this37.user._updateTokensIfNecessary(finalizeMfaResponse); // The user needs to be reloaded to get the new multi-factor information\n      // from server. USER_RELOADED event will be triggered and `enrolledFactors`\n      // will be updated.\n\n      return _this37.user.reload();\n    })();\n  }\n\n  unenroll(infoOrUid) {\n    var _this38 = this;\n\n    return _asyncToGenerator(function* () {\n      const mfaEnrollmentId = typeof infoOrUid === 'string' ? infoOrUid : infoOrUid.uid;\n      const idToken = yield _this38.user.getIdToken();\n\n      try {\n        const idTokenResponse = yield _logoutIfInvalidated(_this38.user, withdrawMfa(_this38.user.auth, {\n          idToken,\n          mfaEnrollmentId\n        })); // Remove the second factor from the user's list.\n\n        _this38.enrolledFactors = _this38.enrolledFactors.filter(({\n          uid\n        }) => uid !== mfaEnrollmentId); // Depending on whether the backend decided to revoke the user's session,\n        // the tokenResponse may be empty. If the tokens were not updated (and they\n        // are now invalid), reloading the user will discover this and invalidate\n        // the user's state accordingly.\n\n        yield _this38.user._updateTokensIfNecessary(idTokenResponse);\n        yield _this38.user.reload();\n      } catch (e) {\n        throw e;\n      }\n    })();\n  }\n\n}\n\nconst multiFactorUserCache = new WeakMap();\n/**\r\n * The {@link MultiFactorUser} corresponding to the user.\r\n *\r\n * @remarks\r\n * This is used to access all multi-factor properties and operations related to the user.\r\n *\r\n * @param user - The user.\r\n *\r\n * @public\r\n */\n\nfunction multiFactor(user) {\n  const userModular = getModularInstance(user);\n\n  if (!multiFactorUserCache.has(userModular)) {\n    multiFactorUserCache.set(userModular, MultiFactorUserImpl._fromUser(userModular));\n  }\n\n  return multiFactorUserCache.get(userModular);\n}\n\nconst STORAGE_AVAILABLE_KEY = '__sak';\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// There are two different browser persistence types: local and session.\n// Both have the same implementation but use a different underlying storage\n// object.\n\nclass BrowserPersistenceClass {\n  constructor(storageRetriever, type) {\n    this.storageRetriever = storageRetriever;\n    this.type = type;\n  }\n\n  _isAvailable() {\n    try {\n      if (!this.storage) {\n        return Promise.resolve(false);\n      }\n\n      this.storage.setItem(STORAGE_AVAILABLE_KEY, '1');\n      this.storage.removeItem(STORAGE_AVAILABLE_KEY);\n      return Promise.resolve(true);\n    } catch (_a) {\n      return Promise.resolve(false);\n    }\n  }\n\n  _set(key, value) {\n    this.storage.setItem(key, JSON.stringify(value));\n    return Promise.resolve();\n  }\n\n  _get(key) {\n    const json = this.storage.getItem(key);\n    return Promise.resolve(json ? JSON.parse(json) : null);\n  }\n\n  _remove(key) {\n    this.storage.removeItem(key);\n    return Promise.resolve();\n  }\n\n  get storage() {\n    return this.storageRetriever();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _iframeCannotSyncWebStorage() {\n  const ua = getUA();\n  return _isSafari(ua) || _isIOS(ua);\n} // The polling period in case events are not supported\n\n\nconst _POLLING_INTERVAL_MS$1 = 1000; // The IE 10 localStorage cross tab synchronization delay in milliseconds\n\nconst IE10_LOCAL_STORAGE_SYNC_DELAY = 10;\nlet BrowserLocalPersistence = /*#__PURE__*/(() => {\n  class BrowserLocalPersistence extends BrowserPersistenceClass {\n    constructor() {\n      super(() => window.localStorage, \"LOCAL\"\n      /* PersistenceType.LOCAL */\n      );\n\n      this.boundEventHandler = (event, poll) => this.onStorageEvent(event, poll);\n\n      this.listeners = {};\n      this.localCache = {}; // setTimeout return value is platform specific\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n      this.pollTimer = null; // Safari or iOS browser and embedded in an iframe.\n\n      this.safariLocalStorageNotSynced = _iframeCannotSyncWebStorage() && _isIframe(); // Whether to use polling instead of depending on window events\n\n      this.fallbackToPolling = _isMobileBrowser();\n      this._shouldAllowMigration = true;\n    }\n\n    forAllChangedKeys(cb) {\n      // Check all keys with listeners on them.\n      for (const key of Object.keys(this.listeners)) {\n        // Get value from localStorage.\n        const newValue = this.storage.getItem(key);\n        const oldValue = this.localCache[key]; // If local map value does not match, trigger listener with storage event.\n        // Differentiate this simulated event from the real storage event.\n\n        if (newValue !== oldValue) {\n          cb(key, oldValue, newValue);\n        }\n      }\n    }\n\n    onStorageEvent(event, poll = false) {\n      // Key would be null in some situations, like when localStorage is cleared\n      if (!event.key) {\n        this.forAllChangedKeys((key, _oldValue, newValue) => {\n          this.notifyListeners(key, newValue);\n        });\n        return;\n      }\n\n      const key = event.key; // Check the mechanism how this event was detected.\n      // The first event will dictate the mechanism to be used.\n\n      if (poll) {\n        // Environment detects storage changes via polling.\n        // Remove storage event listener to prevent possible event duplication.\n        this.detachListener();\n      } else {\n        // Environment detects storage changes via storage event listener.\n        // Remove polling listener to prevent possible event duplication.\n        this.stopPolling();\n      } // Safari embedded iframe. Storage event will trigger with the delta\n      // changes but no changes will be applied to the iframe localStorage.\n\n\n      if (this.safariLocalStorageNotSynced) {\n        // Get current iframe page value.\n        const storedValue = this.storage.getItem(key); // Value not synchronized, synchronize manually.\n\n        if (event.newValue !== storedValue) {\n          if (event.newValue !== null) {\n            // Value changed from current value.\n            this.storage.setItem(key, event.newValue);\n          } else {\n            // Current value deleted.\n            this.storage.removeItem(key);\n          }\n        } else if (this.localCache[key] === event.newValue && !poll) {\n          // Already detected and processed, do not trigger listeners again.\n          return;\n        }\n      }\n\n      const triggerListeners = () => {\n        // Keep local map up to date in case storage event is triggered before\n        // poll.\n        const storedValue = this.storage.getItem(key);\n\n        if (!poll && this.localCache[key] === storedValue) {\n          // Real storage event which has already been detected, do nothing.\n          // This seems to trigger in some IE browsers for some reason.\n          return;\n        }\n\n        this.notifyListeners(key, storedValue);\n      };\n\n      const storedValue = this.storage.getItem(key);\n\n      if (_isIE10() && storedValue !== event.newValue && event.newValue !== event.oldValue) {\n        // IE 10 has this weird bug where a storage event would trigger with the\n        // correct key, oldValue and newValue but localStorage.getItem(key) does\n        // not yield the updated value until a few milliseconds. This ensures\n        // this recovers from that situation.\n        setTimeout(triggerListeners, IE10_LOCAL_STORAGE_SYNC_DELAY);\n      } else {\n        triggerListeners();\n      }\n    }\n\n    notifyListeners(key, value) {\n      this.localCache[key] = value;\n      const listeners = this.listeners[key];\n\n      if (listeners) {\n        for (const listener of Array.from(listeners)) {\n          listener(value ? JSON.parse(value) : value);\n        }\n      }\n    }\n\n    startPolling() {\n      this.stopPolling();\n      this.pollTimer = setInterval(() => {\n        this.forAllChangedKeys((key, oldValue, newValue) => {\n          this.onStorageEvent(new StorageEvent('storage', {\n            key,\n            oldValue,\n            newValue\n          }),\n          /* poll */\n          true);\n        });\n      }, _POLLING_INTERVAL_MS$1);\n    }\n\n    stopPolling() {\n      if (this.pollTimer) {\n        clearInterval(this.pollTimer);\n        this.pollTimer = null;\n      }\n    }\n\n    attachListener() {\n      window.addEventListener('storage', this.boundEventHandler);\n    }\n\n    detachListener() {\n      window.removeEventListener('storage', this.boundEventHandler);\n    }\n\n    _addListener(key, listener) {\n      if (Object.keys(this.listeners).length === 0) {\n        // Whether browser can detect storage event when it had already been pushed to the background.\n        // This may happen in some mobile browsers. A localStorage change in the foreground window\n        // will not be detected in the background window via the storage event.\n        // This was detected in iOS 7.x mobile browsers\n        if (this.fallbackToPolling) {\n          this.startPolling();\n        } else {\n          this.attachListener();\n        }\n      }\n\n      if (!this.listeners[key]) {\n        this.listeners[key] = new Set(); // Populate the cache to avoid spuriously triggering on first poll.\n\n        this.localCache[key] = this.storage.getItem(key);\n      }\n\n      this.listeners[key].add(listener);\n    }\n\n    _removeListener(key, listener) {\n      if (this.listeners[key]) {\n        this.listeners[key].delete(listener);\n\n        if (this.listeners[key].size === 0) {\n          delete this.listeners[key];\n        }\n      }\n\n      if (Object.keys(this.listeners).length === 0) {\n        this.detachListener();\n        this.stopPolling();\n      }\n    } // Update local cache on base operations:\n\n\n    _set(key, value) {\n      var _superprop_get_set = () => super._set,\n          _this39 = this;\n\n      return _asyncToGenerator(function* () {\n        yield _superprop_get_set().call(_this39, key, value);\n        _this39.localCache[key] = JSON.stringify(value);\n      })();\n    }\n\n    _get(key) {\n      var _superprop_get_get = () => super._get,\n          _this40 = this;\n\n      return _asyncToGenerator(function* () {\n        const value = yield _superprop_get_get().call(_this40, key);\n        _this40.localCache[key] = JSON.stringify(value);\n        return value;\n      })();\n    }\n\n    _remove(key) {\n      var _superprop_get_remove = () => super._remove,\n          _this41 = this;\n\n      return _asyncToGenerator(function* () {\n        yield _superprop_get_remove().call(_this41, key);\n        delete _this41.localCache[key];\n      })();\n    }\n\n  }\n\n  BrowserLocalPersistence.type = 'LOCAL';\n  /**\r\n   * An implementation of {@link Persistence} of type `LOCAL` using `localStorage`\r\n   * for the underlying storage.\r\n   *\r\n   * @public\r\n   */\n\n  return BrowserLocalPersistence;\n})();\nconst browserLocalPersistence = BrowserLocalPersistence;\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nlet BrowserSessionPersistence = /*#__PURE__*/(() => {\n  class BrowserSessionPersistence extends BrowserPersistenceClass {\n    constructor() {\n      super(() => window.sessionStorage, \"SESSION\"\n      /* PersistenceType.SESSION */\n      );\n    }\n\n    _addListener(_key, _listener) {\n      // Listeners are not supported for session storage since it cannot be shared across windows\n      return;\n    }\n\n    _removeListener(_key, _listener) {\n      // Listeners are not supported for session storage since it cannot be shared across windows\n      return;\n    }\n\n  }\n\n  BrowserSessionPersistence.type = 'SESSION';\n  /**\r\n   * An implementation of {@link Persistence} of `SESSION` using `sessionStorage`\r\n   * for the underlying storage.\r\n   *\r\n   * @public\r\n   */\n\n  return BrowserSessionPersistence;\n})();\nconst browserSessionPersistence = BrowserSessionPersistence;\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Shim for Promise.allSettled, note the slightly different format of `fulfilled` vs `status`.\r\n *\r\n * @param promises - Array of promises to wait on.\r\n */\n\nfunction _allSettled(promises) {\n  return Promise.all(promises.map( /*#__PURE__*/function () {\n    var _ref12 = _asyncToGenerator(function* (promise) {\n      try {\n        const value = yield promise;\n        return {\n          fulfilled: true,\n          value\n        };\n      } catch (reason) {\n        return {\n          fulfilled: false,\n          reason\n        };\n      }\n    });\n\n    return function (_x132) {\n      return _ref12.apply(this, arguments);\n    };\n  }()));\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Interface class for receiving messages.\r\n *\r\n */\n\n\nlet Receiver = /*#__PURE__*/(() => {\n  class Receiver {\n    constructor(eventTarget) {\n      this.eventTarget = eventTarget;\n      this.handlersMap = {};\n      this.boundEventHandler = this.handleEvent.bind(this);\n    }\n    /**\r\n     * Obtain an instance of a Receiver for a given event target, if none exists it will be created.\r\n     *\r\n     * @param eventTarget - An event target (such as window or self) through which the underlying\r\n     * messages will be received.\r\n     */\n\n\n    static _getInstance(eventTarget) {\n      // The results are stored in an array since objects can't be keys for other\n      // objects. In addition, setting a unique property on an event target as a\n      // hash map key may not be allowed due to CORS restrictions.\n      const existingInstance = this.receivers.find(receiver => receiver.isListeningto(eventTarget));\n\n      if (existingInstance) {\n        return existingInstance;\n      }\n\n      const newInstance = new Receiver(eventTarget);\n      this.receivers.push(newInstance);\n      return newInstance;\n    }\n\n    isListeningto(eventTarget) {\n      return this.eventTarget === eventTarget;\n    }\n    /**\r\n     * Fans out a MessageEvent to the appropriate listeners.\r\n     *\r\n     * @remarks\r\n     * Sends an {@link Status.ACK} upon receipt and a {@link Status.DONE} once all handlers have\r\n     * finished processing.\r\n     *\r\n     * @param event - The MessageEvent.\r\n     *\r\n     */\n\n\n    handleEvent(event) {\n      var _this42 = this;\n\n      return _asyncToGenerator(function* () {\n        const messageEvent = event;\n        const {\n          eventId,\n          eventType,\n          data\n        } = messageEvent.data;\n        const handlers = _this42.handlersMap[eventType];\n\n        if (!(handlers === null || handlers === void 0 ? void 0 : handlers.size)) {\n          return;\n        }\n\n        messageEvent.ports[0].postMessage({\n          status: \"ack\"\n          /* _Status.ACK */\n          ,\n          eventId,\n          eventType\n        });\n        const promises = Array.from(handlers).map( /*#__PURE__*/function () {\n          var _ref13 = _asyncToGenerator(function* (handler) {\n            return handler(messageEvent.origin, data);\n          });\n\n          return function (_x133) {\n            return _ref13.apply(this, arguments);\n          };\n        }());\n        const response = yield _allSettled(promises);\n        messageEvent.ports[0].postMessage({\n          status: \"done\"\n          /* _Status.DONE */\n          ,\n          eventId,\n          eventType,\n          response\n        });\n      })();\n    }\n    /**\r\n     * Subscribe an event handler for a particular event.\r\n     *\r\n     * @param eventType - Event name to subscribe to.\r\n     * @param eventHandler - The event handler which should receive the events.\r\n     *\r\n     */\n\n\n    _subscribe(eventType, eventHandler) {\n      if (Object.keys(this.handlersMap).length === 0) {\n        this.eventTarget.addEventListener('message', this.boundEventHandler);\n      }\n\n      if (!this.handlersMap[eventType]) {\n        this.handlersMap[eventType] = new Set();\n      }\n\n      this.handlersMap[eventType].add(eventHandler);\n    }\n    /**\r\n     * Unsubscribe an event handler from a particular event.\r\n     *\r\n     * @param eventType - Event name to unsubscribe from.\r\n     * @param eventHandler - Optinoal event handler, if none provided, unsubscribe all handlers on this event.\r\n     *\r\n     */\n\n\n    _unsubscribe(eventType, eventHandler) {\n      if (this.handlersMap[eventType] && eventHandler) {\n        this.handlersMap[eventType].delete(eventHandler);\n      }\n\n      if (!eventHandler || this.handlersMap[eventType].size === 0) {\n        delete this.handlersMap[eventType];\n      }\n\n      if (Object.keys(this.handlersMap).length === 0) {\n        this.eventTarget.removeEventListener('message', this.boundEventHandler);\n      }\n    }\n\n  }\n\n  Receiver.receivers = [];\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  return Receiver;\n})();\n\nfunction _generateEventId(prefix = '', digits = 10) {\n  let random = '';\n\n  for (let i = 0; i < digits; i++) {\n    random += Math.floor(Math.random() * 10);\n  }\n\n  return prefix + random;\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Interface for sending messages and waiting for a completion response.\r\n *\r\n */\n\n\nclass Sender {\n  constructor(target) {\n    this.target = target;\n    this.handlers = new Set();\n  }\n  /**\r\n   * Unsubscribe the handler and remove it from our tracking Set.\r\n   *\r\n   * @param handler - The handler to unsubscribe.\r\n   */\n\n\n  removeMessageHandler(handler) {\n    if (handler.messageChannel) {\n      handler.messageChannel.port1.removeEventListener('message', handler.onMessage);\n      handler.messageChannel.port1.close();\n    }\n\n    this.handlers.delete(handler);\n  }\n  /**\r\n   * Send a message to the Receiver located at {@link target}.\r\n   *\r\n   * @remarks\r\n   * We'll first wait a bit for an ACK , if we get one we will wait significantly longer until the\r\n   * receiver has had a chance to fully process the event.\r\n   *\r\n   * @param eventType - Type of event to send.\r\n   * @param data - The payload of the event.\r\n   * @param timeout - Timeout for waiting on an ACK from the receiver.\r\n   *\r\n   * @returns An array of settled promises from all the handlers that were listening on the receiver.\r\n   */\n\n\n  _send(eventType, data, timeout = 50\n  /* _TimeoutDuration.ACK */\n  ) {\n    var _this43 = this;\n\n    return _asyncToGenerator(function* () {\n      const messageChannel = typeof MessageChannel !== 'undefined' ? new MessageChannel() : null;\n\n      if (!messageChannel) {\n        throw new Error(\"connection_unavailable\"\n        /* _MessageError.CONNECTION_UNAVAILABLE */\n        );\n      } // Node timers and browser timers return fundamentally different types.\n      // We don't actually care what the value is but TS won't accept unknown and\n      // we can't cast properly in both environments.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n      let completionTimer;\n      let handler;\n      return new Promise((resolve, reject) => {\n        const eventId = _generateEventId('', 20);\n\n        messageChannel.port1.start();\n        const ackTimer = setTimeout(() => {\n          reject(new Error(\"unsupported_event\"\n          /* _MessageError.UNSUPPORTED_EVENT */\n          ));\n        }, timeout);\n        handler = {\n          messageChannel,\n\n          onMessage(event) {\n            const messageEvent = event;\n\n            if (messageEvent.data.eventId !== eventId) {\n              return;\n            }\n\n            switch (messageEvent.data.status) {\n              case \"ack\"\n              /* _Status.ACK */\n              :\n                // The receiver should ACK first.\n                clearTimeout(ackTimer);\n                completionTimer = setTimeout(() => {\n                  reject(new Error(\"timeout\"\n                  /* _MessageError.TIMEOUT */\n                  ));\n                }, 3000\n                /* _TimeoutDuration.COMPLETION */\n                );\n                break;\n\n              case \"done\"\n              /* _Status.DONE */\n              :\n                // Once the receiver's handlers are finished we will get the results.\n                clearTimeout(completionTimer);\n                resolve(messageEvent.data.response);\n                break;\n\n              default:\n                clearTimeout(ackTimer);\n                clearTimeout(completionTimer);\n                reject(new Error(\"invalid_response\"\n                /* _MessageError.INVALID_RESPONSE */\n                ));\n                break;\n            }\n          }\n\n        };\n\n        _this43.handlers.add(handler);\n\n        messageChannel.port1.addEventListener('message', handler.onMessage);\n\n        _this43.target.postMessage({\n          eventType,\n          eventId,\n          data\n        }, [messageChannel.port2]);\n      }).finally(() => {\n        if (handler) {\n          _this43.removeMessageHandler(handler);\n        }\n      });\n    })();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Lazy accessor for window, since the compat layer won't tree shake this out,\r\n * we need to make sure not to mess with window unless we have to\r\n */\n\n\nfunction _window() {\n  return window;\n}\n\nfunction _setWindowLocation(url) {\n  _window().location.href = url;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC.\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _isWorker() {\n  return typeof _window()['WorkerGlobalScope'] !== 'undefined' && typeof _window()['importScripts'] === 'function';\n}\n\nfunction _getActiveServiceWorker() {\n  return _getActiveServiceWorker2.apply(this, arguments);\n}\n\nfunction _getActiveServiceWorker2() {\n  _getActiveServiceWorker2 = _asyncToGenerator(function* () {\n    if (!(navigator === null || navigator === void 0 ? void 0 : navigator.serviceWorker)) {\n      return null;\n    }\n\n    try {\n      const registration = yield navigator.serviceWorker.ready;\n      return registration.active;\n    } catch (_a) {\n      return null;\n    }\n  });\n  return _getActiveServiceWorker2.apply(this, arguments);\n}\n\nfunction _getServiceWorkerController() {\n  var _a;\n\n  return ((_a = navigator === null || navigator === void 0 ? void 0 : navigator.serviceWorker) === null || _a === void 0 ? void 0 : _a.controller) || null;\n}\n\nfunction _getWorkerGlobalScope() {\n  return _isWorker() ? self : null;\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst DB_NAME = 'firebaseLocalStorageDb';\nconst DB_VERSION = 1;\nconst DB_OBJECTSTORE_NAME = 'firebaseLocalStorage';\nconst DB_DATA_KEYPATH = 'fbase_key';\n/**\r\n * Promise wrapper for IDBRequest\r\n *\r\n * Unfortunately we can't cleanly extend Promise<T> since promises are not callable in ES6\r\n *\r\n */\n\nclass DBPromise {\n  constructor(request) {\n    this.request = request;\n  }\n\n  toPromise() {\n    return new Promise((resolve, reject) => {\n      this.request.addEventListener('success', () => {\n        resolve(this.request.result);\n      });\n      this.request.addEventListener('error', () => {\n        reject(this.request.error);\n      });\n    });\n  }\n\n}\n\nfunction getObjectStore(db, isReadWrite) {\n  return db.transaction([DB_OBJECTSTORE_NAME], isReadWrite ? 'readwrite' : 'readonly').objectStore(DB_OBJECTSTORE_NAME);\n}\n\nfunction _deleteDatabase() {\n  const request = indexedDB.deleteDatabase(DB_NAME);\n  return new DBPromise(request).toPromise();\n}\n\nfunction _openDatabase() {\n  const request = indexedDB.open(DB_NAME, DB_VERSION);\n  return new Promise((resolve, reject) => {\n    request.addEventListener('error', () => {\n      reject(request.error);\n    });\n    request.addEventListener('upgradeneeded', () => {\n      const db = request.result;\n\n      try {\n        db.createObjectStore(DB_OBJECTSTORE_NAME, {\n          keyPath: DB_DATA_KEYPATH\n        });\n      } catch (e) {\n        reject(e);\n      }\n    });\n    request.addEventListener('success', /*#__PURE__*/_asyncToGenerator(function* () {\n      const db = request.result; // Strange bug that occurs in Firefox when multiple tabs are opened at the\n      // same time. The only way to recover seems to be deleting the database\n      // and re-initializing it.\n      // https://github.com/firebase/firebase-js-sdk/issues/634\n\n      if (!db.objectStoreNames.contains(DB_OBJECTSTORE_NAME)) {\n        // Need to close the database or else you get a `blocked` event\n        db.close();\n        yield _deleteDatabase();\n        resolve(yield _openDatabase());\n      } else {\n        resolve(db);\n      }\n    }));\n  });\n}\n\nfunction _putObject(_x134, _x135, _x136) {\n  return _putObject2.apply(this, arguments);\n}\n\nfunction _putObject2() {\n  _putObject2 = _asyncToGenerator(function* (db, key, value) {\n    const request = getObjectStore(db, true).put({\n      [DB_DATA_KEYPATH]: key,\n      value\n    });\n    return new DBPromise(request).toPromise();\n  });\n  return _putObject2.apply(this, arguments);\n}\n\nfunction getObject(_x137, _x138) {\n  return _getObject.apply(this, arguments);\n}\n\nfunction _getObject() {\n  _getObject = _asyncToGenerator(function* (db, key) {\n    const request = getObjectStore(db, false).get(key);\n    const data = yield new DBPromise(request).toPromise();\n    return data === undefined ? null : data.value;\n  });\n  return _getObject.apply(this, arguments);\n}\n\nfunction _deleteObject(db, key) {\n  const request = getObjectStore(db, true).delete(key);\n  return new DBPromise(request).toPromise();\n}\n\nconst _POLLING_INTERVAL_MS = 800;\nconst _TRANSACTION_RETRY_COUNT = 3;\nlet IndexedDBLocalPersistence = /*#__PURE__*/(() => {\n  class IndexedDBLocalPersistence {\n    constructor() {\n      this.type = \"LOCAL\"\n      /* PersistenceType.LOCAL */\n      ;\n      this._shouldAllowMigration = true;\n      this.listeners = {};\n      this.localCache = {}; // setTimeout return value is platform specific\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n      this.pollTimer = null;\n      this.pendingWrites = 0;\n      this.receiver = null;\n      this.sender = null;\n      this.serviceWorkerReceiverAvailable = false;\n      this.activeServiceWorker = null; // Fire & forget the service worker registration as it may never resolve\n\n      this._workerInitializationPromise = this.initializeServiceWorkerMessaging().then(() => {}, () => {});\n    }\n\n    _openDb() {\n      var _this44 = this;\n\n      return _asyncToGenerator(function* () {\n        if (_this44.db) {\n          return _this44.db;\n        }\n\n        _this44.db = yield _openDatabase();\n        return _this44.db;\n      })();\n    }\n\n    _withRetries(op) {\n      var _this45 = this;\n\n      return _asyncToGenerator(function* () {\n        let numAttempts = 0;\n\n        while (true) {\n          try {\n            const db = yield _this45._openDb();\n            return yield op(db);\n          } catch (e) {\n            if (numAttempts++ > _TRANSACTION_RETRY_COUNT) {\n              throw e;\n            }\n\n            if (_this45.db) {\n              _this45.db.close();\n\n              _this45.db = undefined;\n            } // TODO: consider adding exponential backoff\n\n          }\n        }\n      })();\n    }\n    /**\r\n     * IndexedDB events do not propagate from the main window to the worker context.  We rely on a\r\n     * postMessage interface to send these events to the worker ourselves.\r\n     */\n\n\n    initializeServiceWorkerMessaging() {\n      var _this46 = this;\n\n      return _asyncToGenerator(function* () {\n        return _isWorker() ? _this46.initializeReceiver() : _this46.initializeSender();\n      })();\n    }\n    /**\r\n     * As the worker we should listen to events from the main window.\r\n     */\n\n\n    initializeReceiver() {\n      var _this47 = this;\n\n      return _asyncToGenerator(function* () {\n        _this47.receiver = Receiver._getInstance(_getWorkerGlobalScope()); // Refresh from persistence if we receive a KeyChanged message.\n\n        _this47.receiver._subscribe(\"keyChanged\"\n        /* _EventType.KEY_CHANGED */\n        , /*#__PURE__*/function () {\n          var _ref15 = _asyncToGenerator(function* (_origin, data) {\n            const keys = yield _this47._poll();\n            return {\n              keyProcessed: keys.includes(data.key)\n            };\n          });\n\n          return function (_x139, _x140) {\n            return _ref15.apply(this, arguments);\n          };\n        }()); // Let the sender know that we are listening so they give us more timeout.\n\n\n        _this47.receiver._subscribe(\"ping\"\n        /* _EventType.PING */\n        , /*#__PURE__*/function () {\n          var _ref16 = _asyncToGenerator(function* (_origin, _data) {\n            return [\"keyChanged\"\n            /* _EventType.KEY_CHANGED */\n            ];\n          });\n\n          return function (_x141, _x142) {\n            return _ref16.apply(this, arguments);\n          };\n        }());\n      })();\n    }\n    /**\r\n     * As the main window, we should let the worker know when keys change (set and remove).\r\n     *\r\n     * @remarks\r\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/ready | ServiceWorkerContainer.ready}\r\n     * may not resolve.\r\n     */\n\n\n    initializeSender() {\n      var _this48 = this;\n\n      return _asyncToGenerator(function* () {\n        var _a, _b; // Check to see if there's an active service worker.\n\n\n        _this48.activeServiceWorker = yield _getActiveServiceWorker();\n\n        if (!_this48.activeServiceWorker) {\n          return;\n        }\n\n        _this48.sender = new Sender(_this48.activeServiceWorker); // Ping the service worker to check what events they can handle.\n\n        const results = yield _this48.sender._send(\"ping\"\n        /* _EventType.PING */\n        , {}, 800\n        /* _TimeoutDuration.LONG_ACK */\n        );\n\n        if (!results) {\n          return;\n        }\n\n        if (((_a = results[0]) === null || _a === void 0 ? void 0 : _a.fulfilled) && ((_b = results[0]) === null || _b === void 0 ? void 0 : _b.value.includes(\"keyChanged\"\n        /* _EventType.KEY_CHANGED */\n        ))) {\n          _this48.serviceWorkerReceiverAvailable = true;\n        }\n      })();\n    }\n    /**\r\n     * Let the worker know about a changed key, the exact key doesn't technically matter since the\r\n     * worker will just trigger a full sync anyway.\r\n     *\r\n     * @remarks\r\n     * For now, we only support one service worker per page.\r\n     *\r\n     * @param key - Storage key which changed.\r\n     */\n\n\n    notifyServiceWorker(key) {\n      var _this49 = this;\n\n      return _asyncToGenerator(function* () {\n        if (!_this49.sender || !_this49.activeServiceWorker || _getServiceWorkerController() !== _this49.activeServiceWorker) {\n          return;\n        }\n\n        try {\n          yield _this49.sender._send(\"keyChanged\"\n          /* _EventType.KEY_CHANGED */\n          , {\n            key\n          }, // Use long timeout if receiver has previously responded to a ping from us.\n          _this49.serviceWorkerReceiverAvailable ? 800\n          /* _TimeoutDuration.LONG_ACK */\n          : 50\n          /* _TimeoutDuration.ACK */\n          );\n        } catch (_a) {// This is a best effort approach. Ignore errors.\n        }\n      })();\n    }\n\n    _isAvailable() {\n      return _asyncToGenerator(function* () {\n        try {\n          if (!indexedDB) {\n            return false;\n          }\n\n          const db = yield _openDatabase();\n          yield _putObject(db, STORAGE_AVAILABLE_KEY, '1');\n          yield _deleteObject(db, STORAGE_AVAILABLE_KEY);\n          return true;\n        } catch (_a) {}\n\n        return false;\n      })();\n    }\n\n    _withPendingWrite(write) {\n      var _this50 = this;\n\n      return _asyncToGenerator(function* () {\n        _this50.pendingWrites++;\n\n        try {\n          yield write();\n        } finally {\n          _this50.pendingWrites--;\n        }\n      })();\n    }\n\n    _set(key, value) {\n      var _this51 = this;\n\n      return _asyncToGenerator(function* () {\n        return _this51._withPendingWrite( /*#__PURE__*/_asyncToGenerator(function* () {\n          yield _this51._withRetries(db => _putObject(db, key, value));\n          _this51.localCache[key] = value;\n          return _this51.notifyServiceWorker(key);\n        }));\n      })();\n    }\n\n    _get(key) {\n      var _this52 = this;\n\n      return _asyncToGenerator(function* () {\n        const obj = yield _this52._withRetries(db => getObject(db, key));\n        _this52.localCache[key] = obj;\n        return obj;\n      })();\n    }\n\n    _remove(key) {\n      var _this53 = this;\n\n      return _asyncToGenerator(function* () {\n        return _this53._withPendingWrite( /*#__PURE__*/_asyncToGenerator(function* () {\n          yield _this53._withRetries(db => _deleteObject(db, key));\n          delete _this53.localCache[key];\n          return _this53.notifyServiceWorker(key);\n        }));\n      })();\n    }\n\n    _poll() {\n      var _this54 = this;\n\n      return _asyncToGenerator(function* () {\n        // TODO: check if we need to fallback if getAll is not supported\n        const result = yield _this54._withRetries(db => {\n          const getAllRequest = getObjectStore(db, false).getAll();\n          return new DBPromise(getAllRequest).toPromise();\n        });\n\n        if (!result) {\n          return [];\n        } // If we have pending writes in progress abort, we'll get picked up on the next poll\n\n\n        if (_this54.pendingWrites !== 0) {\n          return [];\n        }\n\n        const keys = [];\n        const keysInResult = new Set();\n\n        for (const {\n          fbase_key: key,\n          value\n        } of result) {\n          keysInResult.add(key);\n\n          if (JSON.stringify(_this54.localCache[key]) !== JSON.stringify(value)) {\n            _this54.notifyListeners(key, value);\n\n            keys.push(key);\n          }\n        }\n\n        for (const localKey of Object.keys(_this54.localCache)) {\n          if (_this54.localCache[localKey] && !keysInResult.has(localKey)) {\n            // Deleted\n            _this54.notifyListeners(localKey, null);\n\n            keys.push(localKey);\n          }\n        }\n\n        return keys;\n      })();\n    }\n\n    notifyListeners(key, newValue) {\n      this.localCache[key] = newValue;\n      const listeners = this.listeners[key];\n\n      if (listeners) {\n        for (const listener of Array.from(listeners)) {\n          listener(newValue);\n        }\n      }\n    }\n\n    startPolling() {\n      var _this55 = this;\n\n      this.stopPolling();\n      this.pollTimer = setInterval( /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this55._poll();\n      }), _POLLING_INTERVAL_MS);\n    }\n\n    stopPolling() {\n      if (this.pollTimer) {\n        clearInterval(this.pollTimer);\n        this.pollTimer = null;\n      }\n    }\n\n    _addListener(key, listener) {\n      if (Object.keys(this.listeners).length === 0) {\n        this.startPolling();\n      }\n\n      if (!this.listeners[key]) {\n        this.listeners[key] = new Set(); // Populate the cache to avoid spuriously triggering on first poll.\n\n        void this._get(key); // This can happen in the background async and we can return immediately.\n      }\n\n      this.listeners[key].add(listener);\n    }\n\n    _removeListener(key, listener) {\n      if (this.listeners[key]) {\n        this.listeners[key].delete(listener);\n\n        if (this.listeners[key].size === 0) {\n          delete this.listeners[key];\n        }\n      }\n\n      if (Object.keys(this.listeners).length === 0) {\n        this.stopPolling();\n      }\n    }\n\n  }\n\n  IndexedDBLocalPersistence.type = 'LOCAL';\n  /**\r\n   * An implementation of {@link Persistence} of type `LOCAL` using `indexedDB`\r\n   * for the underlying storage.\r\n   *\r\n   * @public\r\n   */\n\n  return IndexedDBLocalPersistence;\n})();\nconst indexedDBLocalPersistence = IndexedDBLocalPersistence;\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nfunction startSignInPhoneMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaSignIn:start\"\n  /* Endpoint.START_MFA_SIGN_IN */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction finalizeSignInPhoneMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaSignIn:finalize\"\n  /* Endpoint.FINALIZE_MFA_SIGN_IN */\n  , _addTidIfNecessary(auth, request));\n}\n\nfunction finalizeSignInTotpMfa(auth, request) {\n  return _performApiRequest(auth, \"POST\"\n  /* HttpMethod.POST */\n  , \"/v2/accounts/mfaSignIn:finalize\"\n  /* Endpoint.FINALIZE_MFA_SIGN_IN */\n  , _addTidIfNecessary(auth, request));\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst _SOLVE_TIME_MS = 500;\nconst _EXPIRATION_TIME_MS = 60000;\nconst _WIDGET_ID_START = *************;\n\nclass MockReCaptcha {\n  constructor(auth) {\n    this.auth = auth;\n    this.counter = _WIDGET_ID_START;\n    this._widgets = new Map();\n  }\n\n  render(container, parameters) {\n    const id = this.counter;\n\n    this._widgets.set(id, new MockWidget(container, this.auth.name, parameters || {}));\n\n    this.counter++;\n    return id;\n  }\n\n  reset(optWidgetId) {\n    var _a;\n\n    const id = optWidgetId || _WIDGET_ID_START;\n    void ((_a = this._widgets.get(id)) === null || _a === void 0 ? void 0 : _a.delete());\n\n    this._widgets.delete(id);\n  }\n\n  getResponse(optWidgetId) {\n    var _a;\n\n    const id = optWidgetId || _WIDGET_ID_START;\n    return ((_a = this._widgets.get(id)) === null || _a === void 0 ? void 0 : _a.getResponse()) || '';\n  }\n\n  execute(optWidgetId) {\n    var _this56 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      const id = optWidgetId || _WIDGET_ID_START;\n      void ((_a = _this56._widgets.get(id)) === null || _a === void 0 ? void 0 : _a.execute());\n      return '';\n    })();\n  }\n\n}\n\nclass MockWidget {\n  constructor(containerOrId, appName, params) {\n    this.params = params;\n    this.timerId = null;\n    this.deleted = false;\n    this.responseToken = null;\n\n    this.clickHandler = () => {\n      this.execute();\n    };\n\n    const container = typeof containerOrId === 'string' ? document.getElementById(containerOrId) : containerOrId;\n\n    _assert(container, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    , {\n      appName\n    });\n\n    this.container = container;\n    this.isVisible = this.params.size !== 'invisible';\n\n    if (this.isVisible) {\n      this.execute();\n    } else {\n      this.container.addEventListener('click', this.clickHandler);\n    }\n  }\n\n  getResponse() {\n    this.checkIfDeleted();\n    return this.responseToken;\n  }\n\n  delete() {\n    this.checkIfDeleted();\n    this.deleted = true;\n\n    if (this.timerId) {\n      clearTimeout(this.timerId);\n      this.timerId = null;\n    }\n\n    this.container.removeEventListener('click', this.clickHandler);\n  }\n\n  execute() {\n    this.checkIfDeleted();\n\n    if (this.timerId) {\n      return;\n    }\n\n    this.timerId = window.setTimeout(() => {\n      this.responseToken = generateRandomAlphaNumericString(50);\n      const {\n        callback,\n        'expired-callback': expiredCallback\n      } = this.params;\n\n      if (callback) {\n        try {\n          callback(this.responseToken);\n        } catch (e) {}\n      }\n\n      this.timerId = window.setTimeout(() => {\n        this.timerId = null;\n        this.responseToken = null;\n\n        if (expiredCallback) {\n          try {\n            expiredCallback();\n          } catch (e) {}\n        }\n\n        if (this.isVisible) {\n          this.execute();\n        }\n      }, _EXPIRATION_TIME_MS);\n    }, _SOLVE_TIME_MS);\n  }\n\n  checkIfDeleted() {\n    if (this.deleted) {\n      throw new Error('reCAPTCHA mock was already deleted!');\n    }\n  }\n\n}\n\nfunction generateRandomAlphaNumericString(len) {\n  const chars = [];\n  const allowedChars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n\n  for (let i = 0; i < len; i++) {\n    chars.push(allowedChars.charAt(Math.floor(Math.random() * allowedChars.length)));\n  }\n\n  return chars.join('');\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// ReCaptcha will load using the same callback, so the callback function needs\n// to be kept around\n\n\nconst _JSLOAD_CALLBACK = _generateCallbackName('rcb');\n\nconst NETWORK_TIMEOUT_DELAY = new Delay(30000, 60000);\nconst RECAPTCHA_BASE = 'https://www.google.com/recaptcha/api.js?';\n/**\r\n * Loader for the GReCaptcha library. There should only ever be one of this.\r\n */\n\nclass ReCaptchaLoaderImpl {\n  constructor() {\n    var _a;\n\n    this.hostLanguage = '';\n    this.counter = 0;\n    /**\r\n     * Check for `render()` method. `window.grecaptcha` will exist if the Enterprise\r\n     * version of the ReCAPTCHA script was loaded by someone else (e.g. App Check) but\r\n     * `window.grecaptcha.render()` will not. Another load will add it.\r\n     */\n\n    this.librarySeparatelyLoaded = !!((_a = _window().grecaptcha) === null || _a === void 0 ? void 0 : _a.render);\n  }\n\n  load(auth, hl = '') {\n    _assert(isHostLanguageValid(hl), auth, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    if (this.shouldResolveImmediately(hl) && isV2(_window().grecaptcha)) {\n      return Promise.resolve(_window().grecaptcha);\n    }\n\n    return new Promise((resolve, reject) => {\n      const networkTimeout = _window().setTimeout(() => {\n        reject(_createError(auth, \"network-request-failed\"\n        /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n        ));\n      }, NETWORK_TIMEOUT_DELAY.get());\n\n      _window()[_JSLOAD_CALLBACK] = () => {\n        _window().clearTimeout(networkTimeout);\n\n        delete _window()[_JSLOAD_CALLBACK];\n\n        const recaptcha = _window().grecaptcha;\n\n        if (!recaptcha || !isV2(recaptcha)) {\n          reject(_createError(auth, \"internal-error\"\n          /* AuthErrorCode.INTERNAL_ERROR */\n          ));\n          return;\n        } // Wrap the greptcha render function so that we know if the developer has\n        // called it separately\n\n\n        const render = recaptcha.render;\n\n        recaptcha.render = (container, params) => {\n          const widgetId = render(container, params);\n          this.counter++;\n          return widgetId;\n        };\n\n        this.hostLanguage = hl;\n        resolve(recaptcha);\n      };\n\n      const url = `${RECAPTCHA_BASE}?${querystring({\n        onload: _JSLOAD_CALLBACK,\n        render: 'explicit',\n        hl\n      })}`;\n\n      _loadJS(url).catch(() => {\n        clearTimeout(networkTimeout);\n        reject(_createError(auth, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        ));\n      });\n    });\n  }\n\n  clearedOneInstance() {\n    this.counter--;\n  }\n\n  shouldResolveImmediately(hl) {\n    var _a; // We can resolve immediately if:\n    //   • grecaptcha is already defined AND (\n    //     1. the requested language codes are the same OR\n    //     2. there exists already a ReCaptcha on the page\n    //     3. the library was already loaded by the app\n    // In cases (2) and (3), we _can't_ reload as it would break the recaptchas\n    // that are already in the page\n\n\n    return !!((_a = _window().grecaptcha) === null || _a === void 0 ? void 0 : _a.render) && (hl === this.hostLanguage || this.counter > 0 || this.librarySeparatelyLoaded);\n  }\n\n}\n\nfunction isHostLanguageValid(hl) {\n  return hl.length <= 6 && /^\\s*[a-zA-Z0-9\\-]*\\s*$/.test(hl);\n}\n\nclass MockReCaptchaLoaderImpl {\n  load(auth) {\n    return _asyncToGenerator(function* () {\n      return new MockReCaptcha(auth);\n    })();\n  }\n\n  clearedOneInstance() {}\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst RECAPTCHA_VERIFIER_TYPE = 'recaptcha';\nconst DEFAULT_PARAMS = {\n  theme: 'light',\n  type: 'image'\n};\n/**\r\n * An {@link https://www.google.com/recaptcha/ | reCAPTCHA}-based application verifier.\r\n *\r\n * @public\r\n */\n\nclass RecaptchaVerifier {\n  /**\r\n   *\r\n   * @param containerOrId - The reCAPTCHA container parameter.\r\n   *\r\n   * @remarks\r\n   * This has different meaning depending on whether the reCAPTCHA is hidden or visible. For a\r\n   * visible reCAPTCHA the container must be empty. If a string is used, it has to correspond to\r\n   * an element ID. The corresponding element must also must be in the DOM at the time of\r\n   * initialization.\r\n   *\r\n   * @param parameters - The optional reCAPTCHA parameters.\r\n   *\r\n   * @remarks\r\n   * Check the reCAPTCHA docs for a comprehensive list. All parameters are accepted except for\r\n   * the sitekey. Firebase Auth backend provisions a reCAPTCHA for each project and will\r\n   * configure this upon rendering. For an invisible reCAPTCHA, a size key must have the value\r\n   * 'invisible'.\r\n   *\r\n   * @param authExtern - The corresponding Firebase {@link Auth} instance.\r\n   */\n  constructor(containerOrId, parameters = Object.assign({}, DEFAULT_PARAMS), authExtern) {\n    this.parameters = parameters;\n    /**\r\n     * The application verifier type.\r\n     *\r\n     * @remarks\r\n     * For a reCAPTCHA verifier, this is 'recaptcha'.\r\n     */\n\n    this.type = RECAPTCHA_VERIFIER_TYPE;\n    this.destroyed = false;\n    this.widgetId = null;\n    this.tokenChangeListeners = new Set();\n    this.renderPromise = null;\n    this.recaptcha = null;\n    this.auth = _castAuth(authExtern);\n    this.isInvisible = this.parameters.size === 'invisible';\n\n    _assert(typeof document !== 'undefined', this.auth, \"operation-not-supported-in-this-environment\"\n    /* AuthErrorCode.OPERATION_NOT_SUPPORTED */\n    );\n\n    const container = typeof containerOrId === 'string' ? document.getElementById(containerOrId) : containerOrId;\n\n    _assert(container, this.auth, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    this.container = container;\n    this.parameters.callback = this.makeTokenCallback(this.parameters.callback);\n    this._recaptchaLoader = this.auth.settings.appVerificationDisabledForTesting ? new MockReCaptchaLoaderImpl() : new ReCaptchaLoaderImpl();\n    this.validateStartingState(); // TODO: Figure out if sdk version is needed\n  }\n  /**\r\n   * Waits for the user to solve the reCAPTCHA and resolves with the reCAPTCHA token.\r\n   *\r\n   * @returns A Promise for the reCAPTCHA token.\r\n   */\n\n\n  verify() {\n    var _this57 = this;\n\n    return _asyncToGenerator(function* () {\n      _this57.assertNotDestroyed();\n\n      const id = yield _this57.render();\n\n      const recaptcha = _this57.getAssertedRecaptcha();\n\n      const response = recaptcha.getResponse(id);\n\n      if (response) {\n        return response;\n      }\n\n      return new Promise(resolve => {\n        const tokenChange = token => {\n          if (!token) {\n            return; // Ignore token expirations.\n          }\n\n          _this57.tokenChangeListeners.delete(tokenChange);\n\n          resolve(token);\n        };\n\n        _this57.tokenChangeListeners.add(tokenChange);\n\n        if (_this57.isInvisible) {\n          recaptcha.execute(id);\n        }\n      });\n    })();\n  }\n  /**\r\n   * Renders the reCAPTCHA widget on the page.\r\n   *\r\n   * @returns A Promise that resolves with the reCAPTCHA widget ID.\r\n   */\n\n\n  render() {\n    try {\n      this.assertNotDestroyed();\n    } catch (e) {\n      // This method returns a promise. Since it's not async (we want to return the\n      // _same_ promise if rendering is still occurring), the API surface should\n      // reject with the error rather than just throw\n      return Promise.reject(e);\n    }\n\n    if (this.renderPromise) {\n      return this.renderPromise;\n    }\n\n    this.renderPromise = this.makeRenderPromise().catch(e => {\n      this.renderPromise = null;\n      throw e;\n    });\n    return this.renderPromise;\n  }\n  /** @internal */\n\n\n  _reset() {\n    this.assertNotDestroyed();\n\n    if (this.widgetId !== null) {\n      this.getAssertedRecaptcha().reset(this.widgetId);\n    }\n  }\n  /**\r\n   * Clears the reCAPTCHA widget from the page and destroys the instance.\r\n   */\n\n\n  clear() {\n    this.assertNotDestroyed();\n    this.destroyed = true;\n\n    this._recaptchaLoader.clearedOneInstance();\n\n    if (!this.isInvisible) {\n      this.container.childNodes.forEach(node => {\n        this.container.removeChild(node);\n      });\n    }\n  }\n\n  validateStartingState() {\n    _assert(!this.parameters.sitekey, this.auth, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    _assert(this.isInvisible || !this.container.hasChildNodes(), this.auth, \"argument-error\"\n    /* AuthErrorCode.ARGUMENT_ERROR */\n    );\n\n    _assert(typeof document !== 'undefined', this.auth, \"operation-not-supported-in-this-environment\"\n    /* AuthErrorCode.OPERATION_NOT_SUPPORTED */\n    );\n  }\n\n  makeTokenCallback(existing) {\n    return token => {\n      this.tokenChangeListeners.forEach(listener => listener(token));\n\n      if (typeof existing === 'function') {\n        existing(token);\n      } else if (typeof existing === 'string') {\n        const globalFunc = _window()[existing];\n\n        if (typeof globalFunc === 'function') {\n          globalFunc(token);\n        }\n      }\n    };\n  }\n\n  assertNotDestroyed() {\n    _assert(!this.destroyed, this.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n  }\n\n  makeRenderPromise() {\n    var _this58 = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this58.init();\n\n      if (!_this58.widgetId) {\n        let container = _this58.container;\n\n        if (!_this58.isInvisible) {\n          const guaranteedEmpty = document.createElement('div');\n          container.appendChild(guaranteedEmpty);\n          container = guaranteedEmpty;\n        }\n\n        _this58.widgetId = _this58.getAssertedRecaptcha().render(container, _this58.parameters);\n      }\n\n      return _this58.widgetId;\n    })();\n  }\n\n  init() {\n    var _this59 = this;\n\n    return _asyncToGenerator(function* () {\n      _assert(_isHttpOrHttps() && !_isWorker(), _this59.auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      yield domReady();\n      _this59.recaptcha = yield _this59._recaptchaLoader.load(_this59.auth, _this59.auth.languageCode || undefined);\n      const siteKey = yield getRecaptchaParams(_this59.auth);\n\n      _assert(siteKey, _this59.auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n\n      _this59.parameters.sitekey = siteKey;\n    })();\n  }\n\n  getAssertedRecaptcha() {\n    _assert(this.recaptcha, this.auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    return this.recaptcha;\n  }\n\n}\n\nfunction domReady() {\n  let resolver = null;\n  return new Promise(resolve => {\n    if (document.readyState === 'complete') {\n      resolve();\n      return;\n    } // Document not ready, wait for load before resolving.\n    // Save resolver, so we can remove listener in case it was externally\n    // cancelled.\n\n\n    resolver = () => resolve();\n\n    window.addEventListener('load', resolver);\n  }).catch(e => {\n    if (resolver) {\n      window.removeEventListener('load', resolver);\n    }\n\n    throw e;\n  });\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass ConfirmationResultImpl {\n  constructor(verificationId, onConfirmation) {\n    this.verificationId = verificationId;\n    this.onConfirmation = onConfirmation;\n  }\n\n  confirm(verificationCode) {\n    const authCredential = PhoneAuthCredential._fromVerification(this.verificationId, verificationCode);\n\n    return this.onConfirmation(authCredential);\n  }\n\n}\n/**\r\n * Asynchronously signs in using a phone number.\r\n *\r\n * @remarks\r\n * This method sends a code via SMS to the given\r\n * phone number, and returns a {@link ConfirmationResult}. After the user\r\n * provides the code sent to their phone, call {@link ConfirmationResult.confirm}\r\n * with the code to sign the user in.\r\n *\r\n * For abuse prevention, this method also requires a {@link ApplicationVerifier}.\r\n * This SDK includes a reCAPTCHA-based implementation, {@link RecaptchaVerifier}.\r\n * This function can work on other platforms that do not support the\r\n * {@link RecaptchaVerifier} (like React Native), but you need to use a\r\n * third-party {@link ApplicationVerifier} implementation.\r\n *\r\n * @example\r\n * ```javascript\r\n * // 'recaptcha-container' is the ID of an element in the DOM.\r\n * const applicationVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container');\r\n * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\r\n * // Obtain a verificationCode from the user.\r\n * const credential = await confirmationResult.confirm(verificationCode);\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\r\n * @param appVerifier - The {@link ApplicationVerifier}.\r\n *\r\n * @public\r\n */\n\n\nfunction signInWithPhoneNumber(_x143, _x144, _x145) {\n  return _signInWithPhoneNumber.apply(this, arguments);\n}\n/**\r\n * Links the user account with the given phone number.\r\n *\r\n * @param user - The user.\r\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\r\n * @param appVerifier - The {@link ApplicationVerifier}.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithPhoneNumber() {\n  _signInWithPhoneNumber = _asyncToGenerator(function* (auth, phoneNumber, appVerifier) {\n    const authInternal = _castAuth(auth);\n\n    const verificationId = yield _verifyPhoneNumber(authInternal, phoneNumber, getModularInstance(appVerifier));\n    return new ConfirmationResultImpl(verificationId, cred => signInWithCredential(authInternal, cred));\n  });\n  return _signInWithPhoneNumber.apply(this, arguments);\n}\n\nfunction linkWithPhoneNumber(_x146, _x147, _x148) {\n  return _linkWithPhoneNumber.apply(this, arguments);\n}\n/**\r\n * Re-authenticates a user using a fresh phone credential.\r\n *\r\n * @remarks Use before operations such as {@link updatePassword} that require tokens from recent sign-in attempts.\r\n *\r\n * @param user - The user.\r\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\r\n * @param appVerifier - The {@link ApplicationVerifier}.\r\n *\r\n * @public\r\n */\n\n\nfunction _linkWithPhoneNumber() {\n  _linkWithPhoneNumber = _asyncToGenerator(function* (user, phoneNumber, appVerifier) {\n    const userInternal = getModularInstance(user);\n    yield _assertLinkedStatus(false, userInternal, \"phone\"\n    /* ProviderId.PHONE */\n    );\n    const verificationId = yield _verifyPhoneNumber(userInternal.auth, phoneNumber, getModularInstance(appVerifier));\n    return new ConfirmationResultImpl(verificationId, cred => linkWithCredential(userInternal, cred));\n  });\n  return _linkWithPhoneNumber.apply(this, arguments);\n}\n\nfunction reauthenticateWithPhoneNumber(_x149, _x150, _x151) {\n  return _reauthenticateWithPhoneNumber.apply(this, arguments);\n}\n/**\r\n * Returns a verification ID to be used in conjunction with the SMS code that is sent.\r\n *\r\n */\n\n\nfunction _reauthenticateWithPhoneNumber() {\n  _reauthenticateWithPhoneNumber = _asyncToGenerator(function* (user, phoneNumber, appVerifier) {\n    const userInternal = getModularInstance(user);\n    const verificationId = yield _verifyPhoneNumber(userInternal.auth, phoneNumber, getModularInstance(appVerifier));\n    return new ConfirmationResultImpl(verificationId, cred => reauthenticateWithCredential(userInternal, cred));\n  });\n  return _reauthenticateWithPhoneNumber.apply(this, arguments);\n}\n\nfunction _verifyPhoneNumber(_x152, _x153, _x154) {\n  return _verifyPhoneNumber2.apply(this, arguments);\n}\n/**\r\n * Updates the user's phone number.\r\n *\r\n * @example\r\n * ```\r\n * // 'recaptcha-container' is the ID of an element in the DOM.\r\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\r\n * const provider = new PhoneAuthProvider(auth);\r\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\r\n * // Obtain the verificationCode from the user.\r\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\r\n * await updatePhoneNumber(user, phoneCredential);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param credential - A credential authenticating the new phone number.\r\n *\r\n * @public\r\n */\n\n\nfunction _verifyPhoneNumber2() {\n  _verifyPhoneNumber2 = _asyncToGenerator(function* (auth, options, verifier) {\n    var _a;\n\n    const recaptchaToken = yield verifier.verify();\n\n    try {\n      _assert(typeof recaptchaToken === 'string', auth, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      _assert(verifier.type === RECAPTCHA_VERIFIER_TYPE, auth, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      let phoneInfoOptions;\n\n      if (typeof options === 'string') {\n        phoneInfoOptions = {\n          phoneNumber: options\n        };\n      } else {\n        phoneInfoOptions = options;\n      }\n\n      if ('session' in phoneInfoOptions) {\n        const session = phoneInfoOptions.session;\n\n        if ('phoneNumber' in phoneInfoOptions) {\n          _assert(session.type === \"enroll\"\n          /* MultiFactorSessionType.ENROLL */\n          , auth, \"internal-error\"\n          /* AuthErrorCode.INTERNAL_ERROR */\n          );\n\n          const response = yield startEnrollPhoneMfa(auth, {\n            idToken: session.credential,\n            phoneEnrollmentInfo: {\n              phoneNumber: phoneInfoOptions.phoneNumber,\n              recaptchaToken\n            }\n          });\n          return response.phoneSessionInfo.sessionInfo;\n        } else {\n          _assert(session.type === \"signin\"\n          /* MultiFactorSessionType.SIGN_IN */\n          , auth, \"internal-error\"\n          /* AuthErrorCode.INTERNAL_ERROR */\n          );\n\n          const mfaEnrollmentId = ((_a = phoneInfoOptions.multiFactorHint) === null || _a === void 0 ? void 0 : _a.uid) || phoneInfoOptions.multiFactorUid;\n\n          _assert(mfaEnrollmentId, auth, \"missing-multi-factor-info\"\n          /* AuthErrorCode.MISSING_MFA_INFO */\n          );\n\n          const response = yield startSignInPhoneMfa(auth, {\n            mfaPendingCredential: session.credential,\n            mfaEnrollmentId,\n            phoneSignInInfo: {\n              recaptchaToken\n            }\n          });\n          return response.phoneResponseInfo.sessionInfo;\n        }\n      } else {\n        const {\n          sessionInfo\n        } = yield sendPhoneVerificationCode(auth, {\n          phoneNumber: phoneInfoOptions.phoneNumber,\n          recaptchaToken\n        });\n        return sessionInfo;\n      }\n    } finally {\n      verifier._reset();\n    }\n  });\n  return _verifyPhoneNumber2.apply(this, arguments);\n}\n\nfunction updatePhoneNumber(_x155, _x156) {\n  return _updatePhoneNumber.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Provider for generating an {@link PhoneAuthCredential}.\r\n *\r\n * @example\r\n * ```javascript\r\n * // 'recaptcha-container' is the ID of an element in the DOM.\r\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\r\n * const provider = new PhoneAuthProvider(auth);\r\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\r\n * // Obtain the verificationCode from the user.\r\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\r\n * const userCredential = await signInWithCredential(auth, phoneCredential);\r\n * ```\r\n *\r\n * @public\r\n */\n\n\nfunction _updatePhoneNumber() {\n  _updatePhoneNumber = _asyncToGenerator(function* (user, credential) {\n    yield _link$1(getModularInstance(user), credential);\n  });\n  return _updatePhoneNumber.apply(this, arguments);\n}\n\nlet PhoneAuthProvider = /*#__PURE__*/(() => {\n  class PhoneAuthProvider {\n    /**\r\n     * @param auth - The Firebase {@link Auth} instance in which sign-ins should occur.\r\n     *\r\n     */\n    constructor(auth) {\n      /** Always set to {@link ProviderId}.PHONE. */\n      this.providerId = PhoneAuthProvider.PROVIDER_ID;\n      this.auth = _castAuth(auth);\n    }\n    /**\r\n     *\r\n     * Starts a phone number authentication flow by sending a verification code to the given phone\r\n     * number.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * const provider = new PhoneAuthProvider(auth);\r\n     * const verificationId = await provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\r\n     * // Obtain verificationCode from the user.\r\n     * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\r\n     * const userCredential = await signInWithCredential(auth, authCredential);\r\n     * ```\r\n     *\r\n     * @example\r\n     * An alternative flow is provided using the `signInWithPhoneNumber` method.\r\n     * ```javascript\r\n     * const confirmationResult = signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\r\n     * // Obtain verificationCode from the user.\r\n     * const userCredential = confirmationResult.confirm(verificationCode);\r\n     * ```\r\n     *\r\n     * @param phoneInfoOptions - The user's {@link PhoneInfoOptions}. The phone number should be in\r\n     * E.164 format (e.g. +***********).\r\n     * @param applicationVerifier - For abuse prevention, this method also requires a\r\n     * {@link ApplicationVerifier}. This SDK includes a reCAPTCHA-based implementation,\r\n     * {@link RecaptchaVerifier}.\r\n     *\r\n     * @returns A Promise for a verification ID that can be passed to\r\n     * {@link PhoneAuthProvider.credential} to identify this flow..\r\n     */\n\n\n    verifyPhoneNumber(phoneOptions, applicationVerifier) {\n      return _verifyPhoneNumber(this.auth, phoneOptions, getModularInstance(applicationVerifier));\n    }\n    /**\r\n     * Creates a phone auth credential, given the verification ID from\r\n     * {@link PhoneAuthProvider.verifyPhoneNumber} and the code that was sent to the user's\r\n     * mobile device.\r\n     *\r\n     * @example\r\n     * ```javascript\r\n     * const provider = new PhoneAuthProvider(auth);\r\n     * const verificationId = provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\r\n     * // Obtain verificationCode from the user.\r\n     * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\r\n     * const userCredential = signInWithCredential(auth, authCredential);\r\n     * ```\r\n     *\r\n     * @example\r\n     * An alternative flow is provided using the `signInWithPhoneNumber` method.\r\n     * ```javascript\r\n     * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\r\n     * // Obtain verificationCode from the user.\r\n     * const userCredential = await confirmationResult.confirm(verificationCode);\r\n     * ```\r\n     *\r\n     * @param verificationId - The verification ID returned from {@link PhoneAuthProvider.verifyPhoneNumber}.\r\n     * @param verificationCode - The verification code sent to the user's mobile device.\r\n     *\r\n     * @returns The auth provider credential.\r\n     */\n\n\n    static credential(verificationId, verificationCode) {\n      return PhoneAuthCredential._fromVerification(verificationId, verificationCode);\n    }\n    /**\r\n     * Generates an {@link AuthCredential} from a {@link UserCredential}.\r\n     * @param userCredential - The user credential.\r\n     */\n\n\n    static credentialFromResult(userCredential) {\n      const credential = userCredential;\n      return PhoneAuthProvider.credentialFromTaggedObject(credential);\n    }\n    /**\r\n     * Returns an {@link AuthCredential} when passed an error.\r\n     *\r\n     * @remarks\r\n     *\r\n     * This method works for errors like\r\n     * `auth/account-exists-with-different-credentials`. This is useful for\r\n     * recovering when attempting to set a user's phone number but the number\r\n     * in question is already tied to another account. For example, the following\r\n     * code tries to update the current user's phone number, and if that\r\n     * fails, links the user with the account associated with that number:\r\n     *\r\n     * ```js\r\n     * const provider = new PhoneAuthProvider(auth);\r\n     * const verificationId = await provider.verifyPhoneNumber(number, verifier);\r\n     * try {\r\n     *   const code = ''; // Prompt the user for the verification code\r\n     *   await updatePhoneNumber(\r\n     *       auth.currentUser,\r\n     *       PhoneAuthProvider.credential(verificationId, code));\r\n     * } catch (e) {\r\n     *   if ((e as FirebaseError)?.code === 'auth/account-exists-with-different-credential') {\r\n     *     const cred = PhoneAuthProvider.credentialFromError(e);\r\n     *     await linkWithCredential(auth.currentUser, cred);\r\n     *   }\r\n     * }\r\n     *\r\n     * // At this point, auth.currentUser.phoneNumber === number.\r\n     * ```\r\n     *\r\n     * @param error - The error to generate a credential from.\r\n     */\n\n\n    static credentialFromError(error) {\n      return PhoneAuthProvider.credentialFromTaggedObject(error.customData || {});\n    }\n\n    static credentialFromTaggedObject({\n      _tokenResponse: tokenResponse\n    }) {\n      if (!tokenResponse) {\n        return null;\n      }\n\n      const {\n        phoneNumber,\n        temporaryProof\n      } = tokenResponse;\n\n      if (phoneNumber && temporaryProof) {\n        return PhoneAuthCredential._fromTokenResponse(phoneNumber, temporaryProof);\n      }\n\n      return null;\n    }\n\n  }\n\n  /** Always set to {@link ProviderId}.PHONE. */\n\n  /** Always set to {@link SignInMethod}.PHONE. */\n  PhoneAuthProvider.PROVIDER_ID = \"phone\"\n  /* ProviderId.PHONE */\n  ;\n  PhoneAuthProvider.PHONE_SIGN_IN_METHOD = \"phone\"\n  /* SignInMethod.PHONE */\n  ;\n  /**\r\n   * @license\r\n   * Copyright 2021 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  /**\r\n   * Chooses a popup/redirect resolver to use. This prefers the override (which\r\n   * is directly passed in), and falls back to the property set on the auth\r\n   * object. If neither are available, this function errors w/ an argument error.\r\n   */\n\n  return PhoneAuthProvider;\n})();\n\nfunction _withDefaultResolver(auth, resolverOverride) {\n  if (resolverOverride) {\n    return _getInstance(resolverOverride);\n  }\n\n  _assert(auth._popupRedirectResolver, auth, \"argument-error\"\n  /* AuthErrorCode.ARGUMENT_ERROR */\n  );\n\n  return auth._popupRedirectResolver;\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nclass IdpCredential extends AuthCredential {\n  constructor(params) {\n    super(\"custom\"\n    /* ProviderId.CUSTOM */\n    , \"custom\"\n    /* ProviderId.CUSTOM */\n    );\n    this.params = params;\n  }\n\n  _getIdTokenResponse(auth) {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  _linkToIdToken(auth, idToken) {\n    return signInWithIdp(auth, this._buildIdpRequest(idToken));\n  }\n\n  _getReauthenticationResolver(auth) {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  _buildIdpRequest(idToken) {\n    const request = {\n      requestUri: this.params.requestUri,\n      sessionId: this.params.sessionId,\n      postBody: this.params.postBody,\n      tenantId: this.params.tenantId,\n      pendingToken: this.params.pendingToken,\n      returnSecureToken: true,\n      returnIdpCredential: true\n    };\n\n    if (idToken) {\n      request.idToken = idToken;\n    }\n\n    return request;\n  }\n\n}\n\nfunction _signIn(params) {\n  return _signInWithCredential(params.auth, new IdpCredential(params), params.bypassAuthState);\n}\n\nfunction _reauth(params) {\n  const {\n    auth,\n    user\n  } = params;\n\n  _assert(user, auth, \"internal-error\"\n  /* AuthErrorCode.INTERNAL_ERROR */\n  );\n\n  return _reauthenticate(user, new IdpCredential(params), params.bypassAuthState);\n}\n\nfunction _link(_x157) {\n  return _link2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\r\n * events\r\n */\n\n\nfunction _link2() {\n  _link2 = _asyncToGenerator(function* (params) {\n    const {\n      auth,\n      user\n    } = params;\n\n    _assert(user, auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    return _link$1(user, new IdpCredential(params), params.bypassAuthState);\n  });\n  return _link2.apply(this, arguments);\n}\n\nclass AbstractPopupRedirectOperation {\n  constructor(auth, filter, resolver, user, bypassAuthState = false) {\n    this.auth = auth;\n    this.resolver = resolver;\n    this.user = user;\n    this.bypassAuthState = bypassAuthState;\n    this.pendingPromise = null;\n    this.eventManager = null;\n    this.filter = Array.isArray(filter) ? filter : [filter];\n  }\n\n  execute() {\n    var _this60 = this;\n\n    return new Promise( /*#__PURE__*/function () {\n      var _ref20 = _asyncToGenerator(function* (resolve, reject) {\n        _this60.pendingPromise = {\n          resolve,\n          reject\n        };\n\n        try {\n          _this60.eventManager = yield _this60.resolver._initialize(_this60.auth);\n          yield _this60.onExecution();\n\n          _this60.eventManager.registerConsumer(_this60);\n        } catch (e) {\n          _this60.reject(e);\n        }\n      });\n\n      return function (_x158, _x159) {\n        return _ref20.apply(this, arguments);\n      };\n    }());\n  }\n\n  onAuthEvent(event) {\n    var _this61 = this;\n\n    return _asyncToGenerator(function* () {\n      const {\n        urlResponse,\n        sessionId,\n        postBody,\n        tenantId,\n        error,\n        type\n      } = event;\n\n      if (error) {\n        _this61.reject(error);\n\n        return;\n      }\n\n      const params = {\n        auth: _this61.auth,\n        requestUri: urlResponse,\n        sessionId: sessionId,\n        tenantId: tenantId || undefined,\n        postBody: postBody || undefined,\n        user: _this61.user,\n        bypassAuthState: _this61.bypassAuthState\n      };\n\n      try {\n        _this61.resolve(yield _this61.getIdpTask(type)(params));\n      } catch (e) {\n        _this61.reject(e);\n      }\n    })();\n  }\n\n  onError(error) {\n    this.reject(error);\n  }\n\n  getIdpTask(type) {\n    switch (type) {\n      case \"signInViaPopup\"\n      /* AuthEventType.SIGN_IN_VIA_POPUP */\n      :\n      case \"signInViaRedirect\"\n      /* AuthEventType.SIGN_IN_VIA_REDIRECT */\n      :\n        return _signIn;\n\n      case \"linkViaPopup\"\n      /* AuthEventType.LINK_VIA_POPUP */\n      :\n      case \"linkViaRedirect\"\n      /* AuthEventType.LINK_VIA_REDIRECT */\n      :\n        return _link;\n\n      case \"reauthViaPopup\"\n      /* AuthEventType.REAUTH_VIA_POPUP */\n      :\n      case \"reauthViaRedirect\"\n      /* AuthEventType.REAUTH_VIA_REDIRECT */\n      :\n        return _reauth;\n\n      default:\n        _fail(this.auth, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n    }\n  }\n\n  resolve(cred) {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.resolve(cred);\n    this.unregisterAndCleanUp();\n  }\n\n  reject(error) {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.reject(error);\n    this.unregisterAndCleanUp();\n  }\n\n  unregisterAndCleanUp() {\n    if (this.eventManager) {\n      this.eventManager.unregisterConsumer(this);\n    }\n\n    this.pendingPromise = null;\n    this.cleanUp();\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst _POLL_WINDOW_CLOSE_TIMEOUT = new Delay(2000, 10000);\n/**\r\n * Authenticates a Firebase client using a popup-based OAuth authentication flow.\r\n *\r\n * @remarks\r\n * If succeeds, returns the signed in user along with the provider's credential. If sign in was\r\n * unsuccessful, returns an error object containing additional information about the error.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a popup.\r\n * const provider = new FacebookAuthProvider();\r\n * const result = await signInWithPopup(auth, provider);\r\n *\r\n * // The signed-in user info.\r\n * const user = result.user;\r\n * // This gives you a Facebook Access Token.\r\n * const credential = provider.credentialFromResult(auth, result);\r\n * const token = credential.accessToken;\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n *\r\n * @public\r\n */\n\n\nfunction signInWithPopup(_x160, _x161, _x162) {\n  return _signInWithPopup.apply(this, arguments);\n}\n/**\r\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a pop-up based\r\n * OAuth flow.\r\n *\r\n * @remarks\r\n * If the reauthentication is successful, the returned result will contain the user and the\r\n * provider's credential.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a popup.\r\n * const provider = new FacebookAuthProvider();\r\n * const result = await signInWithPopup(auth, provider);\r\n * // Reauthenticate using a popup.\r\n * await reauthenticateWithPopup(result.user, provider);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithPopup() {\n  _signInWithPopup = _asyncToGenerator(function* (auth, provider, resolver) {\n    const authInternal = _castAuth(auth);\n\n    _assertInstanceOf(auth, provider, FederatedAuthProvider);\n\n    const resolverInternal = _withDefaultResolver(authInternal, resolver);\n\n    const action = new PopupOperation(authInternal, \"signInViaPopup\"\n    /* AuthEventType.SIGN_IN_VIA_POPUP */\n    , provider, resolverInternal);\n    return action.executeNotNull();\n  });\n  return _signInWithPopup.apply(this, arguments);\n}\n\nfunction reauthenticateWithPopup(_x163, _x164, _x165) {\n  return _reauthenticateWithPopup.apply(this, arguments);\n}\n/**\r\n * Links the authenticated provider to the user account using a pop-up based OAuth flow.\r\n *\r\n * @remarks\r\n * If the linking is successful, the returned result will contain the user and the provider's credential.\r\n *\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using some other provider.\r\n * const result = await signInWithEmailAndPassword(auth, email, password);\r\n * // Link using a popup.\r\n * const provider = new FacebookAuthProvider();\r\n * await linkWithPopup(result.user, provider);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n * @public\r\n */\n\n\nfunction _reauthenticateWithPopup() {\n  _reauthenticateWithPopup = _asyncToGenerator(function* (user, provider, resolver) {\n    const userInternal = getModularInstance(user);\n\n    _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n\n    const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n    const action = new PopupOperation(userInternal.auth, \"reauthViaPopup\"\n    /* AuthEventType.REAUTH_VIA_POPUP */\n    , provider, resolverInternal, userInternal);\n    return action.executeNotNull();\n  });\n  return _reauthenticateWithPopup.apply(this, arguments);\n}\n\nfunction linkWithPopup(_x166, _x167, _x168) {\n  return _linkWithPopup.apply(this, arguments);\n}\n/**\r\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\r\n * events\r\n *\r\n */\n\n\nfunction _linkWithPopup() {\n  _linkWithPopup = _asyncToGenerator(function* (user, provider, resolver) {\n    const userInternal = getModularInstance(user);\n\n    _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n\n    const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n    const action = new PopupOperation(userInternal.auth, \"linkViaPopup\"\n    /* AuthEventType.LINK_VIA_POPUP */\n    , provider, resolverInternal, userInternal);\n    return action.executeNotNull();\n  });\n  return _linkWithPopup.apply(this, arguments);\n}\n\nlet PopupOperation = /*#__PURE__*/(() => {\n  class PopupOperation extends AbstractPopupRedirectOperation {\n    constructor(auth, filter, provider, resolver, user) {\n      super(auth, filter, resolver, user);\n      this.provider = provider;\n      this.authWindow = null;\n      this.pollId = null;\n\n      if (PopupOperation.currentPopupAction) {\n        PopupOperation.currentPopupAction.cancel();\n      }\n\n      PopupOperation.currentPopupAction = this;\n    }\n\n    executeNotNull() {\n      var _this62 = this;\n\n      return _asyncToGenerator(function* () {\n        const result = yield _this62.execute();\n\n        _assert(result, _this62.auth, \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n        return result;\n      })();\n    }\n\n    onExecution() {\n      var _this63 = this;\n\n      return _asyncToGenerator(function* () {\n        debugAssert(_this63.filter.length === 1, 'Popup operations only handle one event');\n\n        const eventId = _generateEventId();\n\n        _this63.authWindow = yield _this63.resolver._openPopup(_this63.auth, _this63.provider, _this63.filter[0], // There's always one, see constructor\n        eventId);\n        _this63.authWindow.associatedEvent = eventId; // Check for web storage support and origin validation _after_ the popup is\n        // loaded. These operations are slow (~1 second or so) Rather than\n        // waiting on them before opening the window, optimistically open the popup\n        // and check for storage support at the same time. If storage support is\n        // not available, this will cause the whole thing to reject properly. It\n        // will also close the popup, but since the promise has already rejected,\n        // the popup closed by user poll will reject into the void.\n\n        _this63.resolver._originValidation(_this63.auth).catch(e => {\n          _this63.reject(e);\n        });\n\n        _this63.resolver._isIframeWebStorageSupported(_this63.auth, isSupported => {\n          if (!isSupported) {\n            _this63.reject(_createError(_this63.auth, \"web-storage-unsupported\"\n            /* AuthErrorCode.WEB_STORAGE_UNSUPPORTED */\n            ));\n          }\n        }); // Handle user closure. Notice this does *not* use await\n\n\n        _this63.pollUserCancellation();\n      })();\n    }\n\n    get eventId() {\n      var _a;\n\n      return ((_a = this.authWindow) === null || _a === void 0 ? void 0 : _a.associatedEvent) || null;\n    }\n\n    cancel() {\n      this.reject(_createError(this.auth, \"cancelled-popup-request\"\n      /* AuthErrorCode.EXPIRED_POPUP_REQUEST */\n      ));\n    }\n\n    cleanUp() {\n      if (this.authWindow) {\n        this.authWindow.close();\n      }\n\n      if (this.pollId) {\n        window.clearTimeout(this.pollId);\n      }\n\n      this.authWindow = null;\n      this.pollId = null;\n      PopupOperation.currentPopupAction = null;\n    }\n\n    pollUserCancellation() {\n      const poll = () => {\n        var _a, _b;\n\n        if ((_b = (_a = this.authWindow) === null || _a === void 0 ? void 0 : _a.window) === null || _b === void 0 ? void 0 : _b.closed) {\n          // Make sure that there is sufficient time for whatever action to\n          // complete. The window could have closed but the sign in network\n          // call could still be in flight. This is specifically true for\n          // Firefox or if the opener is in an iframe, in which case the oauth\n          // helper closes the popup.\n          this.pollId = window.setTimeout(() => {\n            this.pollId = null;\n            this.reject(_createError(this.auth, \"popup-closed-by-user\"\n            /* AuthErrorCode.POPUP_CLOSED_BY_USER */\n            ));\n          }, 8000\n          /* _Timeout.AUTH_EVENT */\n          );\n          return;\n        }\n\n        this.pollId = window.setTimeout(poll, _POLL_WINDOW_CLOSE_TIMEOUT.get());\n      };\n\n      poll();\n    }\n\n  }\n\n  // Only one popup is ever shown at once. The lifecycle of the current popup\n  // can be managed / cancelled by the constructor.\n  PopupOperation.currentPopupAction = null;\n  /**\r\n   * @license\r\n   * Copyright 2020 Google LLC\r\n   *\r\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n   * you may not use this file except in compliance with the License.\r\n   * You may obtain a copy of the License at\r\n   *\r\n   *   http://www.apache.org/licenses/LICENSE-2.0\r\n   *\r\n   * Unless required by applicable law or agreed to in writing, software\r\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n   * See the License for the specific language governing permissions and\r\n   * limitations under the License.\r\n   */\n\n  return PopupOperation;\n})();\nconst PENDING_REDIRECT_KEY = 'pendingRedirect'; // We only get one redirect outcome for any one auth, so just store it\n// in here.\n\nconst redirectOutcomeMap = new Map();\n\nclass RedirectAction extends AbstractPopupRedirectOperation {\n  constructor(auth, resolver, bypassAuthState = false) {\n    super(auth, [\"signInViaRedirect\"\n    /* AuthEventType.SIGN_IN_VIA_REDIRECT */\n    , \"linkViaRedirect\"\n    /* AuthEventType.LINK_VIA_REDIRECT */\n    , \"reauthViaRedirect\"\n    /* AuthEventType.REAUTH_VIA_REDIRECT */\n    , \"unknown\"\n    /* AuthEventType.UNKNOWN */\n    ], resolver, undefined, bypassAuthState);\n    this.eventId = null;\n  }\n  /**\r\n   * Override the execute function; if we already have a redirect result, then\r\n   * just return it.\r\n   */\n\n\n  execute() {\n    var _superprop_getExecute = () => super.execute,\n        _this64 = this;\n\n    return _asyncToGenerator(function* () {\n      let readyOutcome = redirectOutcomeMap.get(_this64.auth._key());\n\n      if (!readyOutcome) {\n        try {\n          const hasPendingRedirect = yield _getAndClearPendingRedirectStatus(_this64.resolver, _this64.auth);\n          const result = hasPendingRedirect ? yield _superprop_getExecute().call(_this64) : null;\n\n          readyOutcome = () => Promise.resolve(result);\n        } catch (e) {\n          readyOutcome = () => Promise.reject(e);\n        }\n\n        redirectOutcomeMap.set(_this64.auth._key(), readyOutcome);\n      } // If we're not bypassing auth state, the ready outcome should be set to\n      // null.\n\n\n      if (!_this64.bypassAuthState) {\n        redirectOutcomeMap.set(_this64.auth._key(), () => Promise.resolve(null));\n      }\n\n      return readyOutcome();\n    })();\n  }\n\n  onAuthEvent(event) {\n    var _superprop_getOnAuthEvent = () => super.onAuthEvent,\n        _this65 = this;\n\n    return _asyncToGenerator(function* () {\n      if (event.type === \"signInViaRedirect\"\n      /* AuthEventType.SIGN_IN_VIA_REDIRECT */\n      ) {\n        return _superprop_getOnAuthEvent().call(_this65, event);\n      } else if (event.type === \"unknown\"\n      /* AuthEventType.UNKNOWN */\n      ) {\n        // This is a sentinel value indicating there's no pending redirect\n        _this65.resolve(null);\n\n        return;\n      }\n\n      if (event.eventId) {\n        const user = yield _this65.auth._redirectUserForId(event.eventId);\n\n        if (user) {\n          _this65.user = user;\n          return _superprop_getOnAuthEvent().call(_this65, event);\n        } else {\n          _this65.resolve(null);\n        }\n      }\n    })();\n  }\n\n  onExecution() {\n    return _asyncToGenerator(function* () {})();\n  }\n\n  cleanUp() {}\n\n}\n\nfunction _getAndClearPendingRedirectStatus(_x169, _x170) {\n  return _getAndClearPendingRedirectStatus2.apply(this, arguments);\n}\n\nfunction _getAndClearPendingRedirectStatus2() {\n  _getAndClearPendingRedirectStatus2 = _asyncToGenerator(function* (resolver, auth) {\n    const key = pendingRedirectKey(auth);\n    const persistence = resolverPersistence(resolver);\n\n    if (!(yield persistence._isAvailable())) {\n      return false;\n    }\n\n    const hasPendingRedirect = (yield persistence._get(key)) === 'true';\n    yield persistence._remove(key);\n    return hasPendingRedirect;\n  });\n  return _getAndClearPendingRedirectStatus2.apply(this, arguments);\n}\n\nfunction _setPendingRedirectStatus(_x171, _x172) {\n  return _setPendingRedirectStatus2.apply(this, arguments);\n}\n\nfunction _setPendingRedirectStatus2() {\n  _setPendingRedirectStatus2 = _asyncToGenerator(function* (resolver, auth) {\n    return resolverPersistence(resolver)._set(pendingRedirectKey(auth), 'true');\n  });\n  return _setPendingRedirectStatus2.apply(this, arguments);\n}\n\nfunction _clearRedirectOutcomes() {\n  redirectOutcomeMap.clear();\n}\n\nfunction _overrideRedirectResult(auth, result) {\n  redirectOutcomeMap.set(auth._key(), result);\n}\n\nfunction resolverPersistence(resolver) {\n  return _getInstance(resolver._redirectPersistence);\n}\n\nfunction pendingRedirectKey(auth) {\n  return _persistenceKeyName(PENDING_REDIRECT_KEY, auth.config.apiKey, auth.name);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Authenticates a Firebase client using a full-page redirect flow.\r\n *\r\n * @remarks\r\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\r\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\r\n * | best practices} when using {@link signInWithRedirect}.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new FacebookAuthProvider();\r\n * // You can add additional scopes to the provider:\r\n * provider.addScope('user_birthday');\r\n * // Start a sign in process for an unauthenticated user.\r\n * await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * if (result) {\r\n *   // This is the signed-in user\r\n *   const user = result.user;\r\n *   // This gives you a Facebook Access Token.\r\n *   const credential = provider.credentialFromResult(auth, result);\r\n *   const token = credential.accessToken;\r\n * }\r\n * // As this API can be used for sign-in, linking and reauthentication,\r\n * // check the operationType to determine what triggered this redirect\r\n * // operation.\r\n * const operationType = result.operationType;\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n * @public\r\n */\n\n\nfunction signInWithRedirect(auth, provider, resolver) {\n  return _signInWithRedirect(auth, provider, resolver);\n}\n\nfunction _signInWithRedirect(_x173, _x174, _x175) {\n  return _signInWithRedirect2.apply(this, arguments);\n}\n/**\r\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a full-page redirect flow.\r\n * @remarks\r\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\r\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\r\n * | best practices} when using {@link reauthenticateWithRedirect}.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new FacebookAuthProvider();\r\n * const result = await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * // Reauthenticate using a redirect.\r\n * await reauthenticateWithRedirect(result.user, provider);\r\n * // This will again trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n * @public\r\n */\n\n\nfunction _signInWithRedirect2() {\n  _signInWithRedirect2 = _asyncToGenerator(function* (auth, provider, resolver) {\n    const authInternal = _castAuth(auth);\n\n    _assertInstanceOf(auth, provider, FederatedAuthProvider); // Wait for auth initialization to complete, this will process pending redirects and clear the\n    // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n    // redirect and creating a PENDING_REDIRECT_KEY entry.\n\n\n    yield authInternal._initializationPromise;\n\n    const resolverInternal = _withDefaultResolver(authInternal, resolver);\n\n    yield _setPendingRedirectStatus(resolverInternal, authInternal);\n    return resolverInternal._openRedirect(authInternal, provider, \"signInViaRedirect\"\n    /* AuthEventType.SIGN_IN_VIA_REDIRECT */\n    );\n  });\n  return _signInWithRedirect2.apply(this, arguments);\n}\n\nfunction reauthenticateWithRedirect(user, provider, resolver) {\n  return _reauthenticateWithRedirect(user, provider, resolver);\n}\n\nfunction _reauthenticateWithRedirect(_x176, _x177, _x178) {\n  return _reauthenticateWithRedirect2.apply(this, arguments);\n}\n/**\r\n * Links the {@link OAuthProvider} to the user account using a full-page redirect flow.\r\n * @remarks\r\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\r\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\r\n * | best practices} when using {@link linkWithRedirect}.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using some other provider.\r\n * const result = await signInWithEmailAndPassword(auth, email, password);\r\n * // Link using a redirect.\r\n * const provider = new FacebookAuthProvider();\r\n * await linkWithRedirect(result.user, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * ```\r\n *\r\n * @param user - The user.\r\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\r\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n *\r\n * @public\r\n */\n\n\nfunction _reauthenticateWithRedirect2() {\n  _reauthenticateWithRedirect2 = _asyncToGenerator(function* (user, provider, resolver) {\n    const userInternal = getModularInstance(user);\n\n    _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider); // Wait for auth initialization to complete, this will process pending redirects and clear the\n    // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n    // redirect and creating a PENDING_REDIRECT_KEY entry.\n\n\n    yield userInternal.auth._initializationPromise; // Allow the resolver to error before persisting the redirect user\n\n    const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n    yield _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n    const eventId = yield prepareUserForRedirect(userInternal);\n    return resolverInternal._openRedirect(userInternal.auth, provider, \"reauthViaRedirect\"\n    /* AuthEventType.REAUTH_VIA_REDIRECT */\n    , eventId);\n  });\n  return _reauthenticateWithRedirect2.apply(this, arguments);\n}\n\nfunction linkWithRedirect(user, provider, resolver) {\n  return _linkWithRedirect(user, provider, resolver);\n}\n\nfunction _linkWithRedirect(_x179, _x180, _x181) {\n  return _linkWithRedirect2.apply(this, arguments);\n}\n/**\r\n * Returns a {@link UserCredential} from the redirect-based sign-in flow.\r\n *\r\n * @remarks\r\n * If sign-in succeeded, returns the signed in user. If sign-in was unsuccessful, fails with an\r\n * error. If no redirect operation was called, returns `null`.\r\n *\r\n * @example\r\n * ```javascript\r\n * // Sign in using a redirect.\r\n * const provider = new FacebookAuthProvider();\r\n * // You can add additional scopes to the provider:\r\n * provider.addScope('user_birthday');\r\n * // Start a sign in process for an unauthenticated user.\r\n * await signInWithRedirect(auth, provider);\r\n * // This will trigger a full page redirect away from your app\r\n *\r\n * // After returning from the redirect when your app initializes you can obtain the result\r\n * const result = await getRedirectResult(auth);\r\n * if (result) {\r\n *   // This is the signed-in user\r\n *   const user = result.user;\r\n *   // This gives you a Facebook Access Token.\r\n *   const credential = provider.credentialFromResult(auth, result);\r\n *   const token = credential.accessToken;\r\n * }\r\n * // As this API can be used for sign-in, linking and reauthentication,\r\n * // check the operationType to determine what triggered this redirect\r\n * // operation.\r\n * const operationType = result.operationType;\r\n * ```\r\n *\r\n * @param auth - The {@link Auth} instance.\r\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\r\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\r\n *\r\n * @public\r\n */\n\n\nfunction _linkWithRedirect2() {\n  _linkWithRedirect2 = _asyncToGenerator(function* (user, provider, resolver) {\n    const userInternal = getModularInstance(user);\n\n    _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider); // Wait for auth initialization to complete, this will process pending redirects and clear the\n    // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n    // redirect and creating a PENDING_REDIRECT_KEY entry.\n\n\n    yield userInternal.auth._initializationPromise; // Allow the resolver to error before persisting the redirect user\n\n    const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n    yield _assertLinkedStatus(false, userInternal, provider.providerId);\n    yield _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n    const eventId = yield prepareUserForRedirect(userInternal);\n    return resolverInternal._openRedirect(userInternal.auth, provider, \"linkViaRedirect\"\n    /* AuthEventType.LINK_VIA_REDIRECT */\n    , eventId);\n  });\n  return _linkWithRedirect2.apply(this, arguments);\n}\n\nfunction getRedirectResult(_x182, _x183) {\n  return _getRedirectResult2.apply(this, arguments);\n}\n\nfunction _getRedirectResult2() {\n  _getRedirectResult2 = _asyncToGenerator(function* (auth, resolver) {\n    yield _castAuth(auth)._initializationPromise;\n    return _getRedirectResult(auth, resolver, false);\n  });\n  return _getRedirectResult2.apply(this, arguments);\n}\n\nfunction _getRedirectResult(_x184, _x185) {\n  return _getRedirectResult3.apply(this, arguments);\n}\n\nfunction _getRedirectResult3() {\n  _getRedirectResult3 = _asyncToGenerator(function* (auth, resolverExtern, bypassAuthState = false) {\n    const authInternal = _castAuth(auth);\n\n    const resolver = _withDefaultResolver(authInternal, resolverExtern);\n\n    const action = new RedirectAction(authInternal, resolver, bypassAuthState);\n    const result = yield action.execute();\n\n    if (result && !bypassAuthState) {\n      delete result.user._redirectEventId;\n      yield authInternal._persistUserIfCurrent(result.user);\n      yield authInternal._setRedirectUser(null, resolverExtern);\n    }\n\n    return result;\n  });\n  return _getRedirectResult3.apply(this, arguments);\n}\n\nfunction prepareUserForRedirect(_x186) {\n  return _prepareUserForRedirect.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// The amount of time to store the UIDs of seen events; this is\n// set to 10 min by default\n\n\nfunction _prepareUserForRedirect() {\n  _prepareUserForRedirect = _asyncToGenerator(function* (user) {\n    const eventId = _generateEventId(`${user.uid}:::`);\n\n    user._redirectEventId = eventId;\n    yield user.auth._setRedirectUser(user);\n    yield user.auth._persistUserIfCurrent(user);\n    return eventId;\n  });\n  return _prepareUserForRedirect.apply(this, arguments);\n}\n\nconst EVENT_DUPLICATION_CACHE_DURATION_MS = 10 * 60 * 1000;\n\nclass AuthEventManager {\n  constructor(auth) {\n    this.auth = auth;\n    this.cachedEventUids = new Set();\n    this.consumers = new Set();\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n    this.lastProcessedEventTime = Date.now();\n  }\n\n  registerConsumer(authEventConsumer) {\n    this.consumers.add(authEventConsumer);\n\n    if (this.queuedRedirectEvent && this.isEventForConsumer(this.queuedRedirectEvent, authEventConsumer)) {\n      this.sendToConsumer(this.queuedRedirectEvent, authEventConsumer);\n      this.saveEventToCache(this.queuedRedirectEvent);\n      this.queuedRedirectEvent = null;\n    }\n  }\n\n  unregisterConsumer(authEventConsumer) {\n    this.consumers.delete(authEventConsumer);\n  }\n\n  onEvent(event) {\n    // Check if the event has already been handled\n    if (this.hasEventBeenHandled(event)) {\n      return false;\n    }\n\n    let handled = false;\n    this.consumers.forEach(consumer => {\n      if (this.isEventForConsumer(event, consumer)) {\n        handled = true;\n        this.sendToConsumer(event, consumer);\n        this.saveEventToCache(event);\n      }\n    });\n\n    if (this.hasHandledPotentialRedirect || !isRedirectEvent(event)) {\n      // If we've already seen a redirect before, or this is a popup event,\n      // bail now\n      return handled;\n    }\n\n    this.hasHandledPotentialRedirect = true; // If the redirect wasn't handled, hang on to it\n\n    if (!handled) {\n      this.queuedRedirectEvent = event;\n      handled = true;\n    }\n\n    return handled;\n  }\n\n  sendToConsumer(event, consumer) {\n    var _a;\n\n    if (event.error && !isNullRedirectEvent(event)) {\n      const code = ((_a = event.error.code) === null || _a === void 0 ? void 0 : _a.split('auth/')[1]) || \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      ;\n      consumer.onError(_createError(this.auth, code));\n    } else {\n      consumer.onAuthEvent(event);\n    }\n  }\n\n  isEventForConsumer(event, consumer) {\n    const eventIdMatches = consumer.eventId === null || !!event.eventId && event.eventId === consumer.eventId;\n    return consumer.filter.includes(event.type) && eventIdMatches;\n  }\n\n  hasEventBeenHandled(event) {\n    if (Date.now() - this.lastProcessedEventTime >= EVENT_DUPLICATION_CACHE_DURATION_MS) {\n      this.cachedEventUids.clear();\n    }\n\n    return this.cachedEventUids.has(eventUid(event));\n  }\n\n  saveEventToCache(event) {\n    this.cachedEventUids.add(eventUid(event));\n    this.lastProcessedEventTime = Date.now();\n  }\n\n}\n\nfunction eventUid(e) {\n  return [e.type, e.eventId, e.sessionId, e.tenantId].filter(v => v).join('-');\n}\n\nfunction isNullRedirectEvent({\n  type,\n  error\n}) {\n  return type === \"unknown\"\n  /* AuthEventType.UNKNOWN */\n  && (error === null || error === void 0 ? void 0 : error.code) === `auth/${\"no-auth-event\"\n  /* AuthErrorCode.NO_AUTH_EVENT */\n  }`;\n}\n\nfunction isRedirectEvent(event) {\n  switch (event.type) {\n    case \"signInViaRedirect\"\n    /* AuthEventType.SIGN_IN_VIA_REDIRECT */\n    :\n    case \"linkViaRedirect\"\n    /* AuthEventType.LINK_VIA_REDIRECT */\n    :\n    case \"reauthViaRedirect\"\n    /* AuthEventType.REAUTH_VIA_REDIRECT */\n    :\n      return true;\n\n    case \"unknown\"\n    /* AuthEventType.UNKNOWN */\n    :\n      return isNullRedirectEvent(event);\n\n    default:\n      return false;\n  }\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _getProjectConfig(_x187) {\n  return _getProjectConfig2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _getProjectConfig2() {\n  _getProjectConfig2 = _asyncToGenerator(function* (auth, request = {}) {\n    return _performApiRequest(auth, \"GET\"\n    /* HttpMethod.GET */\n    , \"/v1/projects\"\n    /* Endpoint.GET_PROJECT_CONFIG */\n    , request);\n  });\n  return _getProjectConfig2.apply(this, arguments);\n}\n\nconst IP_ADDRESS_REGEX = /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/;\nconst HTTP_REGEX = /^https?/;\n\nfunction _validateOrigin(_x188) {\n  return _validateOrigin2.apply(this, arguments);\n}\n\nfunction _validateOrigin2() {\n  _validateOrigin2 = _asyncToGenerator(function* (auth) {\n    // Skip origin validation if we are in an emulated environment\n    if (auth.config.emulator) {\n      return;\n    }\n\n    const {\n      authorizedDomains\n    } = yield _getProjectConfig(auth);\n\n    for (const domain of authorizedDomains) {\n      try {\n        if (matchDomain(domain)) {\n          return;\n        }\n      } catch (_a) {// Do nothing if there's a URL error; just continue searching\n      }\n    } // In the old SDK, this error also provides helpful messages.\n\n\n    _fail(auth, \"unauthorized-domain\"\n    /* AuthErrorCode.INVALID_ORIGIN */\n    );\n  });\n  return _validateOrigin2.apply(this, arguments);\n}\n\nfunction matchDomain(expected) {\n  const currentUrl = _getCurrentUrl();\n\n  const {\n    protocol,\n    hostname\n  } = new URL(currentUrl);\n\n  if (expected.startsWith('chrome-extension://')) {\n    const ceUrl = new URL(expected);\n\n    if (ceUrl.hostname === '' && hostname === '') {\n      // For some reason we're not parsing chrome URLs properly\n      return protocol === 'chrome-extension:' && expected.replace('chrome-extension://', '') === currentUrl.replace('chrome-extension://', '');\n    }\n\n    return protocol === 'chrome-extension:' && ceUrl.hostname === hostname;\n  }\n\n  if (!HTTP_REGEX.test(protocol)) {\n    return false;\n  }\n\n  if (IP_ADDRESS_REGEX.test(expected)) {\n    // The domain has to be exactly equal to the pattern, as an IP domain will\n    // only contain the IP, no extra character.\n    return hostname === expected;\n  } // Dots in pattern should be escaped.\n\n\n  const escapedDomainPattern = expected.replace(/\\./g, '\\\\.'); // Non ip address domains.\n  // domain.com = *.domain.com OR domain.com\n\n  const re = new RegExp('^(.+\\\\.' + escapedDomainPattern + '|' + escapedDomainPattern + ')$', 'i');\n  return re.test(hostname);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC.\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst NETWORK_TIMEOUT = new Delay(30000, 60000);\n/**\r\n * Reset unlaoded GApi modules. If gapi.load fails due to a network error,\r\n * it will stop working after a retrial. This is a hack to fix this issue.\r\n */\n\nfunction resetUnloadedGapiModules() {\n  // Clear last failed gapi.load state to force next gapi.load to first\n  // load the failed gapi.iframes module.\n  // Get gapix.beacon context.\n  const beacon = _window().___jsl; // Get current hint.\n\n\n  if (beacon === null || beacon === void 0 ? void 0 : beacon.H) {\n    // Get gapi hint.\n    for (const hint of Object.keys(beacon.H)) {\n      // Requested modules.\n      beacon.H[hint].r = beacon.H[hint].r || []; // Loaded modules.\n\n      beacon.H[hint].L = beacon.H[hint].L || []; // Set requested modules to a copy of the loaded modules.\n\n      beacon.H[hint].r = [...beacon.H[hint].L]; // Clear pending callbacks.\n\n      if (beacon.CP) {\n        for (let i = 0; i < beacon.CP.length; i++) {\n          // Remove all failed pending callbacks.\n          beacon.CP[i] = null;\n        }\n      }\n    }\n  }\n}\n\nfunction loadGapi(auth) {\n  return new Promise((resolve, reject) => {\n    var _a, _b, _c; // Function to run when gapi.load is ready.\n\n\n    function loadGapiIframe() {\n      // The developer may have tried to previously run gapi.load and failed.\n      // Run this to fix that.\n      resetUnloadedGapiModules();\n      gapi.load('gapi.iframes', {\n        callback: () => {\n          resolve(gapi.iframes.getContext());\n        },\n        ontimeout: () => {\n          // The above reset may be sufficient, but having this reset after\n          // failure ensures that if the developer calls gapi.load after the\n          // connection is re-established and before another attempt to embed\n          // the iframe, it would work and would not be broken because of our\n          // failed attempt.\n          // Timeout when gapi.iframes.Iframe not loaded.\n          resetUnloadedGapiModules();\n          reject(_createError(auth, \"network-request-failed\"\n          /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n          ));\n        },\n        timeout: NETWORK_TIMEOUT.get()\n      });\n    }\n\n    if ((_b = (_a = _window().gapi) === null || _a === void 0 ? void 0 : _a.iframes) === null || _b === void 0 ? void 0 : _b.Iframe) {\n      // If gapi.iframes.Iframe available, resolve.\n      resolve(gapi.iframes.getContext());\n    } else if (!!((_c = _window().gapi) === null || _c === void 0 ? void 0 : _c.load)) {\n      // Gapi loader ready, load gapi.iframes.\n      loadGapiIframe();\n    } else {\n      // Create a new iframe callback when this is called so as not to overwrite\n      // any previous defined callback. This happens if this method is called\n      // multiple times in parallel and could result in the later callback\n      // overwriting the previous one. This would end up with a iframe\n      // timeout.\n      const cbName = _generateCallbackName('iframefcb'); // GApi loader not available, dynamically load platform.js.\n\n\n      _window()[cbName] = () => {\n        // GApi loader should be ready.\n        if (!!gapi.load) {\n          loadGapiIframe();\n        } else {\n          // Gapi loader failed, throw error.\n          reject(_createError(auth, \"network-request-failed\"\n          /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n          ));\n        }\n      }; // Load GApi loader.\n\n\n      return _loadJS(`https://apis.google.com/js/api.js?onload=${cbName}`).catch(e => reject(e));\n    }\n  }).catch(error => {\n    // Reset cached promise to allow for retrial.\n    cachedGApiLoader = null;\n    throw error;\n  });\n}\n\nlet cachedGApiLoader = null;\n\nfunction _loadGapi(auth) {\n  cachedGApiLoader = cachedGApiLoader || loadGapi(auth);\n  return cachedGApiLoader;\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC.\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst PING_TIMEOUT = new Delay(5000, 15000);\nconst IFRAME_PATH = '__/auth/iframe';\nconst EMULATED_IFRAME_PATH = 'emulator/auth/iframe';\nconst IFRAME_ATTRIBUTES = {\n  style: {\n    position: 'absolute',\n    top: '-100px',\n    width: '1px',\n    height: '1px'\n  },\n  'aria-hidden': 'true',\n  tabindex: '-1'\n}; // Map from apiHost to endpoint ID for passing into iframe. In current SDK, apiHost can be set to\n// anything (not from a list of endpoints with IDs as in legacy), so this is the closest we can get.\n\nconst EID_FROM_APIHOST = new Map([[\"identitytoolkit.googleapis.com\"\n/* DefaultConfig.API_HOST */\n, 'p'], ['staging-identitytoolkit.sandbox.googleapis.com', 's'], ['test-identitytoolkit.sandbox.googleapis.com', 't'] // test\n]);\n\nfunction getIframeUrl(auth) {\n  const config = auth.config;\n\n  _assert(config.authDomain, auth, \"auth-domain-config-required\"\n  /* AuthErrorCode.MISSING_AUTH_DOMAIN */\n  );\n\n  const url = config.emulator ? _emulatorUrl(config, EMULATED_IFRAME_PATH) : `https://${auth.config.authDomain}/${IFRAME_PATH}`;\n  const params = {\n    apiKey: config.apiKey,\n    appName: auth.name,\n    v: SDK_VERSION\n  };\n  const eid = EID_FROM_APIHOST.get(auth.config.apiHost);\n\n  if (eid) {\n    params.eid = eid;\n  }\n\n  const frameworks = auth._getFrameworks();\n\n  if (frameworks.length) {\n    params.fw = frameworks.join(',');\n  }\n\n  return `${url}?${querystring(params).slice(1)}`;\n}\n\nfunction _openIframe(_x189) {\n  return _openIframe2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC.\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction _openIframe2() {\n  _openIframe2 = _asyncToGenerator(function* (auth) {\n    const context = yield _loadGapi(auth);\n\n    const gapi = _window().gapi;\n\n    _assert(gapi, auth, \"internal-error\"\n    /* AuthErrorCode.INTERNAL_ERROR */\n    );\n\n    return context.open({\n      where: document.body,\n      url: getIframeUrl(auth),\n      messageHandlersFilter: gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,\n      attributes: IFRAME_ATTRIBUTES,\n      dontclear: true\n    }, iframe => new Promise( /*#__PURE__*/function () {\n      var _ref27 = _asyncToGenerator(function* (resolve, reject) {\n        yield iframe.restyle({\n          // Prevent iframe from closing on mouse out.\n          setHideOnLeave: false\n        });\n\n        const networkError = _createError(auth, \"network-request-failed\"\n        /* AuthErrorCode.NETWORK_REQUEST_FAILED */\n        ); // Confirm iframe is correctly loaded.\n        // To fallback on failure, set a timeout.\n\n\n        const networkErrorTimer = _window().setTimeout(() => {\n          reject(networkError);\n        }, PING_TIMEOUT.get()); // Clear timer and resolve pending iframe ready promise.\n\n\n        function clearTimerAndResolve() {\n          _window().clearTimeout(networkErrorTimer);\n\n          resolve(iframe);\n        } // This returns an IThenable. However the reject part does not call\n        // when the iframe is not loaded.\n\n\n        iframe.ping(clearTimerAndResolve).then(clearTimerAndResolve, () => {\n          reject(networkError);\n        });\n      });\n\n      return function (_x200, _x201) {\n        return _ref27.apply(this, arguments);\n      };\n    }()));\n  });\n  return _openIframe2.apply(this, arguments);\n}\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nclass AuthPopup {\n  constructor(window) {\n    this.window = window;\n    this.associatedEvent = null;\n  }\n\n  close() {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n\n}\n\nfunction _open(auth, url, name, width = DEFAULT_WIDTH, height = DEFAULT_HEIGHT) {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n  const options = Object.assign(Object.assign({}, BASE_POPUP_OPTIONS), {\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  }); // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL; // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce((accum, [key, value]) => `${accum}${key}=${value},`, '');\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  } // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n\n\n  const newWin = window.open(url || '', target, optionsString);\n\n  _assert(newWin, auth, \"popup-blocked\"\n  /* AuthErrorCode.POPUP_BLOCKED */\n  ); // Flaky on IE edge, encapsulate with a try and catch.\n\n\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url, target) {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent('click', true, true, window, 1, 0, 0, 0, 0, false, false, false, false, 1, null);\n  el.dispatchEvent(click);\n}\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * URL for Authentication widget which will initiate the OAuth handshake\r\n *\r\n * @internal\r\n */\n\n\nconst WIDGET_PATH = '__/auth/handler';\n/**\r\n * URL for emulated environment\r\n *\r\n * @internal\r\n */\n\nconst EMULATOR_WIDGET_PATH = 'emulator/auth/handler';\n/**\r\n * Fragment name for the App Check token that gets passed to the widget\r\n *\r\n * @internal\r\n */\n\nconst FIREBASE_APP_CHECK_FRAGMENT_ID = encodeURIComponent('fac');\n\nfunction _getRedirectUrl(_x190, _x191, _x192, _x193, _x194, _x195) {\n  return _getRedirectUrl2.apply(this, arguments);\n}\n\nfunction _getRedirectUrl2() {\n  _getRedirectUrl2 = _asyncToGenerator(function* (auth, provider, authType, redirectUrl, eventId, additionalParams) {\n    _assert(auth.config.authDomain, auth, \"auth-domain-config-required\"\n    /* AuthErrorCode.MISSING_AUTH_DOMAIN */\n    );\n\n    _assert(auth.config.apiKey, auth, \"invalid-api-key\"\n    /* AuthErrorCode.INVALID_API_KEY */\n    );\n\n    const params = {\n      apiKey: auth.config.apiKey,\n      appName: auth.name,\n      authType,\n      redirectUrl,\n      v: SDK_VERSION,\n      eventId\n    };\n\n    if (provider instanceof FederatedAuthProvider) {\n      provider.setDefaultLanguage(auth.languageCode);\n      params.providerId = provider.providerId || '';\n\n      if (!isEmpty(provider.getCustomParameters())) {\n        params.customParameters = JSON.stringify(provider.getCustomParameters());\n      } // TODO set additionalParams from the provider as well?\n\n\n      for (const [key, value] of Object.entries(additionalParams || {})) {\n        params[key] = value;\n      }\n    }\n\n    if (provider instanceof BaseOAuthProvider) {\n      const scopes = provider.getScopes().filter(scope => scope !== '');\n\n      if (scopes.length > 0) {\n        params.scopes = scopes.join(',');\n      }\n    }\n\n    if (auth.tenantId) {\n      params.tid = auth.tenantId;\n    } // TODO: maybe set eid as endipointId\n    // TODO: maybe set fw as Frameworks.join(\",\")\n\n\n    const paramsDict = params;\n\n    for (const key of Object.keys(paramsDict)) {\n      if (paramsDict[key] === undefined) {\n        delete paramsDict[key];\n      }\n    } // Sets the App Check token to pass to the widget\n\n\n    const appCheckToken = yield auth._getAppCheckToken();\n    const appCheckTokenFragment = appCheckToken ? `#${FIREBASE_APP_CHECK_FRAGMENT_ID}=${encodeURIComponent(appCheckToken)}` : ''; // Start at index 1 to skip the leading '&' in the query string\n\n    return `${getHandlerBase(auth)}?${querystring(paramsDict).slice(1)}${appCheckTokenFragment}`;\n  });\n  return _getRedirectUrl2.apply(this, arguments);\n}\n\nfunction getHandlerBase({\n  config\n}) {\n  if (!config.emulator) {\n    return `https://${config.authDomain}/${WIDGET_PATH}`;\n  }\n\n  return _emulatorUrl(config, EMULATOR_WIDGET_PATH);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * The special web storage event\r\n *\r\n */\n\n\nconst WEB_STORAGE_SUPPORT_KEY = 'webStorageSupport';\n\nclass BrowserPopupRedirectResolver {\n  constructor() {\n    this.eventManagers = {};\n    this.iframes = {};\n    this.originValidationPromises = {};\n    this._redirectPersistence = browserSessionPersistence;\n    this._completeRedirectFn = _getRedirectResult;\n    this._overrideRedirectResult = _overrideRedirectResult;\n  } // Wrapping in async even though we don't await anywhere in order\n  // to make sure errors are raised as promise rejections\n\n\n  _openPopup(auth, provider, authType, eventId) {\n    var _this66 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      debugAssert((_a = _this66.eventManagers[auth._key()]) === null || _a === void 0 ? void 0 : _a.manager, '_initialize() not called before _openPopup()');\n      const url = yield _getRedirectUrl(auth, provider, authType, _getCurrentUrl(), eventId);\n      return _open(auth, url, _generateEventId());\n    })();\n  }\n\n  _openRedirect(auth, provider, authType, eventId) {\n    var _this67 = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this67._originValidation(auth);\n      const url = yield _getRedirectUrl(auth, provider, authType, _getCurrentUrl(), eventId);\n\n      _setWindowLocation(url);\n\n      return new Promise(() => {});\n    })();\n  }\n\n  _initialize(auth) {\n    const key = auth._key();\n\n    if (this.eventManagers[key]) {\n      const {\n        manager,\n        promise\n      } = this.eventManagers[key];\n\n      if (manager) {\n        return Promise.resolve(manager);\n      } else {\n        debugAssert(promise, 'If manager is not set, promise should be');\n        return promise;\n      }\n    }\n\n    const promise = this.initAndGetManager(auth);\n    this.eventManagers[key] = {\n      promise\n    }; // If the promise is rejected, the key should be removed so that the\n    // operation can be retried later.\n\n    promise.catch(() => {\n      delete this.eventManagers[key];\n    });\n    return promise;\n  }\n\n  initAndGetManager(auth) {\n    var _this68 = this;\n\n    return _asyncToGenerator(function* () {\n      const iframe = yield _openIframe(auth);\n      const manager = new AuthEventManager(auth);\n      iframe.register('authEvent', iframeEvent => {\n        _assert(iframeEvent === null || iframeEvent === void 0 ? void 0 : iframeEvent.authEvent, auth, \"invalid-auth-event\"\n        /* AuthErrorCode.INVALID_AUTH_EVENT */\n        ); // TODO: Consider splitting redirect and popup events earlier on\n\n\n        const handled = manager.onEvent(iframeEvent.authEvent);\n        return {\n          status: handled ? \"ACK\"\n          /* GapiOutcome.ACK */\n          : \"ERROR\"\n          /* GapiOutcome.ERROR */\n\n        };\n      }, gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER);\n      _this68.eventManagers[auth._key()] = {\n        manager\n      };\n      _this68.iframes[auth._key()] = iframe;\n      return manager;\n    })();\n  }\n\n  _isIframeWebStorageSupported(auth, cb) {\n    const iframe = this.iframes[auth._key()];\n\n    iframe.send(WEB_STORAGE_SUPPORT_KEY, {\n      type: WEB_STORAGE_SUPPORT_KEY\n    }, result => {\n      var _a;\n\n      const isSupported = (_a = result === null || result === void 0 ? void 0 : result[0]) === null || _a === void 0 ? void 0 : _a[WEB_STORAGE_SUPPORT_KEY];\n\n      if (isSupported !== undefined) {\n        cb(!!isSupported);\n      }\n\n      _fail(auth, \"internal-error\"\n      /* AuthErrorCode.INTERNAL_ERROR */\n      );\n    }, gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER);\n  }\n\n  _originValidation(auth) {\n    const key = auth._key();\n\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  get _shouldInitProactively() {\n    // Mobile browsers and Safari need to optimistically initialize\n    return _isMobileBrowser() || _isSafari() || _isIOS();\n  }\n\n}\n/**\r\n * An implementation of {@link PopupRedirectResolver} suitable for browser\r\n * based applications.\r\n *\r\n * @public\r\n */\n\n\nconst browserPopupRedirectResolver = BrowserPopupRedirectResolver;\n\nclass MultiFactorAssertionImpl {\n  constructor(factorId) {\n    this.factorId = factorId;\n  }\n\n  _process(auth, session, displayName) {\n    switch (session.type) {\n      case \"enroll\"\n      /* MultiFactorSessionType.ENROLL */\n      :\n        return this._finalizeEnroll(auth, session.credential, displayName);\n\n      case \"signin\"\n      /* MultiFactorSessionType.SIGN_IN */\n      :\n        return this._finalizeSignIn(auth, session.credential);\n\n      default:\n        return debugFail('unexpected MultiFactorSessionType');\n    }\n  }\n\n}\n/**\r\n * {@inheritdoc PhoneMultiFactorAssertion}\r\n *\r\n * @public\r\n */\n\n\nclass PhoneMultiFactorAssertionImpl extends MultiFactorAssertionImpl {\n  constructor(credential) {\n    super(\"phone\"\n    /* FactorId.PHONE */\n    );\n    this.credential = credential;\n  }\n  /** @internal */\n\n\n  static _fromCredential(credential) {\n    return new PhoneMultiFactorAssertionImpl(credential);\n  }\n  /** @internal */\n\n\n  _finalizeEnroll(auth, idToken, displayName) {\n    return finalizeEnrollPhoneMfa(auth, {\n      idToken,\n      displayName,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n  /** @internal */\n\n\n  _finalizeSignIn(auth, mfaPendingCredential) {\n    return finalizeSignInPhoneMfa(auth, {\n      mfaPendingCredential,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n\n}\n/**\r\n * Provider for generating a {@link PhoneMultiFactorAssertion}.\r\n *\r\n * @public\r\n */\n\n\nlet PhoneMultiFactorGenerator = /*#__PURE__*/(() => {\n  class PhoneMultiFactorGenerator {\n    constructor() {}\n    /**\r\n     * Provides a {@link PhoneMultiFactorAssertion} to confirm ownership of the phone second factor.\r\n     *\r\n     * @param phoneAuthCredential - A credential provided by {@link PhoneAuthProvider.credential}.\r\n     * @returns A {@link PhoneMultiFactorAssertion} which can be used with\r\n     * {@link MultiFactorResolver.resolveSignIn}\r\n     */\n\n\n    static assertion(credential) {\n      return PhoneMultiFactorAssertionImpl._fromCredential(credential);\n    }\n\n  }\n\n  /**\r\n   * The identifier of the phone second factor: `phone`.\r\n   */\n  PhoneMultiFactorGenerator.FACTOR_ID = 'phone';\n  /**\r\n   * Provider for generating a {@link TotpMultiFactorAssertion}.\r\n   *\r\n   * @public\r\n   */\n\n  return PhoneMultiFactorGenerator;\n})();\nlet TotpMultiFactorGenerator = /*#__PURE__*/(() => {\n  class TotpMultiFactorGenerator {\n    /**\r\n     * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of\r\n     * the TOTP (time-based one-time password) second factor.\r\n     * This assertion is used to complete enrollment in TOTP second factor.\r\n     *\r\n     * @param secret A {@link TotpSecret} containing the shared secret key and other TOTP parameters.\r\n     * @param oneTimePassword One-time password from TOTP App.\r\n     * @returns A {@link TotpMultiFactorAssertion} which can be used with\r\n     * {@link MultiFactorUser.enroll}.\r\n     */\n    static assertionForEnrollment(secret, oneTimePassword) {\n      return TotpMultiFactorAssertionImpl._fromSecret(secret, oneTimePassword);\n    }\n    /**\r\n     * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of the TOTP second factor.\r\n     * This assertion is used to complete signIn with TOTP as the second factor.\r\n     *\r\n     * @param enrollmentId identifies the enrolled TOTP second factor.\r\n     * @param oneTimePassword One-time password from TOTP App.\r\n     * @returns A {@link TotpMultiFactorAssertion} which can be used with\r\n     * {@link MultiFactorResolver.resolveSignIn}.\r\n     */\n\n\n    static assertionForSignIn(enrollmentId, oneTimePassword) {\n      return TotpMultiFactorAssertionImpl._fromEnrollmentId(enrollmentId, oneTimePassword);\n    }\n    /**\r\n     * Returns a promise to {@link TotpSecret} which contains the TOTP shared secret key and other parameters.\r\n     * Creates a TOTP secret as part of enrolling a TOTP second factor.\r\n     * Used for generating a QR code URL or inputting into a TOTP app.\r\n     * This method uses the auth instance corresponding to the user in the multiFactorSession.\r\n     *\r\n     * @param session The {@link MultiFactorSession} that the user is part of.\r\n     * @returns A promise to {@link TotpSecret}.\r\n     */\n\n\n    static generateSecret(session) {\n      return _asyncToGenerator(function* () {\n        const mfaSession = session;\n\n        _assert(typeof mfaSession.auth !== 'undefined', \"internal-error\"\n        /* AuthErrorCode.INTERNAL_ERROR */\n        );\n\n        const response = yield startEnrollTotpMfa(mfaSession.auth, {\n          idToken: mfaSession.credential,\n          totpEnrollmentInfo: {}\n        });\n        return TotpSecret._fromStartTotpMfaEnrollmentResponse(response, mfaSession.auth);\n      })();\n    }\n\n  }\n\n  /**\r\n   * The identifier of the TOTP second factor: `totp`.\r\n   */\n  TotpMultiFactorGenerator.FACTOR_ID = \"totp\"\n  /* FactorId.TOTP */\n  ;\n  return TotpMultiFactorGenerator;\n})();\n\nclass TotpMultiFactorAssertionImpl extends MultiFactorAssertionImpl {\n  constructor(otp, enrollmentId, secret) {\n    super(\"totp\"\n    /* FactorId.TOTP */\n    );\n    this.otp = otp;\n    this.enrollmentId = enrollmentId;\n    this.secret = secret;\n  }\n  /** @internal */\n\n\n  static _fromSecret(secret, otp) {\n    return new TotpMultiFactorAssertionImpl(otp, undefined, secret);\n  }\n  /** @internal */\n\n\n  static _fromEnrollmentId(enrollmentId, otp) {\n    return new TotpMultiFactorAssertionImpl(otp, enrollmentId);\n  }\n  /** @internal */\n\n\n  _finalizeEnroll(auth, idToken, displayName) {\n    var _this69 = this;\n\n    return _asyncToGenerator(function* () {\n      _assert(typeof _this69.secret !== 'undefined', auth, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      return finalizeEnrollTotpMfa(auth, {\n        idToken,\n        displayName,\n        totpVerificationInfo: _this69.secret._makeTotpVerificationInfo(_this69.otp)\n      });\n    })();\n  }\n  /** @internal */\n\n\n  _finalizeSignIn(auth, mfaPendingCredential) {\n    var _this70 = this;\n\n    return _asyncToGenerator(function* () {\n      _assert(_this70.enrollmentId !== undefined && _this70.otp !== undefined, auth, \"argument-error\"\n      /* AuthErrorCode.ARGUMENT_ERROR */\n      );\n\n      const totpVerificationInfo = {\n        verificationCode: _this70.otp\n      };\n      return finalizeSignInTotpMfa(auth, {\n        mfaPendingCredential,\n        mfaEnrollmentId: _this70.enrollmentId,\n        totpVerificationInfo\n      });\n    })();\n  }\n\n}\n/**\r\n * Provider for generating a {@link TotpMultiFactorAssertion}.\r\n *\r\n * Stores the shared secret key and other parameters to generate time-based OTPs.\r\n * Implements methods to retrieve the shared secret key and generate a QR code URL.\r\n * @public\r\n */\n\n\nclass TotpSecret {\n  // The public members are declared outside the constructor so the docs can be generated.\n  constructor(secretKey, hashingAlgorithm, codeLength, codeIntervalSeconds, enrollmentCompletionDeadline, sessionInfo, auth) {\n    this.sessionInfo = sessionInfo;\n    this.auth = auth;\n    this.secretKey = secretKey;\n    this.hashingAlgorithm = hashingAlgorithm;\n    this.codeLength = codeLength;\n    this.codeIntervalSeconds = codeIntervalSeconds;\n    this.enrollmentCompletionDeadline = enrollmentCompletionDeadline;\n  }\n  /** @internal */\n\n\n  static _fromStartTotpMfaEnrollmentResponse(response, auth) {\n    return new TotpSecret(response.totpSessionInfo.sharedSecretKey, response.totpSessionInfo.hashingAlgorithm, response.totpSessionInfo.verificationCodeLength, response.totpSessionInfo.periodSec, new Date(response.totpSessionInfo.finalizeEnrollmentTime).toUTCString(), response.totpSessionInfo.sessionInfo, auth);\n  }\n  /** @internal */\n\n\n  _makeTotpVerificationInfo(otp) {\n    return {\n      sessionInfo: this.sessionInfo,\n      verificationCode: otp\n    };\n  }\n  /**\r\n   * Returns a QR code URL as described in\r\n   * https://github.com/google/google-authenticator/wiki/Key-Uri-Format\r\n   * This can be displayed to the user as a QR code to be scanned into a TOTP app like Google Authenticator.\r\n   * If the optional parameters are unspecified, an accountName of <userEmail> and issuer of <firebaseAppName> are used.\r\n   *\r\n   * @param accountName the name of the account/app along with a user identifier.\r\n   * @param issuer issuer of the TOTP (likely the app name).\r\n   * @returns A QR code URL string.\r\n   */\n\n\n  generateQrCodeUrl(accountName, issuer) {\n    var _a;\n\n    let useDefaults = false;\n\n    if (_isEmptyString(accountName) || _isEmptyString(issuer)) {\n      useDefaults = true;\n    }\n\n    if (useDefaults) {\n      if (_isEmptyString(accountName)) {\n        accountName = ((_a = this.auth.currentUser) === null || _a === void 0 ? void 0 : _a.email) || 'unknownuser';\n      }\n\n      if (_isEmptyString(issuer)) {\n        issuer = this.auth.name;\n      }\n    }\n\n    return `otpauth://totp/${issuer}:${accountName}?secret=${this.secretKey}&issuer=${issuer}&algorithm=${this.hashingAlgorithm}&digits=${this.codeLength}`;\n  }\n\n}\n/** @internal */\n\n\nfunction _isEmptyString(input) {\n  return typeof input === 'undefined' || (input === null || input === void 0 ? void 0 : input.length) === 0;\n}\n\nvar name = \"@firebase/auth\";\nvar version = \"0.23.2\";\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\nclass AuthInterop {\n  constructor(auth) {\n    this.auth = auth;\n    this.internalListeners = new Map();\n  }\n\n  getUid() {\n    var _a;\n\n    this.assertAuthConfigured();\n    return ((_a = this.auth.currentUser) === null || _a === void 0 ? void 0 : _a.uid) || null;\n  }\n\n  getToken(forceRefresh) {\n    var _this71 = this;\n\n    return _asyncToGenerator(function* () {\n      _this71.assertAuthConfigured();\n\n      yield _this71.auth._initializationPromise;\n\n      if (!_this71.auth.currentUser) {\n        return null;\n      }\n\n      const accessToken = yield _this71.auth.currentUser.getIdToken(forceRefresh);\n      return {\n        accessToken\n      };\n    })();\n  }\n\n  addAuthTokenListener(listener) {\n    this.assertAuthConfigured();\n\n    if (this.internalListeners.has(listener)) {\n      return;\n    }\n\n    const unsubscribe = this.auth.onIdTokenChanged(user => {\n      listener((user === null || user === void 0 ? void 0 : user.stsTokenManager.accessToken) || null);\n    });\n    this.internalListeners.set(listener, unsubscribe);\n    this.updateProactiveRefresh();\n  }\n\n  removeAuthTokenListener(listener) {\n    this.assertAuthConfigured();\n    const unsubscribe = this.internalListeners.get(listener);\n\n    if (!unsubscribe) {\n      return;\n    }\n\n    this.internalListeners.delete(listener);\n    unsubscribe();\n    this.updateProactiveRefresh();\n  }\n\n  assertAuthConfigured() {\n    _assert(this.auth._initializationPromise, \"dependent-sdk-initialized-before-auth\"\n    /* AuthErrorCode.DEPENDENT_SDK_INIT_BEFORE_AUTH */\n    );\n  }\n\n  updateProactiveRefresh() {\n    if (this.internalListeners.size > 0) {\n      this.auth._startProactiveRefresh();\n    } else {\n      this.auth._stopProactiveRefresh();\n    }\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nfunction getVersionForPlatform(clientPlatform) {\n  switch (clientPlatform) {\n    case \"Node\"\n    /* ClientPlatform.NODE */\n    :\n      return 'node';\n\n    case \"ReactNative\"\n    /* ClientPlatform.REACT_NATIVE */\n    :\n      return 'rn';\n\n    case \"Worker\"\n    /* ClientPlatform.WORKER */\n    :\n      return 'webworker';\n\n    case \"Cordova\"\n    /* ClientPlatform.CORDOVA */\n    :\n      return 'cordova';\n\n    default:\n      return undefined;\n  }\n}\n/** @internal */\n\n\nfunction registerAuth(clientPlatform) {\n  _registerComponent(new Component(\"auth\"\n  /* _ComponentName.AUTH */\n  , (container, {\n    options: deps\n  }) => {\n    const app = container.getProvider('app').getImmediate();\n    const heartbeatServiceProvider = container.getProvider('heartbeat');\n    const appCheckServiceProvider = container.getProvider('app-check-internal');\n    const {\n      apiKey,\n      authDomain\n    } = app.options;\n\n    _assert(apiKey && !apiKey.includes(':'), \"invalid-api-key\"\n    /* AuthErrorCode.INVALID_API_KEY */\n    , {\n      appName: app.name\n    });\n\n    const config = {\n      apiKey,\n      authDomain,\n      clientPlatform,\n      apiHost: \"identitytoolkit.googleapis.com\"\n      /* DefaultConfig.API_HOST */\n      ,\n      tokenApiHost: \"securetoken.googleapis.com\"\n      /* DefaultConfig.TOKEN_API_HOST */\n      ,\n      apiScheme: \"https\"\n      /* DefaultConfig.API_SCHEME */\n      ,\n      sdkClientVersion: _getClientVersion(clientPlatform)\n    };\n    const authInstance = new AuthImpl(app, heartbeatServiceProvider, appCheckServiceProvider, config);\n\n    _initializeAuthInstance(authInstance, deps);\n\n    return authInstance;\n  }, \"PUBLIC\"\n  /* ComponentType.PUBLIC */\n  )\n  /**\r\n   * Auth can only be initialized by explicitly calling getAuth() or initializeAuth()\r\n   * For why we do this, See go/firebase-next-auth-init\r\n   */\n  .setInstantiationMode(\"EXPLICIT\"\n  /* InstantiationMode.EXPLICIT */\n  )\n  /**\r\n   * Because all firebase products that depend on auth depend on auth-internal directly,\r\n   * we need to initialize auth-internal after auth is initialized to make it available to other firebase products.\r\n   */\n  .setInstanceCreatedCallback((container, _instanceIdentifier, _instance) => {\n    const authInternalProvider = container.getProvider(\"auth-internal\"\n    /* _ComponentName.AUTH_INTERNAL */\n    );\n    authInternalProvider.initialize();\n  }));\n\n  _registerComponent(new Component(\"auth-internal\"\n  /* _ComponentName.AUTH_INTERNAL */\n  , container => {\n    const auth = _castAuth(container.getProvider(\"auth\"\n    /* _ComponentName.AUTH */\n    ).getImmediate());\n\n    return (auth => new AuthInterop(auth))(auth);\n  }, \"PRIVATE\"\n  /* ComponentType.PRIVATE */\n  ).setInstantiationMode(\"EXPLICIT\"\n  /* InstantiationMode.EXPLICIT */\n  ));\n\n  registerVersion(name, version, getVersionForPlatform(clientPlatform)); // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n\n  registerVersion(name, version, 'esm2017');\n}\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst DEFAULT_ID_TOKEN_MAX_AGE = 5 * 60;\nconst authIdTokenMaxAge = getExperimentalSetting('authIdTokenMaxAge') || DEFAULT_ID_TOKEN_MAX_AGE;\nlet lastPostedIdToken = null;\n\nconst mintCookieFactory = url => /*#__PURE__*/function () {\n  var _ref21 = _asyncToGenerator(function* (user) {\n    const idTokenResult = user && (yield user.getIdTokenResult());\n    const idTokenAge = idTokenResult && (new Date().getTime() - Date.parse(idTokenResult.issuedAtTime)) / 1000;\n\n    if (idTokenAge && idTokenAge > authIdTokenMaxAge) {\n      return;\n    } // Specifically trip null => undefined when logged out, to delete any existing cookie\n\n\n    const idToken = idTokenResult === null || idTokenResult === void 0 ? void 0 : idTokenResult.token;\n\n    if (lastPostedIdToken === idToken) {\n      return;\n    }\n\n    lastPostedIdToken = idToken;\n    yield fetch(url, {\n      method: idToken ? 'POST' : 'DELETE',\n      headers: idToken ? {\n        'Authorization': `Bearer ${idToken}`\n      } : {}\n    });\n  });\n\n  return function (_x196) {\n    return _ref21.apply(this, arguments);\n  };\n}();\n/**\r\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\r\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\r\n *\r\n * @param app - The Firebase App.\r\n *\r\n * @public\r\n */\n\n\nfunction getAuth(app = getApp()) {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    popupRedirectResolver: browserPopupRedirectResolver,\n    persistence: [indexedDBLocalPersistence, browserLocalPersistence, browserSessionPersistence]\n  });\n  const authTokenSyncUrl = getExperimentalSetting('authTokenSyncURL');\n\n  if (authTokenSyncUrl) {\n    const mintCookie = mintCookieFactory(authTokenSyncUrl);\n    beforeAuthStateChanged(auth, mintCookie, () => mintCookie(auth.currentUser));\n    onIdTokenChanged(auth, user => mintCookie(user));\n  }\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nregisterAuth(\"Browser\"\n/* ClientPlatform.BROWSER */\n);\nexport { signInWithCredential as $, ActionCodeOperation as A, signOut as B, deleteUser as C, debugErrorMap as D, prodErrorMap as E, FactorId as F, AUTH_ERROR_CODES_MAP_DO_NOT_USE_INTERNALLY as G, initializeAuth as H, connectAuthEmulator as I, AuthCredential as J, EmailAuthCredential as K, OAuthCredential as L, PhoneAuthCredential as M, inMemoryPersistence as N, OperationType as O, PhoneAuthProvider as P, EmailAuthProvider as Q, RecaptchaVerifier as R, SignInMethod as S, TotpMultiFactorGenerator as T, FacebookAuthProvider as U, GoogleAuthProvider as V, GithubAuthProvider as W, OAuthProvider as X, SAMLAuthProvider as Y, TwitterAuthProvider as Z, signInAnonymously as _, browserSessionPersistence as a, linkWithCredential as a0, reauthenticateWithCredential as a1, signInWithCustomToken as a2, sendPasswordResetEmail as a3, confirmPasswordReset as a4, applyActionCode as a5, checkActionCode as a6, verifyPasswordResetCode as a7, createUserWithEmailAndPassword as a8, signInWithEmailAndPassword as a9, _assert as aA, AuthEventManager as aB, _getInstance as aC, _persistenceKeyName as aD, _getRedirectResult as aE, _overrideRedirectResult as aF, _clearRedirectOutcomes as aG, _castAuth as aH, UserImpl as aI, AuthImpl as aJ, _getClientVersion as aK, _generateEventId as aL, AuthPopup as aM, FetchProvider as aN, SAMLAuthCredential as aO, sendSignInLinkToEmail as aa, isSignInWithEmailLink as ab, signInWithEmailLink as ac, fetchSignInMethodsForEmail as ad, sendEmailVerification as ae, verifyBeforeUpdateEmail as af, ActionCodeURL as ag, parseActionCodeURL as ah, updateProfile as ai, updateEmail as aj, updatePassword as ak, getIdToken as al, getIdTokenResult as am, unlink as an, getAdditionalUserInfo as ao, reload as ap, getMultiFactorResolver as aq, multiFactor as ar, debugAssert as as, _isIOS as at, _isAndroid as au, _fail as av, _getRedirectUrl as aw, _getProjectConfig as ax, _isIOS7Or8 as ay, _createError as az, browserLocalPersistence as b, signInWithPopup as c, linkWithPopup as d, reauthenticateWithPopup as e, signInWithRedirect as f, linkWithRedirect as g, reauthenticateWithRedirect as h, indexedDBLocalPersistence as i, getRedirectResult as j, browserPopupRedirectResolver as k, linkWithPhoneNumber as l, PhoneMultiFactorGenerator as m, TotpSecret as n, getAuth as o, ProviderId as p, setPersistence as q, reauthenticateWithPhoneNumber as r, signInWithPhoneNumber as s, initializeRecaptchaConfig as t, updatePhoneNumber as u, onIdTokenChanged as v, beforeAuthStateChanged as w, onAuthStateChanged as x, useDeviceLanguage as y, updateCurrentUser as z }; //# sourceMappingURL=index-e3d5d3f4.js.map", "map": null, "metadata": {}, "sourceType": "module"}