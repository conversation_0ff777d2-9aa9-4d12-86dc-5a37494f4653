<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_33035_52883)">
<rect width="19" height="19" rx="9.5" fill="#0D0D0D" fill-opacity="0.3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.50043 14.7782C12.4153 14.7782 14.7782 12.4153 14.7782 9.50043C14.7782 6.5856 12.4153 4.22266 9.50043 4.22266C6.5856 4.22266 4.22266 6.5856 4.22266 9.50043C4.22266 12.4153 6.5856 14.7782 9.50043 14.7782ZM8.81089 11.5302L11.3021 10.0593C11.7147 9.81574 11.7147 9.18513 11.3021 8.94153L8.81089 7.4707C8.40989 7.23395 7.9171 7.5421 7.9171 8.0296V10.9713C7.9171 11.4588 8.40989 11.7669 8.81089 11.5302Z" fill="white"/>
</g>
<defs>
<filter id="filter0_b_33035_52883" x="-2.71429" y="-2.71429" width="24.4286" height="24.4286" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.35714"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33035_52883"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_33035_52883" result="shape"/>
</filter>
</defs>
</svg>
