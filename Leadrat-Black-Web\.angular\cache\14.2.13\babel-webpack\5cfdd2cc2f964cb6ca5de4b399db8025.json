{"ast": null, "code": "import Lazy<PERSON>rapper from './_LazyWrapper.js';\nimport copyArray from './_copyArray.js';\n/**\n * Creates a clone of the lazy wrapper object.\n *\n * @private\n * @name clone\n * @memberOf LazyWrapper\n * @returns {Object} Returns the cloned `<PERSON><PERSON>Wrapper` object.\n */\n\nfunction lazyClone() {\n  var result = new LazyWrapper(this.__wrapped__);\n  result.__actions__ = copyArray(this.__actions__);\n  result.__dir__ = this.__dir__;\n  result.__filtered__ = this.__filtered__;\n  result.__iteratees__ = copyArray(this.__iteratees__);\n  result.__takeCount__ = this.__takeCount__;\n  result.__views__ = copyArray(this.__views__);\n  return result;\n}\n\nexport default lazyClone;", "map": null, "metadata": {}, "sourceType": "module"}