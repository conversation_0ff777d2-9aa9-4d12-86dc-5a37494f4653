{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nimport LRUCache from './LRUCache.js'; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n// that use-case won't have a lot of benefit.\n\nvar RegExpCache = /*#__PURE__*/function () {\n  function RegExpCache(size) {\n    _classCallCheck(this, RegExpCache);\n\n    this.cache = new LRUCache(size);\n  }\n\n  _createClass(RegExpCache, [{\n    key: \"getPatternForRegExp\",\n    value: function getPatternForRegExp(pattern) {\n      var regExp = this.cache.get(pattern);\n\n      if (!regExp) {\n        regExp = new RegExp('^' + pattern);\n        this.cache.put(pattern, regExp);\n      }\n\n      return regExp;\n    }\n  }]);\n\n  return RegExpCache;\n}();\n\nexport { RegExpCache as default }; //# sourceMappingURL=RegExpCache.js.map", "map": null, "metadata": {}, "sourceType": "module"}