{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { empty } from '../observable/empty';\nexport function take(count) {\n  return source => {\n    if (count === 0) {\n      return empty();\n    } else {\n      return source.lift(new TakeOperator(count));\n    }\n  };\n}\n\nclass TakeOperator {\n  constructor(total) {\n    this.total = total;\n\n    if (this.total < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TakeSubscriber(subscriber, this.total));\n  }\n\n}\n\nclass TakeSubscriber extends Subscriber {\n  constructor(destination, total) {\n    super(destination);\n    this.total = total;\n    this.count = 0;\n  }\n\n  _next(value) {\n    const total = this.total;\n    const count = ++this.count;\n\n    if (count <= total) {\n      this.destination.next(value);\n\n      if (count === total) {\n        this.destination.complete();\n        this.unsubscribe();\n      }\n    }\n  }\n\n} //# sourceMappingURL=take.js.map", "map": null, "metadata": {}, "sourceType": "module"}