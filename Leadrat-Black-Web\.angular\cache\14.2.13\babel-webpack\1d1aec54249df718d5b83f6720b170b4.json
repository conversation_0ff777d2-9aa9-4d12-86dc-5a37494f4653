{"ast": null, "code": "import assocIndexOf from './_assocIndexOf.js';\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\n\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n  return index < 0 ? undefined : data[index][1];\n}\n\nexport default listCacheGet;", "map": null, "metadata": {}, "sourceType": "module"}