{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\n\n\nexport default function mergeArrays(a, b) {\n  var merged = a.slice();\n\n  for (var _iterator = _createForOfIteratorHelperLoose(b), _step; !(_step = _iterator()).done;) {\n    var element = _step.value;\n\n    if (a.indexOf(element) < 0) {\n      merged.push(element);\n    }\n  }\n\n  return merged.sort(function (a, b) {\n    return a - b;\n  }); // ES6 version, requires Set polyfill.\n  // let merged = new Set(a)\n  // for (const element of b) {\n  // \tmerged.add(i)\n  // }\n  // return Array.from(merged).sort((a, b) => a - b)\n} //# sourceMappingURL=mergeArrays.js.map", "map": null, "metadata": {}, "sourceType": "module"}